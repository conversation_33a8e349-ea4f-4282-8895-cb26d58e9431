import React from 'react';
import { motion } from 'framer-motion';
import { Tb<PERSON><PERSON>, TbQuestionMark, Tb<PERSON>sers, TbTrophy, TbPlayerPlay } from 'react-icons/tb';
import { Card, Button } from './index';

const QuizCard = ({
  quiz,
  onStart,
  onView,
  showResults = false,
  userResult = null,
  className = '',
  ...props
}) => {
  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'hard':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (percentage) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card
      interactive
      variant="default"
      className={`quiz-card overflow-hidden ${className}`}
      {...props}
    >
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
              {quiz.name}
            </h3>
            <p className="text-gray-600 text-sm line-clamp-2">
              {quiz.description || 'Test your knowledge with this comprehensive quiz'}
            </p>
          </div>
          
          {quiz.difficulty && (
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(quiz.difficulty)}`}>
              {quiz.difficulty}
            </span>
          )}
        </div>

        {/* Quiz Stats */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <TbQuestionMark className="w-4 h-4 text-primary-600" />
            <span>{quiz.questions?.length || 0} Questions</span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <TbClock className="w-4 h-4 text-primary-600" />
            <span>{quiz.duration || 30} min</span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <TbUsers className="w-4 h-4 text-primary-600" />
            <span>{quiz.attempts || 0} attempts</span>
          </div>
        </div>

        {/* Subject/Category */}
        {quiz.subject && (
          <div className="mb-4">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
              {quiz.subject}
            </span>
          </div>
        )}

        {/* User Result (if available) */}
        {showResults && userResult && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-50 rounded-lg p-4 mb-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <TbTrophy className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium text-gray-700">Your Best Score</span>
              </div>
              <div className={`text-lg font-bold ${getScoreColor(userResult.percentage)}`}>
                {userResult.percentage}%
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              {userResult.correctAnswers}/{userResult.totalQuestions} correct • 
              Completed {new Date(userResult.completedAt).toLocaleDateString()}
            </div>
          </motion.div>
        )}
      </div>

      {/* Actions */}
      <div className="px-6 pb-6">
        <div className="flex space-x-3">
          <Button
            variant="primary"
            size="md"
            className="flex-1"
            onClick={onStart}
            icon={<TbPlayerPlay />}
          >
            {showResults && userResult ? 'Retake Quiz' : 'Start Quiz'}
          </Button>
          
          {showResults && onView && (
            <Button
              variant="secondary"
              size="md"
              onClick={onView}
            >
              View Results
            </Button>
          )}
        </div>
      </div>

      {/* Progress Bar (if quiz is in progress) */}
      {quiz.progress && quiz.progress > 0 && quiz.progress < 100 && (
        <div className="px-6 pb-4">
          <div className="flex items-center justify-between text-xs text-gray-600 mb-2">
            <span>Progress</span>
            <span>{quiz.progress}%</span>
          </div>
          <div className="progress-bar">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${quiz.progress}%` }}
              transition={{ duration: 0.5 }}
              className="progress-fill"
            />
          </div>
        </div>
      )}

      {/* Hover Effect Overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-primary-600/5 to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
        whileHover={{ opacity: 1 }}
      />
    </Card>
  );
};

// Quiz Grid Component
export const QuizGrid = ({ quizzes, onQuizStart, onQuizView, showResults = false, userResults = {}, className = '' }) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {quizzes.map((quiz, index) => (
        <motion.div
          key={quiz._id || index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <QuizCard
            quiz={quiz}
            onStart={() => onQuizStart(quiz)}
            onView={onQuizView ? () => onQuizView(quiz) : undefined}
            showResults={showResults}
            userResult={userResults[quiz._id]}
          />
        </motion.div>
      ))}
    </div>
  );
};

export default QuizCard;
