{"ast": null, "code": "import { ColorBlock, Color as RcColor } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useMemo } from 'react';\nimport Collapse from '../../collapse';\nimport { useLocale } from '../../locale';\nimport theme from '../../theme';\nimport { generateColor } from '../util';\nconst genPresetColor = list => list.map(value => {\n  value.colors = value.colors.map(generateColor);\n  return value;\n});\nconst isBright = (value, bgColorToken) => {\n  const {\n    r,\n    g,\n    b,\n    a\n  } = value.toRgb();\n  const hsv = new RcColor(value.toRgbString()).onBackground(bgColorToken).toHsv();\n  if (a <= 0.5) {\n    // Adapted to dark mode\n    return hsv.v > 0.5;\n  }\n  return r * 0.299 + g * 0.587 + b * 0.114 > 192;\n};\nconst ColorPresets = _ref => {\n  let {\n    prefixCls,\n    presets,\n    value: color,\n    onChange\n  } = _ref;\n  const [locale] = useLocale('ColorPicker');\n  const {\n    token: {\n      colorBgElevated\n    }\n  } = theme.useToken();\n  const [presetsValue] = useMergedState(genPresetColor(presets), {\n    value: genPresetColor(presets),\n    postState: genPresetColor\n  });\n  const colorPresetsPrefixCls = `${prefixCls}-presets`;\n  const activeKeys = useMemo(() => presetsValue.map(preset => `panel-${preset.label}`), [presetsValue]);\n  const handleClick = colorValue => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(colorValue);\n  };\n  const items = presetsValue.map(preset => {\n    var _a;\n    return {\n      key: `panel-${preset.label}`,\n      label: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-label`\n      }, preset === null || preset === void 0 ? void 0 : preset.label),\n      children: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-items`\n      }, Array.isArray(preset === null || preset === void 0 ? void 0 : preset.colors) && ((_a = preset.colors) === null || _a === void 0 ? void 0 : _a.length) > 0 ? preset.colors.map(presetColor => /*#__PURE__*/React.createElement(ColorBlock, {\n        key: `preset-${presetColor.toHexString()}`,\n        color: generateColor(presetColor).toRgbString(),\n        prefixCls: prefixCls,\n        className: classNames(`${colorPresetsPrefixCls}-color`, {\n          [`${colorPresetsPrefixCls}-color-checked`]: presetColor.toHexString() === (color === null || color === void 0 ? void 0 : color.toHexString()),\n          [`${colorPresetsPrefixCls}-color-bright`]: isBright(presetColor, colorBgElevated)\n        }),\n        onClick: () => handleClick(presetColor)\n      })) : /*#__PURE__*/React.createElement(\"span\", {\n        className: `${colorPresetsPrefixCls}-empty`\n      }, locale.presetEmpty))\n    };\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorPresetsPrefixCls\n  }, /*#__PURE__*/React.createElement(Collapse, {\n    defaultActiveKey: activeKeys,\n    ghost: true,\n    items: items\n  }));\n};\nexport default ColorPresets;", "map": {"version": 3, "names": ["ColorBlock", "Color", "RcColor", "classNames", "useMergedState", "React", "useMemo", "Collapse", "useLocale", "theme", "generateColor", "genPresetColor", "list", "map", "value", "colors", "isBright", "bgColorToken", "r", "g", "b", "a", "toRgb", "hsv", "toRgbString", "onBackground", "toHsv", "v", "ColorPresets", "_ref", "prefixCls", "presets", "color", "onChange", "locale", "token", "colorBgElevated", "useToken", "presetsValue", "postState", "colorPresetsPrefixCls", "activeKeys", "preset", "label", "handleClick", "colorValue", "items", "_a", "key", "createElement", "className", "children", "Array", "isArray", "length", "presetColor", "toHexString", "onClick", "presetEmpty", "defaultActiveKey", "ghost"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/color-picker/components/ColorPresets.js"], "sourcesContent": ["import { ColorBlock, Color as RcColor } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useMemo } from 'react';\nimport Collapse from '../../collapse';\nimport { useLocale } from '../../locale';\nimport theme from '../../theme';\nimport { generateColor } from '../util';\nconst genPresetColor = list => list.map(value => {\n  value.colors = value.colors.map(generateColor);\n  return value;\n});\nconst isBright = (value, bgColorToken) => {\n  const {\n    r,\n    g,\n    b,\n    a\n  } = value.toRgb();\n  const hsv = new RcColor(value.toRgbString()).onBackground(bgColorToken).toHsv();\n  if (a <= 0.5) {\n    // Adapted to dark mode\n    return hsv.v > 0.5;\n  }\n  return r * 0.299 + g * 0.587 + b * 0.114 > 192;\n};\nconst ColorPresets = _ref => {\n  let {\n    prefixCls,\n    presets,\n    value: color,\n    onChange\n  } = _ref;\n  const [locale] = useLocale('ColorPicker');\n  const {\n    token: {\n      colorBgElevated\n    }\n  } = theme.useToken();\n  const [presetsValue] = useMergedState(genPresetColor(presets), {\n    value: genPresetColor(presets),\n    postState: genPresetColor\n  });\n  const colorPresetsPrefixCls = `${prefixCls}-presets`;\n  const activeKeys = useMemo(() => presetsValue.map(preset => `panel-${preset.label}`), [presetsValue]);\n  const handleClick = colorValue => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(colorValue);\n  };\n  const items = presetsValue.map(preset => {\n    var _a;\n    return {\n      key: `panel-${preset.label}`,\n      label: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-label`\n      }, preset === null || preset === void 0 ? void 0 : preset.label),\n      children: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-items`\n      }, Array.isArray(preset === null || preset === void 0 ? void 0 : preset.colors) && ((_a = preset.colors) === null || _a === void 0 ? void 0 : _a.length) > 0 ? preset.colors.map(presetColor => /*#__PURE__*/React.createElement(ColorBlock, {\n        key: `preset-${presetColor.toHexString()}`,\n        color: generateColor(presetColor).toRgbString(),\n        prefixCls: prefixCls,\n        className: classNames(`${colorPresetsPrefixCls}-color`, {\n          [`${colorPresetsPrefixCls}-color-checked`]: presetColor.toHexString() === (color === null || color === void 0 ? void 0 : color.toHexString()),\n          [`${colorPresetsPrefixCls}-color-bright`]: isBright(presetColor, colorBgElevated)\n        }),\n        onClick: () => handleClick(presetColor)\n      })) : /*#__PURE__*/React.createElement(\"span\", {\n        className: `${colorPresetsPrefixCls}-empty`\n      }, locale.presetEmpty))\n    };\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorPresetsPrefixCls\n  }, /*#__PURE__*/React.createElement(Collapse, {\n    defaultActiveKey: activeKeys,\n    ghost: true,\n    items: items\n  }));\n};\nexport default ColorPresets;"], "mappings": "AAAA,SAASA,UAAU,EAAEC,KAAK,IAAIC,OAAO,QAAQ,4BAA4B;AACzE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,aAAa,QAAQ,SAAS;AACvC,MAAMC,cAAc,GAAGC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,KAAK,IAAI;EAC/CA,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACF,GAAG,CAACH,aAAa,CAAC;EAC9C,OAAOI,KAAK;AACd,CAAC,CAAC;AACF,MAAME,QAAQ,GAAGA,CAACF,KAAK,EAAEG,YAAY,KAAK;EACxC,MAAM;IACJC,CAAC;IACDC,CAAC;IACDC,CAAC;IACDC;EACF,CAAC,GAAGP,KAAK,CAACQ,KAAK,CAAC,CAAC;EACjB,MAAMC,GAAG,GAAG,IAAIrB,OAAO,CAACY,KAAK,CAACU,WAAW,CAAC,CAAC,CAAC,CAACC,YAAY,CAACR,YAAY,CAAC,CAACS,KAAK,CAAC,CAAC;EAC/E,IAAIL,CAAC,IAAI,GAAG,EAAE;IACZ;IACA,OAAOE,GAAG,CAACI,CAAC,GAAG,GAAG;EACpB;EACA,OAAOT,CAAC,GAAG,KAAK,GAAGC,CAAC,GAAG,KAAK,GAAGC,CAAC,GAAG,KAAK,GAAG,GAAG;AAChD,CAAC;AACD,MAAMQ,YAAY,GAAGC,IAAI,IAAI;EAC3B,IAAI;IACFC,SAAS;IACTC,OAAO;IACPjB,KAAK,EAAEkB,KAAK;IACZC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAM,CAACK,MAAM,CAAC,GAAG1B,SAAS,CAAC,aAAa,CAAC;EACzC,MAAM;IACJ2B,KAAK,EAAE;MACLC;IACF;EACF,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC,CAAC;EACpB,MAAM,CAACC,YAAY,CAAC,GAAGlC,cAAc,CAACO,cAAc,CAACoB,OAAO,CAAC,EAAE;IAC7DjB,KAAK,EAAEH,cAAc,CAACoB,OAAO,CAAC;IAC9BQ,SAAS,EAAE5B;EACb,CAAC,CAAC;EACF,MAAM6B,qBAAqB,GAAI,GAAEV,SAAU,UAAS;EACpD,MAAMW,UAAU,GAAGnC,OAAO,CAAC,MAAMgC,YAAY,CAACzB,GAAG,CAAC6B,MAAM,IAAK,SAAQA,MAAM,CAACC,KAAM,EAAC,CAAC,EAAE,CAACL,YAAY,CAAC,CAAC;EACrG,MAAMM,WAAW,GAAGC,UAAU,IAAI;IAChCZ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACY,UAAU,CAAC;EAC1E,CAAC;EACD,MAAMC,KAAK,GAAGR,YAAY,CAACzB,GAAG,CAAC6B,MAAM,IAAI;IACvC,IAAIK,EAAE;IACN,OAAO;MACLC,GAAG,EAAG,SAAQN,MAAM,CAACC,KAAM,EAAC;MAC5BA,KAAK,EAAE,aAAatC,KAAK,CAAC4C,aAAa,CAAC,KAAK,EAAE;QAC7CC,SAAS,EAAG,GAAEV,qBAAsB;MACtC,CAAC,EAAEE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,KAAK,CAAC;MAChEQ,QAAQ,EAAE,aAAa9C,KAAK,CAAC4C,aAAa,CAAC,KAAK,EAAE;QAChDC,SAAS,EAAG,GAAEV,qBAAsB;MACtC,CAAC,EAAEY,KAAK,CAACC,OAAO,CAACX,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3B,MAAM,CAAC,IAAI,CAAC,CAACgC,EAAE,GAAGL,MAAM,CAAC3B,MAAM,MAAM,IAAI,IAAIgC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,MAAM,IAAI,CAAC,GAAGZ,MAAM,CAAC3B,MAAM,CAACF,GAAG,CAAC0C,WAAW,IAAI,aAAalD,KAAK,CAAC4C,aAAa,CAACjD,UAAU,EAAE;QAC3OgD,GAAG,EAAG,UAASO,WAAW,CAACC,WAAW,CAAC,CAAE,EAAC;QAC1CxB,KAAK,EAAEtB,aAAa,CAAC6C,WAAW,CAAC,CAAC/B,WAAW,CAAC,CAAC;QAC/CM,SAAS,EAAEA,SAAS;QACpBoB,SAAS,EAAE/C,UAAU,CAAE,GAAEqC,qBAAsB,QAAO,EAAE;UACtD,CAAE,GAAEA,qBAAsB,gBAAe,GAAGe,WAAW,CAACC,WAAW,CAAC,CAAC,MAAMxB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACwB,WAAW,CAAC,CAAC,CAAC;UAC7I,CAAE,GAAEhB,qBAAsB,eAAc,GAAGxB,QAAQ,CAACuC,WAAW,EAAEnB,eAAe;QAClF,CAAC,CAAC;QACFqB,OAAO,EAAEA,CAAA,KAAMb,WAAW,CAACW,WAAW;MACxC,CAAC,CAAC,CAAC,GAAG,aAAalD,KAAK,CAAC4C,aAAa,CAAC,MAAM,EAAE;QAC7CC,SAAS,EAAG,GAAEV,qBAAsB;MACtC,CAAC,EAAEN,MAAM,CAACwB,WAAW,CAAC;IACxB,CAAC;EACH,CAAC,CAAC;EACF,OAAO,aAAarD,KAAK,CAAC4C,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEV;EACb,CAAC,EAAE,aAAanC,KAAK,CAAC4C,aAAa,CAAC1C,QAAQ,EAAE;IAC5CoD,gBAAgB,EAAElB,UAAU;IAC5BmB,KAAK,EAAE,IAAI;IACXd,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAelB,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}