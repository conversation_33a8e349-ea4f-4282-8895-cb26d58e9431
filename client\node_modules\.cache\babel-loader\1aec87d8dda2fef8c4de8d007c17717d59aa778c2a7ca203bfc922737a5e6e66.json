{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { forwardRef, useMemo } from 'react';\nimport { ColorPickerPrefixCls, defaultColor, generateColor } from \"./util\";\nimport classNames from 'classnames';\nimport ColorBlock from \"./components/ColorBlock\";\nimport Picker from \"./components/Picker\";\nimport Slider from \"./components/Slider\";\nimport useColorState from \"./hooks/useColorState\";\nvar hueColor = ['rgb(255, 0, 0) 0%', 'rgb(255, 255, 0) 17%', 'rgb(0, 255, 0) 33%', 'rgb(0, 255, 255) 50%', 'rgb(0, 0, 255) 67%', 'rgb(255, 0, 255) 83%', 'rgb(255, 0, 0) 100%'];\nexport default /*#__PURE__*/forwardRef(function (props, ref) {\n  var value = props.value,\n    defaultValue = props.defaultValue,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? ColorPickerPrefixCls : _props$prefixCls,\n    onChange = props.onChange,\n    onChangeComplete = props.onChangeComplete,\n    className = props.className,\n    style = props.style,\n    panelRender = props.panelRender,\n    _props$disabledAlpha = props.disabledAlpha,\n    disabledAlpha = _props$disabledAlpha === void 0 ? false : _props$disabledAlpha,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled;\n  var _useColorState = useColorState(defaultColor, {\n      value: value,\n      defaultValue: defaultValue\n    }),\n    _useColorState2 = _slicedToArray(_useColorState, 2),\n    colorValue = _useColorState2[0],\n    setColorValue = _useColorState2[1];\n  var alphaColor = useMemo(function () {\n    var rgb = generateColor(colorValue.toRgbString());\n    // alpha color need equal 1 for base color\n    rgb.setAlpha(1);\n    return rgb.toRgbString();\n  }, [colorValue]);\n  var mergeCls = classNames(\"\".concat(prefixCls, \"-panel\"), className, _defineProperty({}, \"\".concat(prefixCls, \"-panel-disabled\"), disabled));\n  var basicProps = {\n    prefixCls: prefixCls,\n    onChangeComplete: onChangeComplete,\n    disabled: disabled\n  };\n  var handleChange = function handleChange(data, type) {\n    if (!value) {\n      setColorValue(data);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(data, type);\n  };\n  var defaultPanel = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Picker, _extends({\n    color: colorValue,\n    onChange: handleChange\n  }, basicProps)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-slider-container\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-slider-group\"), _defineProperty({}, \"\".concat(prefixCls, \"-slider-group-disabled-alpha\"), disabledAlpha))\n  }, /*#__PURE__*/React.createElement(Slider, _extends({\n    gradientColors: hueColor,\n    color: colorValue,\n    value: \"hsl(\".concat(colorValue.toHsb().h, \",100%, 50%)\"),\n    onChange: function onChange(color) {\n      return handleChange(color, 'hue');\n    }\n  }, basicProps)), !disabledAlpha && /*#__PURE__*/React.createElement(Slider, _extends({\n    type: \"alpha\",\n    gradientColors: ['rgba(255, 0, 4, 0) 0%', alphaColor],\n    color: colorValue,\n    value: colorValue.toRgbString(),\n    onChange: function onChange(color) {\n      return handleChange(color, 'alpha');\n    }\n  }, basicProps))), /*#__PURE__*/React.createElement(ColorBlock, {\n    color: colorValue.toRgbString(),\n    prefixCls: prefixCls\n  })));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: mergeCls,\n    style: style,\n    ref: ref\n  }, typeof panelRender === 'function' ? panelRender(defaultPanel) : defaultPanel);\n});", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "React", "forwardRef", "useMemo", "ColorPickerPrefixCls", "defaultColor", "generateColor", "classNames", "ColorBlock", "Picker", "Slide<PERSON>", "useColorState", "hueColor", "props", "ref", "value", "defaultValue", "_props$prefixCls", "prefixCls", "onChange", "onChangeComplete", "className", "style", "panelRender", "_props$disabledAlpha", "disabledAlpha", "_props$disabled", "disabled", "_useColorState", "_useColorState2", "colorValue", "setColorValue", "alphaColor", "rgb", "toRgbString", "<PERSON><PERSON><PERSON><PERSON>", "mergeCls", "concat", "basicProps", "handleChange", "data", "type", "defaultPanel", "createElement", "Fragment", "color", "gradientColors", "toHsb", "h"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/color-picker/es/ColorPicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { forwardRef, useMemo } from 'react';\nimport { ColorPickerPrefixCls, defaultColor, generateColor } from \"./util\";\nimport classNames from 'classnames';\nimport ColorBlock from \"./components/ColorBlock\";\nimport Picker from \"./components/Picker\";\nimport Slider from \"./components/Slider\";\nimport useColorState from \"./hooks/useColorState\";\nvar hueColor = ['rgb(255, 0, 0) 0%', 'rgb(255, 255, 0) 17%', 'rgb(0, 255, 0) 33%', 'rgb(0, 255, 255) 50%', 'rgb(0, 0, 255) 67%', 'rgb(255, 0, 255) 83%', 'rgb(255, 0, 0) 100%'];\nexport default /*#__PURE__*/forwardRef(function (props, ref) {\n  var value = props.value,\n    defaultValue = props.defaultValue,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? ColorPickerPrefixCls : _props$prefixCls,\n    onChange = props.onChange,\n    onChangeComplete = props.onChangeComplete,\n    className = props.className,\n    style = props.style,\n    panelRender = props.panelRender,\n    _props$disabledAlpha = props.disabledAlpha,\n    disabledAlpha = _props$disabledAlpha === void 0 ? false : _props$disabledAlpha,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled;\n  var _useColorState = useColorState(defaultColor, {\n      value: value,\n      defaultValue: defaultValue\n    }),\n    _useColorState2 = _slicedToArray(_useColorState, 2),\n    colorValue = _useColorState2[0],\n    setColorValue = _useColorState2[1];\n  var alphaColor = useMemo(function () {\n    var rgb = generateColor(colorValue.toRgbString());\n    // alpha color need equal 1 for base color\n    rgb.setAlpha(1);\n    return rgb.toRgbString();\n  }, [colorValue]);\n  var mergeCls = classNames(\"\".concat(prefixCls, \"-panel\"), className, _defineProperty({}, \"\".concat(prefixCls, \"-panel-disabled\"), disabled));\n  var basicProps = {\n    prefixCls: prefixCls,\n    onChangeComplete: onChangeComplete,\n    disabled: disabled\n  };\n  var handleChange = function handleChange(data, type) {\n    if (!value) {\n      setColorValue(data);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(data, type);\n  };\n  var defaultPanel = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Picker, _extends({\n    color: colorValue,\n    onChange: handleChange\n  }, basicProps)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-slider-container\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-slider-group\"), _defineProperty({}, \"\".concat(prefixCls, \"-slider-group-disabled-alpha\"), disabledAlpha))\n  }, /*#__PURE__*/React.createElement(Slider, _extends({\n    gradientColors: hueColor,\n    color: colorValue,\n    value: \"hsl(\".concat(colorValue.toHsb().h, \",100%, 50%)\"),\n    onChange: function onChange(color) {\n      return handleChange(color, 'hue');\n    }\n  }, basicProps)), !disabledAlpha && /*#__PURE__*/React.createElement(Slider, _extends({\n    type: \"alpha\",\n    gradientColors: ['rgba(255, 0, 4, 0) 0%', alphaColor],\n    color: colorValue,\n    value: colorValue.toRgbString(),\n    onChange: function onChange(color) {\n      return handleChange(color, 'alpha');\n    }\n  }, basicProps))), /*#__PURE__*/React.createElement(ColorBlock, {\n    color: colorValue.toRgbString(),\n    prefixCls: prefixCls\n  })));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: mergeCls,\n    style: style,\n    ref: ref\n  }, typeof panelRender === 'function' ? panelRender(defaultPanel) : defaultPanel);\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,KAAK,IAAIC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAClD,SAASC,oBAAoB,EAAEC,YAAY,EAAEC,aAAa,QAAQ,QAAQ;AAC1E,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,IAAIC,QAAQ,GAAG,CAAC,mBAAmB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,qBAAqB,CAAC;AAC/K,eAAe,aAAaV,UAAU,CAAC,UAAUW,KAAK,EAAEC,GAAG,EAAE;EAC3D,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACrBC,YAAY,GAAGH,KAAK,CAACG,YAAY;IACjCC,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAGb,oBAAoB,GAAGa,gBAAgB;IACjFE,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,gBAAgB,GAAGP,KAAK,CAACO,gBAAgB;IACzCC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,oBAAoB,GAAGX,KAAK,CAACY,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,oBAAoB;IAC9EE,eAAe,GAAGb,KAAK,CAACc,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;EACjE,IAAIE,cAAc,GAAGjB,aAAa,CAACN,YAAY,EAAE;MAC7CU,KAAK,EAAEA,KAAK;MACZC,YAAY,EAAEA;IAChB,CAAC,CAAC;IACFa,eAAe,GAAG7B,cAAc,CAAC4B,cAAc,EAAE,CAAC,CAAC;IACnDE,UAAU,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC/BE,aAAa,GAAGF,eAAe,CAAC,CAAC,CAAC;EACpC,IAAIG,UAAU,GAAG7B,OAAO,CAAC,YAAY;IACnC,IAAI8B,GAAG,GAAG3B,aAAa,CAACwB,UAAU,CAACI,WAAW,CAAC,CAAC,CAAC;IACjD;IACAD,GAAG,CAACE,QAAQ,CAAC,CAAC,CAAC;IACf,OAAOF,GAAG,CAACC,WAAW,CAAC,CAAC;EAC1B,CAAC,EAAE,CAACJ,UAAU,CAAC,CAAC;EAChB,IAAIM,QAAQ,GAAG7B,UAAU,CAAC,EAAE,CAAC8B,MAAM,CAACnB,SAAS,EAAE,QAAQ,CAAC,EAAEG,SAAS,EAAEtB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACsC,MAAM,CAACnB,SAAS,EAAE,iBAAiB,CAAC,EAAES,QAAQ,CAAC,CAAC;EAC5I,IAAIW,UAAU,GAAG;IACfpB,SAAS,EAAEA,SAAS;IACpBE,gBAAgB,EAAEA,gBAAgB;IAClCO,QAAQ,EAAEA;EACZ,CAAC;EACD,IAAIY,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACnD,IAAI,CAAC1B,KAAK,EAAE;MACVgB,aAAa,CAACS,IAAI,CAAC;IACrB;IACArB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACqB,IAAI,EAAEC,IAAI,CAAC;EAC1E,CAAC;EACD,IAAIC,YAAY,GAAG,aAAazC,KAAK,CAAC0C,aAAa,CAAC1C,KAAK,CAAC2C,QAAQ,EAAE,IAAI,EAAE,aAAa3C,KAAK,CAAC0C,aAAa,CAAClC,MAAM,EAAEX,QAAQ,CAAC;IAC1H+C,KAAK,EAAEf,UAAU;IACjBX,QAAQ,EAAEoB;EACZ,CAAC,EAAED,UAAU,CAAC,CAAC,EAAE,aAAarC,KAAK,CAAC0C,aAAa,CAAC,KAAK,EAAE;IACvDtB,SAAS,EAAE,EAAE,CAACgB,MAAM,CAACnB,SAAS,EAAE,mBAAmB;EACrD,CAAC,EAAE,aAAajB,KAAK,CAAC0C,aAAa,CAAC,KAAK,EAAE;IACzCtB,SAAS,EAAEd,UAAU,CAAC,EAAE,CAAC8B,MAAM,CAACnB,SAAS,EAAE,eAAe,CAAC,EAAEnB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACsC,MAAM,CAACnB,SAAS,EAAE,8BAA8B,CAAC,EAAEO,aAAa,CAAC;EACvJ,CAAC,EAAE,aAAaxB,KAAK,CAAC0C,aAAa,CAACjC,MAAM,EAAEZ,QAAQ,CAAC;IACnDgD,cAAc,EAAElC,QAAQ;IACxBiC,KAAK,EAAEf,UAAU;IACjBf,KAAK,EAAE,MAAM,CAACsB,MAAM,CAACP,UAAU,CAACiB,KAAK,CAAC,CAAC,CAACC,CAAC,EAAE,aAAa,CAAC;IACzD7B,QAAQ,EAAE,SAASA,QAAQA,CAAC0B,KAAK,EAAE;MACjC,OAAON,YAAY,CAACM,KAAK,EAAE,KAAK,CAAC;IACnC;EACF,CAAC,EAAEP,UAAU,CAAC,CAAC,EAAE,CAACb,aAAa,IAAI,aAAaxB,KAAK,CAAC0C,aAAa,CAACjC,MAAM,EAAEZ,QAAQ,CAAC;IACnF2C,IAAI,EAAE,OAAO;IACbK,cAAc,EAAE,CAAC,uBAAuB,EAAEd,UAAU,CAAC;IACrDa,KAAK,EAAEf,UAAU;IACjBf,KAAK,EAAEe,UAAU,CAACI,WAAW,CAAC,CAAC;IAC/Bf,QAAQ,EAAE,SAASA,QAAQA,CAAC0B,KAAK,EAAE;MACjC,OAAON,YAAY,CAACM,KAAK,EAAE,OAAO,CAAC;IACrC;EACF,CAAC,EAAEP,UAAU,CAAC,CAAC,CAAC,EAAE,aAAarC,KAAK,CAAC0C,aAAa,CAACnC,UAAU,EAAE;IAC7DqC,KAAK,EAAEf,UAAU,CAACI,WAAW,CAAC,CAAC;IAC/BhB,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,CAAC;EACJ,OAAO,aAAajB,KAAK,CAAC0C,aAAa,CAAC,KAAK,EAAE;IAC7CtB,SAAS,EAAEe,QAAQ;IACnBd,KAAK,EAAEA,KAAK;IACZR,GAAG,EAAEA;EACP,CAAC,EAAE,OAAOS,WAAW,KAAK,UAAU,GAAGA,WAAW,CAACmB,YAAY,CAAC,GAAGA,YAAY,CAAC;AAClF,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}