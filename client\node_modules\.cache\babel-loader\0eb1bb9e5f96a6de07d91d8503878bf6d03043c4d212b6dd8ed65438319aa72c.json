{"ast": null, "code": "import { TinyColor } from '@ctrl/tinycolor';\nexport const getAlphaColor = (baseColor, alpha) => new TinyColor(baseColor).setAlpha(alpha).toRgbString();\nexport const getSolidColor = (baseColor, brightness) => {\n  const instance = new TinyColor(baseColor);\n  return instance.darken(brightness).toHexString();\n};", "map": {"version": 3, "names": ["TinyColor", "getAlphaColor", "baseColor", "alpha", "<PERSON><PERSON><PERSON><PERSON>", "toRgbString", "getSolidColor", "brightness", "instance", "darken", "toHexString"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/theme/themes/default/colorAlgorithm.js"], "sourcesContent": ["import { TinyColor } from '@ctrl/tinycolor';\nexport const getAlphaColor = (baseColor, alpha) => new TinyColor(baseColor).setAlpha(alpha).toRgbString();\nexport const getSolidColor = (baseColor, brightness) => {\n  const instance = new TinyColor(baseColor);\n  return instance.darken(brightness).toHexString();\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,MAAMC,aAAa,GAAGA,CAACC,SAAS,EAAEC,KAAK,KAAK,IAAIH,SAAS,CAACE,SAAS,CAAC,CAACE,QAAQ,CAACD,KAAK,CAAC,CAACE,WAAW,CAAC,CAAC;AACzG,OAAO,MAAMC,aAAa,GAAGA,CAACJ,SAAS,EAAEK,UAAU,KAAK;EACtD,MAAMC,QAAQ,GAAG,IAAIR,SAAS,CAACE,SAAS,CAAC;EACzC,OAAOM,QAAQ,CAACC,MAAM,CAACF,UAAU,CAAC,CAACG,WAAW,CAAC,CAAC;AAClD,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}