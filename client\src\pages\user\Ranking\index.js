import React, { useEffect, useState } from "react";
import './index.css'
import { motion, AnimatePresence } from "framer-motion";
import { getAllReportsForRanking } from "../../../apicalls/reports";
import { getUserInfo } from "../../../apicalls/users";
import { message } from "antd";
import PageTitle from "../../../components/PageTitle";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { Card, Button, Loading } from "../../../components/modern";
import image from '../../../assets/person.png';
import { IoPersonCircleOutline } from "react-icons/io5";
import {
  TbTrophy,
  TbMedal,
  TbCrown,
  TbUsers,
  TbSchool,
  TbStar,
  TbChartBar,
  TbUser,
  TbAward
} from "react-icons/tb";

const Ranking = () => {
    const [rankingData, setRankingData] = useState('');
    const [userRanking, setUserRanking] = useState('');
    const [userData, setUserData] = useState('');
    const [isAdmin, setIsAdmin] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [activeTab, setActiveTab] = useState("overall"); // "overall" or "class"


    const dispatch = useDispatch();

    const fetchReports = async () => {
        try {
            const response = await getAllReportsForRanking();
            if (response.success) {
                setRankingData(response.data);
            } else {
                message.error(response.message);
            }
        } catch (error) {
            message.error(error.message);
        }
    }


    const getUserData = async () => {
        try {
            const response = await getUserInfo();
            if (response.success) {
                if (response.data.isAdmin) {
                    setIsAdmin(true);
                } else {
                    setIsAdmin(false);
                    setUserData(response.data);
                    await fetchReports();
                    dispatch(HideLoading());
                }
            } else {
                message.error(response.message);
            }
        } catch (error) {
            message.error(error.message);
        }
    };

    useEffect(() => {
        if (window.innerWidth < 700) {
            setIsMobile(true);
        }
        else {
            setIsMobile(false);
        }
        if (localStorage.getItem("token")) {
            dispatch(ShowLoading());
            getUserData();
        }
    }, []);

    const getUserStats = () => {
        const Ranking = rankingData
            .map((user, index) => ({
                user,
                ranking: index + 1,
            }))
            .filter((item) => item.user.userId.includes(userData._id));
        setUserRanking(Ranking);
    }

    useEffect(() => {
        if (rankingData) {
            getUserStats();
        }
    }, [rankingData]);

    // Helper function to format user ID for mobile devices
    const formatMobileUserId = (userId) => {
        const prefix = userId.slice(0, 4);
        const suffix = userId.slice(-4);
        return `${prefix}.....${suffix}`;
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
            {!isAdmin && (
                <div className="container-modern py-8">
                    {/* Modern Header */}
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-center mb-8"
                    >
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4">
                            <TbTrophy className="w-8 h-8 text-white" />
                        </div>
                        <h1 className="heading-2 text-gradient mb-4">Leaderboard</h1>
                        <p className="text-xl text-gray-600">
                            See how you rank against other students
                        </p>
                    </motion.div>

                    {/* Modern Tabs */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="mb-8"
                    >
                        <Card className="p-2">
                            <div className="flex gap-2">
                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${
                                        activeTab === "overall"
                                            ? 'bg-primary-600 text-white shadow-md'
                                            : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                                    onClick={() => setActiveTab("overall")}
                                >
                                    <TbUsers className="w-5 h-5" />
                                    <span>Overall Ranking</span>
                                    {activeTab === "overall" && (
                                        <motion.div
                                            layoutId="activeRankingTab"
                                            className="w-2 h-2 bg-white rounded-full"
                                        />
                                    )}
                                </motion.button>
                                <motion.button
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${
                                        activeTab === "class"
                                            ? 'bg-primary-600 text-white shadow-md'
                                            : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                                    onClick={() => setActiveTab("class")}
                                >
                                    <TbSchool className="w-5 h-5" />
                                    <span>Class Ranking</span>
                                    {activeTab === "class" && (
                                        <motion.div
                                            layoutId="activeRankingTab"
                                            className="w-2 h-2 bg-white rounded-full"
                                        />
                                    )}
                                </motion.button>
                            </div>
                        </Card>
                    </motion.div>

                    {/* Modern Leaderboard */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                    >
                        {rankingData.length > 0 ? (
                            <Card className="overflow-hidden">
                                {/* Leaderboard Header */}
                                <div className="bg-gradient-to-r from-primary-600 to-blue-600 text-white p-6">
                                    <div className="flex items-center justify-center space-x-3">
                                        <TbTrophy className="w-8 h-8 text-yellow-300" />
                                        <h2 className="text-2xl font-bold">
                                            {activeTab === "overall" ? "Overall Leaderboard" : "Class Leaderboard"}
                                        </h2>
                                    </div>
                                    <p className="text-center text-blue-100 mt-2">
                                        {activeTab === "overall"
                                            ? "Top performers across all classes"
                                            : `Top performers in ${userData?.class || 'your class'}`
                                        }
                                    </p>
                                </div>

                                {/* Leaderboard Content */}
                                <div className="p-6">
                                    <div className="space-y-4">
                                        {(activeTab === "overall"
                                            ? rankingData
                                            : rankingData.filter(user => user.userClass === userData?.class)
                                        ).map((user, index) => {
                                            const isCurrentUser = user.userId.includes(userData?._id);
                                            const getRankIcon = (position) => {
                                                if (position === 0) return { icon: TbCrown, color: "text-yellow-500", bg: "bg-yellow-50" };
                                                if (position === 1) return { icon: TbMedal, color: "text-gray-400", bg: "bg-gray-50" };
                                                if (position === 2) return { icon: TbAward, color: "text-amber-600", bg: "bg-amber-50" };
                                                return { icon: TbUser, color: "text-gray-500", bg: "bg-gray-50" };
                                            };

                                            const rankInfo = getRankIcon(index);

                                            return (
                                                <motion.div
                                                    key={user.userId}
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    transition={{ delay: index * 0.1 }}
                                                    className={`flex items-center space-x-4 p-4 rounded-xl transition-all duration-200 ${
                                                        isCurrentUser
                                                            ? 'bg-primary-50 border-2 border-primary-200 shadow-md'
                                                            : 'bg-gray-50 hover:bg-gray-100'
                                                    }`}
                                                >
                                                    {/* Rank */}
                                                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${rankInfo.bg}`}>
                                                        {index < 3 ? (
                                                            <rankInfo.icon className={`w-6 h-6 ${rankInfo.color}`} />
                                                        ) : (
                                                            <span className="font-bold text-gray-700">{index + 1}</span>
                                                        )}
                                                    </div>

                                                    {/* Profile Picture */}
                                                    <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                                                        {user.userPhoto ? (
                                                            <img
                                                                src={user.userPhoto}
                                                                alt="profile"
                                                                className="w-full h-full object-cover"
                                                                onError={(e) => { e.target.src = image }}
                                                            />
                                                        ) : (
                                                            <TbUser className="w-6 h-6 text-gray-400" />
                                                        )}
                                                    </div>

                                                    {/* User Info */}
                                                    <div className="flex-1 min-w-0">
                                                        <div className="flex items-center space-x-2 mb-1">
                                                            <h3 className={`font-semibold truncate ${
                                                                isCurrentUser ? 'text-primary-900' : 'text-gray-900'
                                                            }`}>
                                                                {user.userName}
                                                            </h3>
                                                            {isCurrentUser && (
                                                                <span className="badge-primary text-xs">You</span>
                                                            )}
                                                            <span className={`badge-modern text-xs ${
                                                                user.subscriptionStatus === "active"
                                                                    ? 'bg-success-100 text-success-800'
                                                                    : 'bg-warning-100 text-warning-800'
                                                            }`}>
                                                                {user.subscriptionStatus === "active" ? "Premium" : "Free"}
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                                                            <div className="flex items-center space-x-1">
                                                                <TbSchool className="w-4 h-4" />
                                                                <span className="truncate">{user.userSchool || 'Not Enrolled'}</span>
                                                            </div>
                                                            <div className="flex items-center space-x-1">
                                                                <TbUsers className="w-4 h-4" />
                                                                <span>{user.userClass || 'Not Enrolled'}</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    {/* Score */}
                                                    <div className="text-right">
                                                        <div className={`text-2xl font-bold ${
                                                            isCurrentUser ? 'text-primary-600' : 'text-gray-900'
                                                        }`}>
                                                            {user.score}
                                                        </div>
                                                        <div className="text-xs text-gray-500">points</div>
                                                    </div>
                                                </motion.div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </Card>
                        ) : (
                            <Card className="p-12 text-center">
                                <TbChartBar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Rankings Yet</h3>
                                <p className="text-gray-600">
                                    Complete some quizzes to see your ranking on the leaderboard!
                                </p>
                            </Card>
                        )}
                    </motion.div>

                </div>
            )}
        </div>
    );
}

export default Ranking;