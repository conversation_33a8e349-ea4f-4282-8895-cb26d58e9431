{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport raf from \"rc-util/es/raf\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport useOffsets from \"../hooks/useOffsets\";\nimport useSyncState from \"../hooks/useSyncState\";\nimport useTouchMove from \"../hooks/useTouchMove\";\nimport useUpdate, { useUpdateState } from \"../hooks/useUpdate\";\nimport useVisibleRange from \"../hooks/useVisibleRange\";\nimport TabContext from \"../TabContext\";\nimport { genDataNodeKey, stringify } from \"../util\";\nimport AddButton from \"./AddButton\";\nimport ExtraContent from \"./ExtraContent\";\nimport OperationNode from \"./OperationNode\";\nimport TabNode from \"./TabNode\";\nvar getSize = function getSize(refObj) {\n  var _ref = refObj.current || {},\n    _ref$offsetWidth = _ref.offsetWidth,\n    offsetWidth = _ref$offsetWidth === void 0 ? 0 : _ref$offsetWidth,\n    _ref$offsetHeight = _ref.offsetHeight,\n    offsetHeight = _ref$offsetHeight === void 0 ? 0 : _ref$offsetHeight;\n  return [offsetWidth, offsetHeight];\n};\n\n/**\n * Convert `SizeInfo` to unit value. Such as [123, 456] with `top` position get `123`\n */\nvar getUnitValue = function getUnitValue(size, tabPositionTopOrBottom) {\n  return size[tabPositionTopOrBottom ? 0 : 1];\n};\nfunction TabNavList(props, ref) {\n  var _classNames;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var className = props.className,\n    style = props.style,\n    id = props.id,\n    animated = props.animated,\n    activeKey = props.activeKey,\n    rtl = props.rtl,\n    extra = props.extra,\n    editable = props.editable,\n    locale = props.locale,\n    tabPosition = props.tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    children = props.children,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll;\n  var containerRef = useRef();\n  var extraLeftRef = useRef();\n  var extraRightRef = useRef();\n  var tabsWrapperRef = useRef();\n  var tabListRef = useRef();\n  var operationsRef = useRef();\n  var innerAddButtonRef = useRef();\n  // const [getBtnRef, removeBtnRef] = useRefs<HTMLDivElement>();\n\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n  var _useSyncState = useSyncState(0, function (next, prev) {\n      if (tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'left' : 'right'\n        });\n      }\n    }),\n    _useSyncState2 = _slicedToArray(_useSyncState, 2),\n    transformLeft = _useSyncState2[0],\n    setTransformLeft = _useSyncState2[1];\n  var _useSyncState3 = useSyncState(0, function (next, prev) {\n      if (!tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'top' : 'bottom'\n        });\n      }\n    }),\n    _useSyncState4 = _slicedToArray(_useSyncState3, 2),\n    transformTop = _useSyncState4[0],\n    setTransformTop = _useSyncState4[1];\n  var _useState = useState([0, 0]),\n    _useState2 = _slicedToArray(_useState, 2),\n    containerExcludeExtraSize = _useState2[0],\n    setContainerExcludeExtraSize = _useState2[1];\n  var _useState3 = useState([0, 0]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    tabContentSize = _useState4[0],\n    setTabContentSize = _useState4[1];\n  var _useState5 = useState([0, 0]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    addSize = _useState6[0],\n    setAddSize = _useState6[1];\n  var _useState7 = useState([0, 0]),\n    _useState8 = _slicedToArray(_useState7, 2),\n    operationSize = _useState8[0],\n    setOperationSize = _useState8[1];\n  var _useUpdateState = useUpdateState(new Map()),\n    _useUpdateState2 = _slicedToArray(_useUpdateState, 2),\n    tabSizes = _useUpdateState2[0],\n    setTabSizes = _useUpdateState2[1];\n  var tabOffsets = useOffsets(tabs, tabSizes, tabContentSize[0]);\n\n  // ========================== Unit =========================\n  var containerExcludeExtraSizeValue = getUnitValue(containerExcludeExtraSize, tabPositionTopOrBottom);\n  var tabContentSizeValue = getUnitValue(tabContentSize, tabPositionTopOrBottom);\n  var addSizeValue = getUnitValue(addSize, tabPositionTopOrBottom);\n  var operationSizeValue = getUnitValue(operationSize, tabPositionTopOrBottom);\n  var needScroll = containerExcludeExtraSizeValue < tabContentSizeValue + addSizeValue;\n  var visibleTabContentValue = needScroll ? containerExcludeExtraSizeValue - operationSizeValue : containerExcludeExtraSizeValue - addSizeValue;\n\n  // ========================== Util =========================\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, tabContentSizeValue - visibleTabContentValue);\n  } else {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  }\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n    if (value > transformMax) {\n      return transformMax;\n    }\n    return value;\n  }\n\n  // ========================= Mobile ========================\n  var touchMovingRef = useRef();\n  var _useState9 = useState(),\n    _useState10 = _slicedToArray(_useState9, 2),\n    lockAnimation = _useState10[0],\n    setLockAnimation = _useState10[1];\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n  function clearTouchMoving() {\n    window.clearTimeout(touchMovingRef.current);\n  }\n  useTouchMove(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    }\n\n    // Skip scroll if place is enough\n    if (!needScroll) {\n      return false;\n    }\n    if (tabPositionTopOrBottom) {\n      doMove(setTransformLeft, offsetX);\n    } else {\n      doMove(setTransformTop, offsetY);\n    }\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  useEffect(function () {\n    clearTouchMoving();\n    if (lockAnimation) {\n      touchMovingRef.current = window.setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n    return clearTouchMoving;\n  }, [lockAnimation]);\n\n  // ===================== Visible Range =====================\n  // Render tab node & collect tab offset\n  var _useVisibleRange = useVisibleRange(tabOffsets,\n    // Container\n    visibleTabContentValue,\n    // Transform\n    tabPositionTopOrBottom ? transformLeft : transformTop,\n    // Tabs\n    tabContentSizeValue,\n    // Add\n    addSizeValue,\n    // Operation\n    operationSizeValue, _objectSpread(_objectSpread({}, props), {}, {\n      tabs: tabs\n    })),\n    _useVisibleRange2 = _slicedToArray(_useVisibleRange, 2),\n    visibleStart = _useVisibleRange2[0],\n    visibleEnd = _useVisibleRange2[1];\n\n  // ========================= Scroll ========================\n  var scrollToTab = useEvent(function () {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft;\n\n      // RTL\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + visibleTabContentValue) {\n          newTransform = tabOffset.right + tabOffset.width - visibleTabContentValue;\n        }\n      }\n      // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + visibleTabContentValue) {\n        newTransform = -(tabOffset.left + tabOffset.width - visibleTabContentValue);\n      }\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + visibleTabContentValue) {\n        _newTransform = -(tabOffset.top + tabOffset.height - visibleTabContentValue);\n      }\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  });\n\n  // ========================== Tab ==========================\n  var tabNodeStyle = {};\n  if (tabPosition === 'top' || tabPosition === 'bottom') {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/React.createElement(TabNode, {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */,\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onFocus: function onFocus() {\n        scrollToTab(key);\n        doLockAnimation();\n        if (!tabsWrapperRef.current) {\n          return;\n        }\n        // Focus element will make scrollLeft change which we should reset back\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n        tabsWrapperRef.current.scrollTop = 0;\n      }\n    });\n  });\n\n  // Update buttons records\n  var updateTabSizes = function updateTabSizes() {\n    return setTabSizes(function () {\n      var newSizes = new Map();\n      tabs.forEach(function (_ref2) {\n        var _tabListRef$current;\n        var key = _ref2.key;\n        var btnNode = (_tabListRef$current = tabListRef.current) === null || _tabListRef$current === void 0 ? void 0 : _tabListRef$current.querySelector(\"[data-node-key=\\\"\".concat(genDataNodeKey(key), \"\\\"]\"));\n        if (btnNode) {\n          newSizes.set(key, {\n            width: btnNode.offsetWidth,\n            height: btnNode.offsetHeight,\n            left: btnNode.offsetLeft,\n            top: btnNode.offsetTop\n          });\n        }\n      });\n      return newSizes;\n    });\n  };\n  useEffect(function () {\n    updateTabSizes();\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_')]);\n  var onListHolderResize = useUpdate(function () {\n    // Update wrapper records\n    var containerSize = getSize(containerRef);\n    var extraLeftSize = getSize(extraLeftRef);\n    var extraRightSize = getSize(extraRightRef);\n    setContainerExcludeExtraSize([containerSize[0] - extraLeftSize[0] - extraRightSize[0], containerSize[1] - extraLeftSize[1] - extraRightSize[1]]);\n    var newAddSize = getSize(innerAddButtonRef);\n    setAddSize(newAddSize);\n    var newOperationSize = getSize(operationsRef);\n    setOperationSize(newOperationSize);\n\n    // Which includes add button size\n    var tabContentFullSize = getSize(tabListRef);\n    setTabContentSize([tabContentFullSize[0] - newAddSize[0], tabContentFullSize[1] - newAddSize[1]]);\n\n    // Update buttons records\n    updateTabSizes();\n  });\n\n  // ======================== Dropdown =======================\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat(_toConsumableArray(startHiddenTabs), _toConsumableArray(endHiddenTabs));\n\n  // =================== Link & Operations ===================\n  var _useState11 = useState(),\n    _useState12 = _slicedToArray(_useState11, 2),\n    inkStyle = _useState12[0],\n    setInkStyle = _useState12[1];\n  var activeTabOffset = tabOffsets.get(activeKey);\n\n  // Delay set ink style to avoid remove tab blink\n  var inkBarRafRef = useRef();\n  function cleanInkBarRaf() {\n    raf.cancel(inkBarRafRef.current);\n  }\n  useEffect(function () {\n    var newInkStyle = {};\n    if (activeTabOffset) {\n      if (tabPositionTopOrBottom) {\n        if (rtl) {\n          newInkStyle.right = activeTabOffset.right;\n        } else {\n          newInkStyle.left = activeTabOffset.left;\n        }\n        newInkStyle.width = activeTabOffset.width;\n      } else {\n        newInkStyle.top = activeTabOffset.top;\n        newInkStyle.height = activeTabOffset.height;\n      }\n    }\n    cleanInkBarRaf();\n    inkBarRafRef.current = raf(function () {\n      setInkStyle(newInkStyle);\n    });\n    return cleanInkBarRaf;\n  }, [activeTabOffset, tabPositionTopOrBottom, rtl]);\n\n  // ========================= Effect ========================\n  useEffect(function () {\n    scrollToTab();\n    // eslint-disable-next-line\n  }, [activeKey, transformMin, transformMax, stringify(activeTabOffset), stringify(tabOffsets), tabPositionTopOrBottom]);\n\n  // Should recalculate when rtl changed\n  useEffect(function () {\n    onListHolderResize();\n    // eslint-disable-next-line\n  }, [rtl]);\n\n  // ========================= Render ========================\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft !== transformMax;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = transformLeft !== transformMin;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = transformTop !== transformMin;\n  }\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: useComposeRef(ref, containerRef),\n    role: \"tablist\",\n    className: classNames(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraLeftRef,\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom), _classNames)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/React.createElement(AddButton, {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: _objectSpread(_objectSpread({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-ink-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: inkStyle\n  })))), /*#__PURE__*/React.createElement(OperationNode, _extends({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraRightRef,\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  })));\n  /* eslint-enable */\n}\n\nexport default /*#__PURE__*/React.forwardRef(TabNavList);", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_toConsumableArray", "_objectSpread", "_slicedToArray", "classNames", "ResizeObserver", "useEvent", "raf", "useComposeRef", "React", "useEffect", "useRef", "useState", "useOffsets", "useSyncState", "useTouchMove", "useUpdate", "useUpdateState", "useVisibleRange", "TabContext", "genDataNodeKey", "stringify", "AddButton", "ExtraContent", "OperationNode", "TabNode", "getSize", "refObj", "_ref", "current", "_ref$offsetWidth", "offsetWidth", "_ref$offsetHeight", "offsetHeight", "getUnitValue", "size", "tabPositionTopOrBottom", "TabNavList", "props", "ref", "_classNames", "_React$useContext", "useContext", "prefixCls", "tabs", "className", "style", "id", "animated", "active<PERSON><PERSON>", "rtl", "extra", "editable", "locale", "tabPosition", "tabBarGutter", "children", "onTabClick", "onTabScroll", "containerRef", "extraLeftRef", "extraRightRef", "tabsWrapperRef", "tabListRef", "operationsRef", "innerAddButtonRef", "_useSyncState", "next", "prev", "direction", "_useSyncState2", "transformLeft", "setTransformLeft", "_useSyncState3", "_useSyncState4", "transformTop", "setTransformTop", "_useState", "_useState2", "containerExcludeExtraSize", "setContainerExcludeExtraSize", "_useState3", "_useState4", "tabContentSize", "setTabContentSize", "_useState5", "_useState6", "addSize", "setAddSize", "_useState7", "_useState8", "operationSize", "setOperationSize", "_useUpdateState", "Map", "_useUpdateState2", "tabSizes", "setTabSizes", "tabOffsets", "containerExcludeExtraSizeValue", "tabContentSizeValue", "addSizeValue", "operationSizeValue", "needScroll", "visibleTabContentValue", "operationsHiddenClassName", "concat", "transformMin", "transformMax", "Math", "min", "max", "alignInRange", "value", "touchMovingRef", "_useState9", "_useState10", "lockAnimation", "setLockAnimation", "doLockAnimation", "Date", "now", "clearTouchMoving", "window", "clearTimeout", "offsetX", "offsetY", "do<PERSON>ove", "setState", "offset", "newValue", "setTimeout", "_useVisibleRange", "_useVisibleRange2", "visibleStart", "visibleEnd", "scrollToTab", "key", "arguments", "length", "undefined", "tabOffset", "get", "width", "height", "left", "right", "top", "newTransform", "_newTransform", "tabNodeStyle", "marginTop", "tabNodes", "map", "tab", "i", "createElement", "closable", "active", "renderWrapper", "removeAriaLabel", "onClick", "e", "onFocus", "scrollLeft", "scrollTop", "updateTabSizes", "newSizes", "for<PERSON>ach", "_ref2", "_tabListRef$current", "btnNode", "querySelector", "set", "offsetLeft", "offsetTop", "join", "onListHolderResize", "containerSize", "extraLeftSize", "extraRightSize", "newAddSize", "newOperationSize", "tabContentFullSize", "startHiddenTabs", "slice", "endHiddenTabs", "hiddenTabs", "_useState11", "_useState12", "inkStyle", "setInkStyle", "activeTabOffset", "inkBarRafRef", "cleanInkBarRaf", "cancel", "newInkStyle", "hasDropdown", "wrapPrefix", "pingLeft", "pingRight", "pingTop", "pingBottom", "onResize", "role", "onKeyDown", "position", "transform", "transition", "visibility", "inkBar", "tabMoving", "forwardRef"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tabs/es/TabNavList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport raf from \"rc-util/es/raf\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport useOffsets from \"../hooks/useOffsets\";\nimport useSyncState from \"../hooks/useSyncState\";\nimport useTouchMove from \"../hooks/useTouchMove\";\nimport useUpdate, { useUpdateState } from \"../hooks/useUpdate\";\nimport useVisibleRange from \"../hooks/useVisibleRange\";\nimport TabContext from \"../TabContext\";\nimport { genDataNodeKey, stringify } from \"../util\";\nimport AddButton from \"./AddButton\";\nimport ExtraContent from \"./ExtraContent\";\nimport OperationNode from \"./OperationNode\";\nimport TabNode from \"./TabNode\";\nvar getSize = function getSize(refObj) {\n  var _ref = refObj.current || {},\n    _ref$offsetWidth = _ref.offsetWidth,\n    offsetWidth = _ref$offsetWidth === void 0 ? 0 : _ref$offsetWidth,\n    _ref$offsetHeight = _ref.offsetHeight,\n    offsetHeight = _ref$offsetHeight === void 0 ? 0 : _ref$offsetHeight;\n  return [offsetWidth, offsetHeight];\n};\n\n/**\n * Convert `SizeInfo` to unit value. Such as [123, 456] with `top` position get `123`\n */\nvar getUnitValue = function getUnitValue(size, tabPositionTopOrBottom) {\n  return size[tabPositionTopOrBottom ? 0 : 1];\n};\nfunction TabNavList(props, ref) {\n  var _classNames;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var className = props.className,\n    style = props.style,\n    id = props.id,\n    animated = props.animated,\n    activeKey = props.activeKey,\n    rtl = props.rtl,\n    extra = props.extra,\n    editable = props.editable,\n    locale = props.locale,\n    tabPosition = props.tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    children = props.children,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll;\n  var containerRef = useRef();\n  var extraLeftRef = useRef();\n  var extraRightRef = useRef();\n  var tabsWrapperRef = useRef();\n  var tabListRef = useRef();\n  var operationsRef = useRef();\n  var innerAddButtonRef = useRef();\n  // const [getBtnRef, removeBtnRef] = useRefs<HTMLDivElement>();\n\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n  var _useSyncState = useSyncState(0, function (next, prev) {\n      if (tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'left' : 'right'\n        });\n      }\n    }),\n    _useSyncState2 = _slicedToArray(_useSyncState, 2),\n    transformLeft = _useSyncState2[0],\n    setTransformLeft = _useSyncState2[1];\n  var _useSyncState3 = useSyncState(0, function (next, prev) {\n      if (!tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'top' : 'bottom'\n        });\n      }\n    }),\n    _useSyncState4 = _slicedToArray(_useSyncState3, 2),\n    transformTop = _useSyncState4[0],\n    setTransformTop = _useSyncState4[1];\n  var _useState = useState([0, 0]),\n    _useState2 = _slicedToArray(_useState, 2),\n    containerExcludeExtraSize = _useState2[0],\n    setContainerExcludeExtraSize = _useState2[1];\n  var _useState3 = useState([0, 0]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    tabContentSize = _useState4[0],\n    setTabContentSize = _useState4[1];\n  var _useState5 = useState([0, 0]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    addSize = _useState6[0],\n    setAddSize = _useState6[1];\n  var _useState7 = useState([0, 0]),\n    _useState8 = _slicedToArray(_useState7, 2),\n    operationSize = _useState8[0],\n    setOperationSize = _useState8[1];\n  var _useUpdateState = useUpdateState(new Map()),\n    _useUpdateState2 = _slicedToArray(_useUpdateState, 2),\n    tabSizes = _useUpdateState2[0],\n    setTabSizes = _useUpdateState2[1];\n  var tabOffsets = useOffsets(tabs, tabSizes, tabContentSize[0]);\n\n  // ========================== Unit =========================\n  var containerExcludeExtraSizeValue = getUnitValue(containerExcludeExtraSize, tabPositionTopOrBottom);\n  var tabContentSizeValue = getUnitValue(tabContentSize, tabPositionTopOrBottom);\n  var addSizeValue = getUnitValue(addSize, tabPositionTopOrBottom);\n  var operationSizeValue = getUnitValue(operationSize, tabPositionTopOrBottom);\n  var needScroll = containerExcludeExtraSizeValue < tabContentSizeValue + addSizeValue;\n  var visibleTabContentValue = needScroll ? containerExcludeExtraSizeValue - operationSizeValue : containerExcludeExtraSizeValue - addSizeValue;\n\n  // ========================== Util =========================\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, tabContentSizeValue - visibleTabContentValue);\n  } else {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  }\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n    if (value > transformMax) {\n      return transformMax;\n    }\n    return value;\n  }\n\n  // ========================= Mobile ========================\n  var touchMovingRef = useRef();\n  var _useState9 = useState(),\n    _useState10 = _slicedToArray(_useState9, 2),\n    lockAnimation = _useState10[0],\n    setLockAnimation = _useState10[1];\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n  function clearTouchMoving() {\n    window.clearTimeout(touchMovingRef.current);\n  }\n  useTouchMove(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    }\n\n    // Skip scroll if place is enough\n    if (!needScroll) {\n      return false;\n    }\n    if (tabPositionTopOrBottom) {\n      doMove(setTransformLeft, offsetX);\n    } else {\n      doMove(setTransformTop, offsetY);\n    }\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  useEffect(function () {\n    clearTouchMoving();\n    if (lockAnimation) {\n      touchMovingRef.current = window.setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n    return clearTouchMoving;\n  }, [lockAnimation]);\n\n  // ===================== Visible Range =====================\n  // Render tab node & collect tab offset\n  var _useVisibleRange = useVisibleRange(tabOffsets,\n    // Container\n    visibleTabContentValue,\n    // Transform\n    tabPositionTopOrBottom ? transformLeft : transformTop,\n    // Tabs\n    tabContentSizeValue,\n    // Add\n    addSizeValue,\n    // Operation\n    operationSizeValue, _objectSpread(_objectSpread({}, props), {}, {\n      tabs: tabs\n    })),\n    _useVisibleRange2 = _slicedToArray(_useVisibleRange, 2),\n    visibleStart = _useVisibleRange2[0],\n    visibleEnd = _useVisibleRange2[1];\n\n  // ========================= Scroll ========================\n  var scrollToTab = useEvent(function () {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft;\n\n      // RTL\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + visibleTabContentValue) {\n          newTransform = tabOffset.right + tabOffset.width - visibleTabContentValue;\n        }\n      }\n      // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + visibleTabContentValue) {\n        newTransform = -(tabOffset.left + tabOffset.width - visibleTabContentValue);\n      }\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + visibleTabContentValue) {\n        _newTransform = -(tabOffset.top + tabOffset.height - visibleTabContentValue);\n      }\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  });\n\n  // ========================== Tab ==========================\n  var tabNodeStyle = {};\n  if (tabPosition === 'top' || tabPosition === 'bottom') {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/React.createElement(TabNode, {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */,\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onFocus: function onFocus() {\n        scrollToTab(key);\n        doLockAnimation();\n        if (!tabsWrapperRef.current) {\n          return;\n        }\n        // Focus element will make scrollLeft change which we should reset back\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n        tabsWrapperRef.current.scrollTop = 0;\n      }\n    });\n  });\n\n  // Update buttons records\n  var updateTabSizes = function updateTabSizes() {\n    return setTabSizes(function () {\n      var newSizes = new Map();\n      tabs.forEach(function (_ref2) {\n        var _tabListRef$current;\n        var key = _ref2.key;\n        var btnNode = (_tabListRef$current = tabListRef.current) === null || _tabListRef$current === void 0 ? void 0 : _tabListRef$current.querySelector(\"[data-node-key=\\\"\".concat(genDataNodeKey(key), \"\\\"]\"));\n        if (btnNode) {\n          newSizes.set(key, {\n            width: btnNode.offsetWidth,\n            height: btnNode.offsetHeight,\n            left: btnNode.offsetLeft,\n            top: btnNode.offsetTop\n          });\n        }\n      });\n      return newSizes;\n    });\n  };\n  useEffect(function () {\n    updateTabSizes();\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_')]);\n  var onListHolderResize = useUpdate(function () {\n    // Update wrapper records\n    var containerSize = getSize(containerRef);\n    var extraLeftSize = getSize(extraLeftRef);\n    var extraRightSize = getSize(extraRightRef);\n    setContainerExcludeExtraSize([containerSize[0] - extraLeftSize[0] - extraRightSize[0], containerSize[1] - extraLeftSize[1] - extraRightSize[1]]);\n    var newAddSize = getSize(innerAddButtonRef);\n    setAddSize(newAddSize);\n    var newOperationSize = getSize(operationsRef);\n    setOperationSize(newOperationSize);\n\n    // Which includes add button size\n    var tabContentFullSize = getSize(tabListRef);\n    setTabContentSize([tabContentFullSize[0] - newAddSize[0], tabContentFullSize[1] - newAddSize[1]]);\n\n    // Update buttons records\n    updateTabSizes();\n  });\n\n  // ======================== Dropdown =======================\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat(_toConsumableArray(startHiddenTabs), _toConsumableArray(endHiddenTabs));\n\n  // =================== Link & Operations ===================\n  var _useState11 = useState(),\n    _useState12 = _slicedToArray(_useState11, 2),\n    inkStyle = _useState12[0],\n    setInkStyle = _useState12[1];\n  var activeTabOffset = tabOffsets.get(activeKey);\n\n  // Delay set ink style to avoid remove tab blink\n  var inkBarRafRef = useRef();\n  function cleanInkBarRaf() {\n    raf.cancel(inkBarRafRef.current);\n  }\n  useEffect(function () {\n    var newInkStyle = {};\n    if (activeTabOffset) {\n      if (tabPositionTopOrBottom) {\n        if (rtl) {\n          newInkStyle.right = activeTabOffset.right;\n        } else {\n          newInkStyle.left = activeTabOffset.left;\n        }\n        newInkStyle.width = activeTabOffset.width;\n      } else {\n        newInkStyle.top = activeTabOffset.top;\n        newInkStyle.height = activeTabOffset.height;\n      }\n    }\n    cleanInkBarRaf();\n    inkBarRafRef.current = raf(function () {\n      setInkStyle(newInkStyle);\n    });\n    return cleanInkBarRaf;\n  }, [activeTabOffset, tabPositionTopOrBottom, rtl]);\n\n  // ========================= Effect ========================\n  useEffect(function () {\n    scrollToTab();\n    // eslint-disable-next-line\n  }, [activeKey, transformMin, transformMax, stringify(activeTabOffset), stringify(tabOffsets), tabPositionTopOrBottom]);\n\n  // Should recalculate when rtl changed\n  useEffect(function () {\n    onListHolderResize();\n    // eslint-disable-next-line\n  }, [rtl]);\n\n  // ========================= Render ========================\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft !== transformMax;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = transformLeft !== transformMin;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = transformTop !== transformMin;\n  }\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: useComposeRef(ref, containerRef),\n    role: \"tablist\",\n    className: classNames(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraLeftRef,\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom), _classNames)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/React.createElement(AddButton, {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: _objectSpread(_objectSpread({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-ink-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: inkStyle\n  })))), /*#__PURE__*/React.createElement(OperationNode, _extends({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraRightRef,\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  })));\n  /* eslint-enable */\n}\n\nexport default /*#__PURE__*/React.forwardRef(TabNavList);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,SAAS,IAAIC,cAAc,QAAQ,oBAAoB;AAC9D,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,cAAc,EAAEC,SAAS,QAAQ,SAAS;AACnD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,OAAO,MAAM,WAAW;AAC/B,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,MAAM,EAAE;EACrC,IAAIC,IAAI,GAAGD,MAAM,CAACE,OAAO,IAAI,CAAC,CAAC;IAC7BC,gBAAgB,GAAGF,IAAI,CAACG,WAAW;IACnCA,WAAW,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,gBAAgB;IAChEE,iBAAiB,GAAGJ,IAAI,CAACK,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;EACrE,OAAO,CAACD,WAAW,EAAEE,YAAY,CAAC;AACpC,CAAC;;AAED;AACA;AACA;AACA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,sBAAsB,EAAE;EACrE,OAAOD,IAAI,CAACC,sBAAsB,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7C,CAAC;AACD,SAASC,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC9B,IAAIC,WAAW;EACf,IAAIC,iBAAiB,GAAGhC,KAAK,CAACiC,UAAU,CAACvB,UAAU,CAAC;IAClDwB,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;EAC/B,IAAIC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC7BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,EAAE,GAAGT,KAAK,CAACS,EAAE;IACbC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,GAAG,GAAGZ,KAAK,CAACY,GAAG;IACfC,KAAK,GAAGb,KAAK,CAACa,KAAK;IACnBC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;IACzBC,MAAM,GAAGf,KAAK,CAACe,MAAM;IACrBC,WAAW,GAAGhB,KAAK,CAACgB,WAAW;IAC/BC,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;IACzBC,UAAU,GAAGnB,KAAK,CAACmB,UAAU;IAC7BC,WAAW,GAAGpB,KAAK,CAACoB,WAAW;EACjC,IAAIC,YAAY,GAAGhD,MAAM,CAAC,CAAC;EAC3B,IAAIiD,YAAY,GAAGjD,MAAM,CAAC,CAAC;EAC3B,IAAIkD,aAAa,GAAGlD,MAAM,CAAC,CAAC;EAC5B,IAAImD,cAAc,GAAGnD,MAAM,CAAC,CAAC;EAC7B,IAAIoD,UAAU,GAAGpD,MAAM,CAAC,CAAC;EACzB,IAAIqD,aAAa,GAAGrD,MAAM,CAAC,CAAC;EAC5B,IAAIsD,iBAAiB,GAAGtD,MAAM,CAAC,CAAC;EAChC;;EAEA,IAAIyB,sBAAsB,GAAGkB,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ;EAC9E,IAAIY,aAAa,GAAGpD,YAAY,CAAC,CAAC,EAAE,UAAUqD,IAAI,EAAEC,IAAI,EAAE;MACtD,IAAIhC,sBAAsB,IAAIsB,WAAW,EAAE;QACzCA,WAAW,CAAC;UACVW,SAAS,EAAEF,IAAI,GAAGC,IAAI,GAAG,MAAM,GAAG;QACpC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACFE,cAAc,GAAGnE,cAAc,CAAC+D,aAAa,EAAE,CAAC,CAAC;IACjDK,aAAa,GAAGD,cAAc,CAAC,CAAC,CAAC;IACjCE,gBAAgB,GAAGF,cAAc,CAAC,CAAC,CAAC;EACtC,IAAIG,cAAc,GAAG3D,YAAY,CAAC,CAAC,EAAE,UAAUqD,IAAI,EAAEC,IAAI,EAAE;MACvD,IAAI,CAAChC,sBAAsB,IAAIsB,WAAW,EAAE;QAC1CA,WAAW,CAAC;UACVW,SAAS,EAAEF,IAAI,GAAGC,IAAI,GAAG,KAAK,GAAG;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACFM,cAAc,GAAGvE,cAAc,CAACsE,cAAc,EAAE,CAAC,CAAC;IAClDE,YAAY,GAAGD,cAAc,CAAC,CAAC,CAAC;IAChCE,eAAe,GAAGF,cAAc,CAAC,CAAC,CAAC;EACrC,IAAIG,SAAS,GAAGjE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9BkE,UAAU,GAAG3E,cAAc,CAAC0E,SAAS,EAAE,CAAC,CAAC;IACzCE,yBAAyB,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzCE,4BAA4B,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC9C,IAAIG,UAAU,GAAGrE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/BsE,UAAU,GAAG/E,cAAc,CAAC8E,UAAU,EAAE,CAAC,CAAC;IAC1CE,cAAc,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC9BE,iBAAiB,GAAGF,UAAU,CAAC,CAAC,CAAC;EACnC,IAAIG,UAAU,GAAGzE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B0E,UAAU,GAAGnF,cAAc,CAACkF,UAAU,EAAE,CAAC,CAAC;IAC1CE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC5B,IAAIG,UAAU,GAAG7E,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B8E,UAAU,GAAGvF,cAAc,CAACsF,UAAU,EAAE,CAAC,CAAC;IAC1CE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAClC,IAAIG,eAAe,GAAG5E,cAAc,CAAC,IAAI6E,GAAG,CAAC,CAAC,CAAC;IAC7CC,gBAAgB,GAAG5F,cAAc,CAAC0F,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIG,UAAU,GAAGrF,UAAU,CAAC+B,IAAI,EAAEoD,QAAQ,EAAEb,cAAc,CAAC,CAAC,CAAC,CAAC;;EAE9D;EACA,IAAIgB,8BAA8B,GAAGjE,YAAY,CAAC6C,yBAAyB,EAAE3C,sBAAsB,CAAC;EACpG,IAAIgE,mBAAmB,GAAGlE,YAAY,CAACiD,cAAc,EAAE/C,sBAAsB,CAAC;EAC9E,IAAIiE,YAAY,GAAGnE,YAAY,CAACqD,OAAO,EAAEnD,sBAAsB,CAAC;EAChE,IAAIkE,kBAAkB,GAAGpE,YAAY,CAACyD,aAAa,EAAEvD,sBAAsB,CAAC;EAC5E,IAAImE,UAAU,GAAGJ,8BAA8B,GAAGC,mBAAmB,GAAGC,YAAY;EACpF,IAAIG,sBAAsB,GAAGD,UAAU,GAAGJ,8BAA8B,GAAGG,kBAAkB,GAAGH,8BAA8B,GAAGE,YAAY;;EAE7I;EACA,IAAII,yBAAyB,GAAG,EAAE,CAACC,MAAM,CAAC/D,SAAS,EAAE,wBAAwB,CAAC;EAC9E,IAAIgE,YAAY,GAAG,CAAC;EACpB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAI,CAACxE,sBAAsB,EAAE;IAC3BuE,YAAY,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,sBAAsB,GAAGJ,mBAAmB,CAAC;IACxEQ,YAAY,GAAG,CAAC;EAClB,CAAC,MAAM,IAAI1D,GAAG,EAAE;IACdyD,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEX,mBAAmB,GAAGI,sBAAsB,CAAC;EAC1E,CAAC,MAAM;IACLG,YAAY,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,sBAAsB,GAAGJ,mBAAmB,CAAC;IACxEQ,YAAY,GAAG,CAAC;EAClB;EACA,SAASI,YAAYA,CAACC,KAAK,EAAE;IAC3B,IAAIA,KAAK,GAAGN,YAAY,EAAE;MACxB,OAAOA,YAAY;IACrB;IACA,IAAIM,KAAK,GAAGL,YAAY,EAAE;MACxB,OAAOA,YAAY;IACrB;IACA,OAAOK,KAAK;EACd;;EAEA;EACA,IAAIC,cAAc,GAAGvG,MAAM,CAAC,CAAC;EAC7B,IAAIwG,UAAU,GAAGvG,QAAQ,CAAC,CAAC;IACzBwG,WAAW,GAAGjH,cAAc,CAACgH,UAAU,EAAE,CAAC,CAAC;IAC3CE,aAAa,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC9BE,gBAAgB,GAAGF,WAAW,CAAC,CAAC,CAAC;EACnC,SAASG,eAAeA,CAAA,EAAG;IACzBD,gBAAgB,CAACE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC9B;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1BC,MAAM,CAACC,YAAY,CAACV,cAAc,CAACrF,OAAO,CAAC;EAC7C;EACAd,YAAY,CAAC+C,cAAc,EAAE,UAAU+D,OAAO,EAAEC,OAAO,EAAE;IACvD,SAASC,MAAMA,CAACC,QAAQ,EAAEC,MAAM,EAAE;MAChCD,QAAQ,CAAC,UAAUf,KAAK,EAAE;QACxB,IAAIiB,QAAQ,GAAGlB,YAAY,CAACC,KAAK,GAAGgB,MAAM,CAAC;QAC3C,OAAOC,QAAQ;MACjB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAAC3B,UAAU,EAAE;MACf,OAAO,KAAK;IACd;IACA,IAAInE,sBAAsB,EAAE;MAC1B2F,MAAM,CAACvD,gBAAgB,EAAEqD,OAAO,CAAC;IACnC,CAAC,MAAM;MACLE,MAAM,CAACnD,eAAe,EAAEkD,OAAO,CAAC;IAClC;IACAJ,gBAAgB,CAAC,CAAC;IAClBH,eAAe,CAAC,CAAC;IACjB,OAAO,IAAI;EACb,CAAC,CAAC;EACF7G,SAAS,CAAC,YAAY;IACpBgH,gBAAgB,CAAC,CAAC;IAClB,IAAIL,aAAa,EAAE;MACjBH,cAAc,CAACrF,OAAO,GAAG8F,MAAM,CAACQ,UAAU,CAAC,YAAY;QACrDb,gBAAgB,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT;IACA,OAAOI,gBAAgB;EACzB,CAAC,EAAE,CAACL,aAAa,CAAC,CAAC;;EAEnB;EACA;EACA,IAAIe,gBAAgB,GAAGlH,eAAe,CAACgF,UAAU;IAC/C;IACAM,sBAAsB;IACtB;IACApE,sBAAsB,GAAGmC,aAAa,GAAGI,YAAY;IACrD;IACAyB,mBAAmB;IACnB;IACAC,YAAY;IACZ;IACAC,kBAAkB,EAAEpG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9DM,IAAI,EAAEA;IACR,CAAC,CAAC,CAAC;IACHyF,iBAAiB,GAAGlI,cAAc,CAACiI,gBAAgB,EAAE,CAAC,CAAC;IACvDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,UAAU,GAAGF,iBAAiB,CAAC,CAAC,CAAC;;EAEnC;EACA,IAAIG,WAAW,GAAGlI,QAAQ,CAAC,YAAY;IACrC,IAAImI,GAAG,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGzF,SAAS;IACvF,IAAI4F,SAAS,GAAG3C,UAAU,CAAC4C,GAAG,CAACL,GAAG,CAAC,IAAI;MACrCM,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;IACP,CAAC;IACD,IAAI/G,sBAAsB,EAAE;MAC1B;MACA,IAAIgH,YAAY,GAAG7E,aAAa;;MAEhC;MACA,IAAIrB,GAAG,EAAE;QACP,IAAI2F,SAAS,CAACK,KAAK,GAAG3E,aAAa,EAAE;UACnC6E,YAAY,GAAGP,SAAS,CAACK,KAAK;QAChC,CAAC,MAAM,IAAIL,SAAS,CAACK,KAAK,GAAGL,SAAS,CAACE,KAAK,GAAGxE,aAAa,GAAGiC,sBAAsB,EAAE;UACrF4C,YAAY,GAAGP,SAAS,CAACK,KAAK,GAAGL,SAAS,CAACE,KAAK,GAAGvC,sBAAsB;QAC3E;MACF;MACA;MAAA,KACK,IAAIqC,SAAS,CAACI,IAAI,GAAG,CAAC1E,aAAa,EAAE;QACxC6E,YAAY,GAAG,CAACP,SAAS,CAACI,IAAI;MAChC,CAAC,MAAM,IAAIJ,SAAS,CAACI,IAAI,GAAGJ,SAAS,CAACE,KAAK,GAAG,CAACxE,aAAa,GAAGiC,sBAAsB,EAAE;QACrF4C,YAAY,GAAG,EAAEP,SAAS,CAACI,IAAI,GAAGJ,SAAS,CAACE,KAAK,GAAGvC,sBAAsB,CAAC;MAC7E;MACA5B,eAAe,CAAC,CAAC,CAAC;MAClBJ,gBAAgB,CAACwC,YAAY,CAACoC,YAAY,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL;MACA,IAAIC,aAAa,GAAG1E,YAAY;MAChC,IAAIkE,SAAS,CAACM,GAAG,GAAG,CAACxE,YAAY,EAAE;QACjC0E,aAAa,GAAG,CAACR,SAAS,CAACM,GAAG;MAChC,CAAC,MAAM,IAAIN,SAAS,CAACM,GAAG,GAAGN,SAAS,CAACG,MAAM,GAAG,CAACrE,YAAY,GAAG6B,sBAAsB,EAAE;QACpF6C,aAAa,GAAG,EAAER,SAAS,CAACM,GAAG,GAAGN,SAAS,CAACG,MAAM,GAAGxC,sBAAsB,CAAC;MAC9E;MACAhC,gBAAgB,CAAC,CAAC,CAAC;MACnBI,eAAe,CAACoC,YAAY,CAACqC,aAAa,CAAC,CAAC;IAC9C;EACF,CAAC,CAAC;;EAEF;EACA,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIhG,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ,EAAE;IACrDgG,YAAY,CAACpG,GAAG,GAAG,aAAa,GAAG,YAAY,CAAC,GAAGK,YAAY;EACjE,CAAC,MAAM;IACL+F,YAAY,CAACC,SAAS,GAAGhG,YAAY;EACvC;EACA,IAAIiG,QAAQ,GAAG5G,IAAI,CAAC6G,GAAG,CAAC,UAAUC,GAAG,EAAEC,CAAC,EAAE;IACxC,IAAIlB,GAAG,GAAGiB,GAAG,CAACjB,GAAG;IACjB,OAAO,aAAahI,KAAK,CAACmJ,aAAa,CAACnI,OAAO,EAAE;MAC/CsB,EAAE,EAAEA,EAAE;MACNJ,SAAS,EAAEA,SAAS;MACpB8F,GAAG,EAAEA,GAAG;MACRiB,GAAG,EAAEA;MACL;MACA5G,KAAK,EAAE6G,CAAC,KAAK,CAAC,GAAGf,SAAS,GAAGU,YAAY;MACzCO,QAAQ,EAAEH,GAAG,CAACG,QAAQ;MACtBzG,QAAQ,EAAEA,QAAQ;MAClB0G,MAAM,EAAErB,GAAG,KAAKxF,SAAS;MACzB8G,aAAa,EAAEvG,QAAQ;MACvBwG,eAAe,EAAE3G,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC2G,eAAe;MACvFC,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;QAC3BzG,UAAU,CAACgF,GAAG,EAAEyB,CAAC,CAAC;MACpB,CAAC;MACDC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B3B,WAAW,CAACC,GAAG,CAAC;QAChBlB,eAAe,CAAC,CAAC;QACjB,IAAI,CAACzD,cAAc,CAACjC,OAAO,EAAE;UAC3B;QACF;QACA;QACA,IAAI,CAACqB,GAAG,EAAE;UACRY,cAAc,CAACjC,OAAO,CAACuI,UAAU,GAAG,CAAC;QACvC;QACAtG,cAAc,CAACjC,OAAO,CAACwI,SAAS,GAAG,CAAC;MACtC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,OAAOrE,WAAW,CAAC,YAAY;MAC7B,IAAIsE,QAAQ,GAAG,IAAIzE,GAAG,CAAC,CAAC;MACxBlD,IAAI,CAAC4H,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC5B,IAAIC,mBAAmB;QACvB,IAAIjC,GAAG,GAAGgC,KAAK,CAAChC,GAAG;QACnB,IAAIkC,OAAO,GAAG,CAACD,mBAAmB,GAAG3G,UAAU,CAAClC,OAAO,MAAM,IAAI,IAAI6I,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACE,aAAa,CAAC,mBAAmB,CAAClE,MAAM,CAACtF,cAAc,CAACqH,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QACxM,IAAIkC,OAAO,EAAE;UACXJ,QAAQ,CAACM,GAAG,CAACpC,GAAG,EAAE;YAChBM,KAAK,EAAE4B,OAAO,CAAC5I,WAAW;YAC1BiH,MAAM,EAAE2B,OAAO,CAAC1I,YAAY;YAC5BgH,IAAI,EAAE0B,OAAO,CAACG,UAAU;YACxB3B,GAAG,EAAEwB,OAAO,CAACI;UACf,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACF,OAAOR,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC;EACD7J,SAAS,CAAC,YAAY;IACpB4J,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC1H,IAAI,CAAC6G,GAAG,CAAC,UAAUC,GAAG,EAAE;IAC1B,OAAOA,GAAG,CAACjB,GAAG;EAChB,CAAC,CAAC,CAACuC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EACd,IAAIC,kBAAkB,GAAGjK,SAAS,CAAC,YAAY;IAC7C;IACA,IAAIkK,aAAa,GAAGxJ,OAAO,CAACiC,YAAY,CAAC;IACzC,IAAIwH,aAAa,GAAGzJ,OAAO,CAACkC,YAAY,CAAC;IACzC,IAAIwH,cAAc,GAAG1J,OAAO,CAACmC,aAAa,CAAC;IAC3CmB,4BAA4B,CAAC,CAACkG,aAAa,CAAC,CAAC,CAAC,GAAGC,aAAa,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC,EAAEF,aAAa,CAAC,CAAC,CAAC,GAAGC,aAAa,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAChJ,IAAIC,UAAU,GAAG3J,OAAO,CAACuC,iBAAiB,CAAC;IAC3CuB,UAAU,CAAC6F,UAAU,CAAC;IACtB,IAAIC,gBAAgB,GAAG5J,OAAO,CAACsC,aAAa,CAAC;IAC7C4B,gBAAgB,CAAC0F,gBAAgB,CAAC;;IAElC;IACA,IAAIC,kBAAkB,GAAG7J,OAAO,CAACqC,UAAU,CAAC;IAC5CqB,iBAAiB,CAAC,CAACmG,kBAAkB,CAAC,CAAC,CAAC,GAAGF,UAAU,CAAC,CAAC,CAAC,EAAEE,kBAAkB,CAAC,CAAC,CAAC,GAAGF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjG;IACAf,cAAc,CAAC,CAAC;EAClB,CAAC,CAAC;;EAEF;EACA,IAAIkB,eAAe,GAAG5I,IAAI,CAAC6I,KAAK,CAAC,CAAC,EAAEnD,YAAY,CAAC;EACjD,IAAIoD,aAAa,GAAG9I,IAAI,CAAC6I,KAAK,CAAClD,UAAU,GAAG,CAAC,CAAC;EAC9C,IAAIoD,UAAU,GAAG,EAAE,CAACjF,MAAM,CAACzG,kBAAkB,CAACuL,eAAe,CAAC,EAAEvL,kBAAkB,CAACyL,aAAa,CAAC,CAAC;;EAElG;EACA,IAAIE,WAAW,GAAGhL,QAAQ,CAAC,CAAC;IAC1BiL,WAAW,GAAG1L,cAAc,CAACyL,WAAW,EAAE,CAAC,CAAC;IAC5CE,QAAQ,GAAGD,WAAW,CAAC,CAAC,CAAC;IACzBE,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;EAC9B,IAAIG,eAAe,GAAG9F,UAAU,CAAC4C,GAAG,CAAC7F,SAAS,CAAC;;EAE/C;EACA,IAAIgJ,YAAY,GAAGtL,MAAM,CAAC,CAAC;EAC3B,SAASuL,cAAcA,CAAA,EAAG;IACxB3L,GAAG,CAAC4L,MAAM,CAACF,YAAY,CAACpK,OAAO,CAAC;EAClC;EACAnB,SAAS,CAAC,YAAY;IACpB,IAAI0L,WAAW,GAAG,CAAC,CAAC;IACpB,IAAIJ,eAAe,EAAE;MACnB,IAAI5J,sBAAsB,EAAE;QAC1B,IAAIc,GAAG,EAAE;UACPkJ,WAAW,CAAClD,KAAK,GAAG8C,eAAe,CAAC9C,KAAK;QAC3C,CAAC,MAAM;UACLkD,WAAW,CAACnD,IAAI,GAAG+C,eAAe,CAAC/C,IAAI;QACzC;QACAmD,WAAW,CAACrD,KAAK,GAAGiD,eAAe,CAACjD,KAAK;MAC3C,CAAC,MAAM;QACLqD,WAAW,CAACjD,GAAG,GAAG6C,eAAe,CAAC7C,GAAG;QACrCiD,WAAW,CAACpD,MAAM,GAAGgD,eAAe,CAAChD,MAAM;MAC7C;IACF;IACAkD,cAAc,CAAC,CAAC;IAChBD,YAAY,CAACpK,OAAO,GAAGtB,GAAG,CAAC,YAAY;MACrCwL,WAAW,CAACK,WAAW,CAAC;IAC1B,CAAC,CAAC;IACF,OAAOF,cAAc;EACvB,CAAC,EAAE,CAACF,eAAe,EAAE5J,sBAAsB,EAAEc,GAAG,CAAC,CAAC;;EAElD;EACAxC,SAAS,CAAC,YAAY;IACpB8H,WAAW,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACvF,SAAS,EAAE0D,YAAY,EAAEC,YAAY,EAAEvF,SAAS,CAAC2K,eAAe,CAAC,EAAE3K,SAAS,CAAC6E,UAAU,CAAC,EAAE9D,sBAAsB,CAAC,CAAC;;EAEtH;EACA1B,SAAS,CAAC,YAAY;IACpBuK,kBAAkB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC/H,GAAG,CAAC,CAAC;;EAET;EACA,IAAImJ,WAAW,GAAG,CAAC,CAACV,UAAU,CAAChD,MAAM;EACrC,IAAI2D,UAAU,GAAG,EAAE,CAAC5F,MAAM,CAAC/D,SAAS,EAAE,WAAW,CAAC;EAClD,IAAI4J,QAAQ;EACZ,IAAIC,SAAS;EACb,IAAIC,OAAO;EACX,IAAIC,UAAU;EACd,IAAItK,sBAAsB,EAAE;IAC1B,IAAIc,GAAG,EAAE;MACPsJ,SAAS,GAAGjI,aAAa,GAAG,CAAC;MAC7BgI,QAAQ,GAAGhI,aAAa,KAAKqC,YAAY;IAC3C,CAAC,MAAM;MACL2F,QAAQ,GAAGhI,aAAa,GAAG,CAAC;MAC5BiI,SAAS,GAAGjI,aAAa,KAAKoC,YAAY;IAC5C;EACF,CAAC,MAAM;IACL8F,OAAO,GAAG9H,YAAY,GAAG,CAAC;IAC1B+H,UAAU,GAAG/H,YAAY,KAAKgC,YAAY;EAC5C;EACA,OAAO,aAAalG,KAAK,CAACmJ,aAAa,CAACvJ,cAAc,EAAE;IACtDsM,QAAQ,EAAE1B;EACZ,CAAC,EAAE,aAAaxK,KAAK,CAACmJ,aAAa,CAAC,KAAK,EAAE;IACzCrH,GAAG,EAAE/B,aAAa,CAAC+B,GAAG,EAAEoB,YAAY,CAAC;IACrCiJ,IAAI,EAAE,SAAS;IACf/J,SAAS,EAAEzC,UAAU,CAAC,EAAE,CAACsG,MAAM,CAAC/D,SAAS,EAAE,MAAM,CAAC,EAAEE,SAAS,CAAC;IAC9DC,KAAK,EAAEA,KAAK;IACZ+J,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;MAC9B;MACAtF,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,aAAa9G,KAAK,CAACmJ,aAAa,CAACrI,YAAY,EAAE;IAChDgB,GAAG,EAAEqB,YAAY;IACjBkJ,QAAQ,EAAE,MAAM;IAChB3J,KAAK,EAAEA,KAAK;IACZR,SAAS,EAAEA;EACb,CAAC,CAAC,EAAE,aAAalC,KAAK,CAACmJ,aAAa,CAAC,KAAK,EAAE;IAC1C/G,SAAS,EAAEzC,UAAU,CAACkM,UAAU,GAAG9J,WAAW,GAAG,CAAC,CAAC,EAAExC,eAAe,CAACwC,WAAW,EAAE,EAAE,CAACkE,MAAM,CAAC4F,UAAU,EAAE,YAAY,CAAC,EAAEC,QAAQ,CAAC,EAAEvM,eAAe,CAACwC,WAAW,EAAE,EAAE,CAACkE,MAAM,CAAC4F,UAAU,EAAE,aAAa,CAAC,EAAEE,SAAS,CAAC,EAAExM,eAAe,CAACwC,WAAW,EAAE,EAAE,CAACkE,MAAM,CAAC4F,UAAU,EAAE,WAAW,CAAC,EAAEG,OAAO,CAAC,EAAEzM,eAAe,CAACwC,WAAW,EAAE,EAAE,CAACkE,MAAM,CAAC4F,UAAU,EAAE,cAAc,CAAC,EAAEI,UAAU,CAAC,EAAElK,WAAW,CAAC,CAAC;IAC1XD,GAAG,EAAEuB;EACP,CAAC,EAAE,aAAarD,KAAK,CAACmJ,aAAa,CAACvJ,cAAc,EAAE;IAClDsM,QAAQ,EAAE1B;EACZ,CAAC,EAAE,aAAaxK,KAAK,CAACmJ,aAAa,CAAC,KAAK,EAAE;IACzCrH,GAAG,EAAEwB,UAAU;IACflB,SAAS,EAAE,EAAE,CAAC6D,MAAM,CAAC/D,SAAS,EAAE,WAAW,CAAC;IAC5CG,KAAK,EAAE;MACLiK,SAAS,EAAE,YAAY,CAACrG,MAAM,CAACnC,aAAa,EAAE,MAAM,CAAC,CAACmC,MAAM,CAAC/B,YAAY,EAAE,KAAK,CAAC;MACjFqI,UAAU,EAAE3F,aAAa,GAAG,MAAM,GAAGuB;IACvC;EACF,CAAC,EAAEY,QAAQ,EAAE,aAAa/I,KAAK,CAACmJ,aAAa,CAACtI,SAAS,EAAE;IACvDiB,GAAG,EAAE0B,iBAAiB;IACtBtB,SAAS,EAAEA,SAAS;IACpBU,MAAM,EAAEA,MAAM;IACdD,QAAQ,EAAEA,QAAQ;IAClBN,KAAK,EAAE5C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsJ,QAAQ,CAACb,MAAM,KAAK,CAAC,GAAGC,SAAS,GAAGU,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5F2D,UAAU,EAAEZ,WAAW,GAAG,QAAQ,GAAG;IACvC,CAAC;EACH,CAAC,CAAC,EAAE,aAAa5L,KAAK,CAACmJ,aAAa,CAAC,KAAK,EAAE;IAC1C/G,SAAS,EAAEzC,UAAU,CAAC,EAAE,CAACsG,MAAM,CAAC/D,SAAS,EAAE,UAAU,CAAC,EAAE3C,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC0G,MAAM,CAAC/D,SAAS,EAAE,mBAAmB,CAAC,EAAEK,QAAQ,CAACkK,MAAM,CAAC,CAAC;IACxIpK,KAAK,EAAEgJ;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,aAAarL,KAAK,CAACmJ,aAAa,CAACpI,aAAa,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEuC,KAAK,EAAE;IACzE0H,eAAe,EAAE3G,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC2G,eAAe;IACvFzH,GAAG,EAAEyB,aAAa;IAClBrB,SAAS,EAAEA,SAAS;IACpBC,IAAI,EAAE+I,UAAU;IAChB9I,SAAS,EAAE,CAACwJ,WAAW,IAAI5F,yBAAyB;IACpD0G,SAAS,EAAE,CAAC,CAAC9F;EACf,CAAC,CAAC,CAAC,EAAE,aAAa5G,KAAK,CAACmJ,aAAa,CAACrI,YAAY,EAAE;IAClDgB,GAAG,EAAEsB,aAAa;IAClBiJ,QAAQ,EAAE,OAAO;IACjB3J,KAAK,EAAEA,KAAK;IACZR,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;;AAEA,eAAe,aAAalC,KAAK,CAAC2M,UAAU,CAAC/K,UAAU,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}