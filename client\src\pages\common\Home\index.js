import React, { useState, useEffect, useRef } from "react";
import "./index.css";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import {
  TbArrowBigRightLinesFilled,
  TbBrain,
  TbBook,
  TbTrophy,
  TbUsers,
  TbStar,
  TbQuestionMark,
  TbChartBar,
  TbSchool,
  TbMenu2,
  TbX,
  TbMoon,
  TbSun
} from "react-icons/tb";
import { AiOutlinePlus } from "react-icons/ai";
import { message, Rate } from "antd";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { getAllReviews } from "../../../apicalls/reviews";
import Image1 from "../../../assets/collage-1.png";
import Image2 from "../../../assets/collage-2.png";
import { contactUs } from "../../../apicalls/users";
import { useTheme } from "../../../contexts/ThemeContext";
import { <PERSON><PERSON>, <PERSON> } from "../../../components/modern";

const Home = () => {
  const homeSectionRef = useRef(null);
  const aboutUsSectionRef = useRef(null);
  const reviewsSectionRef = useRef(null);
  const contactUsRef = useRef(null);
  const [reviews, setReviews] = useState([]); // Initialize as an array
  const dispatch = useDispatch();
  const [menuOpen, setMenuOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: "",
  });

  const [loading, setLoading] = useState(false);
  const [responseMessage, setResponseMessage] = useState("");

  const getReviews = async () => {
    dispatch(ShowLoading());
    try {
      const response = await getAllReviews();
      if (response.success) {
        setReviews(response.data);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
    dispatch(HideLoading());
  };

  useEffect(() => {
    getReviews();
  }, []);

  const scrollToSection = (ref, offset = 30) => {
    if (ref && ref.current) {
      const sectionTop = ref.current.offsetTop;
      window.scrollTo({
        top: sectionTop - offset,
        behavior: "smooth"
      });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setResponseMessage("");

    try {
      // Assume contactUs returns the parsed JSON response
      const data = await contactUs(formData);

      if (data.success) {
        message.success("Message sent successfully!");
        setResponseMessage("Message sent successfully!");
        setFormData({ name: "", email: "", message: "" }); // Reset form
      } else {
        setResponseMessage(data.message || "Something went wrong.");
      }
    } catch (error) {
      setResponseMessage("Error sending message. Please try again.");
    }
    setLoading(false);
  };

  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900">
      {/* Modern Navigation */}
      <motion.nav
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="nav-modern fixed w-full top-0 z-50"
      >
        <div className="container-modern">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              <TbBrain className="w-8 h-8 text-primary-600" />
              <span className="text-2xl font-bold text-gray-900 dark:text-white">
                Brain<span className="text-primary-600">Wave</span>
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <button
                onClick={() => scrollToSection(homeSectionRef)}
                className="nav-item"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection(aboutUsSectionRef)}
                className="nav-item"
              >
                About Us
              </button>
              <button
                onClick={() => scrollToSection(reviewsSectionRef)}
                className="nav-item"
              >
                Reviews
              </button>
              <button
                onClick={() => scrollToSection(contactUsRef)}
                className="nav-item"
              >
                Contact Us
              </button>
            </div>

            {/* Right Section */}
            <div className="flex items-center space-x-4">
              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
              >
                {isDarkMode ? <TbSun className="w-5 h-5" /> : <TbMoon className="w-5 h-5" />}
              </button>

              {/* Auth Buttons - Desktop */}
              <div className="hidden md:flex items-center space-x-3">
                <Link to="/login">
                  <Button variant="ghost" size="sm">Login</Button>
                </Link>
                <Link to="/register">
                  <Button variant="primary" size="sm">Sign Up</Button>
                </Link>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setMenuOpen(!menuOpen)}
                className="md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
              >
                {menuOpen ? <TbX className="w-6 h-6" /> : <TbMenu2 className="w-6 h-6" />}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {menuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="md:hidden py-4 border-t border-gray-100"
            >
              <div className="flex flex-col space-y-2">
                <button
                  onClick={() => {
                    scrollToSection(homeSectionRef);
                    setMenuOpen(false);
                  }}
                  className="nav-item text-left"
                >
                  Home
                </button>
                <button
                  onClick={() => {
                    scrollToSection(aboutUsSectionRef);
                    setMenuOpen(false);
                  }}
                  className="nav-item text-left"
                >
                  About Us
                </button>
                <button
                  onClick={() => {
                    scrollToSection(reviewsSectionRef);
                    setMenuOpen(false);
                  }}
                  className="nav-item text-left"
                >
                  Reviews
                </button>
                <button
                  onClick={() => {
                    scrollToSection(contactUsRef);
                    setMenuOpen(false);
                  }}
                  className="nav-item text-left"
                >
                  Contact Us
                </button>
                <div className="flex space-x-3 pt-4">
                  <Link to="/login" className="flex-1">
                    <Button variant="ghost" size="sm" className="w-full">Login</Button>
                  </Link>
                  <Link to="/register" className="flex-1">
                    <Button variant="primary" size="sm" className="w-full">Sign Up</Button>
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </motion.nav>
      {/* Modern Hero Section */}
      <section ref={homeSectionRef} className="pt-20 pb-16 lg:pt-24 lg:pb-20">
        <div className="container-modern">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Hero Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="space-y-8"
            >
              <div className="space-y-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="inline-flex items-center px-4 py-2 bg-primary-50 text-primary-700 rounded-full text-sm font-medium"
                >
                  <TbSchool className="w-4 h-4 mr-2" />
                  #1 Educational Platform in Tanzania
                </motion.div>

                <h1 className="heading-1">
                  Fueling Bright Futures with{" "}
                  <span className="text-gradient">
                    <TbArrowBigRightLinesFilled className="inline w-12 h-12 lg:w-16 lg:h-16" />
                    Education.
                  </span>
                </h1>

                <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
                  Discover limitless learning opportunities with our comprehensive
                  online study platform. Study anywhere, anytime, and achieve your
                  academic goals with confidence.
                </p>
              </div>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <Link to="/register">
                  <Button
                    variant="gradient"
                    size="lg"
                    className="w-full sm:w-auto"
                    icon={<TbArrowBigRightLinesFilled />}
                    iconPosition="right"
                  >
                    Get Started Free
                  </Button>
                </Link>
                <Link to="/login">
                  <Button
                    variant="secondary"
                    size="lg"
                    className="w-full sm:w-auto"
                  >
                    Sign In
                  </Button>
                </Link>
              </motion.div>

              {/* Trust Indicators */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="flex items-center space-x-6 text-sm text-gray-500"
              >
                <div className="flex items-center space-x-2">
                  <TbUsers className="w-5 h-5 text-primary-600" />
                  <span>15K+ Students</span>
                </div>
                <div className="flex items-center space-x-2">
                  <TbStar className="w-5 h-5 text-yellow-500" />
                  <span>4.9/5 Rating</span>
                </div>
                <div className="flex items-center space-x-2">
                  <TbTrophy className="w-5 h-5 text-primary-600" />
                  <span>Award Winning</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Hero Image */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="relative"
            >
              <div className="relative z-10">
                <img
                  src={Image1}
                  alt="Students Learning"
                  className="w-full h-auto rounded-2xl shadow-large"
                />

                {/* Floating Elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="absolute -top-4 -left-4 bg-white rounded-xl shadow-medium p-4"
                >
                  <TbBook className="w-8 h-8 text-primary-600" />
                </motion.div>

                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="absolute -bottom-4 -right-4 bg-white rounded-xl shadow-medium p-4"
                >
                  <TbTrophy className="w-8 h-8 text-yellow-500" />
                </motion.div>
              </div>

              {/* Background Decoration */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary-100 to-blue-100 rounded-2xl transform rotate-3 scale-105 -z-10"></div>
            </motion.div>
          </div>
        </div>
      </section>
      {/* Modern Statistics Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {[
              { number: "15K+", text: "Students Joined", icon: TbUsers, color: "text-blue-600" },
              { number: "300+", text: "Expert Mentors", icon: TbSchool, color: "text-green-600" },
              { number: "7K+", text: "Success Stories", icon: TbTrophy, color: "text-yellow-600" },
              { number: "250+", text: "Trendy Courses", icon: TbBook, color: "text-purple-600" },
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="relative">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-primary-100 to-blue-100 mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <stat.icon className={`w-8 h-8 ${stat.color}`} />
                  </div>
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
                    viewport={{ once: true }}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center"
                  >
                    <span className="text-white text-xs font-bold">✓</span>
                  </motion.div>
                </div>
                <motion.div
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 + 0.4 }}
                  viewport={{ once: true }}
                  className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2"
                >
                  {stat.number}
                </motion.div>
                <p className="text-gray-600 dark:text-gray-300 font-medium">
                  {stat.text}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>
      <section ref={aboutUsSectionRef} className="section-3">
        <div className="content-1">
          <div className="title">Discover knowledge in limitless realms.</div>
          <p className="para">
            Education serves as the cornerstone of personal and societal
            development. It is a dynamic process that empowers individuals with
            the knowledge, skills, and critical thinking abilities essential for
            success.
          </p>
          <div className="btn-container">
            <Link to="/user/about-us" className="btn btn-1">
              Learn More
            </Link>
          </div>
        </div>
        <div className="content-2">
          <img src={Image2} alt="Collage-1" className="collage" />
        </div>
      </section>
      <section ref={reviewsSectionRef} className="section-4">
        <div className="content-1">
          <div className="title">
            Reviews from <br />
            some students
          </div>
        </div>
        <div className="content-2">
          {reviews.length !== 0 ? (
            reviews.map((review, index) => (
              <div key={index} className="review-card">
                <Rate defaultValue={review.rating} className="rate" disabled />
                <div className="text">"{review.text}"</div>
                <div className="seperator"></div>
                <div className="name">{review.user?.name}</div>
              </div>
            ))
          ) : (
            <div>No reviews yet.</div>
          )}
        </div>
      </section>

      <section ref={contactUsRef} className="contact-section section-4">
        <div className="content-1">
          <div className="title">Contact Us</div>
        </div>
        <div className="contact-container" style={{ marginTop: "40px" }}>
          <div className="contact-box">
            <form className="contact-form" onSubmit={handleSubmit}>
              <div className="contact-field">
                <label className="contact-label">Name</label>
                <input
                  type="text"
                  name="name"
                  placeholder="Your Name"
                  className="contact-input"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="contact-field">
                <label className="contact-label">Email</label>
                <input
                  type="email"
                  name="email"
                  placeholder="Your Email"
                  className="contact-input"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="contact-field">
                <label className="contact-label">Message</label>
                <textarea
                  name="message"
                  placeholder="Your Message"
                  className="contact-textarea"
                  style={{ width: "93.5%", padding: "10px" }}
                  value={formData.message}
                  onChange={handleChange}
                  required
                ></textarea>
              </div>
              <button
                type="submit"
                className="contact-submit"
                disabled={loading}
              >
                {loading ? "Sending..." : "Send Message"}
              </button>
              {responseMessage && (
                <p className="response-message">{responseMessage}</p>
              )}
            </form>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
