{"ast": null, "code": "import React, { useEffect, useState } from 'react';\nimport { generateColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorRgbInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorRgbInputPrefixCls = `${prefixCls}-rgb-input`;\n  const [rgbValue, setRgbValue] = useState(generateColor(value || '#000'));\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setRgbValue(value);\n    }\n  }, [value]);\n  const handleRgbChange = (step, type) => {\n    const rgb = rgbValue.toRgb();\n    rgb[type] = step || 0;\n    const genColor = generateColor(rgb);\n    if (!value) {\n      setRgbValue(genColor);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorRgbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().r),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'r')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().g),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'g')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().b),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'b')\n  }));\n};\nexport default ColorRgbInput;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "generateColor", "ColorSteppers", "ColorRgbInput", "_ref", "prefixCls", "value", "onChange", "colorRgbInputPrefixCls", "rgbValue", "setRgbValue", "handleRgbChange", "step", "type", "rgb", "toRgb", "genColor", "createElement", "className", "max", "min", "Number", "r", "g", "b"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/color-picker/components/ColorRgbInput.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { generateColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorRgbInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorRgbInputPrefixCls = `${prefixCls}-rgb-input`;\n  const [rgbValue, setRgbValue] = useState(generateColor(value || '#000'));\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setRgbValue(value);\n    }\n  }, [value]);\n  const handleRgbChange = (step, type) => {\n    const rgb = rgbValue.toRgb();\n    rgb[type] = step || 0;\n    const genColor = generateColor(rgb);\n    if (!value) {\n      setRgbValue(genColor);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorRgbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().r),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'r')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().g),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'g')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().b),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'b')\n  }));\n};\nexport default ColorRgbInput;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,SAAS;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,aAAa,GAAGC,IAAI,IAAI;EAC5B,IAAI;IACFC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAGH,IAAI;EACR,MAAMI,sBAAsB,GAAI,GAAEH,SAAU,YAAW;EACvD,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAACC,aAAa,CAACK,KAAK,IAAI,MAAM,CAAC,CAAC;EACxE;EACAP,SAAS,CAAC,MAAM;IACd,IAAIO,KAAK,EAAE;MACTI,WAAW,CAACJ,KAAK,CAAC;IACpB;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,MAAMK,eAAe,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IACtC,MAAMC,GAAG,GAAGL,QAAQ,CAACM,KAAK,CAAC,CAAC;IAC5BD,GAAG,CAACD,IAAI,CAAC,GAAGD,IAAI,IAAI,CAAC;IACrB,MAAMI,QAAQ,GAAGf,aAAa,CAACa,GAAG,CAAC;IACnC,IAAI,CAACR,KAAK,EAAE;MACVI,WAAW,CAACM,QAAQ,CAAC;IACvB;IACAT,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACS,QAAQ,CAAC;EACxE,CAAC;EACD,OAAO,aAAalB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEV;EACb,CAAC,EAAE,aAAaV,KAAK,CAACmB,aAAa,CAACf,aAAa,EAAE;IACjDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNd,KAAK,EAAEe,MAAM,CAACZ,QAAQ,CAACM,KAAK,CAAC,CAAC,CAACO,CAAC,CAAC;IACjCjB,SAAS,EAAEA,SAAS;IACpBa,SAAS,EAAEV,sBAAsB;IACjCD,QAAQ,EAAEK,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAad,KAAK,CAACmB,aAAa,CAACf,aAAa,EAAE;IAClDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNd,KAAK,EAAEe,MAAM,CAACZ,QAAQ,CAACM,KAAK,CAAC,CAAC,CAACQ,CAAC,CAAC;IACjClB,SAAS,EAAEA,SAAS;IACpBa,SAAS,EAAEV,sBAAsB;IACjCD,QAAQ,EAAEK,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,EAAE,aAAad,KAAK,CAACmB,aAAa,CAACf,aAAa,EAAE;IAClDiB,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,CAAC;IACNd,KAAK,EAAEe,MAAM,CAACZ,QAAQ,CAACM,KAAK,CAAC,CAAC,CAACS,CAAC,CAAC;IACjCnB,SAAS,EAAEA,SAAS;IACpBa,SAAS,EAAEV,sBAAsB;IACjCD,QAAQ,EAAEK,IAAI,IAAID,eAAe,CAACU,MAAM,CAACT,IAAI,CAAC,EAAE,GAAG;EACrD,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeT,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}