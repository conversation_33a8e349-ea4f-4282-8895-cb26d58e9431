{"ast": null, "code": "function useProdHMR() {\n  return false;\n}\nvar webpackHMR = false;\nfunction useDevHMR() {\n  return webpackHMR;\n}\nexport default process.env.NODE_ENV === 'production' ? useProdHMR : useDevHMR;\n\n// Webpack `module.hot.accept` do not support any deps update trigger\n// We have to hack handler to force mark as HRM\nif (process.env.NODE_ENV !== 'production' && typeof module !== 'undefined' && module && module.hot && typeof window !== 'undefined') {\n  var win = window;\n  if (typeof win.webpackHotUpdate === 'function') {\n    var originWebpackHotUpdate = win.webpackHotUpdate;\n    win.webpackHotUpdate = function () {\n      webpackHMR = true;\n      setTimeout(function () {\n        webpackHMR = false;\n      }, 0);\n      return originWebpackHotUpdate.apply(void 0, arguments);\n    };\n  }\n}", "map": {"version": 3, "names": ["useProdHMR", "webpackHMR", "useDevHMR", "process", "env", "NODE_ENV", "module", "hot", "window", "win", "webpackHotUpdate", "originWebpackHotUpdate", "setTimeout", "apply", "arguments"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ant-design/cssinjs/es/hooks/useHMR.js"], "sourcesContent": ["function useProdHMR() {\n  return false;\n}\nvar webpackHMR = false;\nfunction useDevHMR() {\n  return webpackHMR;\n}\nexport default process.env.NODE_ENV === 'production' ? useProdHMR : useDevHMR;\n\n// Webpack `module.hot.accept` do not support any deps update trigger\n// We have to hack handler to force mark as HRM\nif (process.env.NODE_ENV !== 'production' && typeof module !== 'undefined' && module && module.hot && typeof window !== 'undefined') {\n  var win = window;\n  if (typeof win.webpackHotUpdate === 'function') {\n    var originWebpackHotUpdate = win.webpackHotUpdate;\n    win.webpackHotUpdate = function () {\n      webpackHMR = true;\n      setTimeout(function () {\n        webpackHMR = false;\n      }, 0);\n      return originWebpackHotUpdate.apply(void 0, arguments);\n    };\n  }\n}"], "mappings": "AAAA,SAASA,UAAUA,CAAA,EAAG;EACpB,OAAO,KAAK;AACd;AACA,IAAIC,UAAU,GAAG,KAAK;AACtB,SAASC,SAASA,CAAA,EAAG;EACnB,OAAOD,UAAU;AACnB;AACA,eAAeE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGL,UAAU,GAAGE,SAAS;;AAE7E;AACA;AACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAIA,MAAM,CAACC,GAAG,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACnI,IAAIC,GAAG,GAAGD,MAAM;EAChB,IAAI,OAAOC,GAAG,CAACC,gBAAgB,KAAK,UAAU,EAAE;IAC9C,IAAIC,sBAAsB,GAAGF,GAAG,CAACC,gBAAgB;IACjDD,GAAG,CAACC,gBAAgB,GAAG,YAAY;MACjCT,UAAU,GAAG,IAAI;MACjBW,UAAU,CAAC,YAAY;QACrBX,UAAU,GAAG,KAAK;MACpB,CAAC,EAAE,CAAC,CAAC;MACL,OAAOU,sBAAsB,CAACE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;IACxD,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}