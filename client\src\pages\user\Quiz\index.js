import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { motion } from "framer-motion";
import { message } from "antd";
import { getAllExams } from "../../../apicalls/exams";
import { getAllReportsByUser } from "../../../apicalls/reports";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import PageTitle from "../../../components/PageTitle";
import { useNavigate } from "react-router-dom";
import Select from "react-select";
import { QuizGrid, Card, Button, Input, Loading } from "../../../components/modern";
import { TbSearch, TbFilter, TbTrophy, TbClock, TbUsers, TbQuestionMark, TbBrain } from "react-icons/tb";
import { BsBookFill } from "react-icons/bs";
import "./style.css";


const primaryClasses = [
  { value: "", label: "All Classes" },
  { value: "1", label: "Class 1" },
  { value: "2", label: "Class 2" },
  { value: "3", label: "Class 3" },
  { value: "4", label: "Class 4" },
  { value: "5", label: "Class 5" },
  { value: "6", label: "Class 6" },
  { value: "7", label: "Class 7" },
];

const secondaryClasses = [
  { value: "", label: "All Classes" },
  { value: "Form-1", label: "Form 1" },
  { value: "Form-2", label: "Form 2" },
  { value: "Form-3", label: "Form 3" },
  { value: "Form-4", label: "Form 4" },
];

const advanceClasses = [
  { value: "", label: "All Classes" },
  { value: "Form-5", label: "Form 5" },
  { value: "Form-6", label: "Form 6" },
];

function Quiz() {
  const [exams, setExams] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [reportsData, setReportsData] = useState([]);
  const [selectedClass, setSelectedClass] = useState(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);
  const [lgSize, setLgSize] = useState(8);

  const availableClasses =
    user?.level?.toLowerCase() === "primary"
      ? primaryClasses
      : user?.level?.toLowerCase() === "secondary"
        ? secondaryClasses
        : advanceClasses;

  useEffect(() => {
    if (user && user.class) {
      const defaultSelectedClass = availableClasses.find(
        (option) => option.value === user.class
      );
      setSelectedClass(defaultSelectedClass);
    }
  }, [user, availableClasses]);

  useEffect(() => {
    const updateLgSize = () => {
      setLgSize(window.innerWidth < 1380 ? 9 : 7);
    };

    // Set initial lg size
    updateLgSize();

    // Add event listener for window resize
    window.addEventListener("resize", updateLgSize);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener("resize", updateLgSize);
    };
  }, []);

  const handleClassChange = (selectedOption) => {
    setSelectedClass(selectedOption);
  };

  const filteredExams = exams.filter(
    (exam) => {
      // Handle class filtering with format compatibility
      let classMatches = true;
      if (selectedClass && selectedClass.value !== "") {
        const selectedValue = selectedClass.value;
        const examClass = exam.class;

        // Check for exact match first
        if (examClass === selectedValue) {
          classMatches = true;
        }
        // Check if exam class has "Class-" prefix and selected value is just the number
        else if (examClass === `Class-${selectedValue}`) {
          classMatches = true;
        }
        // Check if selected value has "Class-" prefix and exam class is just the number
        else if (selectedValue === `Class-${examClass}`) {
          classMatches = true;
        }
        // Check for Form classes (secondary)
        else if (examClass === `Form-${selectedValue.replace('Form-', '')}`) {
          classMatches = true;
        }
        else if (selectedValue === `Form-${examClass.replace('Form-', '')}`) {
          classMatches = true;
        }
        else {
          classMatches = false;
        }
      }

      // Handle search filtering
      const searchMatches = !searchQuery.trim() ||
        exam.name?.toLowerCase().includes(searchQuery.toLowerCase().trim()) ||
        exam.category?.toLowerCase().includes(searchQuery.toLowerCase().trim()) ||
        exam.class?.toLowerCase().includes(searchQuery.toLowerCase().trim());

      return classMatches && searchMatches;
    }
  );

  // Debug logging
  if (exams.length > 0) {
    console.log(`📊 Quiz Debug: ${filteredExams.length}/${exams.length} exams shown | Class: ${selectedClass?.label || 'None'} | Search: "${searchQuery}"`);
  }

  const getExams = async () => {
    try {
      console.log("🔍 Starting to fetch exams...");
      dispatch(ShowLoading());
      const response = await getAllExams();
      console.log("📡 API Response:", response);
      if (response.success) {
        console.log("✅ Exams fetched successfully:", response.data.length, "exams");
        setExams(response.data.reverse());
      } else {
        console.error("❌ API Error:", response.message);
        message.error(response.message);
      }
      dispatch(HideLoading());
    } catch (error) {
      console.error("❌ Network Error:", error);
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  const filterReportsData = (data) => {
    const reportsMap = {};

    // Iterate over the response data (reports)
    data.forEach(report => {
      const examId = report.exam._id;
      const verdict = report.result.verdict;

      // If the examId is not already in the map, add it
      if (!reportsMap[examId]) {
        reportsMap[examId] = report;
      } else {
        // If there is already an entry for this exam, keep the one with "pass" verdict, or just keep the first one if no "pass"
        if (verdict === "Pass" && reportsMap[examId].result.verdict !== "Pass") {
          reportsMap[examId] = report; // Replace with the "pass" verdict report
        }
      }
    });

    return Object.values(reportsMap);
  };

  const getData = async () => {
    try {
      dispatch(ShowLoading());
      const response = await getAllReportsByUser();
      if (response.success) {

        setReportsData(filterReportsData(response.data));
      } else {
        message.error(response.message);
      }
      dispatch(HideLoading());
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  useEffect(() => {
    getData();
    getExams();
  }, []);

  const verifyRetake = async (exam) => {
    try {
      dispatch(ShowLoading());
      const response = await getAllReportsByUser();
      const retakeCount = response.data.filter(
        (item) => item.exam && item.exam._id === exam._id
      ).length;
      console.log("Retake count for exam:", retakeCount);
    } catch (error) {
      message.error("Unable to verify retake");
      dispatch(HideLoading());
      return;
    }
    dispatch(HideLoading());
    navigate(`/user/write-exam/${exam._id}`);
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const shouldRenderFilteredExams = filteredExams.length < exams.length;

  return (
    user && (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6">
        <div className="container-modern">
          {/* Modern Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="text-center mb-6">
              <h1 className="heading-2 text-gradient mb-4">
                <TbBrain className="inline w-10 h-10 mr-3" />
                Challenge Your Brain, Beat the Rest
              </h1>
              <p className="text-xl text-gray-600">
                Test your knowledge and track your progress with our comprehensive quiz system
              </p>
            </div>

            {/* User Info Card */}
            <Card className="p-6 mb-6 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-bold text-lg">
                      {user?.name?.charAt(0)?.toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Welcome back, {user?.name}!</h3>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span>Current Class:</span>
                      <span className="badge-primary">
                        {user?.level === "Primary"
                          ? `Class ${user?.class}`
                          : user?.level === "Secondary"
                          ? `Form ${user?.class?.replace('Form-', '')}`
                          : user?.level === "Advance"
                          ? `Form ${user?.class?.replace('Form-', '')}`
                          : user?.class || 'Not Set'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="hidden md:flex items-center space-x-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary-600">{exams.length}</div>
                    <div className="text-xs text-gray-500">Available Quizzes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-success-600">{reportsData.filter(r => r.result?.verdict === "Pass").length}</div>
                    <div className="text-xs text-gray-500">Passed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-warning-600">{reportsData.length}</div>
                    <div className="text-xs text-gray-500">Total Attempts</div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>


          {/* Modern Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-8"
          >
            <Card className="p-6">
              <div className="flex flex-col lg:flex-row gap-6 items-end">
                {/* Search */}
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Search Quizzes
                  </label>
                  <Input
                    placeholder="Search by quiz title..."
                    value={searchQuery}
                    onChange={(e) => handleSearch(e)}
                    icon={<TbSearch />}
                    className="w-full"
                  />
                </div>

                {/* Class Filter */}
                <div className="w-full lg:w-80">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Filter by Class
                  </label>
                  <Select
                    options={availableClasses}
                    value={selectedClass}
                    onChange={handleClassChange}
                    placeholder="Select Class"
                    className="w-full"
                    isSearchable={false}
                    styles={{
                      control: (base) => ({
                        ...base,
                        minHeight: '48px',
                        borderColor: '#e5e7eb',
                        '&:hover': { borderColor: '#3b82f6' },
                      }),
                    }}
                  />
                </div>

                {/* Filter Button */}
                <Button
                  variant="secondary"
                  icon={<TbFilter />}
                  className="lg:w-auto w-full"
                >
                  Filter
                </Button>
              </div>

              {/* Results Count */}
              {shouldRenderFilteredExams && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <span className="text-sm text-gray-600">
                    Showing {filteredExams.length} of {exams.length} quizzes
                  </span>
                </div>
              )}
            </Card>
          </motion.div>

          {/* Modern Quiz Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            {filteredExams.length > 0 ? (
              <QuizGrid
                quizzes={filteredExams.map(exam => {
                  const examReport = reportsData.find(
                    (report) => report.exam && report.exam._id === exam._id
                  );

                  return {
                    ...exam,
                    subject: exam.category,
                    questions: exam.questions || [],
                    duration: exam.duration,
                    difficulty: exam.difficulty || 'Medium',
                    attempts: reportsData.filter(r => r.exam?._id === exam._id).length,
                    userResult: examReport ? {
                      percentage: examReport.result?.percentage || 0,
                      correctAnswers: examReport.result?.correctAnswers || 0,
                      totalQuestions: examReport.result?.totalQuestions || exam.questions?.length || 0,
                      verdict: examReport.result?.verdict,
                      completedAt: examReport.createdAt
                    } : null
                  };
                })}
                onQuizStart={(quiz) => verifyRetake(quiz)}
                onQuizView={(quiz) => {
                  const examReport = reportsData.find(
                    (report) => report.exam && report.exam._id === quiz._id
                  );
                  if (examReport) {
                    navigate(`/quiz/${quiz._id}/result`, {
                      state: { result: examReport.result, examData: quiz }
                    });
                  }
                }}
                showResults={true}
                userResults={reportsData.reduce((acc, report) => {
                  if (report.exam?._id) {
                    acc[report.exam._id] = {
                      percentage: report.result?.percentage || 0,
                      correctAnswers: report.result?.correctAnswers || 0,
                      totalQuestions: report.result?.totalQuestions || 0,
                      verdict: report.result?.verdict,
                      completedAt: report.createdAt
                    };
                  }
                  return acc;
                }, {})}
              />
            ) : (
              <Card className="p-12 text-center">
                <TbQuestionMark className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Quizzes Found</h3>
                <p className="text-gray-600 mb-6">
                  {searchQuery || selectedClass?.value
                    ? "Try adjusting your search or filter criteria"
                    : "No quizzes are available for your current class level"}
                </p>
                {(searchQuery || selectedClass?.value) && (
                  <Button
                    variant="secondary"
                    onClick={() => {
                      setSearchQuery("");
                      setSelectedClass({ value: "", label: "All Classes" });
                    }}
                  >
                    Clear Filters
                  </Button>
                )}
              </Card>
            )}
          </motion.div>
        </div>
      </div>
    )
  );
}

export default Quiz;
