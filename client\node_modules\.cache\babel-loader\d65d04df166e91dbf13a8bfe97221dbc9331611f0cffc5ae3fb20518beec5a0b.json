{"ast": null, "code": "import * as React from 'react';\n\n/**\n * Cache callback function that always return same ref instead.\n * This is used for context optimization.\n */\nexport default function useMemoCallback(func) {\n  var funRef = React.useRef(func);\n  funRef.current = func;\n  var callback = React.useCallback(function () {\n    var _funRef$current;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return (_funRef$current = funRef.current) === null || _funRef$current === void 0 ? void 0 : _funRef$current.call.apply(_funRef$current, [funRef].concat(args));\n  }, []);\n  return func ? callback : undefined;\n}", "map": {"version": 3, "names": ["React", "useMemoCallback", "func", "funRef", "useRef", "current", "callback", "useCallback", "_funRef$current", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "undefined"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-menu/es/hooks/useMemoCallback.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Cache callback function that always return same ref instead.\n * This is used for context optimization.\n */\nexport default function useMemoCallback(func) {\n  var funRef = React.useRef(func);\n  funRef.current = func;\n  var callback = React.useCallback(function () {\n    var _funRef$current;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return (_funRef$current = funRef.current) === null || _funRef$current === void 0 ? void 0 : _funRef$current.call.apply(_funRef$current, [funRef].concat(args));\n  }, []);\n  return func ? callback : undefined;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC5C,IAAIC,MAAM,GAAGH,KAAK,CAACI,MAAM,CAACF,IAAI,CAAC;EAC/BC,MAAM,CAACE,OAAO,GAAGH,IAAI;EACrB,IAAII,QAAQ,GAAGN,KAAK,CAACO,WAAW,CAAC,YAAY;IAC3C,IAAIC,eAAe;IACnB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACA,OAAO,CAACN,eAAe,GAAGL,MAAM,CAACE,OAAO,MAAM,IAAI,IAAIG,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACO,IAAI,CAACC,KAAK,CAACR,eAAe,EAAE,CAACL,MAAM,CAAC,CAACc,MAAM,CAACL,IAAI,CAAC,CAAC;EAChK,CAAC,EAAE,EAAE,CAAC;EACN,OAAOV,IAAI,GAAGI,QAAQ,GAAGY,SAAS;AACpC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}