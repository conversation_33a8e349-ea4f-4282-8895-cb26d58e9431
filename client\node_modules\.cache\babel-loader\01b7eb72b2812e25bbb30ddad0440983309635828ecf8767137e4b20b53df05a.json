{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resetState = resetState;\nexports.log = log;\nexports.handleBlur = handleBlur;\nexports.handleFocus = handleFocus;\nexports.markForFocusLater = markForFocusLater;\nexports.returnFocus = returnFocus;\nexports.popWithoutFocus = popWithoutFocus;\nexports.setupScopedFocus = setupScopedFocus;\nexports.teardownScopedFocus = teardownScopedFocus;\nvar _tabbable = require(\"../helpers/tabbable\");\nvar _tabbable2 = _interopRequireDefault(_tabbable);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar focusLaterElements = [];\nvar modalElement = null;\nvar needToFocus = false;\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction resetState() {\n  focusLaterElements = [];\n}\n\n/* istanbul ignore next */\nfunction log() {\n  if (process.env.NODE_ENV !== \"production\") {\n    console.log(\"focusManager ----------\");\n    focusLaterElements.forEach(function (f) {\n      var check = f || {};\n      console.log(check.nodeName, check.className, check.id);\n    });\n    console.log(\"end focusManager ----------\");\n  }\n}\n/* eslint-enable no-console */\n\nfunction handleBlur() {\n  needToFocus = true;\n}\nfunction handleFocus() {\n  if (needToFocus) {\n    needToFocus = false;\n    if (!modalElement) {\n      return;\n    }\n    // need to see how jQuery shims document.on('focusin') so we don't need the\n    // setTimeout, firefox doesn't support focusin, if it did, we could focus\n    // the element outside of a setTimeout. Side-effect of this implementation\n    // is that the document.body gets focus, and then we focus our element right\n    // after, seems fine.\n    setTimeout(function () {\n      if (modalElement.contains(document.activeElement)) {\n        return;\n      }\n      var el = (0, _tabbable2.default)(modalElement)[0] || modalElement;\n      el.focus();\n    }, 0);\n  }\n}\nfunction markForFocusLater() {\n  focusLaterElements.push(document.activeElement);\n}\n\n/* eslint-disable no-console */\nfunction returnFocus() {\n  var preventScroll = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  var toFocus = null;\n  try {\n    if (focusLaterElements.length !== 0) {\n      toFocus = focusLaterElements.pop();\n      toFocus.focus({\n        preventScroll: preventScroll\n      });\n    }\n    return;\n  } catch (e) {\n    console.warn([\"You tried to return focus to\", toFocus, \"but it is not in the DOM anymore\"].join(\" \"));\n  }\n}\n/* eslint-enable no-console */\n\nfunction popWithoutFocus() {\n  focusLaterElements.length > 0 && focusLaterElements.pop();\n}\nfunction setupScopedFocus(element) {\n  modalElement = element;\n  if (window.addEventListener) {\n    window.addEventListener(\"blur\", handleBlur, false);\n    document.addEventListener(\"focus\", handleFocus, true);\n  } else {\n    window.attachEvent(\"onBlur\", handleBlur);\n    document.attachEvent(\"onFocus\", handleFocus);\n  }\n}\nfunction teardownScopedFocus() {\n  modalElement = null;\n  if (window.addEventListener) {\n    window.removeEventListener(\"blur\", handleBlur);\n    document.removeEventListener(\"focus\", handleFocus);\n  } else {\n    window.detachEvent(\"onBlur\", handleBlur);\n    document.detachEvent(\"onFocus\", handleFocus);\n  }\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "resetState", "log", "handleBlur", "handleFocus", "markForFocusLater", "returnFocus", "popWithoutFocus", "setupScopedFocus", "teardownScopedFocus", "_tabbable", "require", "_tabbable2", "_interopRequireDefault", "obj", "__esModule", "default", "focusLaterElements", "modalElement", "needToFocus", "process", "env", "NODE_ENV", "console", "for<PERSON>ach", "f", "check", "nodeName", "className", "id", "setTimeout", "contains", "document", "activeElement", "el", "focus", "push", "preventScroll", "arguments", "length", "undefined", "toFocus", "pop", "e", "warn", "join", "element", "window", "addEventListener", "attachEvent", "removeEventListener", "detachEvent"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/react-modal/lib/helpers/focusManager.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resetState = resetState;\nexports.log = log;\nexports.handleBlur = handleBlur;\nexports.handleFocus = handleFocus;\nexports.markForFocusLater = markForFocusLater;\nexports.returnFocus = returnFocus;\nexports.popWithoutFocus = popWithoutFocus;\nexports.setupScopedFocus = setupScopedFocus;\nexports.teardownScopedFocus = teardownScopedFocus;\n\nvar _tabbable = require(\"../helpers/tabbable\");\n\nvar _tabbable2 = _interopRequireDefault(_tabbable);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar focusLaterElements = [];\nvar modalElement = null;\nvar needToFocus = false;\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction resetState() {\n  focusLaterElements = [];\n}\n\n/* istanbul ignore next */\nfunction log() {\n  if (process.env.NODE_ENV !== \"production\") {\n    console.log(\"focusManager ----------\");\n    focusLaterElements.forEach(function (f) {\n      var check = f || {};\n      console.log(check.nodeName, check.className, check.id);\n    });\n    console.log(\"end focusManager ----------\");\n  }\n}\n/* eslint-enable no-console */\n\nfunction handleBlur() {\n  needToFocus = true;\n}\n\nfunction handleFocus() {\n  if (needToFocus) {\n    needToFocus = false;\n    if (!modalElement) {\n      return;\n    }\n    // need to see how jQuery shims document.on('focusin') so we don't need the\n    // setTimeout, firefox doesn't support focusin, if it did, we could focus\n    // the element outside of a setTimeout. Side-effect of this implementation\n    // is that the document.body gets focus, and then we focus our element right\n    // after, seems fine.\n    setTimeout(function () {\n      if (modalElement.contains(document.activeElement)) {\n        return;\n      }\n      var el = (0, _tabbable2.default)(modalElement)[0] || modalElement;\n      el.focus();\n    }, 0);\n  }\n}\n\nfunction markForFocusLater() {\n  focusLaterElements.push(document.activeElement);\n}\n\n/* eslint-disable no-console */\nfunction returnFocus() {\n  var preventScroll = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n\n  var toFocus = null;\n  try {\n    if (focusLaterElements.length !== 0) {\n      toFocus = focusLaterElements.pop();\n      toFocus.focus({ preventScroll: preventScroll });\n    }\n    return;\n  } catch (e) {\n    console.warn([\"You tried to return focus to\", toFocus, \"but it is not in the DOM anymore\"].join(\" \"));\n  }\n}\n/* eslint-enable no-console */\n\nfunction popWithoutFocus() {\n  focusLaterElements.length > 0 && focusLaterElements.pop();\n}\n\nfunction setupScopedFocus(element) {\n  modalElement = element;\n\n  if (window.addEventListener) {\n    window.addEventListener(\"blur\", handleBlur, false);\n    document.addEventListener(\"focus\", handleFocus, true);\n  } else {\n    window.attachEvent(\"onBlur\", handleBlur);\n    document.attachEvent(\"onFocus\", handleFocus);\n  }\n}\n\nfunction teardownScopedFocus() {\n  modalElement = null;\n\n  if (window.addEventListener) {\n    window.removeEventListener(\"blur\", handleBlur);\n    document.removeEventListener(\"focus\", handleFocus);\n  } else {\n    window.detachEvent(\"onBlur\", handleBlur);\n    document.detachEvent(\"onFocus\", handleFocus);\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/BF,OAAO,CAACG,GAAG,GAAGA,GAAG;AACjBH,OAAO,CAACI,UAAU,GAAGA,UAAU;AAC/BJ,OAAO,CAACK,WAAW,GAAGA,WAAW;AACjCL,OAAO,CAACM,iBAAiB,GAAGA,iBAAiB;AAC7CN,OAAO,CAACO,WAAW,GAAGA,WAAW;AACjCP,OAAO,CAACQ,eAAe,GAAGA,eAAe;AACzCR,OAAO,CAACS,gBAAgB,GAAGA,gBAAgB;AAC3CT,OAAO,CAACU,mBAAmB,GAAGA,mBAAmB;AAEjD,IAAIC,SAAS,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAE9C,IAAIC,UAAU,GAAGC,sBAAsB,CAACH,SAAS,CAAC;AAElD,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,IAAIG,kBAAkB,GAAG,EAAE;AAC3B,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,WAAW,GAAG,KAAK;;AAEvB;AACA;AACA,SAASlB,UAAUA,CAAA,EAAG;EACpBgB,kBAAkB,GAAG,EAAE;AACzB;;AAEA;AACA,SAASf,GAAGA,CAAA,EAAG;EACb,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCC,OAAO,CAACrB,GAAG,CAAC,yBAAyB,CAAC;IACtCe,kBAAkB,CAACO,OAAO,CAAC,UAAUC,CAAC,EAAE;MACtC,IAAIC,KAAK,GAAGD,CAAC,IAAI,CAAC,CAAC;MACnBF,OAAO,CAACrB,GAAG,CAACwB,KAAK,CAACC,QAAQ,EAAED,KAAK,CAACE,SAAS,EAAEF,KAAK,CAACG,EAAE,CAAC;IACxD,CAAC,CAAC;IACFN,OAAO,CAACrB,GAAG,CAAC,6BAA6B,CAAC;EAC5C;AACF;AACA;;AAEA,SAASC,UAAUA,CAAA,EAAG;EACpBgB,WAAW,GAAG,IAAI;AACpB;AAEA,SAASf,WAAWA,CAAA,EAAG;EACrB,IAAIe,WAAW,EAAE;IACfA,WAAW,GAAG,KAAK;IACnB,IAAI,CAACD,YAAY,EAAE;MACjB;IACF;IACA;IACA;IACA;IACA;IACA;IACAY,UAAU,CAAC,YAAY;MACrB,IAAIZ,YAAY,CAACa,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC,EAAE;QACjD;MACF;MACA,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEtB,UAAU,CAACI,OAAO,EAAEE,YAAY,CAAC,CAAC,CAAC,CAAC,IAAIA,YAAY;MACjEgB,EAAE,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,EAAE,CAAC,CAAC;EACP;AACF;AAEA,SAAS9B,iBAAiBA,CAAA,EAAG;EAC3BY,kBAAkB,CAACmB,IAAI,CAACJ,QAAQ,CAACC,aAAa,CAAC;AACjD;;AAEA;AACA,SAAS3B,WAAWA,CAAA,EAAG;EACrB,IAAI+B,aAAa,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAE7F,IAAIG,OAAO,GAAG,IAAI;EAClB,IAAI;IACF,IAAIxB,kBAAkB,CAACsB,MAAM,KAAK,CAAC,EAAE;MACnCE,OAAO,GAAGxB,kBAAkB,CAACyB,GAAG,CAAC,CAAC;MAClCD,OAAO,CAACN,KAAK,CAAC;QAAEE,aAAa,EAAEA;MAAc,CAAC,CAAC;IACjD;IACA;EACF,CAAC,CAAC,OAAOM,CAAC,EAAE;IACVpB,OAAO,CAACqB,IAAI,CAAC,CAAC,8BAA8B,EAAEH,OAAO,EAAE,kCAAkC,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC,CAAC;EACvG;AACF;AACA;;AAEA,SAAStC,eAAeA,CAAA,EAAG;EACzBU,kBAAkB,CAACsB,MAAM,GAAG,CAAC,IAAItB,kBAAkB,CAACyB,GAAG,CAAC,CAAC;AAC3D;AAEA,SAASlC,gBAAgBA,CAACsC,OAAO,EAAE;EACjC5B,YAAY,GAAG4B,OAAO;EAEtB,IAAIC,MAAM,CAACC,gBAAgB,EAAE;IAC3BD,MAAM,CAACC,gBAAgB,CAAC,MAAM,EAAE7C,UAAU,EAAE,KAAK,CAAC;IAClD6B,QAAQ,CAACgB,gBAAgB,CAAC,OAAO,EAAE5C,WAAW,EAAE,IAAI,CAAC;EACvD,CAAC,MAAM;IACL2C,MAAM,CAACE,WAAW,CAAC,QAAQ,EAAE9C,UAAU,CAAC;IACxC6B,QAAQ,CAACiB,WAAW,CAAC,SAAS,EAAE7C,WAAW,CAAC;EAC9C;AACF;AAEA,SAASK,mBAAmBA,CAAA,EAAG;EAC7BS,YAAY,GAAG,IAAI;EAEnB,IAAI6B,MAAM,CAACC,gBAAgB,EAAE;IAC3BD,MAAM,CAACG,mBAAmB,CAAC,MAAM,EAAE/C,UAAU,CAAC;IAC9C6B,QAAQ,CAACkB,mBAAmB,CAAC,OAAO,EAAE9C,WAAW,CAAC;EACpD,CAAC,MAAM;IACL2C,MAAM,CAACI,WAAW,CAAC,QAAQ,EAAEhD,UAAU,CAAC;IACxC6B,QAAQ,CAACmB,WAAW,CAAC,SAAS,EAAE/C,WAAW,CAAC;EAC9C;AACF"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}