{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nexport function legacyPropsWarning(props) {\n  var picker = props.picker,\n    disabledHours = props.disabledHours,\n    disabledMinutes = props.disabledMinutes,\n    disabledSeconds = props.disabledSeconds;\n  if (picker === 'time' && (disabledHours || disabledMinutes || disabledSeconds)) {\n    warning(false, \"'disabledHours', 'disabledMinutes', 'disabledSeconds' will be removed in the next major version, please use 'disabledTime' instead.\");\n  }\n}", "map": {"version": 3, "names": ["warning", "legacyPropsWarning", "props", "picker", "disabledHours", "disabledMinutes", "disabledSeconds"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/utils/warnUtil.js"], "sourcesContent": ["import warning from \"rc-util/es/warning\";\nexport function legacyPropsWarning(props) {\n  var picker = props.picker,\n    disabledHours = props.disabledHours,\n    disabledMinutes = props.disabledMinutes,\n    disabledSeconds = props.disabledSeconds;\n  if (picker === 'time' && (disabledHours || disabledMinutes || disabledSeconds)) {\n    warning(false, \"'disabledHours', 'disabledMinutes', 'disabledSeconds' will be removed in the next major version, please use 'disabledTime' instead.\");\n  }\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,OAAO,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACxC,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACvBC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,eAAe,GAAGH,KAAK,CAACG,eAAe;IACvCC,eAAe,GAAGJ,KAAK,CAACI,eAAe;EACzC,IAAIH,MAAM,KAAK,MAAM,KAAKC,aAAa,IAAIC,eAAe,IAAIC,eAAe,CAAC,EAAE;IAC9EN,OAAO,CAAC,KAAK,EAAE,qIAAqI,CAAC;EACvJ;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}