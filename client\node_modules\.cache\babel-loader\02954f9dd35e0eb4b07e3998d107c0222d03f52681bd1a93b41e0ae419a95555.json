{"ast": null, "code": "// ========================= Placeholder ==========================\nconst genEmptyStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-tbody > tr${componentCls}-placeholder`]: {\n        textAlign: 'center',\n        color: token.colorTextDisabled,\n        [`\n          &:hover > th,\n          &:hover > td,\n        `]: {\n          background: token.colorBgContainer\n        }\n      }\n    }\n  };\n};\nexport default genEmptyStyle;", "map": {"version": 3, "names": ["genEmptyStyle", "token", "componentCls", "textAlign", "color", "colorTextDisabled", "background", "colorBgContainer"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/empty.js"], "sourcesContent": ["// ========================= Placeholder ==========================\nconst genEmptyStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-tbody > tr${componentCls}-placeholder`]: {\n        textAlign: 'center',\n        color: token.colorTextDisabled,\n        [`\n          &:hover > th,\n          &:hover > td,\n        `]: {\n          background: token.colorBgContainer\n        }\n      }\n    }\n  };\n};\nexport default genEmptyStyle;"], "mappings": "AAAA;AACA,MAAMA,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAAE,GAAEC,YAAa,UAAS,GAAG;MAC3B,CAAE,GAAEA,YAAa,cAAaA,YAAa,cAAa,GAAG;QACzDC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAEH,KAAK,CAACI,iBAAiB;QAC9B,CAAE;AACV;AACA;AACA,SAAS,GAAG;UACFC,UAAU,EAAEL,KAAK,CAACM;QACpB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeP,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}