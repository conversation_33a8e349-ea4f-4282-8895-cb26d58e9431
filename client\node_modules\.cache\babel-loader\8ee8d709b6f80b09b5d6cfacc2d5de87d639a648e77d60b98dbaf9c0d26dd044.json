{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { YEAR_DECADE_COUNT } from \"./constant\";\nimport useCellClassName from \"../../hooks/useCellClassName\";\nimport { formatValue, isSameYear } from \"../../utils/dateUtil\";\nimport RangeContext from \"../../RangeContext\";\nimport PanelBody from \"../PanelBody\";\nexport var YEAR_COL_COUNT = 3;\nvar YEAR_ROW_COUNT = 4;\nfunction YearBody(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    viewDate = props.viewDate,\n    locale = props.locale,\n    generateConfig = props.generateConfig,\n    cellRender = props.cellRender;\n  var _React$useContext = React.useContext(RangeContext),\n    rangedValue = _React$useContext.rangedValue,\n    hoverRangedValue = _React$useContext.hoverRangedValue;\n  var yearPrefixCls = \"\".concat(prefixCls, \"-cell\");\n\n  // =============================== Year ===============================\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT;\n  var endYear = startYear + YEAR_DECADE_COUNT - 1;\n  var baseYear = generateConfig.setYear(viewDate, startYear - Math.ceil((YEAR_COL_COUNT * YEAR_ROW_COUNT - YEAR_DECADE_COUNT) / 2));\n  var today = generateConfig.getNow();\n  var isInView = function isInView(date) {\n    var currentYearNumber = generateConfig.getYear(date);\n    return startYear <= currentYearNumber && currentYearNumber <= endYear;\n  };\n  var getCellClassName = useCellClassName({\n    cellPrefixCls: yearPrefixCls,\n    value: value,\n    generateConfig: generateConfig,\n    rangedValue: rangedValue,\n    hoverRangedValue: hoverRangedValue,\n    isSameCell: function isSameCell(current, target) {\n      return isSameYear(generateConfig, current, target);\n    },\n    isInView: isInView,\n    offsetCell: function offsetCell(date, offset) {\n      return generateConfig.addYear(date, offset);\n    }\n  });\n  var getCellNode = cellRender ? function (date, wrapperNode) {\n    return cellRender(date, {\n      originNode: wrapperNode,\n      today: today,\n      type: 'year',\n      locale: locale\n    });\n  } : undefined;\n  return /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    rowNum: YEAR_ROW_COUNT,\n    colNum: YEAR_COL_COUNT,\n    baseDate: baseYear,\n    getCellNode: getCellNode,\n    getCellText: generateConfig.getYear,\n    getCellClassName: getCellClassName,\n    getCellDate: generateConfig.addYear,\n    titleCell: function titleCell(date) {\n      return formatValue(date, {\n        locale: locale,\n        format: 'YYYY',\n        generateConfig: generateConfig\n      });\n    }\n  }));\n}\nexport default YearBody;", "map": {"version": 3, "names": ["_extends", "React", "YEAR_DECADE_COUNT", "useCellClassName", "formatValue", "isSameYear", "RangeContext", "PanelBody", "YEAR_COL_COUNT", "YEAR_ROW_COUNT", "YearBody", "props", "prefixCls", "value", "viewDate", "locale", "generateConfig", "cellRender", "_React$useContext", "useContext", "rangedValue", "hoverRangedValue", "yearPrefixCls", "concat", "yearNumber", "getYear", "startYear", "Math", "floor", "endYear", "baseYear", "setYear", "ceil", "today", "getNow", "isInView", "date", "currentYearNumber", "getCellClassName", "cellPrefixCls", "isSameCell", "current", "target", "offsetCell", "offset", "addYear", "getCellNode", "wrapperNode", "originNode", "type", "undefined", "createElement", "row<PERSON>um", "colNum", "baseDate", "getCellText", "getCellDate", "title<PERSON>ell", "format"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/panels/YearPanel/YearBody.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { YEAR_DECADE_COUNT } from \"./constant\";\nimport useCellClassName from \"../../hooks/useCellClassName\";\nimport { formatValue, isSameYear } from \"../../utils/dateUtil\";\nimport RangeContext from \"../../RangeContext\";\nimport PanelBody from \"../PanelBody\";\nexport var YEAR_COL_COUNT = 3;\nvar YEAR_ROW_COUNT = 4;\nfunction YearBody(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    viewDate = props.viewDate,\n    locale = props.locale,\n    generateConfig = props.generateConfig,\n    cellRender = props.cellRender;\n  var _React$useContext = React.useContext(RangeContext),\n    rangedValue = _React$useContext.rangedValue,\n    hoverRangedValue = _React$useContext.hoverRangedValue;\n  var yearPrefixCls = \"\".concat(prefixCls, \"-cell\");\n\n  // =============================== Year ===============================\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT;\n  var endYear = startYear + YEAR_DECADE_COUNT - 1;\n  var baseYear = generateConfig.setYear(viewDate, startYear - Math.ceil((YEAR_COL_COUNT * YEAR_ROW_COUNT - YEAR_DECADE_COUNT) / 2));\n  var today = generateConfig.getNow();\n  var isInView = function isInView(date) {\n    var currentYearNumber = generateConfig.getYear(date);\n    return startYear <= currentYearNumber && currentYearNumber <= endYear;\n  };\n  var getCellClassName = useCellClassName({\n    cellPrefixCls: yearPrefixCls,\n    value: value,\n    generateConfig: generateConfig,\n    rangedValue: rangedValue,\n    hoverRangedValue: hoverRangedValue,\n    isSameCell: function isSameCell(current, target) {\n      return isSameYear(generateConfig, current, target);\n    },\n    isInView: isInView,\n    offsetCell: function offsetCell(date, offset) {\n      return generateConfig.addYear(date, offset);\n    }\n  });\n  var getCellNode = cellRender ? function (date, wrapperNode) {\n    return cellRender(date, {\n      originNode: wrapperNode,\n      today: today,\n      type: 'year',\n      locale: locale\n    });\n  } : undefined;\n  return /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    rowNum: YEAR_ROW_COUNT,\n    colNum: YEAR_COL_COUNT,\n    baseDate: baseYear,\n    getCellNode: getCellNode,\n    getCellText: generateConfig.getYear,\n    getCellClassName: getCellClassName,\n    getCellDate: generateConfig.addYear,\n    titleCell: function titleCell(date) {\n      return formatValue(date, {\n        locale: locale,\n        format: 'YYYY',\n        generateConfig: generateConfig\n      });\n    }\n  }));\n}\nexport default YearBody;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,YAAY;AAC9C,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAC9D,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAO,IAAIC,cAAc,GAAG,CAAC;AAC7B,IAAIC,cAAc,GAAG,CAAC;AACtB,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,UAAU,GAAGN,KAAK,CAACM,UAAU;EAC/B,IAAIC,iBAAiB,GAAGjB,KAAK,CAACkB,UAAU,CAACb,YAAY,CAAC;IACpDc,WAAW,GAAGF,iBAAiB,CAACE,WAAW;IAC3CC,gBAAgB,GAAGH,iBAAiB,CAACG,gBAAgB;EACvD,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACX,SAAS,EAAE,OAAO,CAAC;;EAEjD;EACA,IAAIY,UAAU,GAAGR,cAAc,CAACS,OAAO,CAACX,QAAQ,CAAC;EACjD,IAAIY,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,UAAU,GAAGtB,iBAAiB,CAAC,GAAGA,iBAAiB;EAC9E,IAAI2B,OAAO,GAAGH,SAAS,GAAGxB,iBAAiB,GAAG,CAAC;EAC/C,IAAI4B,QAAQ,GAAGd,cAAc,CAACe,OAAO,CAACjB,QAAQ,EAAEY,SAAS,GAAGC,IAAI,CAACK,IAAI,CAAC,CAACxB,cAAc,GAAGC,cAAc,GAAGP,iBAAiB,IAAI,CAAC,CAAC,CAAC;EACjI,IAAI+B,KAAK,GAAGjB,cAAc,CAACkB,MAAM,CAAC,CAAC;EACnC,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;IACrC,IAAIC,iBAAiB,GAAGrB,cAAc,CAACS,OAAO,CAACW,IAAI,CAAC;IACpD,OAAOV,SAAS,IAAIW,iBAAiB,IAAIA,iBAAiB,IAAIR,OAAO;EACvE,CAAC;EACD,IAAIS,gBAAgB,GAAGnC,gBAAgB,CAAC;IACtCoC,aAAa,EAAEjB,aAAa;IAC5BT,KAAK,EAAEA,KAAK;IACZG,cAAc,EAAEA,cAAc;IAC9BI,WAAW,EAAEA,WAAW;IACxBC,gBAAgB,EAAEA,gBAAgB;IAClCmB,UAAU,EAAE,SAASA,UAAUA,CAACC,OAAO,EAAEC,MAAM,EAAE;MAC/C,OAAOrC,UAAU,CAACW,cAAc,EAAEyB,OAAO,EAAEC,MAAM,CAAC;IACpD,CAAC;IACDP,QAAQ,EAAEA,QAAQ;IAClBQ,UAAU,EAAE,SAASA,UAAUA,CAACP,IAAI,EAAEQ,MAAM,EAAE;MAC5C,OAAO5B,cAAc,CAAC6B,OAAO,CAACT,IAAI,EAAEQ,MAAM,CAAC;IAC7C;EACF,CAAC,CAAC;EACF,IAAIE,WAAW,GAAG7B,UAAU,GAAG,UAAUmB,IAAI,EAAEW,WAAW,EAAE;IAC1D,OAAO9B,UAAU,CAACmB,IAAI,EAAE;MACtBY,UAAU,EAAED,WAAW;MACvBd,KAAK,EAAEA,KAAK;MACZgB,IAAI,EAAE,MAAM;MACZlC,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ,CAAC,GAAGmC,SAAS;EACb,OAAO,aAAajD,KAAK,CAACkD,aAAa,CAAC5C,SAAS,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;IACrEyC,MAAM,EAAE3C,cAAc;IACtB4C,MAAM,EAAE7C,cAAc;IACtB8C,QAAQ,EAAExB,QAAQ;IAClBgB,WAAW,EAAEA,WAAW;IACxBS,WAAW,EAAEvC,cAAc,CAACS,OAAO;IACnCa,gBAAgB,EAAEA,gBAAgB;IAClCkB,WAAW,EAAExC,cAAc,CAAC6B,OAAO;IACnCY,SAAS,EAAE,SAASA,SAASA,CAACrB,IAAI,EAAE;MAClC,OAAOhC,WAAW,CAACgC,IAAI,EAAE;QACvBrB,MAAM,EAAEA,MAAM;QACd2C,MAAM,EAAE,MAAM;QACd1C,cAAc,EAAEA;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;AACL;AACA,eAAeN,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}