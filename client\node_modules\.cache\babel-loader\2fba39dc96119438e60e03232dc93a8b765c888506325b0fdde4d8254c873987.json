{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar DrawerPanel = function DrawerPanel(props) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    containerRef = props.containerRef,\n    onMouseEnter = props.onMouseEnter,\n    onMouseOver = props.onMouseOver,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp;\n  var eventHandlers = {\n    onMouseEnter: onMouseEnter,\n    onMouseOver: onMouseOver,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp\n  };\n  // =============================== Render ===============================\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    style: _objectSpread({}, style),\n    \"aria-modal\": \"true\",\n    role: \"dialog\",\n    ref: containerRef\n  }, eventHandlers), children));\n};\nif (process.env.NODE_ENV !== 'production') {\n  DrawerPanel.displayName = 'DrawerPanel';\n}\nexport default DrawerPanel;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "classNames", "React", "<PERSON>er<PERSON><PERSON><PERSON>", "props", "prefixCls", "className", "style", "children", "containerRef", "onMouseEnter", "onMouseOver", "onMouseLeave", "onClick", "onKeyDown", "onKeyUp", "eventHandlers", "createElement", "Fragment", "concat", "role", "ref", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-drawer/es/DrawerPanel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar DrawerPanel = function DrawerPanel(props) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    containerRef = props.containerRef,\n    onMouseEnter = props.onMouseEnter,\n    onMouseOver = props.onMouseOver,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp;\n  var eventHandlers = {\n    onMouseEnter: onMouseEnter,\n    onMouseOver: onMouseOver,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp\n  };\n  // =============================== Render ===============================\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    style: _objectSpread({}, style),\n    \"aria-modal\": \"true\",\n    role: \"dialog\",\n    ref: containerRef\n  }, eventHandlers), children));\n};\nif (process.env.NODE_ENV !== 'production') {\n  DrawerPanel.displayName = 'DrawerPanel';\n}\nexport default DrawerPanel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;EAC5C,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,YAAY,GAAGN,KAAK,CAACM,YAAY;IACjCC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,YAAY,GAAGR,KAAK,CAACQ,YAAY;IACjCC,OAAO,GAAGT,KAAK,CAACS,OAAO;IACvBC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,OAAO,GAAGX,KAAK,CAACW,OAAO;EACzB,IAAIC,aAAa,GAAG;IAClBN,YAAY,EAAEA,YAAY;IAC1BC,WAAW,EAAEA,WAAW;IACxBC,YAAY,EAAEA,YAAY;IAC1BC,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA;EACX,CAAC;EACD;EACA,OAAO,aAAab,KAAK,CAACe,aAAa,CAACf,KAAK,CAACgB,QAAQ,EAAE,IAAI,EAAE,aAAahB,KAAK,CAACe,aAAa,CAAC,KAAK,EAAElB,QAAQ,CAAC;IAC7GO,SAAS,EAAEL,UAAU,CAAC,EAAE,CAACkB,MAAM,CAACd,SAAS,EAAE,UAAU,CAAC,EAAEC,SAAS,CAAC;IAClEC,KAAK,EAAEP,aAAa,CAAC,CAAC,CAAC,EAAEO,KAAK,CAAC;IAC/B,YAAY,EAAE,MAAM;IACpBa,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAEZ;EACP,CAAC,EAAEO,aAAa,CAAC,EAAER,QAAQ,CAAC,CAAC;AAC/B,CAAC;AACD,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCrB,WAAW,CAACsB,WAAW,GAAG,aAAa;AACzC;AACA,eAAetB,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}