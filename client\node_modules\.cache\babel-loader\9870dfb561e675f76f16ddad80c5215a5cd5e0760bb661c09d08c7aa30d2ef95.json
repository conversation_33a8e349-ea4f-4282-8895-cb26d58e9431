{"ast": null, "code": "import { clearFix, textEllipsis } from '../../style';\nconst genListStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSize,\n    lineHeight\n  } = token;\n  const itemCls = `${componentCls}-list-item`;\n  const actionsCls = `${itemCls}-actions`;\n  const actionCls = `${itemCls}-action`;\n  const listItemHeightSM = Math.round(fontSize * lineHeight);\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-list`]: Object.assign(Object.assign({}, clearFix()), {\n        lineHeight: token.lineHeight,\n        [itemCls]: {\n          position: 'relative',\n          height: token.lineHeight * fontSize,\n          marginTop: token.marginXS,\n          fontSize,\n          display: 'flex',\n          alignItems: 'center',\n          transition: `background-color ${token.motionDurationSlow}`,\n          '&:hover': {\n            backgroundColor: token.controlItemBgHover\n          },\n          [`${itemCls}-name`]: Object.assign(Object.assign({}, textEllipsis), {\n            padding: `0 ${token.paddingXS}px`,\n            lineHeight,\n            flex: 'auto',\n            transition: `all ${token.motionDurationSlow}`\n          }),\n          [actionsCls]: {\n            [actionCls]: {\n              opacity: 0\n            },\n            [`${actionCls}${antCls}-btn-sm`]: {\n              height: listItemHeightSM,\n              border: 0,\n              lineHeight: 1,\n              // FIXME: should not override small button\n              '> span': {\n                transform: 'scale(1)'\n              }\n            },\n            [`\n              ${actionCls}:focus,\n              &.picture ${actionCls}\n            `]: {\n              opacity: 1\n            },\n            [iconCls]: {\n              color: token.actionsColor,\n              transition: `all ${token.motionDurationSlow}`\n            },\n            [`&:hover ${iconCls}`]: {\n              color: token.colorText\n            }\n          },\n          [`${componentCls}-icon ${iconCls}`]: {\n            color: token.colorTextDescription,\n            fontSize\n          },\n          [`${itemCls}-progress`]: {\n            position: 'absolute',\n            bottom: -token.uploadProgressOffset,\n            width: '100%',\n            paddingInlineStart: fontSize + token.paddingXS,\n            fontSize,\n            lineHeight: 0,\n            pointerEvents: 'none',\n            '> div': {\n              margin: 0\n            }\n          }\n        },\n        [`${itemCls}:hover ${actionCls}`]: {\n          opacity: 1,\n          color: token.colorText\n        },\n        [`${itemCls}-error`]: {\n          color: token.colorError,\n          [`${itemCls}-name, ${componentCls}-icon ${iconCls}`]: {\n            color: token.colorError\n          },\n          [actionsCls]: {\n            [`${iconCls}, ${iconCls}:hover`]: {\n              color: token.colorError\n            },\n            [actionCls]: {\n              opacity: 1\n            }\n          }\n        },\n        [`${componentCls}-list-item-container`]: {\n          transition: `opacity ${token.motionDurationSlow}, height ${token.motionDurationSlow}`,\n          // For smooth removing animation\n          '&::before': {\n            display: 'table',\n            width: 0,\n            height: 0,\n            content: '\"\"'\n          }\n        }\n      })\n    }\n  };\n};\nexport default genListStyle;", "map": {"version": 3, "names": ["clearFix", "textEllipsis", "genListStyle", "token", "componentCls", "antCls", "iconCls", "fontSize", "lineHeight", "itemCls", "actionsCls", "actionCls", "listItemHeightSM", "Math", "round", "Object", "assign", "position", "height", "marginTop", "marginXS", "display", "alignItems", "transition", "motionDurationSlow", "backgroundColor", "controlItemBgHover", "padding", "paddingXS", "flex", "opacity", "border", "transform", "color", "actionsColor", "colorText", "colorTextDescription", "bottom", "uploadProgressOffset", "width", "paddingInlineStart", "pointerEvents", "margin", "colorError", "content"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/upload/style/list.js"], "sourcesContent": ["import { clearFix, textEllipsis } from '../../style';\nconst genListStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSize,\n    lineHeight\n  } = token;\n  const itemCls = `${componentCls}-list-item`;\n  const actionsCls = `${itemCls}-actions`;\n  const actionCls = `${itemCls}-action`;\n  const listItemHeightSM = Math.round(fontSize * lineHeight);\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-list`]: Object.assign(Object.assign({}, clearFix()), {\n        lineHeight: token.lineHeight,\n        [itemCls]: {\n          position: 'relative',\n          height: token.lineHeight * fontSize,\n          marginTop: token.marginXS,\n          fontSize,\n          display: 'flex',\n          alignItems: 'center',\n          transition: `background-color ${token.motionDurationSlow}`,\n          '&:hover': {\n            backgroundColor: token.controlItemBgHover\n          },\n          [`${itemCls}-name`]: Object.assign(Object.assign({}, textEllipsis), {\n            padding: `0 ${token.paddingXS}px`,\n            lineHeight,\n            flex: 'auto',\n            transition: `all ${token.motionDurationSlow}`\n          }),\n          [actionsCls]: {\n            [actionCls]: {\n              opacity: 0\n            },\n            [`${actionCls}${antCls}-btn-sm`]: {\n              height: listItemHeightSM,\n              border: 0,\n              lineHeight: 1,\n              // FIXME: should not override small button\n              '> span': {\n                transform: 'scale(1)'\n              }\n            },\n            [`\n              ${actionCls}:focus,\n              &.picture ${actionCls}\n            `]: {\n              opacity: 1\n            },\n            [iconCls]: {\n              color: token.actionsColor,\n              transition: `all ${token.motionDurationSlow}`\n            },\n            [`&:hover ${iconCls}`]: {\n              color: token.colorText\n            }\n          },\n          [`${componentCls}-icon ${iconCls}`]: {\n            color: token.colorTextDescription,\n            fontSize\n          },\n          [`${itemCls}-progress`]: {\n            position: 'absolute',\n            bottom: -token.uploadProgressOffset,\n            width: '100%',\n            paddingInlineStart: fontSize + token.paddingXS,\n            fontSize,\n            lineHeight: 0,\n            pointerEvents: 'none',\n            '> div': {\n              margin: 0\n            }\n          }\n        },\n        [`${itemCls}:hover ${actionCls}`]: {\n          opacity: 1,\n          color: token.colorText\n        },\n        [`${itemCls}-error`]: {\n          color: token.colorError,\n          [`${itemCls}-name, ${componentCls}-icon ${iconCls}`]: {\n            color: token.colorError\n          },\n          [actionsCls]: {\n            [`${iconCls}, ${iconCls}:hover`]: {\n              color: token.colorError\n            },\n            [actionCls]: {\n              opacity: 1\n            }\n          }\n        },\n        [`${componentCls}-list-item-container`]: {\n          transition: `opacity ${token.motionDurationSlow}, height ${token.motionDurationSlow}`,\n          // For smooth removing animation\n          '&::before': {\n            display: 'table',\n            width: 0,\n            height: 0,\n            content: '\"\"'\n          }\n        }\n      })\n    }\n  };\n};\nexport default genListStyle;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,YAAY,QAAQ,aAAa;AACpD,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,OAAO;IACPC,QAAQ;IACRC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,OAAO,GAAI,GAAEL,YAAa,YAAW;EAC3C,MAAMM,UAAU,GAAI,GAAED,OAAQ,UAAS;EACvC,MAAME,SAAS,GAAI,GAAEF,OAAQ,SAAQ;EACrC,MAAMG,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACP,QAAQ,GAAGC,UAAU,CAAC;EAC1D,OAAO;IACL,CAAE,GAAEJ,YAAa,UAAS,GAAG;MAC3B,CAAE,GAAEA,YAAa,OAAM,GAAGW,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAE;QACrEQ,UAAU,EAAEL,KAAK,CAACK,UAAU;QAC5B,CAACC,OAAO,GAAG;UACTQ,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAEf,KAAK,CAACK,UAAU,GAAGD,QAAQ;UACnCY,SAAS,EAAEhB,KAAK,CAACiB,QAAQ;UACzBb,QAAQ;UACRc,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,UAAU,EAAG,oBAAmBpB,KAAK,CAACqB,kBAAmB,EAAC;UAC1D,SAAS,EAAE;YACTC,eAAe,EAAEtB,KAAK,CAACuB;UACzB,CAAC;UACD,CAAE,GAAEjB,OAAQ,OAAM,GAAGM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,YAAY,CAAC,EAAE;YAClE0B,OAAO,EAAG,KAAIxB,KAAK,CAACyB,SAAU,IAAG;YACjCpB,UAAU;YACVqB,IAAI,EAAE,MAAM;YACZN,UAAU,EAAG,OAAMpB,KAAK,CAACqB,kBAAmB;UAC9C,CAAC,CAAC;UACF,CAACd,UAAU,GAAG;YACZ,CAACC,SAAS,GAAG;cACXmB,OAAO,EAAE;YACX,CAAC;YACD,CAAE,GAAEnB,SAAU,GAAEN,MAAO,SAAQ,GAAG;cAChCa,MAAM,EAAEN,gBAAgB;cACxBmB,MAAM,EAAE,CAAC;cACTvB,UAAU,EAAE,CAAC;cACb;cACA,QAAQ,EAAE;gBACRwB,SAAS,EAAE;cACb;YACF,CAAC;YACD,CAAE;AACd,gBAAgBrB,SAAU;AAC1B,0BAA0BA,SAAU;AACpC,aAAa,GAAG;cACFmB,OAAO,EAAE;YACX,CAAC;YACD,CAACxB,OAAO,GAAG;cACT2B,KAAK,EAAE9B,KAAK,CAAC+B,YAAY;cACzBX,UAAU,EAAG,OAAMpB,KAAK,CAACqB,kBAAmB;YAC9C,CAAC;YACD,CAAE,WAAUlB,OAAQ,EAAC,GAAG;cACtB2B,KAAK,EAAE9B,KAAK,CAACgC;YACf;UACF,CAAC;UACD,CAAE,GAAE/B,YAAa,SAAQE,OAAQ,EAAC,GAAG;YACnC2B,KAAK,EAAE9B,KAAK,CAACiC,oBAAoB;YACjC7B;UACF,CAAC;UACD,CAAE,GAAEE,OAAQ,WAAU,GAAG;YACvBQ,QAAQ,EAAE,UAAU;YACpBoB,MAAM,EAAE,CAAClC,KAAK,CAACmC,oBAAoB;YACnCC,KAAK,EAAE,MAAM;YACbC,kBAAkB,EAAEjC,QAAQ,GAAGJ,KAAK,CAACyB,SAAS;YAC9CrB,QAAQ;YACRC,UAAU,EAAE,CAAC;YACbiC,aAAa,EAAE,MAAM;YACrB,OAAO,EAAE;cACPC,MAAM,EAAE;YACV;UACF;QACF,CAAC;QACD,CAAE,GAAEjC,OAAQ,UAASE,SAAU,EAAC,GAAG;UACjCmB,OAAO,EAAE,CAAC;UACVG,KAAK,EAAE9B,KAAK,CAACgC;QACf,CAAC;QACD,CAAE,GAAE1B,OAAQ,QAAO,GAAG;UACpBwB,KAAK,EAAE9B,KAAK,CAACwC,UAAU;UACvB,CAAE,GAAElC,OAAQ,UAASL,YAAa,SAAQE,OAAQ,EAAC,GAAG;YACpD2B,KAAK,EAAE9B,KAAK,CAACwC;UACf,CAAC;UACD,CAACjC,UAAU,GAAG;YACZ,CAAE,GAAEJ,OAAQ,KAAIA,OAAQ,QAAO,GAAG;cAChC2B,KAAK,EAAE9B,KAAK,CAACwC;YACf,CAAC;YACD,CAAChC,SAAS,GAAG;cACXmB,OAAO,EAAE;YACX;UACF;QACF,CAAC;QACD,CAAE,GAAE1B,YAAa,sBAAqB,GAAG;UACvCmB,UAAU,EAAG,WAAUpB,KAAK,CAACqB,kBAAmB,YAAWrB,KAAK,CAACqB,kBAAmB,EAAC;UACrF;UACA,WAAW,EAAE;YACXH,OAAO,EAAE,OAAO;YAChBkB,KAAK,EAAE,CAAC;YACRrB,MAAM,EAAE,CAAC;YACT0B,OAAO,EAAE;UACX;QACF;MACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,eAAe1C,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}