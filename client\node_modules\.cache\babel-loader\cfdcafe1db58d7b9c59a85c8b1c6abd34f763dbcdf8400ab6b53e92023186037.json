{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.log = log;\nexports.resetState = resetState;\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n// Tracks portals that are open and emits events to subscribers\n\nvar PortalOpenInstances = function PortalOpenInstances() {\n  var _this = this;\n  _classCallCheck(this, PortalOpenInstances);\n  this.register = function (openInstance) {\n    if (_this.openInstances.indexOf(openInstance) !== -1) {\n      if (process.env.NODE_ENV !== \"production\") {\n        // eslint-disable-next-line no-console\n        console.warn(\"React-Modal: Cannot register modal instance that's already open\");\n      }\n      return;\n    }\n    _this.openInstances.push(openInstance);\n    _this.emit(\"register\");\n  };\n  this.deregister = function (openInstance) {\n    var index = _this.openInstances.indexOf(openInstance);\n    if (index === -1) {\n      if (process.env.NODE_ENV !== \"production\") {\n        // eslint-disable-next-line no-console\n        console.warn(\"React-Modal: Unable to deregister \" + openInstance + \" as \" + \"it was never registered\");\n      }\n      return;\n    }\n    _this.openInstances.splice(index, 1);\n    _this.emit(\"deregister\");\n  };\n  this.subscribe = function (callback) {\n    _this.subscribers.push(callback);\n  };\n  this.emit = function (eventType) {\n    _this.subscribers.forEach(function (subscriber) {\n      return subscriber(eventType,\n      // shallow copy to avoid accidental mutation\n      _this.openInstances.slice());\n    });\n  };\n  this.openInstances = [];\n  this.subscribers = [];\n};\nvar portalOpenInstances = new PortalOpenInstances();\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction log() {\n  console.log(\"portalOpenInstances ----------\");\n  console.log(portalOpenInstances.openInstances.length);\n  portalOpenInstances.openInstances.forEach(function (p) {\n    return console.log(p);\n  });\n  console.log(\"end portalOpenInstances ----------\");\n}\n\n/* istanbul ignore next */\nfunction resetState() {\n  portalOpenInstances = new PortalOpenInstances();\n}\n/* eslint-enable no-console */\n\nexports.default = portalOpenInstances;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "log", "resetState", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "PortalOpenInstances", "_this", "register", "openInstance", "openInstances", "indexOf", "process", "env", "NODE_ENV", "console", "warn", "push", "emit", "deregister", "index", "splice", "subscribe", "callback", "subscribers", "eventType", "for<PERSON>ach", "subscriber", "slice", "portalOpenInstances", "length", "p", "default"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/react-modal/lib/helpers/portalOpenInstances.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.log = log;\nexports.resetState = resetState;\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n// Tracks portals that are open and emits events to subscribers\n\nvar PortalOpenInstances = function PortalOpenInstances() {\n  var _this = this;\n\n  _classCallCheck(this, PortalOpenInstances);\n\n  this.register = function (openInstance) {\n    if (_this.openInstances.indexOf(openInstance) !== -1) {\n      if (process.env.NODE_ENV !== \"production\") {\n        // eslint-disable-next-line no-console\n        console.warn(\"React-Modal: Cannot register modal instance that's already open\");\n      }\n      return;\n    }\n    _this.openInstances.push(openInstance);\n    _this.emit(\"register\");\n  };\n\n  this.deregister = function (openInstance) {\n    var index = _this.openInstances.indexOf(openInstance);\n    if (index === -1) {\n      if (process.env.NODE_ENV !== \"production\") {\n        // eslint-disable-next-line no-console\n        console.warn(\"React-Modal: Unable to deregister \" + openInstance + \" as \" + \"it was never registered\");\n      }\n      return;\n    }\n    _this.openInstances.splice(index, 1);\n    _this.emit(\"deregister\");\n  };\n\n  this.subscribe = function (callback) {\n    _this.subscribers.push(callback);\n  };\n\n  this.emit = function (eventType) {\n    _this.subscribers.forEach(function (subscriber) {\n      return subscriber(eventType,\n      // shallow copy to avoid accidental mutation\n      _this.openInstances.slice());\n    });\n  };\n\n  this.openInstances = [];\n  this.subscribers = [];\n};\n\nvar portalOpenInstances = new PortalOpenInstances();\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction log() {\n  console.log(\"portalOpenInstances ----------\");\n  console.log(portalOpenInstances.openInstances.length);\n  portalOpenInstances.openInstances.forEach(function (p) {\n    return console.log(p);\n  });\n  console.log(\"end portalOpenInstances ----------\");\n}\n\n/* istanbul ignore next */\nfunction resetState() {\n  portalOpenInstances = new PortalOpenInstances();\n}\n/* eslint-enable no-console */\n\nexports.default = portalOpenInstances;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,GAAG,GAAGA,GAAG;AACjBF,OAAO,CAACG,UAAU,GAAGA,UAAU;AAE/B,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;;AAExJ;;AAEA,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAAA,EAAG;EACvD,IAAIC,KAAK,GAAG,IAAI;EAEhBL,eAAe,CAAC,IAAI,EAAEI,mBAAmB,CAAC;EAE1C,IAAI,CAACE,QAAQ,GAAG,UAAUC,YAAY,EAAE;IACtC,IAAIF,KAAK,CAACG,aAAa,CAACC,OAAO,CAACF,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;MACpD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC;QACAC,OAAO,CAACC,IAAI,CAAC,iEAAiE,CAAC;MACjF;MACA;IACF;IACAT,KAAK,CAACG,aAAa,CAACO,IAAI,CAACR,YAAY,CAAC;IACtCF,KAAK,CAACW,IAAI,CAAC,UAAU,CAAC;EACxB,CAAC;EAED,IAAI,CAACC,UAAU,GAAG,UAAUV,YAAY,EAAE;IACxC,IAAIW,KAAK,GAAGb,KAAK,CAACG,aAAa,CAACC,OAAO,CAACF,YAAY,CAAC;IACrD,IAAIW,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC;QACAC,OAAO,CAACC,IAAI,CAAC,oCAAoC,GAAGP,YAAY,GAAG,MAAM,GAAG,yBAAyB,CAAC;MACxG;MACA;IACF;IACAF,KAAK,CAACG,aAAa,CAACW,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACpCb,KAAK,CAACW,IAAI,CAAC,YAAY,CAAC;EAC1B,CAAC;EAED,IAAI,CAACI,SAAS,GAAG,UAAUC,QAAQ,EAAE;IACnChB,KAAK,CAACiB,WAAW,CAACP,IAAI,CAACM,QAAQ,CAAC;EAClC,CAAC;EAED,IAAI,CAACL,IAAI,GAAG,UAAUO,SAAS,EAAE;IAC/BlB,KAAK,CAACiB,WAAW,CAACE,OAAO,CAAC,UAAUC,UAAU,EAAE;MAC9C,OAAOA,UAAU,CAACF,SAAS;MAC3B;MACAlB,KAAK,CAACG,aAAa,CAACkB,KAAK,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAAClB,aAAa,GAAG,EAAE;EACvB,IAAI,CAACc,WAAW,GAAG,EAAE;AACvB,CAAC;AAED,IAAIK,mBAAmB,GAAG,IAAIvB,mBAAmB,CAAC,CAAC;;AAEnD;AACA;AACA,SAASN,GAAGA,CAAA,EAAG;EACbe,OAAO,CAACf,GAAG,CAAC,gCAAgC,CAAC;EAC7Ce,OAAO,CAACf,GAAG,CAAC6B,mBAAmB,CAACnB,aAAa,CAACoB,MAAM,CAAC;EACrDD,mBAAmB,CAACnB,aAAa,CAACgB,OAAO,CAAC,UAAUK,CAAC,EAAE;IACrD,OAAOhB,OAAO,CAACf,GAAG,CAAC+B,CAAC,CAAC;EACvB,CAAC,CAAC;EACFhB,OAAO,CAACf,GAAG,CAAC,oCAAoC,CAAC;AACnD;;AAEA;AACA,SAASC,UAAUA,CAAA,EAAG;EACpB4B,mBAAmB,GAAG,IAAIvB,mBAAmB,CAAC,CAAC;AACjD;AACA;;AAEAR,OAAO,CAACkC,OAAO,GAAGH,mBAAmB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}