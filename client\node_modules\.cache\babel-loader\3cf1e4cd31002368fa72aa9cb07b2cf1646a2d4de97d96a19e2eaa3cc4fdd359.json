{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport { getOffset } from \"rc-util/es/Dom/css\";\nimport getScrollBarSize from \"rc-util/es/getScrollBarSize\";\nimport * as React from 'react';\nimport TableContext from \"./context/TableContext\";\nimport { useLayoutState } from \"./hooks/useFrame\";\nvar StickyScrollBar = function StickyScrollBar(_ref, ref) {\n  var _scrollBodyRef$curren, _scrollBodyRef$curren2;\n  var scrollBodyRef = _ref.scrollBodyRef,\n    onScroll = _ref.onScroll,\n    offsetScroll = _ref.offsetScroll,\n    container = _ref.container;\n  var prefixCls = useContext(TableContext, 'prefixCls');\n  var bodyScrollWidth = ((_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 ? void 0 : _scrollBodyRef$curren.scrollWidth) || 0;\n  var bodyWidth = ((_scrollBodyRef$curren2 = scrollBodyRef.current) === null || _scrollBodyRef$curren2 === void 0 ? void 0 : _scrollBodyRef$curren2.clientWidth) || 0;\n  var scrollBarWidth = bodyScrollWidth && bodyWidth * (bodyWidth / bodyScrollWidth);\n  var scrollBarRef = React.useRef();\n  var _useLayoutState = useLayoutState({\n      scrollLeft: 0,\n      isHiddenScrollBar: false\n    }),\n    _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n    scrollState = _useLayoutState2[0],\n    setScrollState = _useLayoutState2[1];\n  var refState = React.useRef({\n    delta: 0,\n    x: 0\n  });\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isActive = _React$useState2[0],\n    setActive = _React$useState2[1];\n  var onMouseUp = function onMouseUp() {\n    setActive(false);\n  };\n  var onMouseDown = function onMouseDown(event) {\n    event.persist();\n    refState.current.delta = event.pageX - scrollState.scrollLeft;\n    refState.current.x = 0;\n    setActive(true);\n    event.preventDefault();\n  };\n  var onMouseMove = function onMouseMove(event) {\n    var _window;\n    // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons\n    var _ref2 = event || ((_window = window) === null || _window === void 0 ? void 0 : _window.event),\n      buttons = _ref2.buttons;\n    if (!isActive || buttons === 0) {\n      // If out body mouse up, we can set isActive false when mouse move\n      if (isActive) {\n        setActive(false);\n      }\n      return;\n    }\n    var left = refState.current.x + event.pageX - refState.current.x - refState.current.delta;\n    if (left <= 0) {\n      left = 0;\n    }\n    if (left + scrollBarWidth >= bodyWidth) {\n      left = bodyWidth - scrollBarWidth;\n    }\n    onScroll({\n      scrollLeft: left / bodyWidth * (bodyScrollWidth + 2)\n    });\n    refState.current.x = event.pageX;\n  };\n  var onContainerScroll = function onContainerScroll() {\n    if (!scrollBodyRef.current) {\n      return;\n    }\n    var tableOffsetTop = getOffset(scrollBodyRef.current).top;\n    var tableBottomOffset = tableOffsetTop + scrollBodyRef.current.offsetHeight;\n    var currentClientOffset = container === window ? document.documentElement.scrollTop + window.innerHeight : getOffset(container).top + container.clientHeight;\n    if (tableBottomOffset - getScrollBarSize() <= currentClientOffset || tableOffsetTop >= currentClientOffset - offsetScroll) {\n      setScrollState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isHiddenScrollBar: true\n        });\n      });\n    } else {\n      setScrollState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isHiddenScrollBar: false\n        });\n      });\n    }\n  };\n  var setScrollLeft = function setScrollLeft(left) {\n    setScrollState(function (state) {\n      return _objectSpread(_objectSpread({}, state), {}, {\n        scrollLeft: left / bodyScrollWidth * bodyWidth || 0\n      });\n    });\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      setScrollLeft: setScrollLeft\n    };\n  });\n  React.useEffect(function () {\n    var onMouseUpListener = addEventListener(document.body, 'mouseup', onMouseUp, false);\n    var onMouseMoveListener = addEventListener(document.body, 'mousemove', onMouseMove, false);\n    onContainerScroll();\n    return function () {\n      onMouseUpListener.remove();\n      onMouseMoveListener.remove();\n    };\n  }, [scrollBarWidth, isActive]);\n  React.useEffect(function () {\n    var onScrollListener = addEventListener(container, 'scroll', onContainerScroll, false);\n    var onResizeListener = addEventListener(window, 'resize', onContainerScroll, false);\n    return function () {\n      onScrollListener.remove();\n      onResizeListener.remove();\n    };\n  }, [container]);\n  React.useEffect(function () {\n    if (!scrollState.isHiddenScrollBar) {\n      setScrollState(function (state) {\n        var bodyNode = scrollBodyRef.current;\n        if (!bodyNode) {\n          return state;\n        }\n        return _objectSpread(_objectSpread({}, state), {}, {\n          scrollLeft: bodyNode.scrollLeft / bodyNode.scrollWidth * bodyNode.clientWidth\n        });\n      });\n    }\n  }, [scrollState.isHiddenScrollBar]);\n  if (bodyScrollWidth <= bodyWidth || !scrollBarWidth || scrollState.isHiddenScrollBar) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: getScrollBarSize(),\n      width: bodyWidth,\n      bottom: offsetScroll\n    },\n    className: \"\".concat(prefixCls, \"-sticky-scroll\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onMouseDown,\n    ref: scrollBarRef,\n    className: classNames(\"\".concat(prefixCls, \"-sticky-scroll-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-sticky-scroll-bar-active\"), isActive)),\n    style: {\n      width: \"\".concat(scrollBarWidth, \"px\"),\n      transform: \"translate3d(\".concat(scrollState.scrollLeft, \"px, 0, 0)\")\n    }\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(StickyScrollBar);", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_slicedToArray", "useContext", "classNames", "addEventListener", "getOffset", "getScrollBarSize", "React", "TableContext", "useLayoutState", "StickyScrollBar", "_ref", "ref", "_scrollBodyRef$curren", "_scrollBodyRef$curren2", "scrollBodyRef", "onScroll", "offsetScroll", "container", "prefixCls", "bodyScrollWidth", "current", "scrollWidth", "bodyWidth", "clientWidth", "scrollBarWidth", "scrollBarRef", "useRef", "_useLayoutState", "scrollLeft", "isHiddenScrollBar", "_useLayoutState2", "scrollState", "setScrollState", "refState", "delta", "x", "_React$useState", "useState", "_React$useState2", "isActive", "setActive", "onMouseUp", "onMouseDown", "event", "persist", "pageX", "preventDefault", "onMouseMove", "_window", "_ref2", "window", "buttons", "left", "onContainerScroll", "tableOffsetTop", "top", "tableBottomOffset", "offsetHeight", "currentClientOffset", "document", "documentElement", "scrollTop", "innerHeight", "clientHeight", "state", "setScrollLeft", "useImperativeHandle", "useEffect", "onMouseUpListener", "body", "onMouseMoveListener", "remove", "onScrollListener", "onResizeListener", "bodyNode", "createElement", "style", "height", "width", "bottom", "className", "concat", "transform", "forwardRef"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/stickyScrollBar.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport { getOffset } from \"rc-util/es/Dom/css\";\nimport getScrollBarSize from \"rc-util/es/getScrollBarSize\";\nimport * as React from 'react';\nimport TableContext from \"./context/TableContext\";\nimport { useLayoutState } from \"./hooks/useFrame\";\nvar StickyScrollBar = function StickyScrollBar(_ref, ref) {\n  var _scrollBodyRef$curren, _scrollBodyRef$curren2;\n  var scrollBodyRef = _ref.scrollBodyRef,\n    onScroll = _ref.onScroll,\n    offsetScroll = _ref.offsetScroll,\n    container = _ref.container;\n  var prefixCls = useContext(TableContext, 'prefixCls');\n  var bodyScrollWidth = ((_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 ? void 0 : _scrollBodyRef$curren.scrollWidth) || 0;\n  var bodyWidth = ((_scrollBodyRef$curren2 = scrollBodyRef.current) === null || _scrollBodyRef$curren2 === void 0 ? void 0 : _scrollBodyRef$curren2.clientWidth) || 0;\n  var scrollBarWidth = bodyScrollWidth && bodyWidth * (bodyWidth / bodyScrollWidth);\n  var scrollBarRef = React.useRef();\n  var _useLayoutState = useLayoutState({\n      scrollLeft: 0,\n      isHiddenScrollBar: false\n    }),\n    _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n    scrollState = _useLayoutState2[0],\n    setScrollState = _useLayoutState2[1];\n  var refState = React.useRef({\n    delta: 0,\n    x: 0\n  });\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isActive = _React$useState2[0],\n    setActive = _React$useState2[1];\n  var onMouseUp = function onMouseUp() {\n    setActive(false);\n  };\n  var onMouseDown = function onMouseDown(event) {\n    event.persist();\n    refState.current.delta = event.pageX - scrollState.scrollLeft;\n    refState.current.x = 0;\n    setActive(true);\n    event.preventDefault();\n  };\n  var onMouseMove = function onMouseMove(event) {\n    var _window;\n    // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons\n    var _ref2 = event || ((_window = window) === null || _window === void 0 ? void 0 : _window.event),\n      buttons = _ref2.buttons;\n    if (!isActive || buttons === 0) {\n      // If out body mouse up, we can set isActive false when mouse move\n      if (isActive) {\n        setActive(false);\n      }\n      return;\n    }\n    var left = refState.current.x + event.pageX - refState.current.x - refState.current.delta;\n    if (left <= 0) {\n      left = 0;\n    }\n    if (left + scrollBarWidth >= bodyWidth) {\n      left = bodyWidth - scrollBarWidth;\n    }\n    onScroll({\n      scrollLeft: left / bodyWidth * (bodyScrollWidth + 2)\n    });\n    refState.current.x = event.pageX;\n  };\n  var onContainerScroll = function onContainerScroll() {\n    if (!scrollBodyRef.current) {\n      return;\n    }\n    var tableOffsetTop = getOffset(scrollBodyRef.current).top;\n    var tableBottomOffset = tableOffsetTop + scrollBodyRef.current.offsetHeight;\n    var currentClientOffset = container === window ? document.documentElement.scrollTop + window.innerHeight : getOffset(container).top + container.clientHeight;\n    if (tableBottomOffset - getScrollBarSize() <= currentClientOffset || tableOffsetTop >= currentClientOffset - offsetScroll) {\n      setScrollState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isHiddenScrollBar: true\n        });\n      });\n    } else {\n      setScrollState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isHiddenScrollBar: false\n        });\n      });\n    }\n  };\n  var setScrollLeft = function setScrollLeft(left) {\n    setScrollState(function (state) {\n      return _objectSpread(_objectSpread({}, state), {}, {\n        scrollLeft: left / bodyScrollWidth * bodyWidth || 0\n      });\n    });\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      setScrollLeft: setScrollLeft\n    };\n  });\n  React.useEffect(function () {\n    var onMouseUpListener = addEventListener(document.body, 'mouseup', onMouseUp, false);\n    var onMouseMoveListener = addEventListener(document.body, 'mousemove', onMouseMove, false);\n    onContainerScroll();\n    return function () {\n      onMouseUpListener.remove();\n      onMouseMoveListener.remove();\n    };\n  }, [scrollBarWidth, isActive]);\n  React.useEffect(function () {\n    var onScrollListener = addEventListener(container, 'scroll', onContainerScroll, false);\n    var onResizeListener = addEventListener(window, 'resize', onContainerScroll, false);\n    return function () {\n      onScrollListener.remove();\n      onResizeListener.remove();\n    };\n  }, [container]);\n  React.useEffect(function () {\n    if (!scrollState.isHiddenScrollBar) {\n      setScrollState(function (state) {\n        var bodyNode = scrollBodyRef.current;\n        if (!bodyNode) {\n          return state;\n        }\n        return _objectSpread(_objectSpread({}, state), {}, {\n          scrollLeft: bodyNode.scrollLeft / bodyNode.scrollWidth * bodyNode.clientWidth\n        });\n      });\n    }\n  }, [scrollState.isHiddenScrollBar]);\n  if (bodyScrollWidth <= bodyWidth || !scrollBarWidth || scrollState.isHiddenScrollBar) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: getScrollBarSize(),\n      width: bodyWidth,\n      bottom: offsetScroll\n    },\n    className: \"\".concat(prefixCls, \"-sticky-scroll\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onMouseDown,\n    ref: scrollBarRef,\n    className: classNames(\"\".concat(prefixCls, \"-sticky-scroll-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-sticky-scroll-bar-active\"), isActive)),\n    style: {\n      width: \"\".concat(scrollBarWidth, \"px\"),\n      transform: \"translate3d(\".concat(scrollState.scrollLeft, \"px, 0, 0)\")\n    }\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(StickyScrollBar);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,wBAAwB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACxD,IAAIC,qBAAqB,EAAEC,sBAAsB;EACjD,IAAIC,aAAa,GAAGJ,IAAI,CAACI,aAAa;IACpCC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,YAAY,GAAGN,IAAI,CAACM,YAAY;IAChCC,SAAS,GAAGP,IAAI,CAACO,SAAS;EAC5B,IAAIC,SAAS,GAAGjB,UAAU,CAACM,YAAY,EAAE,WAAW,CAAC;EACrD,IAAIY,eAAe,GAAG,CAAC,CAACP,qBAAqB,GAAGE,aAAa,CAACM,OAAO,MAAM,IAAI,IAAIR,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACS,WAAW,KAAK,CAAC;EACtK,IAAIC,SAAS,GAAG,CAAC,CAACT,sBAAsB,GAAGC,aAAa,CAACM,OAAO,MAAM,IAAI,IAAIP,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACU,WAAW,KAAK,CAAC;EACnK,IAAIC,cAAc,GAAGL,eAAe,IAAIG,SAAS,IAAIA,SAAS,GAAGH,eAAe,CAAC;EACjF,IAAIM,YAAY,GAAGnB,KAAK,CAACoB,MAAM,CAAC,CAAC;EACjC,IAAIC,eAAe,GAAGnB,cAAc,CAAC;MACjCoB,UAAU,EAAE,CAAC;MACbC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACFC,gBAAgB,GAAG9B,cAAc,CAAC2B,eAAe,EAAE,CAAC,CAAC;IACrDI,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,QAAQ,GAAG3B,KAAK,CAACoB,MAAM,CAAC;IAC1BQ,KAAK,EAAE,CAAC;IACRC,CAAC,EAAE;EACL,CAAC,CAAC;EACF,IAAIC,eAAe,GAAG9B,KAAK,CAAC+B,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGtC,cAAc,CAACoC,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjC,IAAIG,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnCD,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EACD,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;IAC5CA,KAAK,CAACC,OAAO,CAAC,CAAC;IACfX,QAAQ,CAACb,OAAO,CAACc,KAAK,GAAGS,KAAK,CAACE,KAAK,GAAGd,WAAW,CAACH,UAAU;IAC7DK,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAG,CAAC;IACtBK,SAAS,CAAC,IAAI,CAAC;IACfG,KAAK,CAACG,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACJ,KAAK,EAAE;IAC5C,IAAIK,OAAO;IACX;IACA,IAAIC,KAAK,GAAGN,KAAK,KAAK,CAACK,OAAO,GAAGE,MAAM,MAAM,IAAI,IAAIF,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACL,KAAK,CAAC;MAC/FQ,OAAO,GAAGF,KAAK,CAACE,OAAO;IACzB,IAAI,CAACZ,QAAQ,IAAIY,OAAO,KAAK,CAAC,EAAE;MAC9B;MACA,IAAIZ,QAAQ,EAAE;QACZC,SAAS,CAAC,KAAK,CAAC;MAClB;MACA;IACF;IACA,IAAIY,IAAI,GAAGnB,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAGQ,KAAK,CAACE,KAAK,GAAGZ,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAGF,QAAQ,CAACb,OAAO,CAACc,KAAK;IACzF,IAAIkB,IAAI,IAAI,CAAC,EAAE;MACbA,IAAI,GAAG,CAAC;IACV;IACA,IAAIA,IAAI,GAAG5B,cAAc,IAAIF,SAAS,EAAE;MACtC8B,IAAI,GAAG9B,SAAS,GAAGE,cAAc;IACnC;IACAT,QAAQ,CAAC;MACPa,UAAU,EAAEwB,IAAI,GAAG9B,SAAS,IAAIH,eAAe,GAAG,CAAC;IACrD,CAAC,CAAC;IACFc,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAGQ,KAAK,CAACE,KAAK;EAClC,CAAC;EACD,IAAIQ,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAI,CAACvC,aAAa,CAACM,OAAO,EAAE;MAC1B;IACF;IACA,IAAIkC,cAAc,GAAGlD,SAAS,CAACU,aAAa,CAACM,OAAO,CAAC,CAACmC,GAAG;IACzD,IAAIC,iBAAiB,GAAGF,cAAc,GAAGxC,aAAa,CAACM,OAAO,CAACqC,YAAY;IAC3E,IAAIC,mBAAmB,GAAGzC,SAAS,KAAKiC,MAAM,GAAGS,QAAQ,CAACC,eAAe,CAACC,SAAS,GAAGX,MAAM,CAACY,WAAW,GAAG1D,SAAS,CAACa,SAAS,CAAC,CAACsC,GAAG,GAAGtC,SAAS,CAAC8C,YAAY;IAC5J,IAAIP,iBAAiB,GAAGnD,gBAAgB,CAAC,CAAC,IAAIqD,mBAAmB,IAAIJ,cAAc,IAAII,mBAAmB,GAAG1C,YAAY,EAAE;MACzHgB,cAAc,CAAC,UAAUgC,KAAK,EAAE;QAC9B,OAAOjE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDnC,iBAAiB,EAAE;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACLG,cAAc,CAAC,UAAUgC,KAAK,EAAE;QAC9B,OAAOjE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDnC,iBAAiB,EAAE;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIoC,aAAa,GAAG,SAASA,aAAaA,CAACb,IAAI,EAAE;IAC/CpB,cAAc,CAAC,UAAUgC,KAAK,EAAE;MAC9B,OAAOjE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDpC,UAAU,EAAEwB,IAAI,GAAGjC,eAAe,GAAGG,SAAS,IAAI;MACpD,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDhB,KAAK,CAAC4D,mBAAmB,CAACvD,GAAG,EAAE,YAAY;IACzC,OAAO;MACLsD,aAAa,EAAEA;IACjB,CAAC;EACH,CAAC,CAAC;EACF3D,KAAK,CAAC6D,SAAS,CAAC,YAAY;IAC1B,IAAIC,iBAAiB,GAAGjE,gBAAgB,CAACwD,QAAQ,CAACU,IAAI,EAAE,SAAS,EAAE5B,SAAS,EAAE,KAAK,CAAC;IACpF,IAAI6B,mBAAmB,GAAGnE,gBAAgB,CAACwD,QAAQ,CAACU,IAAI,EAAE,WAAW,EAAEtB,WAAW,EAAE,KAAK,CAAC;IAC1FM,iBAAiB,CAAC,CAAC;IACnB,OAAO,YAAY;MACjBe,iBAAiB,CAACG,MAAM,CAAC,CAAC;MAC1BD,mBAAmB,CAACC,MAAM,CAAC,CAAC;IAC9B,CAAC;EACH,CAAC,EAAE,CAAC/C,cAAc,EAAEe,QAAQ,CAAC,CAAC;EAC9BjC,KAAK,CAAC6D,SAAS,CAAC,YAAY;IAC1B,IAAIK,gBAAgB,GAAGrE,gBAAgB,CAACc,SAAS,EAAE,QAAQ,EAAEoC,iBAAiB,EAAE,KAAK,CAAC;IACtF,IAAIoB,gBAAgB,GAAGtE,gBAAgB,CAAC+C,MAAM,EAAE,QAAQ,EAAEG,iBAAiB,EAAE,KAAK,CAAC;IACnF,OAAO,YAAY;MACjBmB,gBAAgB,CAACD,MAAM,CAAC,CAAC;MACzBE,gBAAgB,CAACF,MAAM,CAAC,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,CAACtD,SAAS,CAAC,CAAC;EACfX,KAAK,CAAC6D,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACpC,WAAW,CAACF,iBAAiB,EAAE;MAClCG,cAAc,CAAC,UAAUgC,KAAK,EAAE;QAC9B,IAAIU,QAAQ,GAAG5D,aAAa,CAACM,OAAO;QACpC,IAAI,CAACsD,QAAQ,EAAE;UACb,OAAOV,KAAK;QACd;QACA,OAAOjE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDpC,UAAU,EAAE8C,QAAQ,CAAC9C,UAAU,GAAG8C,QAAQ,CAACrD,WAAW,GAAGqD,QAAQ,CAACnD;QACpE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACQ,WAAW,CAACF,iBAAiB,CAAC,CAAC;EACnC,IAAIV,eAAe,IAAIG,SAAS,IAAI,CAACE,cAAc,IAAIO,WAAW,CAACF,iBAAiB,EAAE;IACpF,OAAO,IAAI;EACb;EACA,OAAO,aAAavB,KAAK,CAACqE,aAAa,CAAC,KAAK,EAAE;IAC7CC,KAAK,EAAE;MACLC,MAAM,EAAExE,gBAAgB,CAAC,CAAC;MAC1ByE,KAAK,EAAExD,SAAS;MAChByD,MAAM,EAAE/D;IACV,CAAC;IACDgE,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC/D,SAAS,EAAE,gBAAgB;EAClD,CAAC,EAAE,aAAaZ,KAAK,CAACqE,aAAa,CAAC,KAAK,EAAE;IACzCjC,WAAW,EAAEA,WAAW;IACxB/B,GAAG,EAAEc,YAAY;IACjBuD,SAAS,EAAE9E,UAAU,CAAC,EAAE,CAAC+E,MAAM,CAAC/D,SAAS,EAAE,oBAAoB,CAAC,EAAEpB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACmF,MAAM,CAAC/D,SAAS,EAAE,2BAA2B,CAAC,EAAEqB,QAAQ,CAAC,CAAC;IACnJqC,KAAK,EAAE;MACLE,KAAK,EAAE,EAAE,CAACG,MAAM,CAACzD,cAAc,EAAE,IAAI,CAAC;MACtC0D,SAAS,EAAE,cAAc,CAACD,MAAM,CAAClD,WAAW,CAACH,UAAU,EAAE,WAAW;IACtE;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAe,aAAatB,KAAK,CAAC6E,UAAU,CAAC1E,eAAe,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}