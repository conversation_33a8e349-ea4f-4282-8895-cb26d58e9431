{"ast": null, "code": "import * as React from 'react';\nimport But<PERSON> from '../button';\nexport default function PickerButton(props) {\n  return /*#__PURE__*/React.createElement(Button, Object.assign({\n    size: \"small\",\n    type: \"primary\"\n  }, props));\n}", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "createElement", "Object", "assign", "size", "type"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/date-picker/PickerButton.js"], "sourcesContent": ["import * as React from 'react';\nimport But<PERSON> from '../button';\nexport default function PickerButton(props) {\n  return /*#__PURE__*/React.createElement(Button, Object.assign({\n    size: \"small\",\n    type: \"primary\"\n  }, props));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,eAAe,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC1C,OAAO,aAAaH,KAAK,CAACI,aAAa,CAACH,MAAM,EAAEI,MAAM,CAACC,MAAM,CAAC;IAC5DC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;EACR,CAAC,EAAEL,KAAK,CAAC,CAAC;AACZ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}