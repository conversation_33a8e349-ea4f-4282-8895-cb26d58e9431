{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"children\", \"action\", \"showAction\", \"hideAction\", \"popupVisible\", \"defaultPopupVisible\", \"onPopupVisibleChange\", \"afterPopupVisibleChange\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"focusDelay\", \"blurDelay\", \"mask\", \"maskClosable\", \"getPopupContainer\", \"forceRender\", \"autoDestroy\", \"destroyPopupOnHide\", \"popup\", \"popupClassName\", \"popupStyle\", \"popupPlacement\", \"builtinPlacements\", \"popupAlign\", \"zIndex\", \"stretch\", \"getPopupClassNameFromAlign\", \"alignPoint\", \"onPopupClick\", \"onPopupAlign\", \"arrow\", \"popupMotion\", \"maskMotion\", \"popupTransitionName\", \"popupAnimation\", \"maskTransitionName\", \"maskAnimation\", \"className\", \"getTriggerDOMNode\"];\nimport Portal from '@rc-component/portal';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { isDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useId from \"rc-util/es/hooks/useId\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport * as React from 'react';\nimport { flushSync } from 'react-dom';\nimport TriggerContext from \"./context\";\nimport useAction from \"./hooks/useAction\";\nimport useAlign from \"./hooks/useAlign\";\nimport useWatch from \"./hooks/useWatch\";\nimport useWinClick from \"./hooks/useWinClick\";\nimport Popup from \"./Popup\";\nimport TriggerWrapper from \"./TriggerWrapper\";\nimport { getAlignPopupClassName, getMotion } from \"./util\";\n\n// Removed Props List\n// Seems this can be auto\n// getDocument?: (element?: HTMLElement) => Document;\n// New version will not wrap popup with `rc-trigger-popup-content` when multiple children\nexport function generateTrigger() {\n  var PortalComponent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Portal;\n  var Trigger = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-trigger-popup' : _props$prefixCls,\n      children = props.children,\n      _props$action = props.action,\n      action = _props$action === void 0 ? 'hover' : _props$action,\n      showAction = props.showAction,\n      hideAction = props.hideAction,\n      popupVisible = props.popupVisible,\n      defaultPopupVisible = props.defaultPopupVisible,\n      onPopupVisibleChange = props.onPopupVisibleChange,\n      afterPopupVisibleChange = props.afterPopupVisibleChange,\n      mouseEnterDelay = props.mouseEnterDelay,\n      _props$mouseLeaveDela = props.mouseLeaveDelay,\n      mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,\n      focusDelay = props.focusDelay,\n      blurDelay = props.blurDelay,\n      mask = props.mask,\n      _props$maskClosable = props.maskClosable,\n      maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,\n      getPopupContainer = props.getPopupContainer,\n      forceRender = props.forceRender,\n      autoDestroy = props.autoDestroy,\n      destroyPopupOnHide = props.destroyPopupOnHide,\n      popup = props.popup,\n      popupClassName = props.popupClassName,\n      popupStyle = props.popupStyle,\n      popupPlacement = props.popupPlacement,\n      _props$builtinPlaceme = props.builtinPlacements,\n      builtinPlacements = _props$builtinPlaceme === void 0 ? {} : _props$builtinPlaceme,\n      popupAlign = props.popupAlign,\n      zIndex = props.zIndex,\n      stretch = props.stretch,\n      getPopupClassNameFromAlign = props.getPopupClassNameFromAlign,\n      alignPoint = props.alignPoint,\n      onPopupClick = props.onPopupClick,\n      onPopupAlign = props.onPopupAlign,\n      arrow = props.arrow,\n      popupMotion = props.popupMotion,\n      maskMotion = props.maskMotion,\n      popupTransitionName = props.popupTransitionName,\n      popupAnimation = props.popupAnimation,\n      maskTransitionName = props.maskTransitionName,\n      maskAnimation = props.maskAnimation,\n      className = props.className,\n      getTriggerDOMNode = props.getTriggerDOMNode,\n      restProps = _objectWithoutProperties(props, _excluded);\n    var mergedAutoDestroy = autoDestroy || destroyPopupOnHide || false;\n\n    // =========================== Mobile ===========================\n    var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      mobile = _React$useState2[0],\n      setMobile = _React$useState2[1];\n    useLayoutEffect(function () {\n      setMobile(isMobile());\n    }, []);\n\n    // ========================== Context ===========================\n    var subPopupElements = React.useRef({});\n    var parentContext = React.useContext(TriggerContext);\n    var context = React.useMemo(function () {\n      return {\n        registerSubPopup: function registerSubPopup(id, subPopupEle) {\n          subPopupElements.current[id] = subPopupEle;\n          parentContext === null || parentContext === void 0 ? void 0 : parentContext.registerSubPopup(id, subPopupEle);\n        }\n      };\n    }, [parentContext]);\n\n    // =========================== Popup ============================\n    var id = useId();\n    var _React$useState3 = React.useState(null),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      popupEle = _React$useState4[0],\n      setPopupEle = _React$useState4[1];\n    var setPopupRef = useEvent(function (node) {\n      if (isDOM(node) && popupEle !== node) {\n        setPopupEle(node);\n      }\n      parentContext === null || parentContext === void 0 ? void 0 : parentContext.registerSubPopup(id, node);\n    });\n\n    // =========================== Target ===========================\n    // Use state to control here since `useRef` update not trigger render\n    var _React$useState5 = React.useState(null),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      targetEle = _React$useState6[0],\n      setTargetEle = _React$useState6[1];\n    var setTargetRef = useEvent(function (node) {\n      if (isDOM(node) && targetEle !== node) {\n        setTargetEle(node);\n      }\n    });\n\n    // ========================== Children ==========================\n    var child = React.Children.only(children);\n    var originChildProps = (child === null || child === void 0 ? void 0 : child.props) || {};\n    var cloneProps = {};\n    var inPopupOrChild = useEvent(function (ele) {\n      var _getShadowRoot, _getShadowRoot2;\n      var childDOM = targetEle;\n      return (childDOM === null || childDOM === void 0 ? void 0 : childDOM.contains(ele)) || ((_getShadowRoot = getShadowRoot(childDOM)) === null || _getShadowRoot === void 0 ? void 0 : _getShadowRoot.host) === ele || ele === childDOM || (popupEle === null || popupEle === void 0 ? void 0 : popupEle.contains(ele)) || ((_getShadowRoot2 = getShadowRoot(popupEle)) === null || _getShadowRoot2 === void 0 ? void 0 : _getShadowRoot2.host) === ele || ele === popupEle || Object.values(subPopupElements.current).some(function (subPopupEle) {\n        return (subPopupEle === null || subPopupEle === void 0 ? void 0 : subPopupEle.contains(ele)) || ele === subPopupEle;\n      });\n    });\n\n    // =========================== Motion ===========================\n    var mergePopupMotion = getMotion(prefixCls, popupMotion, popupAnimation, popupTransitionName);\n    var mergeMaskMotion = getMotion(prefixCls, maskMotion, maskAnimation, maskTransitionName);\n\n    // ============================ Open ============================\n    var _React$useState7 = React.useState(defaultPopupVisible || false),\n      _React$useState8 = _slicedToArray(_React$useState7, 2),\n      internalOpen = _React$useState8[0],\n      setInternalOpen = _React$useState8[1];\n\n    // Render still use props as first priority\n    var mergedOpen = popupVisible !== null && popupVisible !== void 0 ? popupVisible : internalOpen;\n\n    // We use effect sync here in case `popupVisible` back to `undefined`\n    var setMergedOpen = useEvent(function (nextOpen) {\n      if (popupVisible === undefined) {\n        setInternalOpen(nextOpen);\n      }\n    });\n    useLayoutEffect(function () {\n      setInternalOpen(popupVisible || false);\n    }, [popupVisible]);\n    var openRef = React.useRef(mergedOpen);\n    openRef.current = mergedOpen;\n    var internalTriggerOpen = useEvent(function (nextOpen) {\n      // Enter or Pointer will both trigger open state change\n      // We only need take one to avoid duplicated change event trigger\n      flushSync(function () {\n        if (mergedOpen !== nextOpen) {\n          setMergedOpen(nextOpen);\n          onPopupVisibleChange === null || onPopupVisibleChange === void 0 ? void 0 : onPopupVisibleChange(nextOpen);\n        }\n      });\n    });\n\n    // Trigger for delay\n    var delayRef = React.useRef();\n    var clearDelay = function clearDelay() {\n      clearTimeout(delayRef.current);\n    };\n    var triggerOpen = function triggerOpen(nextOpen) {\n      var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      clearDelay();\n      if (delay === 0) {\n        internalTriggerOpen(nextOpen);\n      } else {\n        delayRef.current = setTimeout(function () {\n          internalTriggerOpen(nextOpen);\n        }, delay * 1000);\n      }\n    };\n    React.useEffect(function () {\n      return clearDelay;\n    }, []);\n\n    // ========================== Motion ============================\n    var _React$useState9 = React.useState(false),\n      _React$useState10 = _slicedToArray(_React$useState9, 2),\n      inMotion = _React$useState10[0],\n      setInMotion = _React$useState10[1];\n    useLayoutEffect(function (firstMount) {\n      if (!firstMount || mergedOpen) {\n        setInMotion(true);\n      }\n    }, [mergedOpen]);\n    var _React$useState11 = React.useState(null),\n      _React$useState12 = _slicedToArray(_React$useState11, 2),\n      motionPrepareResolve = _React$useState12[0],\n      setMotionPrepareResolve = _React$useState12[1];\n\n    // =========================== Align ============================\n    var _React$useState13 = React.useState([0, 0]),\n      _React$useState14 = _slicedToArray(_React$useState13, 2),\n      mousePos = _React$useState14[0],\n      setMousePos = _React$useState14[1];\n    var setMousePosByEvent = function setMousePosByEvent(event) {\n      setMousePos([event.clientX, event.clientY]);\n    };\n    var _useAlign = useAlign(mergedOpen, popupEle, alignPoint ? mousePos : targetEle, popupPlacement, builtinPlacements, popupAlign, onPopupAlign),\n      _useAlign2 = _slicedToArray(_useAlign, 11),\n      ready = _useAlign2[0],\n      offsetX = _useAlign2[1],\n      offsetY = _useAlign2[2],\n      offsetR = _useAlign2[3],\n      offsetB = _useAlign2[4],\n      arrowX = _useAlign2[5],\n      arrowY = _useAlign2[6],\n      scaleX = _useAlign2[7],\n      scaleY = _useAlign2[8],\n      alignInfo = _useAlign2[9],\n      onAlign = _useAlign2[10];\n    var _useAction = useAction(mobile, action, showAction, hideAction),\n      _useAction2 = _slicedToArray(_useAction, 2),\n      showActions = _useAction2[0],\n      hideActions = _useAction2[1];\n    var clickToShow = showActions.has('click');\n    var clickToHide = hideActions.has('click') || hideActions.has('contextMenu');\n    var triggerAlign = useEvent(function () {\n      if (!inMotion) {\n        onAlign();\n      }\n    });\n    var onScroll = function onScroll() {\n      if (openRef.current && alignPoint && clickToHide) {\n        triggerOpen(false);\n      }\n    };\n    useWatch(mergedOpen, targetEle, popupEle, triggerAlign, onScroll);\n    useLayoutEffect(function () {\n      triggerAlign();\n    }, [mousePos, popupPlacement]);\n\n    // When no builtinPlacements and popupAlign changed\n    useLayoutEffect(function () {\n      if (mergedOpen && !(builtinPlacements !== null && builtinPlacements !== void 0 && builtinPlacements[popupPlacement])) {\n        triggerAlign();\n      }\n    }, [JSON.stringify(popupAlign)]);\n    var alignedClassName = React.useMemo(function () {\n      var baseClassName = getAlignPopupClassName(builtinPlacements, prefixCls, alignInfo, alignPoint);\n      return classNames(baseClassName, getPopupClassNameFromAlign === null || getPopupClassNameFromAlign === void 0 ? void 0 : getPopupClassNameFromAlign(alignInfo));\n    }, [alignInfo, getPopupClassNameFromAlign, builtinPlacements, prefixCls, alignPoint]);\n    React.useImperativeHandle(ref, function () {\n      return {\n        forceAlign: triggerAlign\n      };\n    });\n\n    // ========================== Motion ============================\n    var onVisibleChanged = function onVisibleChanged(visible) {\n      setInMotion(false);\n      onAlign();\n      afterPopupVisibleChange === null || afterPopupVisibleChange === void 0 ? void 0 : afterPopupVisibleChange(visible);\n    };\n\n    // We will trigger align when motion is in prepare\n    var onPrepare = function onPrepare() {\n      return new Promise(function (resolve) {\n        setMotionPrepareResolve(function () {\n          return resolve;\n        });\n      });\n    };\n    useLayoutEffect(function () {\n      if (motionPrepareResolve) {\n        onAlign();\n        motionPrepareResolve();\n        setMotionPrepareResolve(null);\n      }\n    }, [motionPrepareResolve]);\n\n    // ========================== Stretch ===========================\n    var _React$useState15 = React.useState(0),\n      _React$useState16 = _slicedToArray(_React$useState15, 2),\n      targetWidth = _React$useState16[0],\n      setTargetWidth = _React$useState16[1];\n    var _React$useState17 = React.useState(0),\n      _React$useState18 = _slicedToArray(_React$useState17, 2),\n      targetHeight = _React$useState18[0],\n      setTargetHeight = _React$useState18[1];\n    var onTargetResize = function onTargetResize(_, ele) {\n      triggerAlign();\n      if (stretch) {\n        var rect = ele.getBoundingClientRect();\n        setTargetWidth(rect.width);\n        setTargetHeight(rect.height);\n      }\n    };\n\n    // =========================== Action ===========================\n    /**\n     * Util wrapper for trigger action\n     */\n    function wrapperAction(eventName, nextOpen, delay, preEvent) {\n      cloneProps[eventName] = function (event) {\n        var _originChildProps$eve;\n        preEvent === null || preEvent === void 0 ? void 0 : preEvent(event);\n        triggerOpen(nextOpen, delay);\n\n        // Pass to origin\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        (_originChildProps$eve = originChildProps[eventName]) === null || _originChildProps$eve === void 0 ? void 0 : _originChildProps$eve.call.apply(_originChildProps$eve, [originChildProps, event].concat(args));\n      };\n    }\n\n    // ======================= Action: Click ========================\n    if (clickToShow || clickToHide) {\n      cloneProps.onClick = function (event) {\n        var _originChildProps$onC;\n        if (openRef.current && clickToHide) {\n          triggerOpen(false);\n        } else if (!openRef.current && clickToShow) {\n          setMousePosByEvent(event);\n          triggerOpen(true);\n        }\n\n        // Pass to origin\n        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          args[_key2 - 1] = arguments[_key2];\n        }\n        (_originChildProps$onC = originChildProps.onClick) === null || _originChildProps$onC === void 0 ? void 0 : _originChildProps$onC.call.apply(_originChildProps$onC, [originChildProps, event].concat(args));\n      };\n    }\n\n    // Click to hide is special action since click popup element should not hide\n    useWinClick(mergedOpen, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen);\n\n    // ======================= Action: Hover ========================\n    var hoverToShow = showActions.has('hover');\n    var hoverToHide = hideActions.has('hover');\n    var onPopupMouseEnter;\n    var onPopupMouseLeave;\n    if (hoverToShow) {\n      // Compatible with old browser which not support pointer event\n      wrapperAction('onMouseEnter', true, mouseEnterDelay, function (event) {\n        setMousePosByEvent(event);\n      });\n      wrapperAction('onPointerEnter', true, mouseEnterDelay, function (event) {\n        setMousePosByEvent(event);\n      });\n      onPopupMouseEnter = function onPopupMouseEnter() {\n        // Only trigger re-open when popup is visible\n        if (mergedOpen || inMotion) {\n          triggerOpen(true, mouseEnterDelay);\n        }\n      };\n\n      // Align Point\n      if (alignPoint) {\n        cloneProps.onMouseMove = function (event) {\n          var _originChildProps$onM;\n          // setMousePosByEvent(event);\n          (_originChildProps$onM = originChildProps.onMouseMove) === null || _originChildProps$onM === void 0 ? void 0 : _originChildProps$onM.call(originChildProps, event);\n        };\n      }\n    }\n    if (hoverToHide) {\n      wrapperAction('onMouseLeave', false, mouseLeaveDelay);\n      wrapperAction('onPointerLeave', false, mouseLeaveDelay);\n      onPopupMouseLeave = function onPopupMouseLeave() {\n        triggerOpen(false, mouseLeaveDelay);\n      };\n    }\n\n    // ======================= Action: Focus ========================\n    if (showActions.has('focus')) {\n      wrapperAction('onFocus', true, focusDelay);\n    }\n    if (hideActions.has('focus')) {\n      wrapperAction('onBlur', false, blurDelay);\n    }\n\n    // ==================== Action: ContextMenu =====================\n    if (showActions.has('contextMenu')) {\n      cloneProps.onContextMenu = function (event) {\n        var _originChildProps$onC2;\n        if (openRef.current && hideActions.has('contextMenu')) {\n          triggerOpen(false);\n        } else {\n          setMousePosByEvent(event);\n          triggerOpen(true);\n        }\n        event.preventDefault();\n\n        // Pass to origin\n        for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n          args[_key3 - 1] = arguments[_key3];\n        }\n        (_originChildProps$onC2 = originChildProps.onContextMenu) === null || _originChildProps$onC2 === void 0 ? void 0 : _originChildProps$onC2.call.apply(_originChildProps$onC2, [originChildProps, event].concat(args));\n      };\n    }\n\n    // ========================= ClassName ==========================\n    if (className) {\n      cloneProps.className = classNames(originChildProps.className, className);\n    }\n\n    // =========================== Render ===========================\n    var mergedChildrenProps = _objectSpread(_objectSpread({}, originChildProps), cloneProps);\n\n    // Pass props into cloneProps for nest usage\n    var passedProps = {};\n    var passedEventList = ['onContextMenu', 'onClick', 'onMouseDown', 'onTouchStart', 'onMouseEnter', 'onMouseLeave', 'onFocus', 'onBlur'];\n    passedEventList.forEach(function (eventName) {\n      if (restProps[eventName]) {\n        passedProps[eventName] = function () {\n          var _mergedChildrenProps$;\n          for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n            args[_key4] = arguments[_key4];\n          }\n          (_mergedChildrenProps$ = mergedChildrenProps[eventName]) === null || _mergedChildrenProps$ === void 0 ? void 0 : _mergedChildrenProps$.call.apply(_mergedChildrenProps$, [mergedChildrenProps].concat(args));\n          restProps[eventName].apply(restProps, args);\n        };\n      }\n    });\n\n    // Child Node\n    var triggerNode = /*#__PURE__*/React.cloneElement(child, _objectSpread(_objectSpread({}, mergedChildrenProps), passedProps));\n    var arrowPos = {\n      x: arrowX,\n      y: arrowY\n    };\n    var innerArrow = arrow ? _objectSpread({}, arrow !== true ? arrow : {}) : null;\n\n    // Render\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ResizeObserver, {\n      disabled: !mergedOpen,\n      ref: setTargetRef,\n      onResize: onTargetResize\n    }, /*#__PURE__*/React.createElement(TriggerWrapper, {\n      getTriggerDOMNode: getTriggerDOMNode\n    }, triggerNode)), /*#__PURE__*/React.createElement(TriggerContext.Provider, {\n      value: context\n    }, /*#__PURE__*/React.createElement(Popup, {\n      portal: PortalComponent,\n      ref: setPopupRef,\n      prefixCls: prefixCls,\n      popup: popup,\n      className: classNames(popupClassName, alignedClassName),\n      style: popupStyle,\n      target: targetEle,\n      onMouseEnter: onPopupMouseEnter,\n      onMouseLeave: onPopupMouseLeave\n      // https://github.com/ant-design/ant-design/issues/43924\n      ,\n\n      onPointerEnter: onPopupMouseEnter,\n      zIndex: zIndex\n      // Open\n      ,\n\n      open: mergedOpen,\n      keepDom: inMotion\n      // Click\n      ,\n\n      onClick: onPopupClick\n      // Mask\n      ,\n\n      mask: mask\n      // Motion\n      ,\n\n      motion: mergePopupMotion,\n      maskMotion: mergeMaskMotion,\n      onVisibleChanged: onVisibleChanged,\n      onPrepare: onPrepare\n      // Portal\n      ,\n\n      forceRender: forceRender,\n      autoDestroy: mergedAutoDestroy,\n      getPopupContainer: getPopupContainer\n      // Arrow\n      ,\n\n      align: alignInfo,\n      arrow: innerArrow,\n      arrowPos: arrowPos\n      // Align\n      ,\n\n      ready: ready,\n      offsetX: offsetX,\n      offsetY: offsetY,\n      offsetR: offsetR,\n      offsetB: offsetB,\n      onAlign: triggerAlign\n      // Stretch\n      ,\n\n      stretch: stretch,\n      targetWidth: targetWidth / scaleX,\n      targetHeight: targetHeight / scaleY\n    })));\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    Trigger.displayName = 'Trigger';\n  }\n  return Trigger;\n}\nexport default generateTrigger(Portal);", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_excluded", "Portal", "classNames", "ResizeObserver", "isDOM", "getShadowRoot", "useEvent", "useId", "useLayoutEffect", "isMobile", "React", "flushSync", "TriggerContext", "useAction", "useAlign", "useWatch", "useWinClick", "Popup", "TriggerWrapper", "getAlignPopupClassName", "getMotion", "generateTrigger", "PortalComponent", "arguments", "length", "undefined", "<PERSON><PERSON>", "forwardRef", "props", "ref", "_props$prefixCls", "prefixCls", "children", "_props$action", "action", "showAction", "hideAction", "popupVisible", "defaultPopupVisible", "onPopupVisibleChange", "afterPopupVisibleChange", "mouseEnterDelay", "_props$mouseLeaveDela", "mouseLeaveDelay", "focusDelay", "blurDelay", "mask", "_props$maskClosable", "maskClosable", "getPopupContainer", "forceRender", "autoDestroy", "destroyPopupOnHide", "popup", "popupClassName", "popupStyle", "popupPlacement", "_props$builtinPlaceme", "builtinPlacements", "popupAlign", "zIndex", "stretch", "getPopupClassNameFromAlign", "alignPoint", "onPopupClick", "onPopupAlign", "arrow", "popupMotion", "maskMotion", "popupTransitionName", "popupAnimation", "maskTransitionName", "maskAnimation", "className", "getTriggerDOMNode", "restProps", "mergedAutoDestroy", "_React$useState", "useState", "_React$useState2", "mobile", "setMobile", "subPopupElements", "useRef", "parentContext", "useContext", "context", "useMemo", "registerSubPopup", "id", "subPopupEle", "current", "_React$useState3", "_React$useState4", "popup<PERSON>le", "setPopup<PERSON>le", "setPopupRef", "node", "_React$useState5", "_React$useState6", "targetEle", "setTarget<PERSON>le", "setTargetRef", "child", "Children", "only", "originChildProps", "cloneProps", "inPopupOrChild", "ele", "_getShadowRoot", "_getShadowRoot2", "childDOM", "contains", "host", "Object", "values", "some", "mergePopupMotion", "mergeMaskMotion", "_React$useState7", "_React$useState8", "internalOpen", "setInternalOpen", "mergedOpen", "setMergedOpen", "nextOpen", "openRef", "internalTriggerOpen", "delayRef", "clear<PERSON>elay", "clearTimeout", "triggerOpen", "delay", "setTimeout", "useEffect", "_React$useState9", "_React$useState10", "inMotion", "setInMotion", "firstMount", "_React$useState11", "_React$useState12", "motionPrepareResolve", "setMotionPrepareResolve", "_React$useState13", "_React$useState14", "mousePos", "setMousePos", "setMousePosByEvent", "event", "clientX", "clientY", "_useAlign", "_useAlign2", "ready", "offsetX", "offsetY", "offsetR", "offsetB", "arrowX", "arrowY", "scaleX", "scaleY", "alignInfo", "onAlign", "_useAction", "_useAction2", "showActions", "hideActions", "clickToShow", "has", "clickToHide", "triggerAlign", "onScroll", "JSON", "stringify", "alignedClassName", "baseClassName", "useImperativeHandle", "forceAlign", "onVisibleChanged", "visible", "onPrepare", "Promise", "resolve", "_React$useState15", "_React$useState16", "targetWidth", "set<PERSON><PERSON><PERSON><PERSON>id<PERSON>", "_React$useState17", "_React$useState18", "targetHeight", "setTargetHeight", "onTargetResize", "_", "rect", "getBoundingClientRect", "width", "height", "wrapperAction", "eventName", "preEvent", "_originChildProps$eve", "_len", "args", "Array", "_key", "call", "apply", "concat", "onClick", "_originChildProps$onC", "_len2", "_key2", "hoverToShow", "hoverToHide", "onPopupMouseEnter", "onPopupMouseLeave", "onMouseMove", "_originChildProps$onM", "onContextMenu", "_originChildProps$onC2", "preventDefault", "_len3", "_key3", "mergedChildrenProps", "passedProps", "passedEventList", "for<PERSON>ach", "_mergedChildrenProps$", "_len4", "_key4", "triggerNode", "cloneElement", "arrowPos", "x", "y", "innerArrow", "createElement", "Fragment", "disabled", "onResize", "Provider", "value", "portal", "style", "target", "onMouseEnter", "onMouseLeave", "onPointerEnter", "open", "keepDom", "motion", "align", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/trigger/es/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"children\", \"action\", \"showAction\", \"hideAction\", \"popupVisible\", \"defaultPopupVisible\", \"onPopupVisibleChange\", \"afterPopupVisibleChange\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"focusDelay\", \"blurDelay\", \"mask\", \"maskClosable\", \"getPopupContainer\", \"forceRender\", \"autoDestroy\", \"destroyPopupOnHide\", \"popup\", \"popupClassName\", \"popupStyle\", \"popupPlacement\", \"builtinPlacements\", \"popupAlign\", \"zIndex\", \"stretch\", \"getPopupClassNameFromAlign\", \"alignPoint\", \"onPopupClick\", \"onPopupAlign\", \"arrow\", \"popupMotion\", \"maskMotion\", \"popupTransitionName\", \"popupAnimation\", \"maskTransitionName\", \"maskAnimation\", \"className\", \"getTriggerDOMNode\"];\nimport Portal from '@rc-component/portal';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { isDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useId from \"rc-util/es/hooks/useId\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport * as React from 'react';\nimport { flushSync } from 'react-dom';\nimport TriggerContext from \"./context\";\nimport useAction from \"./hooks/useAction\";\nimport useAlign from \"./hooks/useAlign\";\nimport useWatch from \"./hooks/useWatch\";\nimport useWinClick from \"./hooks/useWinClick\";\nimport Popup from \"./Popup\";\nimport TriggerWrapper from \"./TriggerWrapper\";\nimport { getAlignPopupClassName, getMotion } from \"./util\";\n\n// Removed Props List\n// Seems this can be auto\n// getDocument?: (element?: HTMLElement) => Document;\n// New version will not wrap popup with `rc-trigger-popup-content` when multiple children\nexport function generateTrigger() {\n  var PortalComponent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Portal;\n  var Trigger = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-trigger-popup' : _props$prefixCls,\n      children = props.children,\n      _props$action = props.action,\n      action = _props$action === void 0 ? 'hover' : _props$action,\n      showAction = props.showAction,\n      hideAction = props.hideAction,\n      popupVisible = props.popupVisible,\n      defaultPopupVisible = props.defaultPopupVisible,\n      onPopupVisibleChange = props.onPopupVisibleChange,\n      afterPopupVisibleChange = props.afterPopupVisibleChange,\n      mouseEnterDelay = props.mouseEnterDelay,\n      _props$mouseLeaveDela = props.mouseLeaveDelay,\n      mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,\n      focusDelay = props.focusDelay,\n      blurDelay = props.blurDelay,\n      mask = props.mask,\n      _props$maskClosable = props.maskClosable,\n      maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,\n      getPopupContainer = props.getPopupContainer,\n      forceRender = props.forceRender,\n      autoDestroy = props.autoDestroy,\n      destroyPopupOnHide = props.destroyPopupOnHide,\n      popup = props.popup,\n      popupClassName = props.popupClassName,\n      popupStyle = props.popupStyle,\n      popupPlacement = props.popupPlacement,\n      _props$builtinPlaceme = props.builtinPlacements,\n      builtinPlacements = _props$builtinPlaceme === void 0 ? {} : _props$builtinPlaceme,\n      popupAlign = props.popupAlign,\n      zIndex = props.zIndex,\n      stretch = props.stretch,\n      getPopupClassNameFromAlign = props.getPopupClassNameFromAlign,\n      alignPoint = props.alignPoint,\n      onPopupClick = props.onPopupClick,\n      onPopupAlign = props.onPopupAlign,\n      arrow = props.arrow,\n      popupMotion = props.popupMotion,\n      maskMotion = props.maskMotion,\n      popupTransitionName = props.popupTransitionName,\n      popupAnimation = props.popupAnimation,\n      maskTransitionName = props.maskTransitionName,\n      maskAnimation = props.maskAnimation,\n      className = props.className,\n      getTriggerDOMNode = props.getTriggerDOMNode,\n      restProps = _objectWithoutProperties(props, _excluded);\n    var mergedAutoDestroy = autoDestroy || destroyPopupOnHide || false;\n\n    // =========================== Mobile ===========================\n    var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      mobile = _React$useState2[0],\n      setMobile = _React$useState2[1];\n    useLayoutEffect(function () {\n      setMobile(isMobile());\n    }, []);\n\n    // ========================== Context ===========================\n    var subPopupElements = React.useRef({});\n    var parentContext = React.useContext(TriggerContext);\n    var context = React.useMemo(function () {\n      return {\n        registerSubPopup: function registerSubPopup(id, subPopupEle) {\n          subPopupElements.current[id] = subPopupEle;\n          parentContext === null || parentContext === void 0 ? void 0 : parentContext.registerSubPopup(id, subPopupEle);\n        }\n      };\n    }, [parentContext]);\n\n    // =========================== Popup ============================\n    var id = useId();\n    var _React$useState3 = React.useState(null),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      popupEle = _React$useState4[0],\n      setPopupEle = _React$useState4[1];\n    var setPopupRef = useEvent(function (node) {\n      if (isDOM(node) && popupEle !== node) {\n        setPopupEle(node);\n      }\n      parentContext === null || parentContext === void 0 ? void 0 : parentContext.registerSubPopup(id, node);\n    });\n\n    // =========================== Target ===========================\n    // Use state to control here since `useRef` update not trigger render\n    var _React$useState5 = React.useState(null),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      targetEle = _React$useState6[0],\n      setTargetEle = _React$useState6[1];\n    var setTargetRef = useEvent(function (node) {\n      if (isDOM(node) && targetEle !== node) {\n        setTargetEle(node);\n      }\n    });\n\n    // ========================== Children ==========================\n    var child = React.Children.only(children);\n    var originChildProps = (child === null || child === void 0 ? void 0 : child.props) || {};\n    var cloneProps = {};\n    var inPopupOrChild = useEvent(function (ele) {\n      var _getShadowRoot, _getShadowRoot2;\n      var childDOM = targetEle;\n      return (childDOM === null || childDOM === void 0 ? void 0 : childDOM.contains(ele)) || ((_getShadowRoot = getShadowRoot(childDOM)) === null || _getShadowRoot === void 0 ? void 0 : _getShadowRoot.host) === ele || ele === childDOM || (popupEle === null || popupEle === void 0 ? void 0 : popupEle.contains(ele)) || ((_getShadowRoot2 = getShadowRoot(popupEle)) === null || _getShadowRoot2 === void 0 ? void 0 : _getShadowRoot2.host) === ele || ele === popupEle || Object.values(subPopupElements.current).some(function (subPopupEle) {\n        return (subPopupEle === null || subPopupEle === void 0 ? void 0 : subPopupEle.contains(ele)) || ele === subPopupEle;\n      });\n    });\n\n    // =========================== Motion ===========================\n    var mergePopupMotion = getMotion(prefixCls, popupMotion, popupAnimation, popupTransitionName);\n    var mergeMaskMotion = getMotion(prefixCls, maskMotion, maskAnimation, maskTransitionName);\n\n    // ============================ Open ============================\n    var _React$useState7 = React.useState(defaultPopupVisible || false),\n      _React$useState8 = _slicedToArray(_React$useState7, 2),\n      internalOpen = _React$useState8[0],\n      setInternalOpen = _React$useState8[1];\n\n    // Render still use props as first priority\n    var mergedOpen = popupVisible !== null && popupVisible !== void 0 ? popupVisible : internalOpen;\n\n    // We use effect sync here in case `popupVisible` back to `undefined`\n    var setMergedOpen = useEvent(function (nextOpen) {\n      if (popupVisible === undefined) {\n        setInternalOpen(nextOpen);\n      }\n    });\n    useLayoutEffect(function () {\n      setInternalOpen(popupVisible || false);\n    }, [popupVisible]);\n    var openRef = React.useRef(mergedOpen);\n    openRef.current = mergedOpen;\n    var internalTriggerOpen = useEvent(function (nextOpen) {\n      // Enter or Pointer will both trigger open state change\n      // We only need take one to avoid duplicated change event trigger\n      flushSync(function () {\n        if (mergedOpen !== nextOpen) {\n          setMergedOpen(nextOpen);\n          onPopupVisibleChange === null || onPopupVisibleChange === void 0 ? void 0 : onPopupVisibleChange(nextOpen);\n        }\n      });\n    });\n\n    // Trigger for delay\n    var delayRef = React.useRef();\n    var clearDelay = function clearDelay() {\n      clearTimeout(delayRef.current);\n    };\n    var triggerOpen = function triggerOpen(nextOpen) {\n      var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      clearDelay();\n      if (delay === 0) {\n        internalTriggerOpen(nextOpen);\n      } else {\n        delayRef.current = setTimeout(function () {\n          internalTriggerOpen(nextOpen);\n        }, delay * 1000);\n      }\n    };\n    React.useEffect(function () {\n      return clearDelay;\n    }, []);\n\n    // ========================== Motion ============================\n    var _React$useState9 = React.useState(false),\n      _React$useState10 = _slicedToArray(_React$useState9, 2),\n      inMotion = _React$useState10[0],\n      setInMotion = _React$useState10[1];\n    useLayoutEffect(function (firstMount) {\n      if (!firstMount || mergedOpen) {\n        setInMotion(true);\n      }\n    }, [mergedOpen]);\n    var _React$useState11 = React.useState(null),\n      _React$useState12 = _slicedToArray(_React$useState11, 2),\n      motionPrepareResolve = _React$useState12[0],\n      setMotionPrepareResolve = _React$useState12[1];\n\n    // =========================== Align ============================\n    var _React$useState13 = React.useState([0, 0]),\n      _React$useState14 = _slicedToArray(_React$useState13, 2),\n      mousePos = _React$useState14[0],\n      setMousePos = _React$useState14[1];\n    var setMousePosByEvent = function setMousePosByEvent(event) {\n      setMousePos([event.clientX, event.clientY]);\n    };\n    var _useAlign = useAlign(mergedOpen, popupEle, alignPoint ? mousePos : targetEle, popupPlacement, builtinPlacements, popupAlign, onPopupAlign),\n      _useAlign2 = _slicedToArray(_useAlign, 11),\n      ready = _useAlign2[0],\n      offsetX = _useAlign2[1],\n      offsetY = _useAlign2[2],\n      offsetR = _useAlign2[3],\n      offsetB = _useAlign2[4],\n      arrowX = _useAlign2[5],\n      arrowY = _useAlign2[6],\n      scaleX = _useAlign2[7],\n      scaleY = _useAlign2[8],\n      alignInfo = _useAlign2[9],\n      onAlign = _useAlign2[10];\n    var _useAction = useAction(mobile, action, showAction, hideAction),\n      _useAction2 = _slicedToArray(_useAction, 2),\n      showActions = _useAction2[0],\n      hideActions = _useAction2[1];\n    var clickToShow = showActions.has('click');\n    var clickToHide = hideActions.has('click') || hideActions.has('contextMenu');\n    var triggerAlign = useEvent(function () {\n      if (!inMotion) {\n        onAlign();\n      }\n    });\n    var onScroll = function onScroll() {\n      if (openRef.current && alignPoint && clickToHide) {\n        triggerOpen(false);\n      }\n    };\n    useWatch(mergedOpen, targetEle, popupEle, triggerAlign, onScroll);\n    useLayoutEffect(function () {\n      triggerAlign();\n    }, [mousePos, popupPlacement]);\n\n    // When no builtinPlacements and popupAlign changed\n    useLayoutEffect(function () {\n      if (mergedOpen && !(builtinPlacements !== null && builtinPlacements !== void 0 && builtinPlacements[popupPlacement])) {\n        triggerAlign();\n      }\n    }, [JSON.stringify(popupAlign)]);\n    var alignedClassName = React.useMemo(function () {\n      var baseClassName = getAlignPopupClassName(builtinPlacements, prefixCls, alignInfo, alignPoint);\n      return classNames(baseClassName, getPopupClassNameFromAlign === null || getPopupClassNameFromAlign === void 0 ? void 0 : getPopupClassNameFromAlign(alignInfo));\n    }, [alignInfo, getPopupClassNameFromAlign, builtinPlacements, prefixCls, alignPoint]);\n    React.useImperativeHandle(ref, function () {\n      return {\n        forceAlign: triggerAlign\n      };\n    });\n\n    // ========================== Motion ============================\n    var onVisibleChanged = function onVisibleChanged(visible) {\n      setInMotion(false);\n      onAlign();\n      afterPopupVisibleChange === null || afterPopupVisibleChange === void 0 ? void 0 : afterPopupVisibleChange(visible);\n    };\n\n    // We will trigger align when motion is in prepare\n    var onPrepare = function onPrepare() {\n      return new Promise(function (resolve) {\n        setMotionPrepareResolve(function () {\n          return resolve;\n        });\n      });\n    };\n    useLayoutEffect(function () {\n      if (motionPrepareResolve) {\n        onAlign();\n        motionPrepareResolve();\n        setMotionPrepareResolve(null);\n      }\n    }, [motionPrepareResolve]);\n\n    // ========================== Stretch ===========================\n    var _React$useState15 = React.useState(0),\n      _React$useState16 = _slicedToArray(_React$useState15, 2),\n      targetWidth = _React$useState16[0],\n      setTargetWidth = _React$useState16[1];\n    var _React$useState17 = React.useState(0),\n      _React$useState18 = _slicedToArray(_React$useState17, 2),\n      targetHeight = _React$useState18[0],\n      setTargetHeight = _React$useState18[1];\n    var onTargetResize = function onTargetResize(_, ele) {\n      triggerAlign();\n      if (stretch) {\n        var rect = ele.getBoundingClientRect();\n        setTargetWidth(rect.width);\n        setTargetHeight(rect.height);\n      }\n    };\n\n    // =========================== Action ===========================\n    /**\n     * Util wrapper for trigger action\n     */\n    function wrapperAction(eventName, nextOpen, delay, preEvent) {\n      cloneProps[eventName] = function (event) {\n        var _originChildProps$eve;\n        preEvent === null || preEvent === void 0 ? void 0 : preEvent(event);\n        triggerOpen(nextOpen, delay);\n\n        // Pass to origin\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        (_originChildProps$eve = originChildProps[eventName]) === null || _originChildProps$eve === void 0 ? void 0 : _originChildProps$eve.call.apply(_originChildProps$eve, [originChildProps, event].concat(args));\n      };\n    }\n\n    // ======================= Action: Click ========================\n    if (clickToShow || clickToHide) {\n      cloneProps.onClick = function (event) {\n        var _originChildProps$onC;\n        if (openRef.current && clickToHide) {\n          triggerOpen(false);\n        } else if (!openRef.current && clickToShow) {\n          setMousePosByEvent(event);\n          triggerOpen(true);\n        }\n\n        // Pass to origin\n        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          args[_key2 - 1] = arguments[_key2];\n        }\n        (_originChildProps$onC = originChildProps.onClick) === null || _originChildProps$onC === void 0 ? void 0 : _originChildProps$onC.call.apply(_originChildProps$onC, [originChildProps, event].concat(args));\n      };\n    }\n\n    // Click to hide is special action since click popup element should not hide\n    useWinClick(mergedOpen, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen);\n\n    // ======================= Action: Hover ========================\n    var hoverToShow = showActions.has('hover');\n    var hoverToHide = hideActions.has('hover');\n    var onPopupMouseEnter;\n    var onPopupMouseLeave;\n    if (hoverToShow) {\n      // Compatible with old browser which not support pointer event\n      wrapperAction('onMouseEnter', true, mouseEnterDelay, function (event) {\n        setMousePosByEvent(event);\n      });\n      wrapperAction('onPointerEnter', true, mouseEnterDelay, function (event) {\n        setMousePosByEvent(event);\n      });\n      onPopupMouseEnter = function onPopupMouseEnter() {\n        // Only trigger re-open when popup is visible\n        if (mergedOpen || inMotion) {\n          triggerOpen(true, mouseEnterDelay);\n        }\n      };\n\n      // Align Point\n      if (alignPoint) {\n        cloneProps.onMouseMove = function (event) {\n          var _originChildProps$onM;\n          // setMousePosByEvent(event);\n          (_originChildProps$onM = originChildProps.onMouseMove) === null || _originChildProps$onM === void 0 ? void 0 : _originChildProps$onM.call(originChildProps, event);\n        };\n      }\n    }\n    if (hoverToHide) {\n      wrapperAction('onMouseLeave', false, mouseLeaveDelay);\n      wrapperAction('onPointerLeave', false, mouseLeaveDelay);\n      onPopupMouseLeave = function onPopupMouseLeave() {\n        triggerOpen(false, mouseLeaveDelay);\n      };\n    }\n\n    // ======================= Action: Focus ========================\n    if (showActions.has('focus')) {\n      wrapperAction('onFocus', true, focusDelay);\n    }\n    if (hideActions.has('focus')) {\n      wrapperAction('onBlur', false, blurDelay);\n    }\n\n    // ==================== Action: ContextMenu =====================\n    if (showActions.has('contextMenu')) {\n      cloneProps.onContextMenu = function (event) {\n        var _originChildProps$onC2;\n        if (openRef.current && hideActions.has('contextMenu')) {\n          triggerOpen(false);\n        } else {\n          setMousePosByEvent(event);\n          triggerOpen(true);\n        }\n        event.preventDefault();\n\n        // Pass to origin\n        for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n          args[_key3 - 1] = arguments[_key3];\n        }\n        (_originChildProps$onC2 = originChildProps.onContextMenu) === null || _originChildProps$onC2 === void 0 ? void 0 : _originChildProps$onC2.call.apply(_originChildProps$onC2, [originChildProps, event].concat(args));\n      };\n    }\n\n    // ========================= ClassName ==========================\n    if (className) {\n      cloneProps.className = classNames(originChildProps.className, className);\n    }\n\n    // =========================== Render ===========================\n    var mergedChildrenProps = _objectSpread(_objectSpread({}, originChildProps), cloneProps);\n\n    // Pass props into cloneProps for nest usage\n    var passedProps = {};\n    var passedEventList = ['onContextMenu', 'onClick', 'onMouseDown', 'onTouchStart', 'onMouseEnter', 'onMouseLeave', 'onFocus', 'onBlur'];\n    passedEventList.forEach(function (eventName) {\n      if (restProps[eventName]) {\n        passedProps[eventName] = function () {\n          var _mergedChildrenProps$;\n          for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n            args[_key4] = arguments[_key4];\n          }\n          (_mergedChildrenProps$ = mergedChildrenProps[eventName]) === null || _mergedChildrenProps$ === void 0 ? void 0 : _mergedChildrenProps$.call.apply(_mergedChildrenProps$, [mergedChildrenProps].concat(args));\n          restProps[eventName].apply(restProps, args);\n        };\n      }\n    });\n\n    // Child Node\n    var triggerNode = /*#__PURE__*/React.cloneElement(child, _objectSpread(_objectSpread({}, mergedChildrenProps), passedProps));\n    var arrowPos = {\n      x: arrowX,\n      y: arrowY\n    };\n    var innerArrow = arrow ? _objectSpread({}, arrow !== true ? arrow : {}) : null;\n\n    // Render\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ResizeObserver, {\n      disabled: !mergedOpen,\n      ref: setTargetRef,\n      onResize: onTargetResize\n    }, /*#__PURE__*/React.createElement(TriggerWrapper, {\n      getTriggerDOMNode: getTriggerDOMNode\n    }, triggerNode)), /*#__PURE__*/React.createElement(TriggerContext.Provider, {\n      value: context\n    }, /*#__PURE__*/React.createElement(Popup, {\n      portal: PortalComponent,\n      ref: setPopupRef,\n      prefixCls: prefixCls,\n      popup: popup,\n      className: classNames(popupClassName, alignedClassName),\n      style: popupStyle,\n      target: targetEle,\n      onMouseEnter: onPopupMouseEnter,\n      onMouseLeave: onPopupMouseLeave\n      // https://github.com/ant-design/ant-design/issues/43924\n      ,\n      onPointerEnter: onPopupMouseEnter,\n      zIndex: zIndex\n      // Open\n      ,\n      open: mergedOpen,\n      keepDom: inMotion\n      // Click\n      ,\n      onClick: onPopupClick\n      // Mask\n      ,\n      mask: mask\n      // Motion\n      ,\n      motion: mergePopupMotion,\n      maskMotion: mergeMaskMotion,\n      onVisibleChanged: onVisibleChanged,\n      onPrepare: onPrepare\n      // Portal\n      ,\n      forceRender: forceRender,\n      autoDestroy: mergedAutoDestroy,\n      getPopupContainer: getPopupContainer\n      // Arrow\n      ,\n      align: alignInfo,\n      arrow: innerArrow,\n      arrowPos: arrowPos\n      // Align\n      ,\n      ready: ready,\n      offsetX: offsetX,\n      offsetY: offsetY,\n      offsetR: offsetR,\n      offsetB: offsetB,\n      onAlign: triggerAlign\n      // Stretch\n      ,\n      stretch: stretch,\n      targetWidth: targetWidth / scaleX,\n      targetHeight: targetHeight / scaleY\n    })));\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    Trigger.displayName = 'Trigger';\n  }\n  return Trigger;\n}\nexport default generateTrigger(Portal);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,mBAAmB,EAAE,aAAa,EAAE,aAAa,EAAE,oBAAoB,EAAE,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,4BAA4B,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,eAAe,EAAE,WAAW,EAAE,mBAAmB,CAAC;AAClqB,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,KAAK,QAAQ,4BAA4B;AAClD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,sBAAsB,EAAEC,SAAS,QAAQ,QAAQ;;AAE1D;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,IAAIC,eAAe,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGtB,MAAM;EAChG,IAAIyB,OAAO,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAChE,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;MACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,kBAAkB,GAAGA,gBAAgB;MAC/EE,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;MACzBC,aAAa,GAAGL,KAAK,CAACM,MAAM;MAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,aAAa;MAC3DE,UAAU,GAAGP,KAAK,CAACO,UAAU;MAC7BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;MAC7BC,YAAY,GAAGT,KAAK,CAACS,YAAY;MACjCC,mBAAmB,GAAGV,KAAK,CAACU,mBAAmB;MAC/CC,oBAAoB,GAAGX,KAAK,CAACW,oBAAoB;MACjDC,uBAAuB,GAAGZ,KAAK,CAACY,uBAAuB;MACvDC,eAAe,GAAGb,KAAK,CAACa,eAAe;MACvCC,qBAAqB,GAAGd,KAAK,CAACe,eAAe;MAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;MAChFE,UAAU,GAAGhB,KAAK,CAACgB,UAAU;MAC7BC,SAAS,GAAGjB,KAAK,CAACiB,SAAS;MAC3BC,IAAI,GAAGlB,KAAK,CAACkB,IAAI;MACjBC,mBAAmB,GAAGnB,KAAK,CAACoB,YAAY;MACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,mBAAmB;MAC1EE,iBAAiB,GAAGrB,KAAK,CAACqB,iBAAiB;MAC3CC,WAAW,GAAGtB,KAAK,CAACsB,WAAW;MAC/BC,WAAW,GAAGvB,KAAK,CAACuB,WAAW;MAC/BC,kBAAkB,GAAGxB,KAAK,CAACwB,kBAAkB;MAC7CC,KAAK,GAAGzB,KAAK,CAACyB,KAAK;MACnBC,cAAc,GAAG1B,KAAK,CAAC0B,cAAc;MACrCC,UAAU,GAAG3B,KAAK,CAAC2B,UAAU;MAC7BC,cAAc,GAAG5B,KAAK,CAAC4B,cAAc;MACrCC,qBAAqB,GAAG7B,KAAK,CAAC8B,iBAAiB;MAC/CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;MACjFE,UAAU,GAAG/B,KAAK,CAAC+B,UAAU;MAC7BC,MAAM,GAAGhC,KAAK,CAACgC,MAAM;MACrBC,OAAO,GAAGjC,KAAK,CAACiC,OAAO;MACvBC,0BAA0B,GAAGlC,KAAK,CAACkC,0BAA0B;MAC7DC,UAAU,GAAGnC,KAAK,CAACmC,UAAU;MAC7BC,YAAY,GAAGpC,KAAK,CAACoC,YAAY;MACjCC,YAAY,GAAGrC,KAAK,CAACqC,YAAY;MACjCC,KAAK,GAAGtC,KAAK,CAACsC,KAAK;MACnBC,WAAW,GAAGvC,KAAK,CAACuC,WAAW;MAC/BC,UAAU,GAAGxC,KAAK,CAACwC,UAAU;MAC7BC,mBAAmB,GAAGzC,KAAK,CAACyC,mBAAmB;MAC/CC,cAAc,GAAG1C,KAAK,CAAC0C,cAAc;MACrCC,kBAAkB,GAAG3C,KAAK,CAAC2C,kBAAkB;MAC7CC,aAAa,GAAG5C,KAAK,CAAC4C,aAAa;MACnCC,SAAS,GAAG7C,KAAK,CAAC6C,SAAS;MAC3BC,iBAAiB,GAAG9C,KAAK,CAAC8C,iBAAiB;MAC3CC,SAAS,GAAG5E,wBAAwB,CAAC6B,KAAK,EAAE5B,SAAS,CAAC;IACxD,IAAI4E,iBAAiB,GAAGzB,WAAW,IAAIC,kBAAkB,IAAI,KAAK;;IAElE;IACA,IAAIyB,eAAe,GAAGnE,KAAK,CAACoE,QAAQ,CAAC,KAAK,CAAC;MACzCC,gBAAgB,GAAGjF,cAAc,CAAC+E,eAAe,EAAE,CAAC,CAAC;MACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;MAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IACjCvE,eAAe,CAAC,YAAY;MAC1ByE,SAAS,CAACxE,QAAQ,CAAC,CAAC,CAAC;IACvB,CAAC,EAAE,EAAE,CAAC;;IAEN;IACA,IAAIyE,gBAAgB,GAAGxE,KAAK,CAACyE,MAAM,CAAC,CAAC,CAAC,CAAC;IACvC,IAAIC,aAAa,GAAG1E,KAAK,CAAC2E,UAAU,CAACzE,cAAc,CAAC;IACpD,IAAI0E,OAAO,GAAG5E,KAAK,CAAC6E,OAAO,CAAC,YAAY;MACtC,OAAO;QACLC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACC,EAAE,EAAEC,WAAW,EAAE;UAC3DR,gBAAgB,CAACS,OAAO,CAACF,EAAE,CAAC,GAAGC,WAAW;UAC1CN,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACI,gBAAgB,CAACC,EAAE,EAAEC,WAAW,CAAC;QAC/G;MACF,CAAC;IACH,CAAC,EAAE,CAACN,aAAa,CAAC,CAAC;;IAEnB;IACA,IAAIK,EAAE,GAAGlF,KAAK,CAAC,CAAC;IAChB,IAAIqF,gBAAgB,GAAGlF,KAAK,CAACoE,QAAQ,CAAC,IAAI,CAAC;MACzCe,gBAAgB,GAAG/F,cAAc,CAAC8F,gBAAgB,EAAE,CAAC,CAAC;MACtDE,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;MAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IACnC,IAAIG,WAAW,GAAG1F,QAAQ,CAAC,UAAU2F,IAAI,EAAE;MACzC,IAAI7F,KAAK,CAAC6F,IAAI,CAAC,IAAIH,QAAQ,KAAKG,IAAI,EAAE;QACpCF,WAAW,CAACE,IAAI,CAAC;MACnB;MACAb,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACI,gBAAgB,CAACC,EAAE,EAAEQ,IAAI,CAAC;IACxG,CAAC,CAAC;;IAEF;IACA;IACA,IAAIC,gBAAgB,GAAGxF,KAAK,CAACoE,QAAQ,CAAC,IAAI,CAAC;MACzCqB,gBAAgB,GAAGrG,cAAc,CAACoG,gBAAgB,EAAE,CAAC,CAAC;MACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;MAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IACpC,IAAIG,YAAY,GAAGhG,QAAQ,CAAC,UAAU2F,IAAI,EAAE;MAC1C,IAAI7F,KAAK,CAAC6F,IAAI,CAAC,IAAIG,SAAS,KAAKH,IAAI,EAAE;QACrCI,YAAY,CAACJ,IAAI,CAAC;MACpB;IACF,CAAC,CAAC;;IAEF;IACA,IAAIM,KAAK,GAAG7F,KAAK,CAAC8F,QAAQ,CAACC,IAAI,CAACzE,QAAQ,CAAC;IACzC,IAAI0E,gBAAgB,GAAG,CAACH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC3E,KAAK,KAAK,CAAC,CAAC;IACxF,IAAI+E,UAAU,GAAG,CAAC,CAAC;IACnB,IAAIC,cAAc,GAAGtG,QAAQ,CAAC,UAAUuG,GAAG,EAAE;MAC3C,IAAIC,cAAc,EAAEC,eAAe;MACnC,IAAIC,QAAQ,GAAGZ,SAAS;MACxB,OAAO,CAACY,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,QAAQ,CAACJ,GAAG,CAAC,KAAK,CAAC,CAACC,cAAc,GAAGzG,aAAa,CAAC2G,QAAQ,CAAC,MAAM,IAAI,IAAIF,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,IAAI,MAAML,GAAG,IAAIA,GAAG,KAAKG,QAAQ,KAAKlB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACmB,QAAQ,CAACJ,GAAG,CAAC,CAAC,IAAI,CAAC,CAACE,eAAe,GAAG1G,aAAa,CAACyF,QAAQ,CAAC,MAAM,IAAI,IAAIiB,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACG,IAAI,MAAML,GAAG,IAAIA,GAAG,KAAKf,QAAQ,IAAIqB,MAAM,CAACC,MAAM,CAAClC,gBAAgB,CAACS,OAAO,CAAC,CAAC0B,IAAI,CAAC,UAAU3B,WAAW,EAAE;QAC9gB,OAAO,CAACA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACuB,QAAQ,CAACJ,GAAG,CAAC,KAAKA,GAAG,KAAKnB,WAAW;MACrH,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,IAAI4B,gBAAgB,GAAGlG,SAAS,CAACW,SAAS,EAAEoC,WAAW,EAAEG,cAAc,EAAED,mBAAmB,CAAC;IAC7F,IAAIkD,eAAe,GAAGnG,SAAS,CAACW,SAAS,EAAEqC,UAAU,EAAEI,aAAa,EAAED,kBAAkB,CAAC;;IAEzF;IACA,IAAIiD,gBAAgB,GAAG9G,KAAK,CAACoE,QAAQ,CAACxC,mBAAmB,IAAI,KAAK,CAAC;MACjEmF,gBAAgB,GAAG3H,cAAc,CAAC0H,gBAAgB,EAAE,CAAC,CAAC;MACtDE,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;MAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;IAEvC;IACA,IAAIG,UAAU,GAAGvF,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGqF,YAAY;;IAE/F;IACA,IAAIG,aAAa,GAAGvH,QAAQ,CAAC,UAAUwH,QAAQ,EAAE;MAC/C,IAAIzF,YAAY,KAAKZ,SAAS,EAAE;QAC9BkG,eAAe,CAACG,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC;IACFtH,eAAe,CAAC,YAAY;MAC1BmH,eAAe,CAACtF,YAAY,IAAI,KAAK,CAAC;IACxC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;IAClB,IAAI0F,OAAO,GAAGrH,KAAK,CAACyE,MAAM,CAACyC,UAAU,CAAC;IACtCG,OAAO,CAACpC,OAAO,GAAGiC,UAAU;IAC5B,IAAII,mBAAmB,GAAG1H,QAAQ,CAAC,UAAUwH,QAAQ,EAAE;MACrD;MACA;MACAnH,SAAS,CAAC,YAAY;QACpB,IAAIiH,UAAU,KAAKE,QAAQ,EAAE;UAC3BD,aAAa,CAACC,QAAQ,CAAC;UACvBvF,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACuF,QAAQ,CAAC;QAC5G;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,IAAIG,QAAQ,GAAGvH,KAAK,CAACyE,MAAM,CAAC,CAAC;IAC7B,IAAI+C,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;MACrCC,YAAY,CAACF,QAAQ,CAACtC,OAAO,CAAC;IAChC,CAAC;IACD,IAAIyC,WAAW,GAAG,SAASA,WAAWA,CAACN,QAAQ,EAAE;MAC/C,IAAIO,KAAK,GAAG9G,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;MACjF2G,UAAU,CAAC,CAAC;MACZ,IAAIG,KAAK,KAAK,CAAC,EAAE;QACfL,mBAAmB,CAACF,QAAQ,CAAC;MAC/B,CAAC,MAAM;QACLG,QAAQ,CAACtC,OAAO,GAAG2C,UAAU,CAAC,YAAY;UACxCN,mBAAmB,CAACF,QAAQ,CAAC;QAC/B,CAAC,EAAEO,KAAK,GAAG,IAAI,CAAC;MAClB;IACF,CAAC;IACD3H,KAAK,CAAC6H,SAAS,CAAC,YAAY;MAC1B,OAAOL,UAAU;IACnB,CAAC,EAAE,EAAE,CAAC;;IAEN;IACA,IAAIM,gBAAgB,GAAG9H,KAAK,CAACoE,QAAQ,CAAC,KAAK,CAAC;MAC1C2D,iBAAiB,GAAG3I,cAAc,CAAC0I,gBAAgB,EAAE,CAAC,CAAC;MACvDE,QAAQ,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MAC/BE,WAAW,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACpCjI,eAAe,CAAC,UAAUoI,UAAU,EAAE;MACpC,IAAI,CAACA,UAAU,IAAIhB,UAAU,EAAE;QAC7Be,WAAW,CAAC,IAAI,CAAC;MACnB;IACF,CAAC,EAAE,CAACf,UAAU,CAAC,CAAC;IAChB,IAAIiB,iBAAiB,GAAGnI,KAAK,CAACoE,QAAQ,CAAC,IAAI,CAAC;MAC1CgE,iBAAiB,GAAGhJ,cAAc,CAAC+I,iBAAiB,EAAE,CAAC,CAAC;MACxDE,oBAAoB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MAC3CE,uBAAuB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;;IAEhD;IACA,IAAIG,iBAAiB,GAAGvI,KAAK,CAACoE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5CoE,iBAAiB,GAAGpJ,cAAc,CAACmJ,iBAAiB,EAAE,CAAC,CAAC;MACxDE,QAAQ,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MAC/BE,WAAW,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACpC,IAAIG,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAE;MAC1DF,WAAW,CAAC,CAACE,KAAK,CAACC,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC;IAC7C,CAAC;IACD,IAAIC,SAAS,GAAG3I,QAAQ,CAAC8G,UAAU,EAAE9B,QAAQ,EAAE/B,UAAU,GAAGoF,QAAQ,GAAG/C,SAAS,EAAE5C,cAAc,EAAEE,iBAAiB,EAAEC,UAAU,EAAEM,YAAY,CAAC;MAC5IyF,UAAU,GAAG5J,cAAc,CAAC2J,SAAS,EAAE,EAAE,CAAC;MAC1CE,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;MACrBE,OAAO,GAAGF,UAAU,CAAC,CAAC,CAAC;MACvBG,OAAO,GAAGH,UAAU,CAAC,CAAC,CAAC;MACvBI,OAAO,GAAGJ,UAAU,CAAC,CAAC,CAAC;MACvBK,OAAO,GAAGL,UAAU,CAAC,CAAC,CAAC;MACvBM,MAAM,GAAGN,UAAU,CAAC,CAAC,CAAC;MACtBO,MAAM,GAAGP,UAAU,CAAC,CAAC,CAAC;MACtBQ,MAAM,GAAGR,UAAU,CAAC,CAAC,CAAC;MACtBS,MAAM,GAAGT,UAAU,CAAC,CAAC,CAAC;MACtBU,SAAS,GAAGV,UAAU,CAAC,CAAC,CAAC;MACzBW,OAAO,GAAGX,UAAU,CAAC,EAAE,CAAC;IAC1B,IAAIY,UAAU,GAAGzJ,SAAS,CAACmE,MAAM,EAAE9C,MAAM,EAAEC,UAAU,EAAEC,UAAU,CAAC;MAChEmI,WAAW,GAAGzK,cAAc,CAACwK,UAAU,EAAE,CAAC,CAAC;MAC3CE,WAAW,GAAGD,WAAW,CAAC,CAAC,CAAC;MAC5BE,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;IAC9B,IAAIG,WAAW,GAAGF,WAAW,CAACG,GAAG,CAAC,OAAO,CAAC;IAC1C,IAAIC,WAAW,GAAGH,WAAW,CAACE,GAAG,CAAC,OAAO,CAAC,IAAIF,WAAW,CAACE,GAAG,CAAC,aAAa,CAAC;IAC5E,IAAIE,YAAY,GAAGvK,QAAQ,CAAC,YAAY;MACtC,IAAI,CAACoI,QAAQ,EAAE;QACb2B,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;IACF,IAAIS,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;MACjC,IAAI/C,OAAO,CAACpC,OAAO,IAAI5B,UAAU,IAAI6G,WAAW,EAAE;QAChDxC,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC;IACDrH,QAAQ,CAAC6G,UAAU,EAAExB,SAAS,EAAEN,QAAQ,EAAE+E,YAAY,EAAEC,QAAQ,CAAC;IACjEtK,eAAe,CAAC,YAAY;MAC1BqK,YAAY,CAAC,CAAC;IAChB,CAAC,EAAE,CAAC1B,QAAQ,EAAE3F,cAAc,CAAC,CAAC;;IAE9B;IACAhD,eAAe,CAAC,YAAY;MAC1B,IAAIoH,UAAU,IAAI,EAAElE,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACF,cAAc,CAAC,CAAC,EAAE;QACpHqH,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,EAAE,CAACE,IAAI,CAACC,SAAS,CAACrH,UAAU,CAAC,CAAC,CAAC;IAChC,IAAIsH,gBAAgB,GAAGvK,KAAK,CAAC6E,OAAO,CAAC,YAAY;MAC/C,IAAI2F,aAAa,GAAG/J,sBAAsB,CAACuC,iBAAiB,EAAE3B,SAAS,EAAEqI,SAAS,EAAErG,UAAU,CAAC;MAC/F,OAAO7D,UAAU,CAACgL,aAAa,EAAEpH,0BAA0B,KAAK,IAAI,IAAIA,0BAA0B,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,0BAA0B,CAACsG,SAAS,CAAC,CAAC;IACjK,CAAC,EAAE,CAACA,SAAS,EAAEtG,0BAA0B,EAAEJ,iBAAiB,EAAE3B,SAAS,EAAEgC,UAAU,CAAC,CAAC;IACrFrD,KAAK,CAACyK,mBAAmB,CAACtJ,GAAG,EAAE,YAAY;MACzC,OAAO;QACLuJ,UAAU,EAAEP;MACd,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,IAAIQ,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,OAAO,EAAE;MACxD3C,WAAW,CAAC,KAAK,CAAC;MAClB0B,OAAO,CAAC,CAAC;MACT7H,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAAC8I,OAAO,CAAC;IACpH,CAAC;;IAED;IACA,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;MACnC,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;QACpCzC,uBAAuB,CAAC,YAAY;UAClC,OAAOyC,OAAO;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDjL,eAAe,CAAC,YAAY;MAC1B,IAAIuI,oBAAoB,EAAE;QACxBsB,OAAO,CAAC,CAAC;QACTtB,oBAAoB,CAAC,CAAC;QACtBC,uBAAuB,CAAC,IAAI,CAAC;MAC/B;IACF,CAAC,EAAE,CAACD,oBAAoB,CAAC,CAAC;;IAE1B;IACA,IAAI2C,iBAAiB,GAAGhL,KAAK,CAACoE,QAAQ,CAAC,CAAC,CAAC;MACvC6G,iBAAiB,GAAG7L,cAAc,CAAC4L,iBAAiB,EAAE,CAAC,CAAC;MACxDE,WAAW,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MAClCE,cAAc,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACvC,IAAIG,iBAAiB,GAAGpL,KAAK,CAACoE,QAAQ,CAAC,CAAC,CAAC;MACvCiH,iBAAiB,GAAGjM,cAAc,CAACgM,iBAAiB,EAAE,CAAC,CAAC;MACxDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MACnCE,eAAe,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACxC,IAAIG,cAAc,GAAG,SAASA,cAAcA,CAACC,CAAC,EAAEtF,GAAG,EAAE;MACnDgE,YAAY,CAAC,CAAC;MACd,IAAIhH,OAAO,EAAE;QACX,IAAIuI,IAAI,GAAGvF,GAAG,CAACwF,qBAAqB,CAAC,CAAC;QACtCR,cAAc,CAACO,IAAI,CAACE,KAAK,CAAC;QAC1BL,eAAe,CAACG,IAAI,CAACG,MAAM,CAAC;MAC9B;IACF,CAAC;;IAED;IACA;AACJ;AACA;IACI,SAASC,aAAaA,CAACC,SAAS,EAAE3E,QAAQ,EAAEO,KAAK,EAAEqE,QAAQ,EAAE;MAC3D/F,UAAU,CAAC8F,SAAS,CAAC,GAAG,UAAUnD,KAAK,EAAE;QACvC,IAAIqD,qBAAqB;QACzBD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACpD,KAAK,CAAC;QACnElB,WAAW,CAACN,QAAQ,EAAEO,KAAK,CAAC;;QAE5B;QACA,KAAK,IAAIuE,IAAI,GAAGrL,SAAS,CAACC,MAAM,EAAEqL,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;UAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGxL,SAAS,CAACwL,IAAI,CAAC;QAClC;QACA,CAACJ,qBAAqB,GAAGjG,gBAAgB,CAAC+F,SAAS,CAAC,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACK,IAAI,CAACC,KAAK,CAACN,qBAAqB,EAAE,CAACjG,gBAAgB,EAAE4C,KAAK,CAAC,CAAC4D,MAAM,CAACL,IAAI,CAAC,CAAC;MAC/M,CAAC;IACH;;IAEA;IACA,IAAInC,WAAW,IAAIE,WAAW,EAAE;MAC9BjE,UAAU,CAACwG,OAAO,GAAG,UAAU7D,KAAK,EAAE;QACpC,IAAI8D,qBAAqB;QACzB,IAAIrF,OAAO,CAACpC,OAAO,IAAIiF,WAAW,EAAE;UAClCxC,WAAW,CAAC,KAAK,CAAC;QACpB,CAAC,MAAM,IAAI,CAACL,OAAO,CAACpC,OAAO,IAAI+E,WAAW,EAAE;UAC1CrB,kBAAkB,CAACC,KAAK,CAAC;UACzBlB,WAAW,CAAC,IAAI,CAAC;QACnB;;QAEA;QACA,KAAK,IAAIiF,KAAK,GAAG9L,SAAS,CAACC,MAAM,EAAEqL,IAAI,GAAG,IAAIC,KAAK,CAACO,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UACjHT,IAAI,CAACS,KAAK,GAAG,CAAC,CAAC,GAAG/L,SAAS,CAAC+L,KAAK,CAAC;QACpC;QACA,CAACF,qBAAqB,GAAG1G,gBAAgB,CAACyG,OAAO,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACJ,IAAI,CAACC,KAAK,CAACG,qBAAqB,EAAE,CAAC1G,gBAAgB,EAAE4C,KAAK,CAAC,CAAC4D,MAAM,CAACL,IAAI,CAAC,CAAC;MAC5M,CAAC;IACH;;IAEA;IACA7L,WAAW,CAAC4G,UAAU,EAAEgD,WAAW,EAAExE,SAAS,EAAEN,QAAQ,EAAEhD,IAAI,EAAEE,YAAY,EAAE4D,cAAc,EAAEwB,WAAW,CAAC;;IAE1G;IACA,IAAImF,WAAW,GAAG/C,WAAW,CAACG,GAAG,CAAC,OAAO,CAAC;IAC1C,IAAI6C,WAAW,GAAG/C,WAAW,CAACE,GAAG,CAAC,OAAO,CAAC;IAC1C,IAAI8C,iBAAiB;IACrB,IAAIC,iBAAiB;IACrB,IAAIH,WAAW,EAAE;MACf;MACAf,aAAa,CAAC,cAAc,EAAE,IAAI,EAAE/J,eAAe,EAAE,UAAU6G,KAAK,EAAE;QACpED,kBAAkB,CAACC,KAAK,CAAC;MAC3B,CAAC,CAAC;MACFkD,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE/J,eAAe,EAAE,UAAU6G,KAAK,EAAE;QACtED,kBAAkB,CAACC,KAAK,CAAC;MAC3B,CAAC,CAAC;MACFmE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;QAC/C;QACA,IAAI7F,UAAU,IAAIc,QAAQ,EAAE;UAC1BN,WAAW,CAAC,IAAI,EAAE3F,eAAe,CAAC;QACpC;MACF,CAAC;;MAED;MACA,IAAIsB,UAAU,EAAE;QACd4C,UAAU,CAACgH,WAAW,GAAG,UAAUrE,KAAK,EAAE;UACxC,IAAIsE,qBAAqB;UACzB;UACA,CAACA,qBAAqB,GAAGlH,gBAAgB,CAACiH,WAAW,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACZ,IAAI,CAACtG,gBAAgB,EAAE4C,KAAK,CAAC;QACpK,CAAC;MACH;IACF;IACA,IAAIkE,WAAW,EAAE;MACfhB,aAAa,CAAC,cAAc,EAAE,KAAK,EAAE7J,eAAe,CAAC;MACrD6J,aAAa,CAAC,gBAAgB,EAAE,KAAK,EAAE7J,eAAe,CAAC;MACvD+K,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;QAC/CtF,WAAW,CAAC,KAAK,EAAEzF,eAAe,CAAC;MACrC,CAAC;IACH;;IAEA;IACA,IAAI6H,WAAW,CAACG,GAAG,CAAC,OAAO,CAAC,EAAE;MAC5B6B,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE5J,UAAU,CAAC;IAC5C;IACA,IAAI6H,WAAW,CAACE,GAAG,CAAC,OAAO,CAAC,EAAE;MAC5B6B,aAAa,CAAC,QAAQ,EAAE,KAAK,EAAE3J,SAAS,CAAC;IAC3C;;IAEA;IACA,IAAI2H,WAAW,CAACG,GAAG,CAAC,aAAa,CAAC,EAAE;MAClChE,UAAU,CAACkH,aAAa,GAAG,UAAUvE,KAAK,EAAE;QAC1C,IAAIwE,sBAAsB;QAC1B,IAAI/F,OAAO,CAACpC,OAAO,IAAI8E,WAAW,CAACE,GAAG,CAAC,aAAa,CAAC,EAAE;UACrDvC,WAAW,CAAC,KAAK,CAAC;QACpB,CAAC,MAAM;UACLiB,kBAAkB,CAACC,KAAK,CAAC;UACzBlB,WAAW,CAAC,IAAI,CAAC;QACnB;QACAkB,KAAK,CAACyE,cAAc,CAAC,CAAC;;QAEtB;QACA,KAAK,IAAIC,KAAK,GAAGzM,SAAS,CAACC,MAAM,EAAEqL,IAAI,GAAG,IAAIC,KAAK,CAACkB,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UACjHpB,IAAI,CAACoB,KAAK,GAAG,CAAC,CAAC,GAAG1M,SAAS,CAAC0M,KAAK,CAAC;QACpC;QACA,CAACH,sBAAsB,GAAGpH,gBAAgB,CAACmH,aAAa,MAAM,IAAI,IAAIC,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACd,IAAI,CAACC,KAAK,CAACa,sBAAsB,EAAE,CAACpH,gBAAgB,EAAE4C,KAAK,CAAC,CAAC4D,MAAM,CAACL,IAAI,CAAC,CAAC;MACtN,CAAC;IACH;;IAEA;IACA,IAAIpI,SAAS,EAAE;MACbkC,UAAU,CAAClC,SAAS,GAAGvE,UAAU,CAACwG,gBAAgB,CAACjC,SAAS,EAAEA,SAAS,CAAC;IAC1E;;IAEA;IACA,IAAIyJ,mBAAmB,GAAGrO,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,gBAAgB,CAAC,EAAEC,UAAU,CAAC;;IAExF;IACA,IAAIwH,WAAW,GAAG,CAAC,CAAC;IACpB,IAAIC,eAAe,GAAG,CAAC,eAAe,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,CAAC;IACtIA,eAAe,CAACC,OAAO,CAAC,UAAU5B,SAAS,EAAE;MAC3C,IAAI9H,SAAS,CAAC8H,SAAS,CAAC,EAAE;QACxB0B,WAAW,CAAC1B,SAAS,CAAC,GAAG,YAAY;UACnC,IAAI6B,qBAAqB;UACzB,KAAK,IAAIC,KAAK,GAAGhN,SAAS,CAACC,MAAM,EAAEqL,IAAI,GAAG,IAAIC,KAAK,CAACyB,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;YAC7F3B,IAAI,CAAC2B,KAAK,CAAC,GAAGjN,SAAS,CAACiN,KAAK,CAAC;UAChC;UACA,CAACF,qBAAqB,GAAGJ,mBAAmB,CAACzB,SAAS,CAAC,MAAM,IAAI,IAAI6B,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACtB,IAAI,CAACC,KAAK,CAACqB,qBAAqB,EAAE,CAACJ,mBAAmB,CAAC,CAAChB,MAAM,CAACL,IAAI,CAAC,CAAC;UAC5MlI,SAAS,CAAC8H,SAAS,CAAC,CAACQ,KAAK,CAACtI,SAAS,EAAEkI,IAAI,CAAC;QAC7C,CAAC;MACH;IACF,CAAC,CAAC;;IAEF;IACA,IAAI4B,WAAW,GAAG,aAAa/N,KAAK,CAACgO,YAAY,CAACnI,KAAK,EAAE1G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqO,mBAAmB,CAAC,EAAEC,WAAW,CAAC,CAAC;IAC5H,IAAIQ,QAAQ,GAAG;MACbC,CAAC,EAAE5E,MAAM;MACT6E,CAAC,EAAE5E;IACL,CAAC;IACD,IAAI6E,UAAU,GAAG5K,KAAK,GAAGrE,aAAa,CAAC,CAAC,CAAC,EAAEqE,KAAK,KAAK,IAAI,GAAGA,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;;IAE9E;IACA,OAAO,aAAaxD,KAAK,CAACqO,aAAa,CAACrO,KAAK,CAACsO,QAAQ,EAAE,IAAI,EAAE,aAAatO,KAAK,CAACqO,aAAa,CAAC5O,cAAc,EAAE;MAC7G8O,QAAQ,EAAE,CAACrH,UAAU;MACrB/F,GAAG,EAAEyE,YAAY;MACjB4I,QAAQ,EAAEhD;IACZ,CAAC,EAAE,aAAaxL,KAAK,CAACqO,aAAa,CAAC7N,cAAc,EAAE;MAClDwD,iBAAiB,EAAEA;IACrB,CAAC,EAAE+J,WAAW,CAAC,CAAC,EAAE,aAAa/N,KAAK,CAACqO,aAAa,CAACnO,cAAc,CAACuO,QAAQ,EAAE;MAC1EC,KAAK,EAAE9J;IACT,CAAC,EAAE,aAAa5E,KAAK,CAACqO,aAAa,CAAC9N,KAAK,EAAE;MACzCoO,MAAM,EAAE/N,eAAe;MACvBO,GAAG,EAAEmE,WAAW;MAChBjE,SAAS,EAAEA,SAAS;MACpBsB,KAAK,EAAEA,KAAK;MACZoB,SAAS,EAAEvE,UAAU,CAACoD,cAAc,EAAE2H,gBAAgB,CAAC;MACvDqE,KAAK,EAAE/L,UAAU;MACjBgM,MAAM,EAAEnJ,SAAS;MACjBoJ,YAAY,EAAE/B,iBAAiB;MAC/BgC,YAAY,EAAE/B;MACd;MAAA;;MAEAgC,cAAc,EAAEjC,iBAAiB;MACjC7J,MAAM,EAAEA;MACR;MAAA;;MAEA+L,IAAI,EAAE/H,UAAU;MAChBgI,OAAO,EAAElH;MACT;MAAA;;MAEAyE,OAAO,EAAEnJ;MACT;MAAA;;MAEAlB,IAAI,EAAEA;MACN;MAAA;;MAEA+M,MAAM,EAAEvI,gBAAgB;MACxBlD,UAAU,EAAEmD,eAAe;MAC3B8D,gBAAgB,EAAEA,gBAAgB;MAClCE,SAAS,EAAEA;MACX;MAAA;;MAEArI,WAAW,EAAEA,WAAW;MACxBC,WAAW,EAAEyB,iBAAiB;MAC9B3B,iBAAiB,EAAEA;MACnB;MAAA;;MAEA6M,KAAK,EAAE1F,SAAS;MAChBlG,KAAK,EAAE4K,UAAU;MACjBH,QAAQ,EAAEA;MACV;MAAA;;MAEAhF,KAAK,EAAEA,KAAK;MACZC,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA,OAAO;MAChBM,OAAO,EAAEQ;MACT;MAAA;;MAEAhH,OAAO,EAAEA,OAAO;MAChB+H,WAAW,EAAEA,WAAW,GAAG1B,MAAM;MACjC8B,YAAY,EAAEA,YAAY,GAAG7B;IAC/B,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC;EACF,IAAI4F,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCvO,OAAO,CAACwO,WAAW,GAAG,SAAS;EACjC;EACA,OAAOxO,OAAO;AAChB;AACA,eAAeL,eAAe,CAACpB,MAAM,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}