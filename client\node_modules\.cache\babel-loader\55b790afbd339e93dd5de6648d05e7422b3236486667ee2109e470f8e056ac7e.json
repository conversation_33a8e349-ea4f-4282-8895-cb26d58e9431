{"ast": null, "code": "import React, { useEffect, useState } from 'react';\nimport { generateColor, getAlphaColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorAlphaInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorAlphaInputPrefixCls = `${prefixCls}-alpha-input`;\n  const [alphaValue, setAlphaValue] = useState(generateColor(value || '#000'));\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setAlphaValue(value);\n    }\n  }, [value]);\n  const handleAlphaChange = step => {\n    const hsba = alphaValue.toHsb();\n    hsba.a = (step || 0) / 100;\n    const genColor = generateColor(hsba);\n    if (!value) {\n      setAlphaValue(genColor);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(ColorSteppers, {\n    value: getAlphaColor(alphaValue),\n    prefixCls: prefixCls,\n    formatter: step => `${step}%`,\n    className: colorAlphaInputPrefixCls,\n    onChange: handleAlphaChange\n  });\n};\nexport default ColorAlphaInput;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "generateColor", "getAlphaColor", "ColorSteppers", "ColorAlphaInput", "_ref", "prefixCls", "value", "onChange", "colorAlphaInputPrefixCls", "alphaValue", "setAlphaValue", "handleAlphaChange", "step", "hsba", "toHsb", "a", "genColor", "createElement", "formatter", "className"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/color-picker/components/ColorAlphaInput.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { generateColor, getAlphaColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorAlphaInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorAlphaInputPrefixCls = `${prefixCls}-alpha-input`;\n  const [alphaValue, setAlphaValue] = useState(generateColor(value || '#000'));\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setAlphaValue(value);\n    }\n  }, [value]);\n  const handleAlphaChange = step => {\n    const hsba = alphaValue.toHsb();\n    hsba.a = (step || 0) / 100;\n    const genColor = generateColor(hsba);\n    if (!value) {\n      setAlphaValue(genColor);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(ColorSteppers, {\n    value: getAlphaColor(alphaValue),\n    prefixCls: prefixCls,\n    formatter: step => `${step}%`,\n    className: colorAlphaInputPrefixCls,\n    onChange: handleAlphaChange\n  });\n};\nexport default ColorAlphaInput;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,aAAa,EAAEC,aAAa,QAAQ,SAAS;AACtD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,eAAe,GAAGC,IAAI,IAAI;EAC9B,IAAI;IACFC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAGH,IAAI;EACR,MAAMI,wBAAwB,GAAI,GAAEH,SAAU,cAAa;EAC3D,MAAM,CAACI,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAACC,aAAa,CAACM,KAAK,IAAI,MAAM,CAAC,CAAC;EAC5E;EACAR,SAAS,CAAC,MAAM;IACd,IAAIQ,KAAK,EAAE;MACTI,aAAa,CAACJ,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,MAAMK,iBAAiB,GAAGC,IAAI,IAAI;IAChC,MAAMC,IAAI,GAAGJ,UAAU,CAACK,KAAK,CAAC,CAAC;IAC/BD,IAAI,CAACE,CAAC,GAAG,CAACH,IAAI,IAAI,CAAC,IAAI,GAAG;IAC1B,MAAMI,QAAQ,GAAGhB,aAAa,CAACa,IAAI,CAAC;IACpC,IAAI,CAACP,KAAK,EAAE;MACVI,aAAa,CAACM,QAAQ,CAAC;IACzB;IACAT,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACS,QAAQ,CAAC;EACxE,CAAC;EACD,OAAO,aAAanB,KAAK,CAACoB,aAAa,CAACf,aAAa,EAAE;IACrDI,KAAK,EAAEL,aAAa,CAACQ,UAAU,CAAC;IAChCJ,SAAS,EAAEA,SAAS;IACpBa,SAAS,EAAEN,IAAI,IAAK,GAAEA,IAAK,GAAE;IAC7BO,SAAS,EAAEX,wBAAwB;IACnCD,QAAQ,EAAEI;EACZ,CAAC,CAAC;AACJ,CAAC;AACD,eAAeR,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}