{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n/**\n * Feature:\n *  - fixed not need to set width\n *  - support `rowExpandable` to config row expand logic\n *  - add `summary` to support `() => ReactNode`\n *\n * Update:\n *  - `dataIndex` is `array[]` now\n *  - `expandable` wrap all the expand related props\n *\n * Removed:\n *  - expandIconAsCell\n *  - useFixedHeader\n *  - rowRef\n *  - columns[number].onCellClick\n *  - onRowClick\n *  - onRowDoubleClick\n *  - onRowMouseEnter\n *  - onRowMouseLeave\n *  - getBodyWrapper\n *  - bodyStyle\n *\n * Deprecated:\n *  - All expanded props, move into expandable\n */\n\nimport { makeImmutable } from '@rc-component/context';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nimport { getTargetScrollBarSize } from \"rc-util/es/getScrollBarSize\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport getValue from \"rc-util/es/utils/get\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport Body from \"./Body\";\nimport ColGroup from \"./ColGroup\";\nimport { EXPAND_COLUMN, INTERNAL_HOOKS } from \"./constant\";\nimport TableContext from \"./context/TableContext\";\nimport FixedHolder from \"./FixedHolder\";\nimport Footer, { FooterComponents } from \"./Footer\";\nimport Summary from \"./Footer/Summary\";\nimport Header from \"./Header/Header\";\nimport useColumns from \"./hooks/useColumns\";\nimport useExpand from \"./hooks/useExpand\";\nimport useFixedInfo from \"./hooks/useFixedInfo\";\nimport { useLayoutState, useTimeoutLock } from \"./hooks/useFrame\";\nimport useHover from \"./hooks/useHover\";\nimport useSticky from \"./hooks/useSticky\";\nimport useStickyOffsets from \"./hooks/useStickyOffsets\";\nimport Panel from \"./Panel\";\nimport StickyScrollBar from \"./stickyScrollBar\";\nimport Column from \"./sugar/Column\";\nimport ColumnGroup from \"./sugar/ColumnGroup\";\nimport { getColumnsKey, validateValue } from \"./utils/valueUtil\";\n\n// Used for conditions cache\nvar EMPTY_DATA = [];\n\n// Used for customize scroll\nvar EMPTY_SCROLL_TARGET = {};\nfunction defaultEmpty() {\n  return 'No Data';\n}\nfunction Table(tableProps) {\n  var _classNames;\n  var props = _objectSpread({\n    rowKey: 'key',\n    prefixCls: 'rc-table',\n    emptyText: defaultEmpty\n  }, tableProps);\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    rowClassName = props.rowClassName,\n    style = props.style,\n    data = props.data,\n    rowKey = props.rowKey,\n    scroll = props.scroll,\n    tableLayout = props.tableLayout,\n    direction = props.direction,\n    title = props.title,\n    footer = props.footer,\n    summary = props.summary,\n    caption = props.caption,\n    id = props.id,\n    showHeader = props.showHeader,\n    components = props.components,\n    emptyText = props.emptyText,\n    onRow = props.onRow,\n    onHeaderRow = props.onHeaderRow,\n    internalHooks = props.internalHooks,\n    transformColumns = props.transformColumns,\n    internalRefs = props.internalRefs,\n    sticky = props.sticky;\n  var mergedData = data || EMPTY_DATA;\n  var hasData = !!mergedData.length;\n\n  // ===================== Warning ======================\n  if (process.env.NODE_ENV !== 'production') {\n    ['onRowClick', 'onRowDoubleClick', 'onRowContextMenu', 'onRowMouseEnter', 'onRowMouseLeave'].forEach(function (name) {\n      warning(props[name] === undefined, \"`\".concat(name, \"` is removed, please use `onRow` instead.\"));\n    });\n    warning(!('getBodyWrapper' in props), '`getBodyWrapper` is deprecated, please use custom `components` instead.');\n  }\n\n  // ==================== Customize =====================\n  var getComponent = React.useCallback(function (path, defaultComponent) {\n    return getValue(components, path) || defaultComponent;\n  }, [components]);\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record) {\n      var key = record && record[rowKey];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(key !== undefined, 'Each record in table should have a unique `key` prop, or set `rowKey` to an unique primary key.');\n      }\n      return key;\n    };\n  }, [rowKey]);\n\n  // ====================== Hover =======================\n  var _useHover = useHover(),\n    _useHover2 = _slicedToArray(_useHover, 3),\n    startRow = _useHover2[0],\n    endRow = _useHover2[1],\n    onHover = _useHover2[2];\n\n  // ====================== Expand ======================\n  var _useExpand = useExpand(props, mergedData, getRowKey),\n    _useExpand2 = _slicedToArray(_useExpand, 6),\n    expandableConfig = _useExpand2[0],\n    expandableType = _useExpand2[1],\n    mergedExpandedKeys = _useExpand2[2],\n    mergedExpandIcon = _useExpand2[3],\n    mergedChildrenColumnName = _useExpand2[4],\n    onTriggerExpand = _useExpand2[5];\n\n  // ====================== Column ======================\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    componentWidth = _React$useState2[0],\n    setComponentWidth = _React$useState2[1];\n  var _useColumns = useColumns(_objectSpread(_objectSpread(_objectSpread({}, props), expandableConfig), {}, {\n      expandable: !!expandableConfig.expandedRowRender,\n      columnTitle: expandableConfig.columnTitle,\n      expandedKeys: mergedExpandedKeys,\n      getRowKey: getRowKey,\n      // https://github.com/ant-design/ant-design/issues/23894\n      onTriggerExpand: onTriggerExpand,\n      expandIcon: mergedExpandIcon,\n      expandIconColumnIndex: expandableConfig.expandIconColumnIndex,\n      direction: direction\n    }), internalHooks === INTERNAL_HOOKS ? transformColumns : null),\n    _useColumns2 = _slicedToArray(_useColumns, 2),\n    columns = _useColumns2[0],\n    flattenColumns = _useColumns2[1];\n  var columnContext = React.useMemo(function () {\n    return {\n      columns: columns,\n      flattenColumns: flattenColumns\n    };\n  }, [columns, flattenColumns]);\n\n  // ====================== Scroll ======================\n  var fullTableRef = React.useRef();\n  var scrollHeaderRef = React.useRef();\n  var scrollBodyRef = React.useRef();\n  var scrollBodyContainerRef = React.useRef();\n  var scrollSummaryRef = React.useRef();\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    pingedLeft = _React$useState4[0],\n    setPingedLeft = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    pingedRight = _React$useState6[0],\n    setPingedRight = _React$useState6[1];\n  var _useLayoutState = useLayoutState(new Map()),\n    _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n    colsWidths = _useLayoutState2[0],\n    updateColsWidths = _useLayoutState2[1];\n\n  // Convert map to number width\n  var colsKeys = getColumnsKey(flattenColumns);\n  var pureColWidths = colsKeys.map(function (columnKey) {\n    return colsWidths.get(columnKey);\n  });\n  var colWidths = React.useMemo(function () {\n    return pureColWidths;\n  }, [pureColWidths.join('_')]);\n  var stickyOffsets = useStickyOffsets(colWidths, flattenColumns.length, direction);\n  var fixHeader = scroll && validateValue(scroll.y);\n  var horizonScroll = scroll && validateValue(scroll.x) || Boolean(expandableConfig.fixed);\n  var fixColumn = horizonScroll && flattenColumns.some(function (_ref) {\n    var fixed = _ref.fixed;\n    return fixed;\n  });\n\n  // Sticky\n  var stickyRef = React.useRef();\n  var _useSticky = useSticky(sticky, prefixCls),\n    isSticky = _useSticky.isSticky,\n    offsetHeader = _useSticky.offsetHeader,\n    offsetSummary = _useSticky.offsetSummary,\n    offsetScroll = _useSticky.offsetScroll,\n    stickyClassName = _useSticky.stickyClassName,\n    container = _useSticky.container;\n\n  // Footer (Fix footer must fixed header)\n  var summaryNode = React.useMemo(function () {\n    return summary === null || summary === void 0 ? void 0 : summary(mergedData);\n  }, [summary, mergedData]);\n  var fixFooter = (fixHeader || isSticky) && /*#__PURE__*/React.isValidElement(summaryNode) && summaryNode.type === Summary && summaryNode.props.fixed;\n\n  // Scroll\n  var scrollXStyle;\n  var scrollYStyle;\n  var scrollTableStyle;\n  if (fixHeader) {\n    scrollYStyle = {\n      overflowY: 'scroll',\n      maxHeight: scroll.y\n    };\n  }\n  if (horizonScroll) {\n    scrollXStyle = {\n      overflowX: 'auto'\n    };\n    // When no vertical scrollbar, should hide it\n    // https://github.com/ant-design/ant-design/pull/20705\n    // https://github.com/ant-design/ant-design/issues/21879\n    if (!fixHeader) {\n      scrollYStyle = {\n        overflowY: 'hidden'\n      };\n    }\n    scrollTableStyle = {\n      width: (scroll === null || scroll === void 0 ? void 0 : scroll.x) === true ? 'auto' : scroll === null || scroll === void 0 ? void 0 : scroll.x,\n      minWidth: '100%'\n    };\n  }\n  var onColumnResize = React.useCallback(function (columnKey, width) {\n    if (isVisible(fullTableRef.current)) {\n      updateColsWidths(function (widths) {\n        if (widths.get(columnKey) !== width) {\n          var newWidths = new Map(widths);\n          newWidths.set(columnKey, width);\n          return newWidths;\n        }\n        return widths;\n      });\n    }\n  }, []);\n  var _useTimeoutLock = useTimeoutLock(null),\n    _useTimeoutLock2 = _slicedToArray(_useTimeoutLock, 2),\n    setScrollTarget = _useTimeoutLock2[0],\n    getScrollTarget = _useTimeoutLock2[1];\n  function forceScroll(scrollLeft, target) {\n    if (!target) {\n      return;\n    }\n    if (typeof target === 'function') {\n      target(scrollLeft);\n    } else if (target.scrollLeft !== scrollLeft) {\n      target.scrollLeft = scrollLeft;\n\n      // Delay to force scroll position if not sync\n      // ref: https://github.com/ant-design/ant-design/issues/37179\n      if (target.scrollLeft !== scrollLeft) {\n        setTimeout(function () {\n          target.scrollLeft = scrollLeft;\n        }, 0);\n      }\n    }\n  }\n  var onScroll = useEvent(function (_ref2) {\n    var currentTarget = _ref2.currentTarget,\n      scrollLeft = _ref2.scrollLeft;\n    var isRTL = direction === 'rtl';\n    var mergedScrollLeft = typeof scrollLeft === 'number' ? scrollLeft : currentTarget.scrollLeft;\n    var compareTarget = currentTarget || EMPTY_SCROLL_TARGET;\n    if (!getScrollTarget() || getScrollTarget() === compareTarget) {\n      var _stickyRef$current;\n      setScrollTarget(compareTarget);\n      forceScroll(mergedScrollLeft, scrollHeaderRef.current);\n      forceScroll(mergedScrollLeft, scrollBodyRef.current);\n      forceScroll(mergedScrollLeft, scrollSummaryRef.current);\n      forceScroll(mergedScrollLeft, (_stickyRef$current = stickyRef.current) === null || _stickyRef$current === void 0 ? void 0 : _stickyRef$current.setScrollLeft);\n    }\n    if (currentTarget) {\n      var scrollWidth = currentTarget.scrollWidth,\n        clientWidth = currentTarget.clientWidth;\n      // There is no space to scroll\n      if (scrollWidth === clientWidth) {\n        setPingedLeft(false);\n        setPingedRight(false);\n        return;\n      }\n      if (isRTL) {\n        setPingedLeft(-mergedScrollLeft < scrollWidth - clientWidth);\n        setPingedRight(-mergedScrollLeft > 0);\n      } else {\n        setPingedLeft(mergedScrollLeft > 0);\n        setPingedRight(mergedScrollLeft < scrollWidth - clientWidth);\n      }\n    }\n  });\n  var triggerOnScroll = function triggerOnScroll() {\n    if (horizonScroll && scrollBodyRef.current) {\n      onScroll({\n        currentTarget: scrollBodyRef.current\n      });\n    } else {\n      setPingedLeft(false);\n      setPingedRight(false);\n    }\n  };\n  var onFullTableResize = function onFullTableResize(_ref3) {\n    var width = _ref3.width;\n    if (width !== componentWidth) {\n      triggerOnScroll();\n      setComponentWidth(fullTableRef.current ? fullTableRef.current.offsetWidth : width);\n    }\n  };\n\n  // Sync scroll bar when init or `horizonScroll`, `data` and `columns.length` changed\n  var mounted = React.useRef(false);\n  React.useEffect(function () {\n    // onFullTableResize will be trigger once when ResizeObserver is mounted\n    // This will reduce one duplicated triggerOnScroll time\n    if (mounted.current) {\n      triggerOnScroll();\n    }\n  }, [horizonScroll, data, columns.length]);\n  React.useEffect(function () {\n    mounted.current = true;\n  }, []);\n\n  // ===================== Effects ======================\n  var _React$useState7 = React.useState(0),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    scrollbarSize = _React$useState8[0],\n    setScrollbarSize = _React$useState8[1];\n  var _React$useState9 = React.useState(true),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    supportSticky = _React$useState10[0],\n    setSupportSticky = _React$useState10[1]; // Only IE not support, we mark as support first\n\n  React.useEffect(function () {\n    if (scrollBodyRef.current instanceof Element) {\n      setScrollbarSize(getTargetScrollBarSize(scrollBodyRef.current).width);\n    } else {\n      setScrollbarSize(getTargetScrollBarSize(scrollBodyContainerRef.current).width);\n    }\n    setSupportSticky(isStyleSupport('position', 'sticky'));\n  }, []);\n\n  // ================== INTERNAL HOOKS ==================\n  React.useEffect(function () {\n    if (internalHooks === INTERNAL_HOOKS && internalRefs) {\n      internalRefs.body.current = scrollBodyRef.current;\n    }\n  });\n\n  // ========================================================================\n  // ==                               Render                               ==\n  // ========================================================================\n  // =================== Render: Func ===================\n  var renderFixedHeaderTable = React.useCallback(function (fixedHolderPassProps) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Header, fixedHolderPassProps), fixFooter === 'top' && /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode));\n  }, [fixFooter, summaryNode]);\n  var renderFixedFooterTable = React.useCallback(function (fixedHolderPassProps) {\n    return /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode);\n  }, [summaryNode]);\n\n  // =================== Render: Node ===================\n  var TableComponent = getComponent(['table'], 'table');\n\n  // Table layout\n  var mergedTableLayout = React.useMemo(function () {\n    if (tableLayout) {\n      return tableLayout;\n    }\n    // https://github.com/ant-design/ant-design/issues/25227\n    // When scroll.x is max-content, no need to fix table layout\n    // it's width should stretch out to fit content\n    if (fixColumn) {\n      return (scroll === null || scroll === void 0 ? void 0 : scroll.x) === 'max-content' ? 'auto' : 'fixed';\n    }\n    if (fixHeader || isSticky || flattenColumns.some(function (_ref4) {\n      var ellipsis = _ref4.ellipsis;\n      return ellipsis;\n    })) {\n      return 'fixed';\n    }\n    return 'auto';\n  }, [fixHeader, fixColumn, flattenColumns, tableLayout, isSticky]);\n  var groupTableNode;\n\n  // Header props\n  var headerProps = {\n    colWidths: colWidths,\n    columCount: flattenColumns.length,\n    stickyOffsets: stickyOffsets,\n    onHeaderRow: onHeaderRow,\n    fixHeader: fixHeader,\n    scroll: scroll\n  };\n\n  // Empty\n  var emptyNode = React.useMemo(function () {\n    if (hasData) {\n      return null;\n    }\n    if (typeof emptyText === 'function') {\n      return emptyText();\n    }\n    return emptyText;\n  }, [hasData, emptyText]);\n\n  // Body\n  var bodyTable = /*#__PURE__*/React.createElement(Body, {\n    data: mergedData,\n    measureColumnWidth: fixHeader || horizonScroll || isSticky,\n    expandedKeys: mergedExpandedKeys,\n    rowExpandable: expandableConfig.rowExpandable,\n    getRowKey: getRowKey,\n    onRow: onRow,\n    emptyNode: emptyNode,\n    childrenColumnName: mergedChildrenColumnName\n  });\n  var bodyColGroup = /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: flattenColumns.map(function (_ref5) {\n      var width = _ref5.width;\n      return width;\n    }),\n    columns: flattenColumns\n  });\n  var captionElement = caption !== null && caption !== undefined ? /*#__PURE__*/React.createElement(\"caption\", {\n    className: \"\".concat(prefixCls, \"-caption\")\n  }, caption) : undefined;\n  var customizeScrollBody = getComponent(['body']);\n  if (process.env.NODE_ENV !== 'production' && typeof customizeScrollBody === 'function' && hasData && !fixHeader) {\n    warning(false, '`components.body` with render props is only work on `scroll.y`.');\n  }\n  var dataProps = pickAttrs(props, {\n    data: true\n  });\n  var ariaProps = pickAttrs(props, {\n    aria: true\n  });\n  if (fixHeader || isSticky) {\n    // >>>>>> Fixed Header\n    var bodyContent;\n    if (typeof customizeScrollBody === 'function') {\n      bodyContent = customizeScrollBody(mergedData, {\n        scrollbarSize: scrollbarSize,\n        ref: scrollBodyRef,\n        onScroll: onScroll\n      });\n      headerProps.colWidths = flattenColumns.map(function (_ref6, index) {\n        var width = _ref6.width;\n        var colWidth = index === flattenColumns.length - 1 ? width - scrollbarSize : width;\n        if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {\n          return colWidth;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          warning(props.columns.length === 0, 'When use `components.body` with render props. Each column should have a fixed `width` value.');\n        }\n        return 0;\n      });\n    } else {\n      bodyContent = /*#__PURE__*/React.createElement(\"div\", {\n        style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n        onScroll: onScroll,\n        ref: scrollBodyRef,\n        className: classNames(\"\".concat(prefixCls, \"-body\"))\n      }, /*#__PURE__*/React.createElement(TableComponent, _extends({\n        style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n          tableLayout: mergedTableLayout\n        })\n      }, ariaProps), captionElement, bodyColGroup, bodyTable, !fixFooter && summaryNode && /*#__PURE__*/React.createElement(Footer, {\n        stickyOffsets: stickyOffsets,\n        flattenColumns: flattenColumns,\n        columns: columns\n      }, summaryNode)));\n    }\n\n    // Fixed holder share the props\n    var fixedHolderProps = _objectSpread(_objectSpread(_objectSpread({\n      noData: !mergedData.length,\n      maxContentScroll: horizonScroll && scroll.x === 'max-content'\n    }, headerProps), columnContext), {}, {\n      direction: direction,\n      stickyClassName: stickyClassName,\n      onScroll: onScroll\n    });\n    groupTableNode = /*#__PURE__*/React.createElement(React.Fragment, null, showHeader !== false && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyTopOffset: offsetHeader,\n      className: \"\".concat(prefixCls, \"-header\"),\n      ref: scrollHeaderRef\n    }), renderFixedHeaderTable), bodyContent, fixFooter && fixFooter !== 'top' && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyBottomOffset: offsetSummary,\n      className: \"\".concat(prefixCls, \"-summary\"),\n      ref: scrollSummaryRef\n    }), renderFixedFooterTable), isSticky && /*#__PURE__*/React.createElement(StickyScrollBar, {\n      ref: stickyRef,\n      offsetScroll: offsetScroll,\n      scrollBodyRef: scrollBodyRef,\n      onScroll: onScroll,\n      container: container\n    }));\n  } else {\n    // >>>>>> Unique table\n    groupTableNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n      className: classNames(\"\".concat(prefixCls, \"-content\")),\n      onScroll: onScroll,\n      ref: scrollBodyRef\n    }, /*#__PURE__*/React.createElement(TableComponent, _extends({\n      style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n        tableLayout: mergedTableLayout\n      })\n    }, ariaProps), captionElement, bodyColGroup, showHeader !== false && /*#__PURE__*/React.createElement(Header, _extends({}, headerProps, columnContext)), bodyTable, summaryNode && /*#__PURE__*/React.createElement(Footer, {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns,\n      columns: columns\n    }, summaryNode)));\n  }\n  var fullTable = /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-left\"), pingedLeft), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-right\"), pingedRight), _defineProperty(_classNames, \"\".concat(prefixCls, \"-layout-fixed\"), tableLayout === 'fixed'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-header\"), fixHeader), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-column\"), fixColumn), _defineProperty(_classNames, \"\".concat(prefixCls, \"-scroll-horizontal\"), horizonScroll), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-left\"), flattenColumns[0] && flattenColumns[0].fixed), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-right\"), flattenColumns[flattenColumns.length - 1] && flattenColumns[flattenColumns.length - 1].fixed === 'right'), _classNames)),\n    style: style,\n    id: id,\n    ref: fullTableRef\n  }, dataProps), title && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title(mergedData)), /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollBodyContainerRef,\n    className: \"\".concat(prefixCls, \"-container\")\n  }, groupTableNode), footer && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, footer(mergedData)));\n  if (horizonScroll) {\n    fullTable = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onFullTableResize\n    }, fullTable);\n  }\n  var fixedInfoList = useFixedInfo(flattenColumns, stickyOffsets, direction, columns);\n  var TableContextValue = React.useMemo(function () {\n    return {\n      // Table\n      prefixCls: prefixCls,\n      getComponent: getComponent,\n      scrollbarSize: scrollbarSize,\n      direction: direction,\n      fixedInfoList: fixedInfoList,\n      isSticky: isSticky,\n      supportSticky: supportSticky,\n      componentWidth: componentWidth,\n      fixHeader: fixHeader,\n      fixColumn: fixColumn,\n      horizonScroll: horizonScroll,\n      // Body\n\n      tableLayout: mergedTableLayout,\n      rowClassName: rowClassName,\n      expandedRowClassName: expandableConfig.expandedRowClassName,\n      expandIcon: mergedExpandIcon,\n      expandableType: expandableType,\n      expandRowByClick: expandableConfig.expandRowByClick,\n      expandedRowRender: expandableConfig.expandedRowRender,\n      onTriggerExpand: onTriggerExpand,\n      expandIconColumnIndex: expandableConfig.expandIconColumnIndex,\n      indentSize: expandableConfig.indentSize,\n      allColumnsFixedLeft: flattenColumns.every(function (col) {\n        return col.fixed === 'left';\n      }),\n      // Column\n      columns: columns,\n      flattenColumns: flattenColumns,\n      onColumnResize: onColumnResize,\n      // Row\n      hoverStartRow: startRow,\n      hoverEndRow: endRow,\n      onHover: onHover\n    };\n  }, [\n  // Table\n  prefixCls, getComponent, scrollbarSize, direction, fixedInfoList, isSticky, supportSticky, componentWidth, fixHeader, fixColumn, horizonScroll,\n  // Body\n  mergedTableLayout, rowClassName, expandableConfig.expandedRowClassName, mergedExpandIcon, expandableType, expandableConfig.expandRowByClick, expandableConfig.expandedRowRender, onTriggerExpand, expandableConfig.expandIconColumnIndex, expandableConfig.indentSize,\n  // Column\n  columns, flattenColumns, onColumnResize,\n  // Row\n  startRow, endRow, onHover]);\n  return /*#__PURE__*/React.createElement(TableContext.Provider, {\n    value: TableContextValue\n  }, fullTable);\n}\nexport function genTable(shouldTriggerRender) {\n  return makeImmutable(Table, shouldTriggerRender);\n}\nvar ImmutableTable = genTable();\nImmutableTable.EXPAND_COLUMN = EXPAND_COLUMN;\nImmutableTable.INTERNAL_HOOKS = INTERNAL_HOOKS;\nImmutableTable.Column = Column;\nImmutableTable.ColumnGroup = ColumnGroup;\nImmutableTable.Summary = FooterComponents;\nexport default ImmutableTable;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_slicedToArray", "_objectSpread", "makeImmutable", "classNames", "ResizeObserver", "isVisible", "isStyleSupport", "getTargetScrollBarSize", "useEvent", "pickAttrs", "getValue", "warning", "React", "Body", "ColGroup", "EXPAND_COLUMN", "INTERNAL_HOOKS", "TableContext", "FixedHolder", "Footer", "FooterComponents", "Summary", "Header", "useColumns", "useExpand", "useFixedInfo", "useLayoutState", "useTimeoutLock", "useHover", "useSticky", "useStickyOffsets", "Panel", "StickyScrollBar", "Column", "ColumnGroup", "getColumnsKey", "validate<PERSON><PERSON>ue", "EMPTY_DATA", "EMPTY_SCROLL_TARGET", "defaultEmpty", "Table", "tableProps", "_classNames", "props", "<PERSON><PERSON><PERSON>", "prefixCls", "emptyText", "className", "rowClassName", "style", "data", "scroll", "tableLayout", "direction", "title", "footer", "summary", "caption", "id", "showHeader", "components", "onRow", "onHeaderRow", "internalHooks", "transformColumns", "internalRefs", "sticky", "mergedData", "hasData", "length", "process", "env", "NODE_ENV", "for<PERSON>ach", "name", "undefined", "concat", "getComponent", "useCallback", "path", "defaultComponent", "getRowKey", "useMemo", "record", "key", "_useHover", "_useHover2", "startRow", "endRow", "onHover", "_useExpand", "_useExpand2", "expandableConfig", "expandableType", "mergedExpandedKeys", "mergedExpandIcon", "mergedChildrenColumnName", "onTriggerExpand", "_React$useState", "useState", "_React$useState2", "componentWidth", "setComponentWidth", "_useColumns", "expandable", "expandedRowRender", "columnTitle", "expandedKeys", "expandIcon", "expandIconColumnIndex", "_useColumns2", "columns", "flattenColumns", "columnContext", "fullTableRef", "useRef", "scrollHeaderRef", "scrollBodyRef", "scrollBodyContainerRef", "scrollSummaryRef", "_React$useState3", "_React$useState4", "pingedLeft", "setPingedLeft", "_React$useState5", "_React$useState6", "pingedRight", "setPingedRight", "_useLayoutState", "Map", "_useLayoutState2", "colsWidths", "updateColsWidths", "colsKeys", "pureColWidths", "map", "column<PERSON>ey", "get", "col<PERSON><PERSON><PERSON>", "join", "stickyOffsets", "fixHeader", "y", "horizonScroll", "x", "Boolean", "fixed", "fixColumn", "some", "_ref", "stickyRef", "_useSticky", "isSticky", "offsetHeader", "offsetSummary", "offsetScroll", "stickyClassName", "container", "summaryNode", "fixFooter", "isValidElement", "type", "scrollXStyle", "scrollYStyle", "scrollTableStyle", "overflowY", "maxHeight", "overflowX", "width", "min<PERSON><PERSON><PERSON>", "onColumnResize", "current", "widths", "newWidths", "set", "_useTimeoutLock", "_useTimeoutLock2", "setScrollTarget", "getScrollTarget", "forceScroll", "scrollLeft", "target", "setTimeout", "onScroll", "_ref2", "currentTarget", "isRTL", "mergedScrollLeft", "compareTarget", "_stickyRef$current", "setScrollLeft", "scrollWidth", "clientWidth", "triggerOnScroll", "onFullTableResize", "_ref3", "offsetWidth", "mounted", "useEffect", "_React$useState7", "_React$useState8", "scrollbarSize", "setScrollbarSize", "_React$useState9", "_React$useState10", "supportSticky", "setSupportSticky", "Element", "body", "renderFixedHeaderTable", "fixedHolderPassProps", "createElement", "Fragment", "renderFixedFooterTable", "TableComponent", "mergedTableLayout", "_ref4", "ellipsis", "groupTableNode", "headerProps", "columCount", "emptyNode", "bodyTable", "measureColumnWidth", "rowExpandable", "childrenColumnName", "bodyColGroup", "_ref5", "captionElement", "customizeScrollBody", "dataProps", "ariaProps", "aria", "bodyContent", "ref", "_ref6", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "fixedHolderProps", "noData", "maxContentScroll", "stickyTopOffset", "stickyBottomOffset", "fullTable", "onResize", "fixedInfoList", "TableContextValue", "expandedRowClassName", "expandRowByClick", "indentSize", "allColumnsFixedLeft", "every", "col", "hoverStartRow", "hoverEndRow", "Provider", "value", "genTable", "should<PERSON>rigger<PERSON>ender", "ImmutableTable"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/Table.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n/**\n * Feature:\n *  - fixed not need to set width\n *  - support `rowExpandable` to config row expand logic\n *  - add `summary` to support `() => ReactNode`\n *\n * Update:\n *  - `dataIndex` is `array[]` now\n *  - `expandable` wrap all the expand related props\n *\n * Removed:\n *  - expandIconAsCell\n *  - useFixedHeader\n *  - rowRef\n *  - columns[number].onCellClick\n *  - onRowClick\n *  - onRowDoubleClick\n *  - onRowMouseEnter\n *  - onRowMouseLeave\n *  - getBodyWrapper\n *  - bodyStyle\n *\n * Deprecated:\n *  - All expanded props, move into expandable\n */\n\nimport { makeImmutable } from '@rc-component/context';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nimport { getTargetScrollBarSize } from \"rc-util/es/getScrollBarSize\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport getValue from \"rc-util/es/utils/get\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport Body from \"./Body\";\nimport ColGroup from \"./ColGroup\";\nimport { EXPAND_COLUMN, INTERNAL_HOOKS } from \"./constant\";\nimport TableContext from \"./context/TableContext\";\nimport FixedHolder from \"./FixedHolder\";\nimport Footer, { FooterComponents } from \"./Footer\";\nimport Summary from \"./Footer/Summary\";\nimport Header from \"./Header/Header\";\nimport useColumns from \"./hooks/useColumns\";\nimport useExpand from \"./hooks/useExpand\";\nimport useFixedInfo from \"./hooks/useFixedInfo\";\nimport { useLayoutState, useTimeoutLock } from \"./hooks/useFrame\";\nimport useHover from \"./hooks/useHover\";\nimport useSticky from \"./hooks/useSticky\";\nimport useStickyOffsets from \"./hooks/useStickyOffsets\";\nimport Panel from \"./Panel\";\nimport StickyScrollBar from \"./stickyScrollBar\";\nimport Column from \"./sugar/Column\";\nimport ColumnGroup from \"./sugar/ColumnGroup\";\nimport { getColumnsKey, validateValue } from \"./utils/valueUtil\";\n\n// Used for conditions cache\nvar EMPTY_DATA = [];\n\n// Used for customize scroll\nvar EMPTY_SCROLL_TARGET = {};\nfunction defaultEmpty() {\n  return 'No Data';\n}\nfunction Table(tableProps) {\n  var _classNames;\n  var props = _objectSpread({\n    rowKey: 'key',\n    prefixCls: 'rc-table',\n    emptyText: defaultEmpty\n  }, tableProps);\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    rowClassName = props.rowClassName,\n    style = props.style,\n    data = props.data,\n    rowKey = props.rowKey,\n    scroll = props.scroll,\n    tableLayout = props.tableLayout,\n    direction = props.direction,\n    title = props.title,\n    footer = props.footer,\n    summary = props.summary,\n    caption = props.caption,\n    id = props.id,\n    showHeader = props.showHeader,\n    components = props.components,\n    emptyText = props.emptyText,\n    onRow = props.onRow,\n    onHeaderRow = props.onHeaderRow,\n    internalHooks = props.internalHooks,\n    transformColumns = props.transformColumns,\n    internalRefs = props.internalRefs,\n    sticky = props.sticky;\n  var mergedData = data || EMPTY_DATA;\n  var hasData = !!mergedData.length;\n\n  // ===================== Warning ======================\n  if (process.env.NODE_ENV !== 'production') {\n    ['onRowClick', 'onRowDoubleClick', 'onRowContextMenu', 'onRowMouseEnter', 'onRowMouseLeave'].forEach(function (name) {\n      warning(props[name] === undefined, \"`\".concat(name, \"` is removed, please use `onRow` instead.\"));\n    });\n    warning(!('getBodyWrapper' in props), '`getBodyWrapper` is deprecated, please use custom `components` instead.');\n  }\n\n  // ==================== Customize =====================\n  var getComponent = React.useCallback(function (path, defaultComponent) {\n    return getValue(components, path) || defaultComponent;\n  }, [components]);\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record) {\n      var key = record && record[rowKey];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(key !== undefined, 'Each record in table should have a unique `key` prop, or set `rowKey` to an unique primary key.');\n      }\n      return key;\n    };\n  }, [rowKey]);\n\n  // ====================== Hover =======================\n  var _useHover = useHover(),\n    _useHover2 = _slicedToArray(_useHover, 3),\n    startRow = _useHover2[0],\n    endRow = _useHover2[1],\n    onHover = _useHover2[2];\n\n  // ====================== Expand ======================\n  var _useExpand = useExpand(props, mergedData, getRowKey),\n    _useExpand2 = _slicedToArray(_useExpand, 6),\n    expandableConfig = _useExpand2[0],\n    expandableType = _useExpand2[1],\n    mergedExpandedKeys = _useExpand2[2],\n    mergedExpandIcon = _useExpand2[3],\n    mergedChildrenColumnName = _useExpand2[4],\n    onTriggerExpand = _useExpand2[5];\n\n  // ====================== Column ======================\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    componentWidth = _React$useState2[0],\n    setComponentWidth = _React$useState2[1];\n  var _useColumns = useColumns(_objectSpread(_objectSpread(_objectSpread({}, props), expandableConfig), {}, {\n      expandable: !!expandableConfig.expandedRowRender,\n      columnTitle: expandableConfig.columnTitle,\n      expandedKeys: mergedExpandedKeys,\n      getRowKey: getRowKey,\n      // https://github.com/ant-design/ant-design/issues/23894\n      onTriggerExpand: onTriggerExpand,\n      expandIcon: mergedExpandIcon,\n      expandIconColumnIndex: expandableConfig.expandIconColumnIndex,\n      direction: direction\n    }), internalHooks === INTERNAL_HOOKS ? transformColumns : null),\n    _useColumns2 = _slicedToArray(_useColumns, 2),\n    columns = _useColumns2[0],\n    flattenColumns = _useColumns2[1];\n  var columnContext = React.useMemo(function () {\n    return {\n      columns: columns,\n      flattenColumns: flattenColumns\n    };\n  }, [columns, flattenColumns]);\n\n  // ====================== Scroll ======================\n  var fullTableRef = React.useRef();\n  var scrollHeaderRef = React.useRef();\n  var scrollBodyRef = React.useRef();\n  var scrollBodyContainerRef = React.useRef();\n  var scrollSummaryRef = React.useRef();\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    pingedLeft = _React$useState4[0],\n    setPingedLeft = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    pingedRight = _React$useState6[0],\n    setPingedRight = _React$useState6[1];\n  var _useLayoutState = useLayoutState(new Map()),\n    _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n    colsWidths = _useLayoutState2[0],\n    updateColsWidths = _useLayoutState2[1];\n\n  // Convert map to number width\n  var colsKeys = getColumnsKey(flattenColumns);\n  var pureColWidths = colsKeys.map(function (columnKey) {\n    return colsWidths.get(columnKey);\n  });\n  var colWidths = React.useMemo(function () {\n    return pureColWidths;\n  }, [pureColWidths.join('_')]);\n  var stickyOffsets = useStickyOffsets(colWidths, flattenColumns.length, direction);\n  var fixHeader = scroll && validateValue(scroll.y);\n  var horizonScroll = scroll && validateValue(scroll.x) || Boolean(expandableConfig.fixed);\n  var fixColumn = horizonScroll && flattenColumns.some(function (_ref) {\n    var fixed = _ref.fixed;\n    return fixed;\n  });\n\n  // Sticky\n  var stickyRef = React.useRef();\n  var _useSticky = useSticky(sticky, prefixCls),\n    isSticky = _useSticky.isSticky,\n    offsetHeader = _useSticky.offsetHeader,\n    offsetSummary = _useSticky.offsetSummary,\n    offsetScroll = _useSticky.offsetScroll,\n    stickyClassName = _useSticky.stickyClassName,\n    container = _useSticky.container;\n\n  // Footer (Fix footer must fixed header)\n  var summaryNode = React.useMemo(function () {\n    return summary === null || summary === void 0 ? void 0 : summary(mergedData);\n  }, [summary, mergedData]);\n  var fixFooter = (fixHeader || isSticky) && /*#__PURE__*/React.isValidElement(summaryNode) && summaryNode.type === Summary && summaryNode.props.fixed;\n\n  // Scroll\n  var scrollXStyle;\n  var scrollYStyle;\n  var scrollTableStyle;\n  if (fixHeader) {\n    scrollYStyle = {\n      overflowY: 'scroll',\n      maxHeight: scroll.y\n    };\n  }\n  if (horizonScroll) {\n    scrollXStyle = {\n      overflowX: 'auto'\n    };\n    // When no vertical scrollbar, should hide it\n    // https://github.com/ant-design/ant-design/pull/20705\n    // https://github.com/ant-design/ant-design/issues/21879\n    if (!fixHeader) {\n      scrollYStyle = {\n        overflowY: 'hidden'\n      };\n    }\n    scrollTableStyle = {\n      width: (scroll === null || scroll === void 0 ? void 0 : scroll.x) === true ? 'auto' : scroll === null || scroll === void 0 ? void 0 : scroll.x,\n      minWidth: '100%'\n    };\n  }\n  var onColumnResize = React.useCallback(function (columnKey, width) {\n    if (isVisible(fullTableRef.current)) {\n      updateColsWidths(function (widths) {\n        if (widths.get(columnKey) !== width) {\n          var newWidths = new Map(widths);\n          newWidths.set(columnKey, width);\n          return newWidths;\n        }\n        return widths;\n      });\n    }\n  }, []);\n  var _useTimeoutLock = useTimeoutLock(null),\n    _useTimeoutLock2 = _slicedToArray(_useTimeoutLock, 2),\n    setScrollTarget = _useTimeoutLock2[0],\n    getScrollTarget = _useTimeoutLock2[1];\n  function forceScroll(scrollLeft, target) {\n    if (!target) {\n      return;\n    }\n    if (typeof target === 'function') {\n      target(scrollLeft);\n    } else if (target.scrollLeft !== scrollLeft) {\n      target.scrollLeft = scrollLeft;\n\n      // Delay to force scroll position if not sync\n      // ref: https://github.com/ant-design/ant-design/issues/37179\n      if (target.scrollLeft !== scrollLeft) {\n        setTimeout(function () {\n          target.scrollLeft = scrollLeft;\n        }, 0);\n      }\n    }\n  }\n  var onScroll = useEvent(function (_ref2) {\n    var currentTarget = _ref2.currentTarget,\n      scrollLeft = _ref2.scrollLeft;\n    var isRTL = direction === 'rtl';\n    var mergedScrollLeft = typeof scrollLeft === 'number' ? scrollLeft : currentTarget.scrollLeft;\n    var compareTarget = currentTarget || EMPTY_SCROLL_TARGET;\n    if (!getScrollTarget() || getScrollTarget() === compareTarget) {\n      var _stickyRef$current;\n      setScrollTarget(compareTarget);\n      forceScroll(mergedScrollLeft, scrollHeaderRef.current);\n      forceScroll(mergedScrollLeft, scrollBodyRef.current);\n      forceScroll(mergedScrollLeft, scrollSummaryRef.current);\n      forceScroll(mergedScrollLeft, (_stickyRef$current = stickyRef.current) === null || _stickyRef$current === void 0 ? void 0 : _stickyRef$current.setScrollLeft);\n    }\n    if (currentTarget) {\n      var scrollWidth = currentTarget.scrollWidth,\n        clientWidth = currentTarget.clientWidth;\n      // There is no space to scroll\n      if (scrollWidth === clientWidth) {\n        setPingedLeft(false);\n        setPingedRight(false);\n        return;\n      }\n      if (isRTL) {\n        setPingedLeft(-mergedScrollLeft < scrollWidth - clientWidth);\n        setPingedRight(-mergedScrollLeft > 0);\n      } else {\n        setPingedLeft(mergedScrollLeft > 0);\n        setPingedRight(mergedScrollLeft < scrollWidth - clientWidth);\n      }\n    }\n  });\n  var triggerOnScroll = function triggerOnScroll() {\n    if (horizonScroll && scrollBodyRef.current) {\n      onScroll({\n        currentTarget: scrollBodyRef.current\n      });\n    } else {\n      setPingedLeft(false);\n      setPingedRight(false);\n    }\n  };\n  var onFullTableResize = function onFullTableResize(_ref3) {\n    var width = _ref3.width;\n    if (width !== componentWidth) {\n      triggerOnScroll();\n      setComponentWidth(fullTableRef.current ? fullTableRef.current.offsetWidth : width);\n    }\n  };\n\n  // Sync scroll bar when init or `horizonScroll`, `data` and `columns.length` changed\n  var mounted = React.useRef(false);\n  React.useEffect(function () {\n    // onFullTableResize will be trigger once when ResizeObserver is mounted\n    // This will reduce one duplicated triggerOnScroll time\n    if (mounted.current) {\n      triggerOnScroll();\n    }\n  }, [horizonScroll, data, columns.length]);\n  React.useEffect(function () {\n    mounted.current = true;\n  }, []);\n\n  // ===================== Effects ======================\n  var _React$useState7 = React.useState(0),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    scrollbarSize = _React$useState8[0],\n    setScrollbarSize = _React$useState8[1];\n  var _React$useState9 = React.useState(true),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    supportSticky = _React$useState10[0],\n    setSupportSticky = _React$useState10[1]; // Only IE not support, we mark as support first\n\n  React.useEffect(function () {\n    if (scrollBodyRef.current instanceof Element) {\n      setScrollbarSize(getTargetScrollBarSize(scrollBodyRef.current).width);\n    } else {\n      setScrollbarSize(getTargetScrollBarSize(scrollBodyContainerRef.current).width);\n    }\n    setSupportSticky(isStyleSupport('position', 'sticky'));\n  }, []);\n\n  // ================== INTERNAL HOOKS ==================\n  React.useEffect(function () {\n    if (internalHooks === INTERNAL_HOOKS && internalRefs) {\n      internalRefs.body.current = scrollBodyRef.current;\n    }\n  });\n\n  // ========================================================================\n  // ==                               Render                               ==\n  // ========================================================================\n  // =================== Render: Func ===================\n  var renderFixedHeaderTable = React.useCallback(function (fixedHolderPassProps) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Header, fixedHolderPassProps), fixFooter === 'top' && /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode));\n  }, [fixFooter, summaryNode]);\n  var renderFixedFooterTable = React.useCallback(function (fixedHolderPassProps) {\n    return /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode);\n  }, [summaryNode]);\n\n  // =================== Render: Node ===================\n  var TableComponent = getComponent(['table'], 'table');\n\n  // Table layout\n  var mergedTableLayout = React.useMemo(function () {\n    if (tableLayout) {\n      return tableLayout;\n    }\n    // https://github.com/ant-design/ant-design/issues/25227\n    // When scroll.x is max-content, no need to fix table layout\n    // it's width should stretch out to fit content\n    if (fixColumn) {\n      return (scroll === null || scroll === void 0 ? void 0 : scroll.x) === 'max-content' ? 'auto' : 'fixed';\n    }\n    if (fixHeader || isSticky || flattenColumns.some(function (_ref4) {\n      var ellipsis = _ref4.ellipsis;\n      return ellipsis;\n    })) {\n      return 'fixed';\n    }\n    return 'auto';\n  }, [fixHeader, fixColumn, flattenColumns, tableLayout, isSticky]);\n  var groupTableNode;\n\n  // Header props\n  var headerProps = {\n    colWidths: colWidths,\n    columCount: flattenColumns.length,\n    stickyOffsets: stickyOffsets,\n    onHeaderRow: onHeaderRow,\n    fixHeader: fixHeader,\n    scroll: scroll\n  };\n\n  // Empty\n  var emptyNode = React.useMemo(function () {\n    if (hasData) {\n      return null;\n    }\n    if (typeof emptyText === 'function') {\n      return emptyText();\n    }\n    return emptyText;\n  }, [hasData, emptyText]);\n\n  // Body\n  var bodyTable = /*#__PURE__*/React.createElement(Body, {\n    data: mergedData,\n    measureColumnWidth: fixHeader || horizonScroll || isSticky,\n    expandedKeys: mergedExpandedKeys,\n    rowExpandable: expandableConfig.rowExpandable,\n    getRowKey: getRowKey,\n    onRow: onRow,\n    emptyNode: emptyNode,\n    childrenColumnName: mergedChildrenColumnName\n  });\n  var bodyColGroup = /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: flattenColumns.map(function (_ref5) {\n      var width = _ref5.width;\n      return width;\n    }),\n    columns: flattenColumns\n  });\n  var captionElement = caption !== null && caption !== undefined ? /*#__PURE__*/React.createElement(\"caption\", {\n    className: \"\".concat(prefixCls, \"-caption\")\n  }, caption) : undefined;\n  var customizeScrollBody = getComponent(['body']);\n  if (process.env.NODE_ENV !== 'production' && typeof customizeScrollBody === 'function' && hasData && !fixHeader) {\n    warning(false, '`components.body` with render props is only work on `scroll.y`.');\n  }\n  var dataProps = pickAttrs(props, {\n    data: true\n  });\n  var ariaProps = pickAttrs(props, {\n    aria: true\n  });\n  if (fixHeader || isSticky) {\n    // >>>>>> Fixed Header\n    var bodyContent;\n    if (typeof customizeScrollBody === 'function') {\n      bodyContent = customizeScrollBody(mergedData, {\n        scrollbarSize: scrollbarSize,\n        ref: scrollBodyRef,\n        onScroll: onScroll\n      });\n      headerProps.colWidths = flattenColumns.map(function (_ref6, index) {\n        var width = _ref6.width;\n        var colWidth = index === flattenColumns.length - 1 ? width - scrollbarSize : width;\n        if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {\n          return colWidth;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          warning(props.columns.length === 0, 'When use `components.body` with render props. Each column should have a fixed `width` value.');\n        }\n        return 0;\n      });\n    } else {\n      bodyContent = /*#__PURE__*/React.createElement(\"div\", {\n        style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n        onScroll: onScroll,\n        ref: scrollBodyRef,\n        className: classNames(\"\".concat(prefixCls, \"-body\"))\n      }, /*#__PURE__*/React.createElement(TableComponent, _extends({\n        style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n          tableLayout: mergedTableLayout\n        })\n      }, ariaProps), captionElement, bodyColGroup, bodyTable, !fixFooter && summaryNode && /*#__PURE__*/React.createElement(Footer, {\n        stickyOffsets: stickyOffsets,\n        flattenColumns: flattenColumns,\n        columns: columns\n      }, summaryNode)));\n    }\n\n    // Fixed holder share the props\n    var fixedHolderProps = _objectSpread(_objectSpread(_objectSpread({\n      noData: !mergedData.length,\n      maxContentScroll: horizonScroll && scroll.x === 'max-content'\n    }, headerProps), columnContext), {}, {\n      direction: direction,\n      stickyClassName: stickyClassName,\n      onScroll: onScroll\n    });\n    groupTableNode = /*#__PURE__*/React.createElement(React.Fragment, null, showHeader !== false && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyTopOffset: offsetHeader,\n      className: \"\".concat(prefixCls, \"-header\"),\n      ref: scrollHeaderRef\n    }), renderFixedHeaderTable), bodyContent, fixFooter && fixFooter !== 'top' && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyBottomOffset: offsetSummary,\n      className: \"\".concat(prefixCls, \"-summary\"),\n      ref: scrollSummaryRef\n    }), renderFixedFooterTable), isSticky && /*#__PURE__*/React.createElement(StickyScrollBar, {\n      ref: stickyRef,\n      offsetScroll: offsetScroll,\n      scrollBodyRef: scrollBodyRef,\n      onScroll: onScroll,\n      container: container\n    }));\n  } else {\n    // >>>>>> Unique table\n    groupTableNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n      className: classNames(\"\".concat(prefixCls, \"-content\")),\n      onScroll: onScroll,\n      ref: scrollBodyRef\n    }, /*#__PURE__*/React.createElement(TableComponent, _extends({\n      style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n        tableLayout: mergedTableLayout\n      })\n    }, ariaProps), captionElement, bodyColGroup, showHeader !== false && /*#__PURE__*/React.createElement(Header, _extends({}, headerProps, columnContext)), bodyTable, summaryNode && /*#__PURE__*/React.createElement(Footer, {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns,\n      columns: columns\n    }, summaryNode)));\n  }\n  var fullTable = /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-left\"), pingedLeft), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ping-right\"), pingedRight), _defineProperty(_classNames, \"\".concat(prefixCls, \"-layout-fixed\"), tableLayout === 'fixed'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-header\"), fixHeader), _defineProperty(_classNames, \"\".concat(prefixCls, \"-fixed-column\"), fixColumn), _defineProperty(_classNames, \"\".concat(prefixCls, \"-scroll-horizontal\"), horizonScroll), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-left\"), flattenColumns[0] && flattenColumns[0].fixed), _defineProperty(_classNames, \"\".concat(prefixCls, \"-has-fix-right\"), flattenColumns[flattenColumns.length - 1] && flattenColumns[flattenColumns.length - 1].fixed === 'right'), _classNames)),\n    style: style,\n    id: id,\n    ref: fullTableRef\n  }, dataProps), title && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title(mergedData)), /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollBodyContainerRef,\n    className: \"\".concat(prefixCls, \"-container\")\n  }, groupTableNode), footer && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, footer(mergedData)));\n  if (horizonScroll) {\n    fullTable = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onFullTableResize\n    }, fullTable);\n  }\n  var fixedInfoList = useFixedInfo(flattenColumns, stickyOffsets, direction, columns);\n  var TableContextValue = React.useMemo(function () {\n    return {\n      // Table\n      prefixCls: prefixCls,\n      getComponent: getComponent,\n      scrollbarSize: scrollbarSize,\n      direction: direction,\n      fixedInfoList: fixedInfoList,\n      isSticky: isSticky,\n      supportSticky: supportSticky,\n      componentWidth: componentWidth,\n      fixHeader: fixHeader,\n      fixColumn: fixColumn,\n      horizonScroll: horizonScroll,\n      // Body\n\n      tableLayout: mergedTableLayout,\n      rowClassName: rowClassName,\n      expandedRowClassName: expandableConfig.expandedRowClassName,\n      expandIcon: mergedExpandIcon,\n      expandableType: expandableType,\n      expandRowByClick: expandableConfig.expandRowByClick,\n      expandedRowRender: expandableConfig.expandedRowRender,\n      onTriggerExpand: onTriggerExpand,\n      expandIconColumnIndex: expandableConfig.expandIconColumnIndex,\n      indentSize: expandableConfig.indentSize,\n      allColumnsFixedLeft: flattenColumns.every(function (col) {\n        return col.fixed === 'left';\n      }),\n      // Column\n      columns: columns,\n      flattenColumns: flattenColumns,\n      onColumnResize: onColumnResize,\n      // Row\n      hoverStartRow: startRow,\n      hoverEndRow: endRow,\n      onHover: onHover\n    };\n  }, [\n  // Table\n  prefixCls, getComponent, scrollbarSize, direction, fixedInfoList, isSticky, supportSticky, componentWidth, fixHeader, fixColumn, horizonScroll,\n  // Body\n  mergedTableLayout, rowClassName, expandableConfig.expandedRowClassName, mergedExpandIcon, expandableType, expandableConfig.expandRowByClick, expandableConfig.expandedRowRender, onTriggerExpand, expandableConfig.expandIconColumnIndex, expandableConfig.indentSize,\n  // Column\n  columns, flattenColumns, onColumnResize,\n  // Row\n  startRow, endRow, onHover]);\n  return /*#__PURE__*/React.createElement(TableContext.Provider, {\n    value: TableContextValue\n  }, fullTable);\n}\nexport function genTable(shouldTriggerRender) {\n  return makeImmutable(Table, shouldTriggerRender);\n}\nvar ImmutableTable = genTable();\nImmutableTable.EXPAND_COLUMN = EXPAND_COLUMN;\nImmutableTable.INTERNAL_HOOKS = INTERNAL_HOOKS;\nImmutableTable.Column = Column;\nImmutableTable.ColumnGroup = ColumnGroup;\nImmutableTable.Summary = FooterComponents;\nexport default ImmutableTable;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,aAAa,EAAEC,cAAc,QAAQ,YAAY;AAC1D,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,MAAM,IAAIC,gBAAgB,QAAQ,UAAU;AACnD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SAASC,cAAc,EAAEC,cAAc,QAAQ,kBAAkB;AACjE,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,aAAa,EAAEC,aAAa,QAAQ,mBAAmB;;AAEhE;AACA,IAAIC,UAAU,GAAG,EAAE;;AAEnB;AACA,IAAIC,mBAAmB,GAAG,CAAC,CAAC;AAC5B,SAASC,YAAYA,CAAA,EAAG;EACtB,OAAO,SAAS;AAClB;AACA,SAASC,KAAKA,CAACC,UAAU,EAAE;EACzB,IAAIC,WAAW;EACf,IAAIC,KAAK,GAAG1C,aAAa,CAAC;IACxB2C,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAEP;EACb,CAAC,EAAEE,UAAU,CAAC;EACd,IAAII,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BE,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,IAAI,GAAGP,KAAK,CAACO,IAAI;IACjBN,MAAM,GAAGD,KAAK,CAACC,MAAM;IACrBO,MAAM,GAAGR,KAAK,CAACQ,MAAM;IACrBC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,MAAM,GAAGZ,KAAK,CAACY,MAAM;IACrBC,OAAO,GAAGb,KAAK,CAACa,OAAO;IACvBC,OAAO,GAAGd,KAAK,CAACc,OAAO;IACvBC,EAAE,GAAGf,KAAK,CAACe,EAAE;IACbC,UAAU,GAAGhB,KAAK,CAACgB,UAAU;IAC7BC,UAAU,GAAGjB,KAAK,CAACiB,UAAU;IAC7Bd,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3Be,KAAK,GAAGlB,KAAK,CAACkB,KAAK;IACnBC,WAAW,GAAGnB,KAAK,CAACmB,WAAW;IAC/BC,aAAa,GAAGpB,KAAK,CAACoB,aAAa;IACnCC,gBAAgB,GAAGrB,KAAK,CAACqB,gBAAgB;IACzCC,YAAY,GAAGtB,KAAK,CAACsB,YAAY;IACjCC,MAAM,GAAGvB,KAAK,CAACuB,MAAM;EACvB,IAAIC,UAAU,GAAGjB,IAAI,IAAIb,UAAU;EACnC,IAAI+B,OAAO,GAAG,CAAC,CAACD,UAAU,CAACE,MAAM;;EAEjC;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,CAAC,YAAY,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MACnH/D,OAAO,CAACgC,KAAK,CAAC+B,IAAI,CAAC,KAAKC,SAAS,EAAE,GAAG,CAACC,MAAM,CAACF,IAAI,EAAE,2CAA2C,CAAC,CAAC;IACnG,CAAC,CAAC;IACF/D,OAAO,CAAC,EAAE,gBAAgB,IAAIgC,KAAK,CAAC,EAAE,yEAAyE,CAAC;EAClH;;EAEA;EACA,IAAIkC,YAAY,GAAGjE,KAAK,CAACkE,WAAW,CAAC,UAAUC,IAAI,EAAEC,gBAAgB,EAAE;IACrE,OAAOtE,QAAQ,CAACkD,UAAU,EAAEmB,IAAI,CAAC,IAAIC,gBAAgB;EACvD,CAAC,EAAE,CAACpB,UAAU,CAAC,CAAC;EAChB,IAAIqB,SAAS,GAAGrE,KAAK,CAACsE,OAAO,CAAC,YAAY;IACxC,IAAI,OAAOtC,MAAM,KAAK,UAAU,EAAE;MAChC,OAAOA,MAAM;IACf;IACA,OAAO,UAAUuC,MAAM,EAAE;MACvB,IAAIC,GAAG,GAAGD,MAAM,IAAIA,MAAM,CAACvC,MAAM,CAAC;MAClC,IAAI0B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC7D,OAAO,CAACyE,GAAG,KAAKT,SAAS,EAAE,iGAAiG,CAAC;MAC/H;MACA,OAAOS,GAAG;IACZ,CAAC;EACH,CAAC,EAAE,CAACxC,MAAM,CAAC,CAAC;;EAEZ;EACA,IAAIyC,SAAS,GAAGzD,QAAQ,CAAC,CAAC;IACxB0D,UAAU,GAAGtF,cAAc,CAACqF,SAAS,EAAE,CAAC,CAAC;IACzCE,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;IACxBE,MAAM,GAAGF,UAAU,CAAC,CAAC,CAAC;IACtBG,OAAO,GAAGH,UAAU,CAAC,CAAC,CAAC;;EAEzB;EACA,IAAII,UAAU,GAAGlE,SAAS,CAACmB,KAAK,EAAEwB,UAAU,EAAEc,SAAS,CAAC;IACtDU,WAAW,GAAG3F,cAAc,CAAC0F,UAAU,EAAE,CAAC,CAAC;IAC3CE,gBAAgB,GAAGD,WAAW,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,WAAW,CAAC,CAAC,CAAC;IAC/BG,kBAAkB,GAAGH,WAAW,CAAC,CAAC,CAAC;IACnCI,gBAAgB,GAAGJ,WAAW,CAAC,CAAC,CAAC;IACjCK,wBAAwB,GAAGL,WAAW,CAAC,CAAC,CAAC;IACzCM,eAAe,GAAGN,WAAW,CAAC,CAAC,CAAC;;EAElC;EACA,IAAIO,eAAe,GAAGtF,KAAK,CAACuF,QAAQ,CAAC,CAAC,CAAC;IACrCC,gBAAgB,GAAGpG,cAAc,CAACkG,eAAe,EAAE,CAAC,CAAC;IACrDG,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzC,IAAIG,WAAW,GAAGhF,UAAU,CAACtB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0C,KAAK,CAAC,EAAEiD,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;MACtGY,UAAU,EAAE,CAAC,CAACZ,gBAAgB,CAACa,iBAAiB;MAChDC,WAAW,EAAEd,gBAAgB,CAACc,WAAW;MACzCC,YAAY,EAAEb,kBAAkB;MAChCb,SAAS,EAAEA,SAAS;MACpB;MACAgB,eAAe,EAAEA,eAAe;MAChCW,UAAU,EAAEb,gBAAgB;MAC5Bc,qBAAqB,EAAEjB,gBAAgB,CAACiB,qBAAqB;MAC7DxD,SAAS,EAAEA;IACb,CAAC,CAAC,EAAEU,aAAa,KAAK/C,cAAc,GAAGgD,gBAAgB,GAAG,IAAI,CAAC;IAC/D8C,YAAY,GAAG9G,cAAc,CAACuG,WAAW,EAAE,CAAC,CAAC;IAC7CQ,OAAO,GAAGD,YAAY,CAAC,CAAC,CAAC;IACzBE,cAAc,GAAGF,YAAY,CAAC,CAAC,CAAC;EAClC,IAAIG,aAAa,GAAGrG,KAAK,CAACsE,OAAO,CAAC,YAAY;IAC5C,OAAO;MACL6B,OAAO,EAAEA,OAAO;MAChBC,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACD,OAAO,EAAEC,cAAc,CAAC,CAAC;;EAE7B;EACA,IAAIE,YAAY,GAAGtG,KAAK,CAACuG,MAAM,CAAC,CAAC;EACjC,IAAIC,eAAe,GAAGxG,KAAK,CAACuG,MAAM,CAAC,CAAC;EACpC,IAAIE,aAAa,GAAGzG,KAAK,CAACuG,MAAM,CAAC,CAAC;EAClC,IAAIG,sBAAsB,GAAG1G,KAAK,CAACuG,MAAM,CAAC,CAAC;EAC3C,IAAII,gBAAgB,GAAG3G,KAAK,CAACuG,MAAM,CAAC,CAAC;EACrC,IAAIK,gBAAgB,GAAG5G,KAAK,CAACuF,QAAQ,CAAC,KAAK,CAAC;IAC1CsB,gBAAgB,GAAGzH,cAAc,CAACwH,gBAAgB,EAAE,CAAC,CAAC;IACtDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,gBAAgB,GAAGhH,KAAK,CAACuF,QAAQ,CAAC,KAAK,CAAC;IAC1C0B,gBAAgB,GAAG7H,cAAc,CAAC4H,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,eAAe,GAAGtG,cAAc,CAAC,IAAIuG,GAAG,CAAC,CAAC,CAAC;IAC7CC,gBAAgB,GAAGlI,cAAc,CAACgI,eAAe,EAAE,CAAC,CAAC;IACrDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAExC;EACA,IAAIG,QAAQ,GAAGlG,aAAa,CAAC6E,cAAc,CAAC;EAC5C,IAAIsB,aAAa,GAAGD,QAAQ,CAACE,GAAG,CAAC,UAAUC,SAAS,EAAE;IACpD,OAAOL,UAAU,CAACM,GAAG,CAACD,SAAS,CAAC;EAClC,CAAC,CAAC;EACF,IAAIE,SAAS,GAAG9H,KAAK,CAACsE,OAAO,CAAC,YAAY;IACxC,OAAOoD,aAAa;EACtB,CAAC,EAAE,CAACA,aAAa,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EAC7B,IAAIC,aAAa,GAAG9G,gBAAgB,CAAC4G,SAAS,EAAE1B,cAAc,CAAC3C,MAAM,EAAEhB,SAAS,CAAC;EACjF,IAAIwF,SAAS,GAAG1F,MAAM,IAAIf,aAAa,CAACe,MAAM,CAAC2F,CAAC,CAAC;EACjD,IAAIC,aAAa,GAAG5F,MAAM,IAAIf,aAAa,CAACe,MAAM,CAAC6F,CAAC,CAAC,IAAIC,OAAO,CAACrD,gBAAgB,CAACsD,KAAK,CAAC;EACxF,IAAIC,SAAS,GAAGJ,aAAa,IAAI/B,cAAc,CAACoC,IAAI,CAAC,UAAUC,IAAI,EAAE;IACnE,IAAIH,KAAK,GAAGG,IAAI,CAACH,KAAK;IACtB,OAAOA,KAAK;EACd,CAAC,CAAC;;EAEF;EACA,IAAII,SAAS,GAAG1I,KAAK,CAACuG,MAAM,CAAC,CAAC;EAC9B,IAAIoC,UAAU,GAAG1H,SAAS,CAACqC,MAAM,EAAErB,SAAS,CAAC;IAC3C2G,QAAQ,GAAGD,UAAU,CAACC,QAAQ;IAC9BC,YAAY,GAAGF,UAAU,CAACE,YAAY;IACtCC,aAAa,GAAGH,UAAU,CAACG,aAAa;IACxCC,YAAY,GAAGJ,UAAU,CAACI,YAAY;IACtCC,eAAe,GAAGL,UAAU,CAACK,eAAe;IAC5CC,SAAS,GAAGN,UAAU,CAACM,SAAS;;EAElC;EACA,IAAIC,WAAW,GAAGlJ,KAAK,CAACsE,OAAO,CAAC,YAAY;IAC1C,OAAO1B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,UAAU,CAAC;EAC9E,CAAC,EAAE,CAACX,OAAO,EAAEW,UAAU,CAAC,CAAC;EACzB,IAAI4F,SAAS,GAAG,CAAClB,SAAS,IAAIW,QAAQ,KAAK,aAAa5I,KAAK,CAACoJ,cAAc,CAACF,WAAW,CAAC,IAAIA,WAAW,CAACG,IAAI,KAAK5I,OAAO,IAAIyI,WAAW,CAACnH,KAAK,CAACuG,KAAK;;EAEpJ;EACA,IAAIgB,YAAY;EAChB,IAAIC,YAAY;EAChB,IAAIC,gBAAgB;EACpB,IAAIvB,SAAS,EAAE;IACbsB,YAAY,GAAG;MACbE,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAEnH,MAAM,CAAC2F;IACpB,CAAC;EACH;EACA,IAAIC,aAAa,EAAE;IACjBmB,YAAY,GAAG;MACbK,SAAS,EAAE;IACb,CAAC;IACD;IACA;IACA;IACA,IAAI,CAAC1B,SAAS,EAAE;MACdsB,YAAY,GAAG;QACbE,SAAS,EAAE;MACb,CAAC;IACH;IACAD,gBAAgB,GAAG;MACjBI,KAAK,EAAE,CAACrH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6F,CAAC,MAAM,IAAI,GAAG,MAAM,GAAG7F,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6F,CAAC;MAC9IyB,QAAQ,EAAE;IACZ,CAAC;EACH;EACA,IAAIC,cAAc,GAAG9J,KAAK,CAACkE,WAAW,CAAC,UAAU0D,SAAS,EAAEgC,KAAK,EAAE;IACjE,IAAInK,SAAS,CAAC6G,YAAY,CAACyD,OAAO,CAAC,EAAE;MACnCvC,gBAAgB,CAAC,UAAUwC,MAAM,EAAE;QACjC,IAAIA,MAAM,CAACnC,GAAG,CAACD,SAAS,CAAC,KAAKgC,KAAK,EAAE;UACnC,IAAIK,SAAS,GAAG,IAAI5C,GAAG,CAAC2C,MAAM,CAAC;UAC/BC,SAAS,CAACC,GAAG,CAACtC,SAAS,EAAEgC,KAAK,CAAC;UAC/B,OAAOK,SAAS;QAClB;QACA,OAAOD,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAIG,eAAe,GAAGpJ,cAAc,CAAC,IAAI,CAAC;IACxCqJ,gBAAgB,GAAGhL,cAAc,CAAC+K,eAAe,EAAE,CAAC,CAAC;IACrDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,SAASG,WAAWA,CAACC,UAAU,EAAEC,MAAM,EAAE;IACvC,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IACA,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChCA,MAAM,CAACD,UAAU,CAAC;IACpB,CAAC,MAAM,IAAIC,MAAM,CAACD,UAAU,KAAKA,UAAU,EAAE;MAC3CC,MAAM,CAACD,UAAU,GAAGA,UAAU;;MAE9B;MACA;MACA,IAAIC,MAAM,CAACD,UAAU,KAAKA,UAAU,EAAE;QACpCE,UAAU,CAAC,YAAY;UACrBD,MAAM,CAACD,UAAU,GAAGA,UAAU;QAChC,CAAC,EAAE,CAAC,CAAC;MACP;IACF;EACF;EACA,IAAIG,QAAQ,GAAG/K,QAAQ,CAAC,UAAUgL,KAAK,EAAE;IACvC,IAAIC,aAAa,GAAGD,KAAK,CAACC,aAAa;MACrCL,UAAU,GAAGI,KAAK,CAACJ,UAAU;IAC/B,IAAIM,KAAK,GAAGrI,SAAS,KAAK,KAAK;IAC/B,IAAIsI,gBAAgB,GAAG,OAAOP,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGK,aAAa,CAACL,UAAU;IAC7F,IAAIQ,aAAa,GAAGH,aAAa,IAAInJ,mBAAmB;IACxD,IAAI,CAAC4I,eAAe,CAAC,CAAC,IAAIA,eAAe,CAAC,CAAC,KAAKU,aAAa,EAAE;MAC7D,IAAIC,kBAAkB;MACtBZ,eAAe,CAACW,aAAa,CAAC;MAC9BT,WAAW,CAACQ,gBAAgB,EAAEvE,eAAe,CAACuD,OAAO,CAAC;MACtDQ,WAAW,CAACQ,gBAAgB,EAAEtE,aAAa,CAACsD,OAAO,CAAC;MACpDQ,WAAW,CAACQ,gBAAgB,EAAEpE,gBAAgB,CAACoD,OAAO,CAAC;MACvDQ,WAAW,CAACQ,gBAAgB,EAAE,CAACE,kBAAkB,GAAGvC,SAAS,CAACqB,OAAO,MAAM,IAAI,IAAIkB,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,aAAa,CAAC;IAC/J;IACA,IAAIL,aAAa,EAAE;MACjB,IAAIM,WAAW,GAAGN,aAAa,CAACM,WAAW;QACzCC,WAAW,GAAGP,aAAa,CAACO,WAAW;MACzC;MACA,IAAID,WAAW,KAAKC,WAAW,EAAE;QAC/BrE,aAAa,CAAC,KAAK,CAAC;QACpBI,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;MACA,IAAI2D,KAAK,EAAE;QACT/D,aAAa,CAAC,CAACgE,gBAAgB,GAAGI,WAAW,GAAGC,WAAW,CAAC;QAC5DjE,cAAc,CAAC,CAAC4D,gBAAgB,GAAG,CAAC,CAAC;MACvC,CAAC,MAAM;QACLhE,aAAa,CAACgE,gBAAgB,GAAG,CAAC,CAAC;QACnC5D,cAAc,CAAC4D,gBAAgB,GAAGI,WAAW,GAAGC,WAAW,CAAC;MAC9D;IACF;EACF,CAAC,CAAC;EACF,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIlD,aAAa,IAAI1B,aAAa,CAACsD,OAAO,EAAE;MAC1CY,QAAQ,CAAC;QACPE,aAAa,EAAEpE,aAAa,CAACsD;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLhD,aAAa,CAAC,KAAK,CAAC;MACpBI,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EACD,IAAImE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;IACxD,IAAI3B,KAAK,GAAG2B,KAAK,CAAC3B,KAAK;IACvB,IAAIA,KAAK,KAAKnE,cAAc,EAAE;MAC5B4F,eAAe,CAAC,CAAC;MACjB3F,iBAAiB,CAACY,YAAY,CAACyD,OAAO,GAAGzD,YAAY,CAACyD,OAAO,CAACyB,WAAW,GAAG5B,KAAK,CAAC;IACpF;EACF,CAAC;;EAED;EACA,IAAI6B,OAAO,GAAGzL,KAAK,CAACuG,MAAM,CAAC,KAAK,CAAC;EACjCvG,KAAK,CAAC0L,SAAS,CAAC,YAAY;IAC1B;IACA;IACA,IAAID,OAAO,CAAC1B,OAAO,EAAE;MACnBsB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAClD,aAAa,EAAE7F,IAAI,EAAE6D,OAAO,CAAC1C,MAAM,CAAC,CAAC;EACzCzD,KAAK,CAAC0L,SAAS,CAAC,YAAY;IAC1BD,OAAO,CAAC1B,OAAO,GAAG,IAAI;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI4B,gBAAgB,GAAG3L,KAAK,CAACuF,QAAQ,CAAC,CAAC,CAAC;IACtCqG,gBAAgB,GAAGxM,cAAc,CAACuM,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIG,gBAAgB,GAAG/L,KAAK,CAACuF,QAAQ,CAAC,IAAI,CAAC;IACzCyG,iBAAiB,GAAG5M,cAAc,CAAC2M,gBAAgB,EAAE,CAAC,CAAC;IACvDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE3ChM,KAAK,CAAC0L,SAAS,CAAC,YAAY;IAC1B,IAAIjF,aAAa,CAACsD,OAAO,YAAYoC,OAAO,EAAE;MAC5CL,gBAAgB,CAACnM,sBAAsB,CAAC8G,aAAa,CAACsD,OAAO,CAAC,CAACH,KAAK,CAAC;IACvE,CAAC,MAAM;MACLkC,gBAAgB,CAACnM,sBAAsB,CAAC+G,sBAAsB,CAACqD,OAAO,CAAC,CAACH,KAAK,CAAC;IAChF;IACAsC,gBAAgB,CAACxM,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;EACxD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAM,KAAK,CAAC0L,SAAS,CAAC,YAAY;IAC1B,IAAIvI,aAAa,KAAK/C,cAAc,IAAIiD,YAAY,EAAE;MACpDA,YAAY,CAAC+I,IAAI,CAACrC,OAAO,GAAGtD,aAAa,CAACsD,OAAO;IACnD;EACF,CAAC,CAAC;;EAEF;EACA;EACA;EACA;EACA,IAAIsC,sBAAsB,GAAGrM,KAAK,CAACkE,WAAW,CAAC,UAAUoI,oBAAoB,EAAE;IAC7E,OAAO,aAAatM,KAAK,CAACuM,aAAa,CAACvM,KAAK,CAACwM,QAAQ,EAAE,IAAI,EAAE,aAAaxM,KAAK,CAACuM,aAAa,CAAC7L,MAAM,EAAE4L,oBAAoB,CAAC,EAAEnD,SAAS,KAAK,KAAK,IAAI,aAAanJ,KAAK,CAACuM,aAAa,CAAChM,MAAM,EAAE+L,oBAAoB,EAAEpD,WAAW,CAAC,CAAC;EACnO,CAAC,EAAE,CAACC,SAAS,EAAED,WAAW,CAAC,CAAC;EAC5B,IAAIuD,sBAAsB,GAAGzM,KAAK,CAACkE,WAAW,CAAC,UAAUoI,oBAAoB,EAAE;IAC7E,OAAO,aAAatM,KAAK,CAACuM,aAAa,CAAChM,MAAM,EAAE+L,oBAAoB,EAAEpD,WAAW,CAAC;EACpF,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA,IAAIwD,cAAc,GAAGzI,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;;EAErD;EACA,IAAI0I,iBAAiB,GAAG3M,KAAK,CAACsE,OAAO,CAAC,YAAY;IAChD,IAAI9B,WAAW,EAAE;MACf,OAAOA,WAAW;IACpB;IACA;IACA;IACA;IACA,IAAI+F,SAAS,EAAE;MACb,OAAO,CAAChG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6F,CAAC,MAAM,aAAa,GAAG,MAAM,GAAG,OAAO;IACxG;IACA,IAAIH,SAAS,IAAIW,QAAQ,IAAIxC,cAAc,CAACoC,IAAI,CAAC,UAAUoE,KAAK,EAAE;MAChE,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC7B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EAAE;MACF,OAAO,OAAO;IAChB;IACA,OAAO,MAAM;EACf,CAAC,EAAE,CAAC5E,SAAS,EAAEM,SAAS,EAAEnC,cAAc,EAAE5D,WAAW,EAAEoG,QAAQ,CAAC,CAAC;EACjE,IAAIkE,cAAc;;EAElB;EACA,IAAIC,WAAW,GAAG;IAChBjF,SAAS,EAAEA,SAAS;IACpBkF,UAAU,EAAE5G,cAAc,CAAC3C,MAAM;IACjCuE,aAAa,EAAEA,aAAa;IAC5B9E,WAAW,EAAEA,WAAW;IACxB+E,SAAS,EAAEA,SAAS;IACpB1F,MAAM,EAAEA;EACV,CAAC;;EAED;EACA,IAAI0K,SAAS,GAAGjN,KAAK,CAACsE,OAAO,CAAC,YAAY;IACxC,IAAId,OAAO,EAAE;MACX,OAAO,IAAI;IACb;IACA,IAAI,OAAOtB,SAAS,KAAK,UAAU,EAAE;MACnC,OAAOA,SAAS,CAAC,CAAC;IACpB;IACA,OAAOA,SAAS;EAClB,CAAC,EAAE,CAACsB,OAAO,EAAEtB,SAAS,CAAC,CAAC;;EAExB;EACA,IAAIgL,SAAS,GAAG,aAAalN,KAAK,CAACuM,aAAa,CAACtM,IAAI,EAAE;IACrDqC,IAAI,EAAEiB,UAAU;IAChB4J,kBAAkB,EAAElF,SAAS,IAAIE,aAAa,IAAIS,QAAQ;IAC1D7C,YAAY,EAAEb,kBAAkB;IAChCkI,aAAa,EAAEpI,gBAAgB,CAACoI,aAAa;IAC7C/I,SAAS,EAAEA,SAAS;IACpBpB,KAAK,EAAEA,KAAK;IACZgK,SAAS,EAAEA,SAAS;IACpBI,kBAAkB,EAAEjI;EACtB,CAAC,CAAC;EACF,IAAIkI,YAAY,GAAG,aAAatN,KAAK,CAACuM,aAAa,CAACrM,QAAQ,EAAE;IAC5D4H,SAAS,EAAE1B,cAAc,CAACuB,GAAG,CAAC,UAAU4F,KAAK,EAAE;MAC7C,IAAI3D,KAAK,GAAG2D,KAAK,CAAC3D,KAAK;MACvB,OAAOA,KAAK;IACd,CAAC,CAAC;IACFzD,OAAO,EAAEC;EACX,CAAC,CAAC;EACF,IAAIoH,cAAc,GAAG3K,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKkB,SAAS,GAAG,aAAa/D,KAAK,CAACuM,aAAa,CAAC,SAAS,EAAE;IAC3GpK,SAAS,EAAE,EAAE,CAAC6B,MAAM,CAAC/B,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEY,OAAO,CAAC,GAAGkB,SAAS;EACvB,IAAI0J,mBAAmB,GAAGxJ,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC;EAChD,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAO6J,mBAAmB,KAAK,UAAU,IAAIjK,OAAO,IAAI,CAACyE,SAAS,EAAE;IAC/GlI,OAAO,CAAC,KAAK,EAAE,iEAAiE,CAAC;EACnF;EACA,IAAI2N,SAAS,GAAG7N,SAAS,CAACkC,KAAK,EAAE;IAC/BO,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAIqL,SAAS,GAAG9N,SAAS,CAACkC,KAAK,EAAE;IAC/B6L,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAI3F,SAAS,IAAIW,QAAQ,EAAE;IACzB;IACA,IAAIiF,WAAW;IACf,IAAI,OAAOJ,mBAAmB,KAAK,UAAU,EAAE;MAC7CI,WAAW,GAAGJ,mBAAmB,CAAClK,UAAU,EAAE;QAC5CsI,aAAa,EAAEA,aAAa;QAC5BiC,GAAG,EAAErH,aAAa;QAClBkE,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACFoC,WAAW,CAACjF,SAAS,GAAG1B,cAAc,CAACuB,GAAG,CAAC,UAAUoG,KAAK,EAAEC,KAAK,EAAE;QACjE,IAAIpE,KAAK,GAAGmE,KAAK,CAACnE,KAAK;QACvB,IAAIqE,QAAQ,GAAGD,KAAK,KAAK5H,cAAc,CAAC3C,MAAM,GAAG,CAAC,GAAGmG,KAAK,GAAGiC,aAAa,GAAGjC,KAAK;QAClF,IAAI,OAAOqE,QAAQ,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAQ,CAAC,EAAE;UAC3D,OAAOA,QAAQ;QACjB;QACA,IAAIvK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC7D,OAAO,CAACgC,KAAK,CAACoE,OAAO,CAAC1C,MAAM,KAAK,CAAC,EAAE,8FAA8F,CAAC;QACrI;QACA,OAAO,CAAC;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACLoK,WAAW,GAAG,aAAa7N,KAAK,CAACuM,aAAa,CAAC,KAAK,EAAE;QACpDlK,KAAK,EAAEhD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiK,YAAY,CAAC,EAAEC,YAAY,CAAC;QACnEoB,QAAQ,EAAEA,QAAQ;QAClBmD,GAAG,EAAErH,aAAa;QAClBtE,SAAS,EAAE5C,UAAU,CAAC,EAAE,CAACyE,MAAM,CAAC/B,SAAS,EAAE,OAAO,CAAC;MACrD,CAAC,EAAE,aAAajC,KAAK,CAACuM,aAAa,CAACG,cAAc,EAAEvN,QAAQ,CAAC;QAC3DkD,KAAK,EAAEhD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmK,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5DhH,WAAW,EAAEmK;QACf,CAAC;MACH,CAAC,EAAEgB,SAAS,CAAC,EAAEH,cAAc,EAAEF,YAAY,EAAEJ,SAAS,EAAE,CAAC/D,SAAS,IAAID,WAAW,IAAI,aAAalJ,KAAK,CAACuM,aAAa,CAAChM,MAAM,EAAE;QAC5HyH,aAAa,EAAEA,aAAa;QAC5B5B,cAAc,EAAEA,cAAc;QAC9BD,OAAO,EAAEA;MACX,CAAC,EAAE+C,WAAW,CAAC,CAAC,CAAC;IACnB;;IAEA;IACA,IAAIkF,gBAAgB,GAAG/O,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MAC/DgP,MAAM,EAAE,CAAC9K,UAAU,CAACE,MAAM;MAC1B6K,gBAAgB,EAAEnG,aAAa,IAAI5F,MAAM,CAAC6F,CAAC,KAAK;IAClD,CAAC,EAAE2E,WAAW,CAAC,EAAE1G,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACnC5D,SAAS,EAAEA,SAAS;MACpBuG,eAAe,EAAEA,eAAe;MAChC2B,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACFmC,cAAc,GAAG,aAAa9M,KAAK,CAACuM,aAAa,CAACvM,KAAK,CAACwM,QAAQ,EAAE,IAAI,EAAEzJ,UAAU,KAAK,KAAK,IAAI,aAAa/C,KAAK,CAACuM,aAAa,CAACjM,WAAW,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAAEiP,gBAAgB,EAAE;MAC3KG,eAAe,EAAE1F,YAAY;MAC7B1G,SAAS,EAAE,EAAE,CAAC6B,MAAM,CAAC/B,SAAS,EAAE,SAAS,CAAC;MAC1C6L,GAAG,EAAEtH;IACP,CAAC,CAAC,EAAE6F,sBAAsB,CAAC,EAAEwB,WAAW,EAAE1E,SAAS,IAAIA,SAAS,KAAK,KAAK,IAAI,aAAanJ,KAAK,CAACuM,aAAa,CAACjM,WAAW,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAAEiP,gBAAgB,EAAE;MACzJI,kBAAkB,EAAE1F,aAAa;MACjC3G,SAAS,EAAE,EAAE,CAAC6B,MAAM,CAAC/B,SAAS,EAAE,UAAU,CAAC;MAC3C6L,GAAG,EAAEnH;IACP,CAAC,CAAC,EAAE8F,sBAAsB,CAAC,EAAE7D,QAAQ,IAAI,aAAa5I,KAAK,CAACuM,aAAa,CAACnL,eAAe,EAAE;MACzF0M,GAAG,EAAEpF,SAAS;MACdK,YAAY,EAAEA,YAAY;MAC1BtC,aAAa,EAAEA,aAAa;MAC5BkE,QAAQ,EAAEA,QAAQ;MAClB1B,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL,CAAC,MAAM;IACL;IACA6D,cAAc,GAAG,aAAa9M,KAAK,CAACuM,aAAa,CAAC,KAAK,EAAE;MACvDlK,KAAK,EAAEhD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiK,YAAY,CAAC,EAAEC,YAAY,CAAC;MACnEpH,SAAS,EAAE5C,UAAU,CAAC,EAAE,CAACyE,MAAM,CAAC/B,SAAS,EAAE,UAAU,CAAC,CAAC;MACvD0I,QAAQ,EAAEA,QAAQ;MAClBmD,GAAG,EAAErH;IACP,CAAC,EAAE,aAAazG,KAAK,CAACuM,aAAa,CAACG,cAAc,EAAEvN,QAAQ,CAAC;MAC3DkD,KAAK,EAAEhD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmK,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5DhH,WAAW,EAAEmK;MACf,CAAC;IACH,CAAC,EAAEgB,SAAS,CAAC,EAAEH,cAAc,EAAEF,YAAY,EAAEvK,UAAU,KAAK,KAAK,IAAI,aAAa/C,KAAK,CAACuM,aAAa,CAAC7L,MAAM,EAAEvB,QAAQ,CAAC,CAAC,CAAC,EAAE4N,WAAW,EAAE1G,aAAa,CAAC,CAAC,EAAE6G,SAAS,EAAEhE,WAAW,IAAI,aAAalJ,KAAK,CAACuM,aAAa,CAAChM,MAAM,EAAE;MAC1NyH,aAAa,EAAEA,aAAa;MAC5B5B,cAAc,EAAEA,cAAc;MAC9BD,OAAO,EAAEA;IACX,CAAC,EAAE+C,WAAW,CAAC,CAAC,CAAC;EACnB;EACA,IAAIuF,SAAS,GAAG,aAAazO,KAAK,CAACuM,aAAa,CAAC,KAAK,EAAEpN,QAAQ,CAAC;IAC/DgD,SAAS,EAAE5C,UAAU,CAAC0C,SAAS,EAAEE,SAAS,GAAGL,WAAW,GAAG,CAAC,CAAC,EAAE5C,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC/B,SAAS,EAAE,MAAM,CAAC,EAAEQ,SAAS,KAAK,KAAK,CAAC,EAAEvD,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC/B,SAAS,EAAE,YAAY,CAAC,EAAE6E,UAAU,CAAC,EAAE5H,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC/B,SAAS,EAAE,aAAa,CAAC,EAAEiF,WAAW,CAAC,EAAEhI,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEO,WAAW,KAAK,OAAO,CAAC,EAAEtD,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEgG,SAAS,CAAC,EAAE/I,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEsG,SAAS,CAAC,EAAErJ,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC/B,SAAS,EAAE,oBAAoB,CAAC,EAAEkG,aAAa,CAAC,EAAEjJ,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC/B,SAAS,EAAE,eAAe,CAAC,EAAEmE,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,CAAC,CAAC,CAAC,CAACkC,KAAK,CAAC,EAAEpJ,eAAe,CAAC4C,WAAW,EAAE,EAAE,CAACkC,MAAM,CAAC/B,SAAS,EAAE,gBAAgB,CAAC,EAAEmE,cAAc,CAACA,cAAc,CAAC3C,MAAM,GAAG,CAAC,CAAC,IAAI2C,cAAc,CAACA,cAAc,CAAC3C,MAAM,GAAG,CAAC,CAAC,CAAC6E,KAAK,KAAK,OAAO,CAAC,EAAExG,WAAW,CAAC,CAAC;IACr7BO,KAAK,EAAEA,KAAK;IACZS,EAAE,EAAEA,EAAE;IACNgL,GAAG,EAAExH;EACP,CAAC,EAAEoH,SAAS,CAAC,EAAEhL,KAAK,IAAI,aAAa1C,KAAK,CAACuM,aAAa,CAACpL,KAAK,EAAE;IAC9DgB,SAAS,EAAE,EAAE,CAAC6B,MAAM,CAAC/B,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAES,KAAK,CAACa,UAAU,CAAC,CAAC,EAAE,aAAavD,KAAK,CAACuM,aAAa,CAAC,KAAK,EAAE;IAC7DuB,GAAG,EAAEpH,sBAAsB;IAC3BvE,SAAS,EAAE,EAAE,CAAC6B,MAAM,CAAC/B,SAAS,EAAE,YAAY;EAC9C,CAAC,EAAE6K,cAAc,CAAC,EAAEnK,MAAM,IAAI,aAAa3C,KAAK,CAACuM,aAAa,CAACpL,KAAK,EAAE;IACpEgB,SAAS,EAAE,EAAE,CAAC6B,MAAM,CAAC/B,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEU,MAAM,CAACY,UAAU,CAAC,CAAC,CAAC;EACvB,IAAI4E,aAAa,EAAE;IACjBsG,SAAS,GAAG,aAAazO,KAAK,CAACuM,aAAa,CAAC/M,cAAc,EAAE;MAC3DkP,QAAQ,EAAEpD;IACZ,CAAC,EAAEmD,SAAS,CAAC;EACf;EACA,IAAIE,aAAa,GAAG9N,YAAY,CAACuF,cAAc,EAAE4B,aAAa,EAAEvF,SAAS,EAAE0D,OAAO,CAAC;EACnF,IAAIyI,iBAAiB,GAAG5O,KAAK,CAACsE,OAAO,CAAC,YAAY;IAChD,OAAO;MACL;MACArC,SAAS,EAAEA,SAAS;MACpBgC,YAAY,EAAEA,YAAY;MAC1B4H,aAAa,EAAEA,aAAa;MAC5BpJ,SAAS,EAAEA,SAAS;MACpBkM,aAAa,EAAEA,aAAa;MAC5B/F,QAAQ,EAAEA,QAAQ;MAClBqD,aAAa,EAAEA,aAAa;MAC5BxG,cAAc,EAAEA,cAAc;MAC9BwC,SAAS,EAAEA,SAAS;MACpBM,SAAS,EAAEA,SAAS;MACpBJ,aAAa,EAAEA,aAAa;MAC5B;;MAEA3F,WAAW,EAAEmK,iBAAiB;MAC9BvK,YAAY,EAAEA,YAAY;MAC1ByM,oBAAoB,EAAE7J,gBAAgB,CAAC6J,oBAAoB;MAC3D7I,UAAU,EAAEb,gBAAgB;MAC5BF,cAAc,EAAEA,cAAc;MAC9B6J,gBAAgB,EAAE9J,gBAAgB,CAAC8J,gBAAgB;MACnDjJ,iBAAiB,EAAEb,gBAAgB,CAACa,iBAAiB;MACrDR,eAAe,EAAEA,eAAe;MAChCY,qBAAqB,EAAEjB,gBAAgB,CAACiB,qBAAqB;MAC7D8I,UAAU,EAAE/J,gBAAgB,CAAC+J,UAAU;MACvCC,mBAAmB,EAAE5I,cAAc,CAAC6I,KAAK,CAAC,UAAUC,GAAG,EAAE;QACvD,OAAOA,GAAG,CAAC5G,KAAK,KAAK,MAAM;MAC7B,CAAC,CAAC;MACF;MACAnC,OAAO,EAAEA,OAAO;MAChBC,cAAc,EAAEA,cAAc;MAC9B0D,cAAc,EAAEA,cAAc;MAC9B;MACAqF,aAAa,EAAExK,QAAQ;MACvByK,WAAW,EAAExK,MAAM;MACnBC,OAAO,EAAEA;IACX,CAAC;EACH,CAAC,EAAE;EACH;EACA5C,SAAS,EAAEgC,YAAY,EAAE4H,aAAa,EAAEpJ,SAAS,EAAEkM,aAAa,EAAE/F,QAAQ,EAAEqD,aAAa,EAAExG,cAAc,EAAEwC,SAAS,EAAEM,SAAS,EAAEJ,aAAa;EAC9I;EACAwE,iBAAiB,EAAEvK,YAAY,EAAE4C,gBAAgB,CAAC6J,oBAAoB,EAAE1J,gBAAgB,EAAEF,cAAc,EAAED,gBAAgB,CAAC8J,gBAAgB,EAAE9J,gBAAgB,CAACa,iBAAiB,EAAER,eAAe,EAAEL,gBAAgB,CAACiB,qBAAqB,EAAEjB,gBAAgB,CAAC+J,UAAU;EACrQ;EACA5I,OAAO,EAAEC,cAAc,EAAE0D,cAAc;EACvC;EACAnF,QAAQ,EAAEC,MAAM,EAAEC,OAAO,CAAC,CAAC;EAC3B,OAAO,aAAa7E,KAAK,CAACuM,aAAa,CAAClM,YAAY,CAACgP,QAAQ,EAAE;IAC7DC,KAAK,EAAEV;EACT,CAAC,EAAEH,SAAS,CAAC;AACf;AACA,OAAO,SAASc,QAAQA,CAACC,mBAAmB,EAAE;EAC5C,OAAOlQ,aAAa,CAACsC,KAAK,EAAE4N,mBAAmB,CAAC;AAClD;AACA,IAAIC,cAAc,GAAGF,QAAQ,CAAC,CAAC;AAC/BE,cAAc,CAACtP,aAAa,GAAGA,aAAa;AAC5CsP,cAAc,CAACrP,cAAc,GAAGA,cAAc;AAC9CqP,cAAc,CAACpO,MAAM,GAAGA,MAAM;AAC9BoO,cAAc,CAACnO,WAAW,GAAGA,WAAW;AACxCmO,cAAc,CAAChP,OAAO,GAAGD,gBAAgB;AACzC,eAAeiP,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}