{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React from 'react';\nimport classNames from 'classnames';\nimport Portal from '@rc-component/portal';\nimport useId from \"rc-util/es/hooks/useId\";\nvar COVER_PROPS = {\n  fill: 'transparent',\n  pointerEvents: 'auto'\n};\nvar Mask = function Mask(props) {\n  var prefixCls = props.prefixCls,\n    rootClassName = props.rootClassName,\n    pos = props.pos,\n    showMask = props.showMask,\n    _props$style = props.style,\n    style = _props$style === void 0 ? {} : _props$style,\n    _props$fill = props.fill,\n    fill = _props$fill === void 0 ? \"rgba(0,0,0,0.5)\" : _props$fill,\n    open = props.open,\n    animated = props.animated,\n    zIndex = props.zIndex;\n  var id = useId();\n  var maskId = \"\".concat(prefixCls, \"-mask-\").concat(id);\n  var mergedAnimated = _typeof(animated) === 'object' ? animated === null || animated === void 0 ? void 0 : animated.placeholder : animated;\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: open,\n    autoLock: true\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-mask\"), rootClassName),\n    style: _objectSpread({\n      position: 'fixed',\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      zIndex: zIndex,\n      pointerEvents: 'none'\n    }, style)\n  }, showMask ? /*#__PURE__*/React.createElement(\"svg\", {\n    style: {\n      width: '100%',\n      height: '100%'\n    }\n  }, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"mask\", {\n    id: maskId\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: \"0\",\n    y: \"0\",\n    width: \"100vw\",\n    height: \"100vh\",\n    fill: \"white\"\n  }), pos && /*#__PURE__*/React.createElement(\"rect\", {\n    x: pos.left,\n    y: pos.top,\n    rx: pos.radius,\n    width: pos.width,\n    height: pos.height,\n    fill: \"black\",\n    className: mergedAnimated ? \"\".concat(prefixCls, \"-placeholder-animated\") : ''\n  }))), /*#__PURE__*/React.createElement(\"rect\", {\n    x: \"0\",\n    y: \"0\",\n    width: \"100%\",\n    height: \"100%\",\n    fill: fill,\n    mask: \"url(#\".concat(maskId, \")\")\n  }), pos && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: \"0\",\n    width: \"100%\",\n    height: pos.top\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: \"0\",\n    width: pos.left,\n    height: \"100%\"\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: pos.top + pos.height,\n    width: \"100%\",\n    height: \"calc(100vh - \".concat(pos.top + pos.height, \"px)\")\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: pos.left + pos.width,\n    y: \"0\",\n    width: \"calc(100vw - \".concat(pos.left + pos.width, \"px)\"),\n    height: \"100%\"\n  })))) : null));\n};\nexport default Mask;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_typeof", "React", "classNames", "Portal", "useId", "COVER_PROPS", "fill", "pointerEvents", "Mask", "props", "prefixCls", "rootClassName", "pos", "showMask", "_props$style", "style", "_props$fill", "open", "animated", "zIndex", "id", "maskId", "concat", "mergedAnimated", "placeholder", "createElement", "autoLock", "className", "position", "left", "right", "top", "bottom", "width", "height", "x", "y", "rx", "radius", "mask", "Fragment"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/tour/es/Mask.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React from 'react';\nimport classNames from 'classnames';\nimport Portal from '@rc-component/portal';\nimport useId from \"rc-util/es/hooks/useId\";\nvar COVER_PROPS = {\n  fill: 'transparent',\n  pointerEvents: 'auto'\n};\nvar Mask = function Mask(props) {\n  var prefixCls = props.prefixCls,\n    rootClassName = props.rootClassName,\n    pos = props.pos,\n    showMask = props.showMask,\n    _props$style = props.style,\n    style = _props$style === void 0 ? {} : _props$style,\n    _props$fill = props.fill,\n    fill = _props$fill === void 0 ? \"rgba(0,0,0,0.5)\" : _props$fill,\n    open = props.open,\n    animated = props.animated,\n    zIndex = props.zIndex;\n  var id = useId();\n  var maskId = \"\".concat(prefixCls, \"-mask-\").concat(id);\n  var mergedAnimated = _typeof(animated) === 'object' ? animated === null || animated === void 0 ? void 0 : animated.placeholder : animated;\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: open,\n    autoLock: true\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-mask\"), rootClassName),\n    style: _objectSpread({\n      position: 'fixed',\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      zIndex: zIndex,\n      pointerEvents: 'none'\n    }, style)\n  }, showMask ? /*#__PURE__*/React.createElement(\"svg\", {\n    style: {\n      width: '100%',\n      height: '100%'\n    }\n  }, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"mask\", {\n    id: maskId\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: \"0\",\n    y: \"0\",\n    width: \"100vw\",\n    height: \"100vh\",\n    fill: \"white\"\n  }), pos && /*#__PURE__*/React.createElement(\"rect\", {\n    x: pos.left,\n    y: pos.top,\n    rx: pos.radius,\n    width: pos.width,\n    height: pos.height,\n    fill: \"black\",\n    className: mergedAnimated ? \"\".concat(prefixCls, \"-placeholder-animated\") : ''\n  }))), /*#__PURE__*/React.createElement(\"rect\", {\n    x: \"0\",\n    y: \"0\",\n    width: \"100%\",\n    height: \"100%\",\n    fill: fill,\n    mask: \"url(#\".concat(maskId, \")\")\n  }), pos && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: \"0\",\n    width: \"100%\",\n    height: pos.top\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: \"0\",\n    width: pos.left,\n    height: \"100%\"\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: \"0\",\n    y: pos.top + pos.height,\n    width: \"100%\",\n    height: \"calc(100vh - \".concat(pos.top + pos.height, \"px)\")\n  })), /*#__PURE__*/React.createElement(\"rect\", _extends({}, COVER_PROPS, {\n    x: pos.left + pos.width,\n    y: \"0\",\n    width: \"calc(100vw - \".concat(pos.left + pos.width, \"px)\"),\n    height: \"100%\"\n  })))) : null));\n};\nexport default Mask;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAE;AACjB,CAAC;AACD,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;EAC9B,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,GAAG,GAAGH,KAAK,CAACG,GAAG;IACfC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,YAAY,GAAGL,KAAK,CAACM,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,YAAY;IACnDE,WAAW,GAAGP,KAAK,CAACH,IAAI;IACxBA,IAAI,GAAGU,WAAW,KAAK,KAAK,CAAC,GAAG,iBAAiB,GAAGA,WAAW;IAC/DC,IAAI,GAAGR,KAAK,CAACQ,IAAI;IACjBC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,MAAM,GAAGV,KAAK,CAACU,MAAM;EACvB,IAAIC,EAAE,GAAGhB,KAAK,CAAC,CAAC;EAChB,IAAIiB,MAAM,GAAG,EAAE,CAACC,MAAM,CAACZ,SAAS,EAAE,QAAQ,CAAC,CAACY,MAAM,CAACF,EAAE,CAAC;EACtD,IAAIG,cAAc,GAAGvB,OAAO,CAACkB,QAAQ,CAAC,KAAK,QAAQ,GAAGA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACM,WAAW,GAAGN,QAAQ;EACzI,OAAO,aAAajB,KAAK,CAACwB,aAAa,CAACtB,MAAM,EAAE;IAC9Cc,IAAI,EAAEA,IAAI;IACVS,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAazB,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IACzCE,SAAS,EAAEzB,UAAU,CAAC,EAAE,CAACoB,MAAM,CAACZ,SAAS,EAAE,OAAO,CAAC,EAAEC,aAAa,CAAC;IACnEI,KAAK,EAAEhB,aAAa,CAAC;MACnB6B,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTb,MAAM,EAAEA,MAAM;MACdZ,aAAa,EAAE;IACjB,CAAC,EAAEQ,KAAK;EACV,CAAC,EAAEF,QAAQ,GAAG,aAAaZ,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IACpDV,KAAK,EAAE;MACLkB,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE;IACV;EACF,CAAC,EAAE,aAAajC,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAaxB,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;IACzFL,EAAE,EAAEC;EACN,CAAC,EAAE,aAAapB,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;IAC1CU,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNH,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACf5B,IAAI,EAAE;EACR,CAAC,CAAC,EAAEM,GAAG,IAAI,aAAaX,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;IAClDU,CAAC,EAAEvB,GAAG,CAACiB,IAAI;IACXO,CAAC,EAAExB,GAAG,CAACmB,GAAG;IACVM,EAAE,EAAEzB,GAAG,CAAC0B,MAAM;IACdL,KAAK,EAAErB,GAAG,CAACqB,KAAK;IAChBC,MAAM,EAAEtB,GAAG,CAACsB,MAAM;IAClB5B,IAAI,EAAE,OAAO;IACbqB,SAAS,EAAEJ,cAAc,GAAG,EAAE,CAACD,MAAM,CAACZ,SAAS,EAAE,uBAAuB,CAAC,GAAG;EAC9E,CAAC,CAAC,CAAC,CAAC,EAAE,aAAaT,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;IAC7CU,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNH,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACd5B,IAAI,EAAEA,IAAI;IACViC,IAAI,EAAE,OAAO,CAACjB,MAAM,CAACD,MAAM,EAAE,GAAG;EAClC,CAAC,CAAC,EAAET,GAAG,IAAI,aAAaX,KAAK,CAACwB,aAAa,CAACxB,KAAK,CAACuC,QAAQ,EAAE,IAAI,EAAE,aAAavC,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE3B,QAAQ,CAAC,CAAC,CAAC,EAAEO,WAAW,EAAE;IACnI8B,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNH,KAAK,EAAE,MAAM;IACbC,MAAM,EAAEtB,GAAG,CAACmB;EACd,CAAC,CAAC,CAAC,EAAE,aAAa9B,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE3B,QAAQ,CAAC,CAAC,CAAC,EAAEO,WAAW,EAAE;IACtE8B,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNH,KAAK,EAAErB,GAAG,CAACiB,IAAI;IACfK,MAAM,EAAE;EACV,CAAC,CAAC,CAAC,EAAE,aAAajC,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE3B,QAAQ,CAAC,CAAC,CAAC,EAAEO,WAAW,EAAE;IACtE8B,CAAC,EAAE,GAAG;IACNC,CAAC,EAAExB,GAAG,CAACmB,GAAG,GAAGnB,GAAG,CAACsB,MAAM;IACvBD,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,eAAe,CAACZ,MAAM,CAACV,GAAG,CAACmB,GAAG,GAAGnB,GAAG,CAACsB,MAAM,EAAE,KAAK;EAC5D,CAAC,CAAC,CAAC,EAAE,aAAajC,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE3B,QAAQ,CAAC,CAAC,CAAC,EAAEO,WAAW,EAAE;IACtE8B,CAAC,EAAEvB,GAAG,CAACiB,IAAI,GAAGjB,GAAG,CAACqB,KAAK;IACvBG,CAAC,EAAE,GAAG;IACNH,KAAK,EAAE,eAAe,CAACX,MAAM,CAACV,GAAG,CAACiB,IAAI,GAAGjB,GAAG,CAACqB,KAAK,EAAE,KAAK,CAAC;IAC1DC,MAAM,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAChB,CAAC;AACD,eAAe1B,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}