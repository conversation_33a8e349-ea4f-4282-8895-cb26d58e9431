{"ast": null, "code": "import * as React from 'react';\nimport { executeValue } from \"./utils/miscUtil\";\nexport default function PresetPanel(props) {\n  var prefixCls = props.prefixCls,\n    presets = props.presets,\n    _onClick = props.onClick,\n    onHover = props.onHover;\n  if (!presets.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-presets\")\n  }, /*#__PURE__*/React.createElement(\"ul\", null, presets.map(function (_ref, index) {\n    var label = _ref.label,\n      value = _ref.value;\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: index,\n      onClick: function onClick() {\n        return _onClick === null || _onClick === void 0 ? void 0 : _onClick(executeValue(value));\n      },\n      onMouseEnter: function onMouseEnter() {\n        return onHover === null || onHover === void 0 ? void 0 : onHover(executeValue(value));\n      },\n      onMouseLeave: function onMouseLeave() {\n        return onHover === null || onHover === void 0 ? void 0 : onHover(null);\n      }\n    }, label);\n  })));\n}", "map": {"version": 3, "names": ["React", "executeValue", "PresetPanel", "props", "prefixCls", "presets", "_onClick", "onClick", "onHover", "length", "createElement", "className", "concat", "map", "_ref", "index", "label", "value", "key", "onMouseEnter", "onMouseLeave"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/PresetPanel.js"], "sourcesContent": ["import * as React from 'react';\nimport { executeValue } from \"./utils/miscUtil\";\nexport default function PresetPanel(props) {\n  var prefixCls = props.prefixCls,\n    presets = props.presets,\n    _onClick = props.onClick,\n    onHover = props.onHover;\n  if (!presets.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-presets\")\n  }, /*#__PURE__*/React.createElement(\"ul\", null, presets.map(function (_ref, index) {\n    var label = _ref.label,\n      value = _ref.value;\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: index,\n      onClick: function onClick() {\n        return _onClick === null || _onClick === void 0 ? void 0 : _onClick(executeValue(value));\n      },\n      onMouseEnter: function onMouseEnter() {\n        return onHover === null || onHover === void 0 ? void 0 : onHover(executeValue(value));\n      },\n      onMouseLeave: function onMouseLeave() {\n        return onHover === null || onHover === void 0 ? void 0 : onHover(null);\n      }\n    }, label);\n  })));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACzC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,QAAQ,GAAGH,KAAK,CAACI,OAAO;IACxBC,OAAO,GAAGL,KAAK,CAACK,OAAO;EACzB,IAAI,CAACH,OAAO,CAACI,MAAM,EAAE;IACnB,OAAO,IAAI;EACb;EACA,OAAO,aAAaT,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAE,aAAaJ,KAAK,CAACU,aAAa,CAAC,IAAI,EAAE,IAAI,EAAEL,OAAO,CAACQ,GAAG,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACjF,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;MACpBC,KAAK,GAAGH,IAAI,CAACG,KAAK;IACpB,OAAO,aAAajB,KAAK,CAACU,aAAa,CAAC,IAAI,EAAE;MAC5CQ,GAAG,EAAEH,KAAK;MACVR,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B,OAAOD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACL,YAAY,CAACgB,KAAK,CAAC,CAAC;MAC1F,CAAC;MACDE,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpC,OAAOX,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACP,YAAY,CAACgB,KAAK,CAAC,CAAC;MACvF,CAAC;MACDG,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpC,OAAOZ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,IAAI,CAAC;MACxE;IACF,CAAC,EAAEQ,KAAK,CAAC;EACX,CAAC,CAAC,CAAC,CAAC;AACN"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}