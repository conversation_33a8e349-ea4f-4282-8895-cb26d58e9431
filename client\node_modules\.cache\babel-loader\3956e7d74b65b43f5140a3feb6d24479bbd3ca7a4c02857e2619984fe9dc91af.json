{"ast": null, "code": "const genBorderedStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const tableBorder = `${token.lineWidth}px ${token.lineType} ${token.tableBorderColor}`;\n  const getSizeBorderStyle = (size, paddingVertical, paddingHorizontal) => ({\n    [`&${componentCls}-${size}`]: {\n      [`> ${componentCls}-container`]: {\n        [`> ${componentCls}-content, > ${componentCls}-body`]: {\n          [`\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          `]: {\n            [`> ${componentCls}-expanded-row-fixed`]: {\n              margin: `-${paddingVertical}px -${paddingHorizontal + token.lineWidth}px`\n            }\n          }\n        }\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}${componentCls}-bordered`]: Object.assign(Object.assign(Object.assign({\n        // ============================ Title =============================\n        [`> ${componentCls}-title`]: {\n          border: tableBorder,\n          borderBottom: 0\n        },\n        // ============================ Content ============================\n        [`> ${componentCls}-container`]: {\n          borderInlineStart: tableBorder,\n          borderTop: tableBorder,\n          [`\n            > ${componentCls}-content,\n            > ${componentCls}-header,\n            > ${componentCls}-body,\n            > ${componentCls}-summary\n          `]: {\n            '> table': {\n              // ============================= Cell =============================\n              [`\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              `]: {\n                borderInlineEnd: tableBorder\n              },\n              // ============================ Header ============================\n              '> thead': {\n                '> tr:not(:last-child) > th': {\n                  borderBottom: tableBorder\n                },\n                '> tr > th::before': {\n                  backgroundColor: 'transparent !important'\n                }\n              },\n              // Fixed right should provides additional border\n              [`\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              `]: {\n                [`> ${componentCls}-cell-fix-right-first::after`]: {\n                  borderInlineEnd: tableBorder\n                }\n              },\n              // ========================== Expandable ==========================\n              [`\n                > tbody > tr > th,\n                > tbody > tr > td\n              `]: {\n                [`> ${componentCls}-expanded-row-fixed`]: {\n                  margin: `-${token.tablePaddingVertical}px -${token.tablePaddingHorizontal + token.lineWidth}px`,\n                  '&::after': {\n                    position: 'absolute',\n                    top: 0,\n                    insetInlineEnd: token.lineWidth,\n                    bottom: 0,\n                    borderInlineEnd: tableBorder,\n                    content: '\"\"'\n                  }\n                }\n              }\n            }\n          }\n        },\n        // ============================ Scroll ============================\n        [`&${componentCls}-scroll-horizontal`]: {\n          [`> ${componentCls}-container > ${componentCls}-body`]: {\n            '> table > tbody': {\n              [`\n                > tr${componentCls}-expanded-row,\n                > tr${componentCls}-placeholder\n              `]: {\n                [`> th, > td`]: {\n                  borderInlineEnd: 0\n                }\n              }\n            }\n          }\n        }\n      }, getSizeBorderStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle)), getSizeBorderStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall)), {\n        // ============================ Footer ============================\n        [`> ${componentCls}-footer`]: {\n          border: tableBorder,\n          borderTop: 0\n        }\n      }),\n      // ============================ Nested ============================\n      [`${componentCls}-cell`]: {\n        [`${componentCls}-container:first-child`]: {\n          // :first-child to avoid the case when bordered and title is set\n          borderTop: 0\n        },\n        // https://github.com/ant-design/ant-design/issues/35577\n        '&-scrollbar:not([rowspan])': {\n          boxShadow: `0 ${token.lineWidth}px 0 ${token.lineWidth}px ${token.tableHeaderBg}`\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-cell-scrollbar`]: {\n        borderInlineEnd: tableBorder\n      }\n    }\n  };\n};\nexport default genBorderedStyle;", "map": {"version": 3, "names": ["genBorderedStyle", "token", "componentCls", "tableBorder", "lineWidth", "lineType", "tableBorderColor", "getSizeBorderStyle", "size", "paddingVertical", "paddingHorizontal", "margin", "Object", "assign", "border", "borderBottom", "borderInlineStart", "borderTop", "borderInlineEnd", "backgroundColor", "tablePaddingVertical", "tablePaddingHorizontal", "position", "top", "insetInlineEnd", "bottom", "content", "tablePaddingVerticalMiddle", "tablePaddingHorizontalMiddle", "tablePaddingVerticalSmall", "tablePaddingHorizontalSmall", "boxShadow", "tableHeaderBg"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/bordered.js"], "sourcesContent": ["const genBorderedStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const tableBorder = `${token.lineWidth}px ${token.lineType} ${token.tableBorderColor}`;\n  const getSizeBorderStyle = (size, paddingVertical, paddingHorizontal) => ({\n    [`&${componentCls}-${size}`]: {\n      [`> ${componentCls}-container`]: {\n        [`> ${componentCls}-content, > ${componentCls}-body`]: {\n          [`\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          `]: {\n            [`> ${componentCls}-expanded-row-fixed`]: {\n              margin: `-${paddingVertical}px -${paddingHorizontal + token.lineWidth}px`\n            }\n          }\n        }\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}${componentCls}-bordered`]: Object.assign(Object.assign(Object.assign({\n        // ============================ Title =============================\n        [`> ${componentCls}-title`]: {\n          border: tableBorder,\n          borderBottom: 0\n        },\n        // ============================ Content ============================\n        [`> ${componentCls}-container`]: {\n          borderInlineStart: tableBorder,\n          borderTop: tableBorder,\n          [`\n            > ${componentCls}-content,\n            > ${componentCls}-header,\n            > ${componentCls}-body,\n            > ${componentCls}-summary\n          `]: {\n            '> table': {\n              // ============================= Cell =============================\n              [`\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              `]: {\n                borderInlineEnd: tableBorder\n              },\n              // ============================ Header ============================\n              '> thead': {\n                '> tr:not(:last-child) > th': {\n                  borderBottom: tableBorder\n                },\n                '> tr > th::before': {\n                  backgroundColor: 'transparent !important'\n                }\n              },\n              // Fixed right should provides additional border\n              [`\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              `]: {\n                [`> ${componentCls}-cell-fix-right-first::after`]: {\n                  borderInlineEnd: tableBorder\n                }\n              },\n              // ========================== Expandable ==========================\n              [`\n                > tbody > tr > th,\n                > tbody > tr > td\n              `]: {\n                [`> ${componentCls}-expanded-row-fixed`]: {\n                  margin: `-${token.tablePaddingVertical}px -${token.tablePaddingHorizontal + token.lineWidth}px`,\n                  '&::after': {\n                    position: 'absolute',\n                    top: 0,\n                    insetInlineEnd: token.lineWidth,\n                    bottom: 0,\n                    borderInlineEnd: tableBorder,\n                    content: '\"\"'\n                  }\n                }\n              }\n            }\n          }\n        },\n        // ============================ Scroll ============================\n        [`&${componentCls}-scroll-horizontal`]: {\n          [`> ${componentCls}-container > ${componentCls}-body`]: {\n            '> table > tbody': {\n              [`\n                > tr${componentCls}-expanded-row,\n                > tr${componentCls}-placeholder\n              `]: {\n                [`> th, > td`]: {\n                  borderInlineEnd: 0\n                }\n              }\n            }\n          }\n        }\n      }, getSizeBorderStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle)), getSizeBorderStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall)), {\n        // ============================ Footer ============================\n        [`> ${componentCls}-footer`]: {\n          border: tableBorder,\n          borderTop: 0\n        }\n      }),\n      // ============================ Nested ============================\n      [`${componentCls}-cell`]: {\n        [`${componentCls}-container:first-child`]: {\n          // :first-child to avoid the case when bordered and title is set\n          borderTop: 0\n        },\n        // https://github.com/ant-design/ant-design/issues/35577\n        '&-scrollbar:not([rowspan])': {\n          boxShadow: `0 ${token.lineWidth}px 0 ${token.lineWidth}px ${token.tableHeaderBg}`\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-cell-scrollbar`]: {\n        borderInlineEnd: tableBorder\n      }\n    }\n  };\n};\nexport default genBorderedStyle;"], "mappings": "AAAA,MAAMA,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,WAAW,GAAI,GAAEF,KAAK,CAACG,SAAU,MAAKH,KAAK,CAACI,QAAS,IAAGJ,KAAK,CAACK,gBAAiB,EAAC;EACtF,MAAMC,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,eAAe,EAAEC,iBAAiB,MAAM;IACxE,CAAE,IAAGR,YAAa,IAAGM,IAAK,EAAC,GAAG;MAC5B,CAAE,KAAIN,YAAa,YAAW,GAAG;QAC/B,CAAE,KAAIA,YAAa,eAAcA,YAAa,OAAM,GAAG;UACrD,CAAE;AACZ;AACA;AACA,WAAW,GAAG;YACF,CAAE,KAAIA,YAAa,qBAAoB,GAAG;cACxCS,MAAM,EAAG,IAAGF,eAAgB,OAAMC,iBAAiB,GAAGT,KAAK,CAACG,SAAU;YACxE;UACF;QACF;MACF;IACF;EACF,CAAC,CAAC;EACF,OAAO;IACL,CAAE,GAAEF,YAAa,UAAS,GAAG;MAC3B,CAAE,GAAEA,YAAa,GAAEA,YAAa,WAAU,GAAGU,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QACrF;QACA,CAAE,KAAIX,YAAa,QAAO,GAAG;UAC3BY,MAAM,EAAEX,WAAW;UACnBY,YAAY,EAAE;QAChB,CAAC;QACD;QACA,CAAE,KAAIb,YAAa,YAAW,GAAG;UAC/Bc,iBAAiB,EAAEb,WAAW;UAC9Bc,SAAS,EAAEd,WAAW;UACtB,CAAE;AACZ,gBAAgBD,YAAa;AAC7B,gBAAgBA,YAAa;AAC7B,gBAAgBA,YAAa;AAC7B,gBAAgBA,YAAa;AAC7B,WAAW,GAAG;YACF,SAAS,EAAE;cACT;cACA,CAAE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,GAAG;gBACFgB,eAAe,EAAEf;cACnB,CAAC;cACD;cACA,SAAS,EAAE;gBACT,4BAA4B,EAAE;kBAC5BY,YAAY,EAAEZ;gBAChB,CAAC;gBACD,mBAAmB,EAAE;kBACnBgB,eAAe,EAAE;gBACnB;cACF,CAAC;cACD;cACA,CAAE;AAChB;AACA;AACA;AACA,eAAe,GAAG;gBACF,CAAE,KAAIjB,YAAa,8BAA6B,GAAG;kBACjDgB,eAAe,EAAEf;gBACnB;cACF,CAAC;cACD;cACA,CAAE;AAChB;AACA;AACA,eAAe,GAAG;gBACF,CAAE,KAAID,YAAa,qBAAoB,GAAG;kBACxCS,MAAM,EAAG,IAAGV,KAAK,CAACmB,oBAAqB,OAAMnB,KAAK,CAACoB,sBAAsB,GAAGpB,KAAK,CAACG,SAAU,IAAG;kBAC/F,UAAU,EAAE;oBACVkB,QAAQ,EAAE,UAAU;oBACpBC,GAAG,EAAE,CAAC;oBACNC,cAAc,EAAEvB,KAAK,CAACG,SAAS;oBAC/BqB,MAAM,EAAE,CAAC;oBACTP,eAAe,EAAEf,WAAW;oBAC5BuB,OAAO,EAAE;kBACX;gBACF;cACF;YACF;UACF;QACF,CAAC;QACD;QACA,CAAE,IAAGxB,YAAa,oBAAmB,GAAG;UACtC,CAAE,KAAIA,YAAa,gBAAeA,YAAa,OAAM,GAAG;YACtD,iBAAiB,EAAE;cACjB,CAAE;AAChB,sBAAsBA,YAAa;AACnC,sBAAsBA,YAAa;AACnC,eAAe,GAAG;gBACF,CAAE,YAAW,GAAG;kBACdgB,eAAe,EAAE;gBACnB;cACF;YACF;UACF;QACF;MACF,CAAC,EAAEX,kBAAkB,CAAC,QAAQ,EAAEN,KAAK,CAAC0B,0BAA0B,EAAE1B,KAAK,CAAC2B,4BAA4B,CAAC,CAAC,EAAErB,kBAAkB,CAAC,OAAO,EAAEN,KAAK,CAAC4B,yBAAyB,EAAE5B,KAAK,CAAC6B,2BAA2B,CAAC,CAAC,EAAE;QACxM;QACA,CAAE,KAAI5B,YAAa,SAAQ,GAAG;UAC5BY,MAAM,EAAEX,WAAW;UACnBc,SAAS,EAAE;QACb;MACF,CAAC,CAAC;MACF;MACA,CAAE,GAAEf,YAAa,OAAM,GAAG;QACxB,CAAE,GAAEA,YAAa,wBAAuB,GAAG;UACzC;UACAe,SAAS,EAAE;QACb,CAAC;QACD;QACA,4BAA4B,EAAE;UAC5Bc,SAAS,EAAG,KAAI9B,KAAK,CAACG,SAAU,QAAOH,KAAK,CAACG,SAAU,MAAKH,KAAK,CAAC+B,aAAc;QAClF;MACF,CAAC;MACD,CAAE,GAAE9B,YAAa,aAAYA,YAAa,iBAAgB,GAAG;QAC3DgB,eAAe,EAAEf;MACnB;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeH,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}