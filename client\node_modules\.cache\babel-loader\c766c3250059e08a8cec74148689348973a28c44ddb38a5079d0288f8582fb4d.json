{"ast": null, "code": "const genSummaryStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    tableBorderColor\n  } = token;\n  const tableBorder = `${lineWidth}px ${token.lineType} ${tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-summary`]: {\n        position: 'relative',\n        zIndex: token.zIndexTableFixed,\n        background: token.tableBg,\n        '> tr': {\n          '> th, > td': {\n            borderBottom: tableBorder\n          }\n        }\n      },\n      [`div${componentCls}-summary`]: {\n        boxShadow: `0 -${lineWidth}px 0 ${tableBorderColor}`\n      }\n    }\n  };\n};\nexport default genSummaryStyle;", "map": {"version": 3, "names": ["genSummaryStyle", "token", "componentCls", "lineWidth", "tableBorderColor", "tableBorder", "lineType", "position", "zIndex", "zIndexTableFixed", "background", "tableBg", "borderBottom", "boxShadow"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/summary.js"], "sourcesContent": ["const genSummaryStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    tableBorderColor\n  } = token;\n  const tableBorder = `${lineWidth}px ${token.lineType} ${tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-summary`]: {\n        position: 'relative',\n        zIndex: token.zIndexTableFixed,\n        background: token.tableBg,\n        '> tr': {\n          '> th, > td': {\n            borderBottom: tableBorder\n          }\n        }\n      },\n      [`div${componentCls}-summary`]: {\n        boxShadow: `0 -${lineWidth}px 0 ${tableBorderColor}`\n      }\n    }\n  };\n};\nexport default genSummaryStyle;"], "mappings": "AAAA,MAAMA,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,WAAW,GAAI,GAAEF,SAAU,MAAKF,KAAK,CAACK,QAAS,IAAGF,gBAAiB,EAAC;EAC1E,OAAO;IACL,CAAE,GAAEF,YAAa,UAAS,GAAG;MAC3B,CAAE,GAAEA,YAAa,UAAS,GAAG;QAC3BK,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAEP,KAAK,CAACQ,gBAAgB;QAC9BC,UAAU,EAAET,KAAK,CAACU,OAAO;QACzB,MAAM,EAAE;UACN,YAAY,EAAE;YACZC,YAAY,EAAEP;UAChB;QACF;MACF,CAAC;MACD,CAAE,MAAKH,YAAa,UAAS,GAAG;QAC9BW,SAAS,EAAG,MAAKV,SAAU,QAAOC,gBAAiB;MACrD;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeJ,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}