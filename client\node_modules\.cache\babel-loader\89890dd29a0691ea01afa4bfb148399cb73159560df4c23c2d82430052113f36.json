{"ast": null, "code": "/* eslint-disable max-classes-per-file */\n\nimport BigIntDecimal from \"./BigIntDecimal\";\nimport NumberDecimal from \"./NumberDecimal\";\nimport { trimNumber } from \"./numberUtil\";\nimport { supportBigInt } from \"./supportUtil\";\n\n// Still support origin export\nexport { NumberDecimal, BigIntDecimal };\nexport default function getMiniDecimal(value) {\n  // We use BigInt here.\n  // Will fallback to Number if not support.\n  if (supportBigInt()) {\n    return new BigIntDecimal(value);\n  }\n  return new NumberDecimal(value);\n}\n\n/**\n * Align the logic of toFixed to around like 1.5 => 2.\n * If set `cutOnly`, will just remove the over decimal part.\n */\nexport function toFixed(numStr, separatorStr, precision) {\n  var cutOnly = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (numStr === '') {\n    return '';\n  }\n  var _trimNumber = trimNumber(numStr),\n    negativeStr = _trimNumber.negativeStr,\n    integerStr = _trimNumber.integerStr,\n    decimalStr = _trimNumber.decimalStr;\n  var precisionDecimalStr = \"\".concat(separatorStr).concat(decimalStr);\n  var numberWithoutDecimal = \"\".concat(negativeStr).concat(integerStr);\n  if (precision >= 0) {\n    // We will get last + 1 number to check if need advanced number\n    var advancedNum = Number(decimalStr[precision]);\n    if (advancedNum >= 5 && !cutOnly) {\n      var advancedDecimal = getMiniDecimal(numStr).add(\"\".concat(negativeStr, \"0.\").concat('0'.repeat(precision)).concat(10 - advancedNum));\n      return toFixed(advancedDecimal.toString(), separatorStr, precision, cutOnly);\n    }\n    if (precision === 0) {\n      return numberWithoutDecimal;\n    }\n    return \"\".concat(numberWithoutDecimal).concat(separatorStr).concat(decimalStr.padEnd(precision, '0').slice(0, precision));\n  }\n  if (precisionDecimalStr === '.0') {\n    return numberWithoutDecimal;\n  }\n  return \"\".concat(numberWithoutDecimal).concat(precisionDecimalStr);\n}", "map": {"version": 3, "names": ["BigIntDecimal", "NumberDecimal", "trimNumber", "supportBigInt", "getMiniDecimal", "value", "toFixed", "numStr", "separatorStr", "precision", "cutOnly", "arguments", "length", "undefined", "_trimNumber", "negativeStr", "integerStr", "decimalStr", "precisionDecimalStr", "concat", "numberWithoutDecimal", "advancedNum", "Number", "advancedDecimal", "add", "repeat", "toString", "padEnd", "slice"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/mini-decimal/es/MiniDecimal.js"], "sourcesContent": ["/* eslint-disable max-classes-per-file */\n\nimport BigIntDecimal from \"./BigIntDecimal\";\nimport NumberDecimal from \"./NumberDecimal\";\nimport { trimNumber } from \"./numberUtil\";\nimport { supportBigInt } from \"./supportUtil\";\n\n// Still support origin export\nexport { NumberDecimal, BigIntDecimal };\nexport default function getMiniDecimal(value) {\n  // We use BigInt here.\n  // Will fallback to Number if not support.\n  if (supportBigInt()) {\n    return new BigIntDecimal(value);\n  }\n  return new NumberDecimal(value);\n}\n\n/**\n * Align the logic of toFixed to around like 1.5 => 2.\n * If set `cutOnly`, will just remove the over decimal part.\n */\nexport function toFixed(numStr, separatorStr, precision) {\n  var cutOnly = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (numStr === '') {\n    return '';\n  }\n  var _trimNumber = trimNumber(numStr),\n    negativeStr = _trimNumber.negativeStr,\n    integerStr = _trimNumber.integerStr,\n    decimalStr = _trimNumber.decimalStr;\n  var precisionDecimalStr = \"\".concat(separatorStr).concat(decimalStr);\n  var numberWithoutDecimal = \"\".concat(negativeStr).concat(integerStr);\n  if (precision >= 0) {\n    // We will get last + 1 number to check if need advanced number\n    var advancedNum = Number(decimalStr[precision]);\n    if (advancedNum >= 5 && !cutOnly) {\n      var advancedDecimal = getMiniDecimal(numStr).add(\"\".concat(negativeStr, \"0.\").concat('0'.repeat(precision)).concat(10 - advancedNum));\n      return toFixed(advancedDecimal.toString(), separatorStr, precision, cutOnly);\n    }\n    if (precision === 0) {\n      return numberWithoutDecimal;\n    }\n    return \"\".concat(numberWithoutDecimal).concat(separatorStr).concat(decimalStr.padEnd(precision, '0').slice(0, precision));\n  }\n  if (precisionDecimalStr === '.0') {\n    return numberWithoutDecimal;\n  }\n  return \"\".concat(numberWithoutDecimal).concat(precisionDecimalStr);\n}"], "mappings": "AAAA;;AAEA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,aAAa,QAAQ,eAAe;;AAE7C;AACA,SAASF,aAAa,EAAED,aAAa;AACrC,eAAe,SAASI,cAAcA,CAACC,KAAK,EAAE;EAC5C;EACA;EACA,IAAIF,aAAa,CAAC,CAAC,EAAE;IACnB,OAAO,IAAIH,aAAa,CAACK,KAAK,CAAC;EACjC;EACA,OAAO,IAAIJ,aAAa,CAACI,KAAK,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAE;EACvD,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACvF,IAAIJ,MAAM,KAAK,EAAE,EAAE;IACjB,OAAO,EAAE;EACX;EACA,IAAIO,WAAW,GAAGZ,UAAU,CAACK,MAAM,CAAC;IAClCQ,WAAW,GAAGD,WAAW,CAACC,WAAW;IACrCC,UAAU,GAAGF,WAAW,CAACE,UAAU;IACnCC,UAAU,GAAGH,WAAW,CAACG,UAAU;EACrC,IAAIC,mBAAmB,GAAG,EAAE,CAACC,MAAM,CAACX,YAAY,CAAC,CAACW,MAAM,CAACF,UAAU,CAAC;EACpE,IAAIG,oBAAoB,GAAG,EAAE,CAACD,MAAM,CAACJ,WAAW,CAAC,CAACI,MAAM,CAACH,UAAU,CAAC;EACpE,IAAIP,SAAS,IAAI,CAAC,EAAE;IAClB;IACA,IAAIY,WAAW,GAAGC,MAAM,CAACL,UAAU,CAACR,SAAS,CAAC,CAAC;IAC/C,IAAIY,WAAW,IAAI,CAAC,IAAI,CAACX,OAAO,EAAE;MAChC,IAAIa,eAAe,GAAGnB,cAAc,CAACG,MAAM,CAAC,CAACiB,GAAG,CAAC,EAAE,CAACL,MAAM,CAACJ,WAAW,EAAE,IAAI,CAAC,CAACI,MAAM,CAAC,GAAG,CAACM,MAAM,CAAChB,SAAS,CAAC,CAAC,CAACU,MAAM,CAAC,EAAE,GAAGE,WAAW,CAAC,CAAC;MACrI,OAAOf,OAAO,CAACiB,eAAe,CAACG,QAAQ,CAAC,CAAC,EAAElB,YAAY,EAAEC,SAAS,EAAEC,OAAO,CAAC;IAC9E;IACA,IAAID,SAAS,KAAK,CAAC,EAAE;MACnB,OAAOW,oBAAoB;IAC7B;IACA,OAAO,EAAE,CAACD,MAAM,CAACC,oBAAoB,CAAC,CAACD,MAAM,CAACX,YAAY,CAAC,CAACW,MAAM,CAACF,UAAU,CAACU,MAAM,CAAClB,SAAS,EAAE,GAAG,CAAC,CAACmB,KAAK,CAAC,CAAC,EAAEnB,SAAS,CAAC,CAAC;EAC3H;EACA,IAAIS,mBAAmB,KAAK,IAAI,EAAE;IAChC,OAAOE,oBAAoB;EAC7B;EACA,OAAO,EAAE,CAACD,MAAM,CAACC,oBAAoB,CAAC,CAACD,MAAM,CAACD,mBAAmB,CAAC;AACpE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}