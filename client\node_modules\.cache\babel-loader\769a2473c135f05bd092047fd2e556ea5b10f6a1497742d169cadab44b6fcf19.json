{"ast": null, "code": "import * as React from 'react';\n// TODO: Remove when use `responsiveImmutable`\nvar PerfContext = /*#__PURE__*/React.createContext({\n  renderWithProps: false\n});\nexport default PerfContext;", "map": {"version": 3, "names": ["React", "PerfContext", "createContext", "renderWithProps"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/context/PerfContext.js"], "sourcesContent": ["import * as React from 'react';\n// TODO: Remove when use `responsiveImmutable`\nvar PerfContext = /*#__PURE__*/React.createContext({\n  renderWithProps: false\n});\nexport default PerfContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA,IAAIC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC;EACjDC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF,eAAeF,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}