{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport getValue from \"rc-util/es/utils/get\";\nimport setValue from \"rc-util/es/utils/set\";\nimport { toArray } from './typeUtil';\nexport { getValue, setValue };\n/**\n * Convert name to internal supported format.\n * This function should keep since we still thinking if need support like `a.b.c` format.\n * 'a' => ['a']\n * 123 => [123]\n * ['a', 123] => ['a', 123]\n */\nexport function getNamePath(path) {\n  return toArray(path);\n}\nexport function cloneByNamePathList(store, namePathList) {\n  var newStore = {};\n  namePathList.forEach(function (namePath) {\n    var value = getValue(store, namePath);\n    newStore = setValue(newStore, namePath, value);\n  });\n  return newStore;\n}\nexport function containsNamePath(namePathList, namePath) {\n  return namePathList && namePathList.some(function (path) {\n    return matchNamePath(path, namePath);\n  });\n}\nexport function matchNamePath(namePath, changedNamePath) {\n  if (!namePath || !changedNamePath || namePath.length !== changedNamePath.length) {\n    return false;\n  }\n  return namePath.every(function (nameUnit, i) {\n    return changedNamePath[i] === nameUnit;\n  });\n}\nexport function isSimilar(source, target) {\n  if (source === target) {\n    return true;\n  }\n  if (!source && target || source && !target) {\n    return false;\n  }\n  if (!source || !target || _typeof(source) !== 'object' || _typeof(target) !== 'object') {\n    return false;\n  }\n  var sourceKeys = Object.keys(source);\n  var targetKeys = Object.keys(target);\n  var keys = new Set([].concat(sourceKeys, targetKeys));\n  return _toConsumableArray(keys).every(function (key) {\n    var sourceValue = source[key];\n    var targetValue = target[key];\n    if (typeof sourceValue === 'function' && typeof targetValue === 'function') {\n      return true;\n    }\n    return sourceValue === targetValue;\n  });\n}\nexport function defaultGetValueFromEvent(valuePropName) {\n  var event = arguments.length <= 1 ? undefined : arguments[1];\n  if (event && event.target && _typeof(event.target) === 'object' && valuePropName in event.target) {\n    return event.target[valuePropName];\n  }\n  return event;\n}\n/**\n * Moves an array item from one position in an array to another.\n *\n * Note: This is a pure function so a new array will be returned, instead\n * of altering the array argument.\n *\n * @param array         Array in which to move an item.         (required)\n * @param moveIndex     The index of the item to move.          (required)\n * @param toIndex       The index to move item at moveIndex to. (required)\n */\nexport function move(array, moveIndex, toIndex) {\n  var length = array.length;\n  if (moveIndex < 0 || moveIndex >= length || toIndex < 0 || toIndex >= length) {\n    return array;\n  }\n  var item = array[moveIndex];\n  var diff = moveIndex - toIndex;\n  if (diff > 0) {\n    // move left\n    return [].concat(_toConsumableArray(array.slice(0, toIndex)), [item], _toConsumableArray(array.slice(toIndex, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, length)));\n  }\n  if (diff < 0) {\n    // move right\n    return [].concat(_toConsumableArray(array.slice(0, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, toIndex + 1)), [item], _toConsumableArray(array.slice(toIndex + 1, length)));\n  }\n  return array;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_typeof", "getValue", "setValue", "toArray", "getNamePath", "path", "cloneByNamePathList", "store", "namePathList", "newStore", "for<PERSON>ach", "namePath", "value", "containsNamePath", "some", "matchNamePath", "changedNamePath", "length", "every", "nameUnit", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source", "target", "sourceKeys", "Object", "keys", "targetKeys", "Set", "concat", "key", "sourceValue", "targetValue", "defaultGetValueFromEvent", "valuePropName", "event", "arguments", "undefined", "move", "array", "moveIndex", "toIndex", "item", "diff", "slice"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-field-form/es/utils/valueUtil.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport getValue from \"rc-util/es/utils/get\";\nimport setValue from \"rc-util/es/utils/set\";\nimport { toArray } from './typeUtil';\nexport { getValue, setValue };\n/**\n * Convert name to internal supported format.\n * This function should keep since we still thinking if need support like `a.b.c` format.\n * 'a' => ['a']\n * 123 => [123]\n * ['a', 123] => ['a', 123]\n */\nexport function getNamePath(path) {\n  return toArray(path);\n}\nexport function cloneByNamePathList(store, namePathList) {\n  var newStore = {};\n  namePathList.forEach(function (namePath) {\n    var value = getValue(store, namePath);\n    newStore = setValue(newStore, namePath, value);\n  });\n  return newStore;\n}\nexport function containsNamePath(namePathList, namePath) {\n  return namePathList && namePathList.some(function (path) {\n    return matchNamePath(path, namePath);\n  });\n}\nexport function matchNamePath(namePath, changedNamePath) {\n  if (!namePath || !changedNamePath || namePath.length !== changedNamePath.length) {\n    return false;\n  }\n  return namePath.every(function (nameUnit, i) {\n    return changedNamePath[i] === nameUnit;\n  });\n}\nexport function isSimilar(source, target) {\n  if (source === target) {\n    return true;\n  }\n  if (!source && target || source && !target) {\n    return false;\n  }\n  if (!source || !target || _typeof(source) !== 'object' || _typeof(target) !== 'object') {\n    return false;\n  }\n  var sourceKeys = Object.keys(source);\n  var targetKeys = Object.keys(target);\n  var keys = new Set([].concat(sourceKeys, targetKeys));\n  return _toConsumableArray(keys).every(function (key) {\n    var sourceValue = source[key];\n    var targetValue = target[key];\n    if (typeof sourceValue === 'function' && typeof targetValue === 'function') {\n      return true;\n    }\n    return sourceValue === targetValue;\n  });\n}\nexport function defaultGetValueFromEvent(valuePropName) {\n  var event = arguments.length <= 1 ? undefined : arguments[1];\n  if (event && event.target && _typeof(event.target) === 'object' && valuePropName in event.target) {\n    return event.target[valuePropName];\n  }\n  return event;\n}\n/**\n * Moves an array item from one position in an array to another.\n *\n * Note: This is a pure function so a new array will be returned, instead\n * of altering the array argument.\n *\n * @param array         Array in which to move an item.         (required)\n * @param moveIndex     The index of the item to move.          (required)\n * @param toIndex       The index to move item at moveIndex to. (required)\n */\nexport function move(array, moveIndex, toIndex) {\n  var length = array.length;\n  if (moveIndex < 0 || moveIndex >= length || toIndex < 0 || toIndex >= length) {\n    return array;\n  }\n  var item = array[moveIndex];\n  var diff = moveIndex - toIndex;\n  if (diff > 0) {\n    // move left\n    return [].concat(_toConsumableArray(array.slice(0, toIndex)), [item], _toConsumableArray(array.slice(toIndex, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, length)));\n  }\n  if (diff < 0) {\n    // move right\n    return [].concat(_toConsumableArray(array.slice(0, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, toIndex + 1)), [item], _toConsumableArray(array.slice(toIndex + 1, length)));\n  }\n  return array;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASF,QAAQ,EAAEC,QAAQ;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAOF,OAAO,CAACE,IAAI,CAAC;AACtB;AACA,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAEC,YAAY,EAAE;EACvD,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjBD,YAAY,CAACE,OAAO,CAAC,UAAUC,QAAQ,EAAE;IACvC,IAAIC,KAAK,GAAGX,QAAQ,CAACM,KAAK,EAAEI,QAAQ,CAAC;IACrCF,QAAQ,GAAGP,QAAQ,CAACO,QAAQ,EAAEE,QAAQ,EAAEC,KAAK,CAAC;EAChD,CAAC,CAAC;EACF,OAAOH,QAAQ;AACjB;AACA,OAAO,SAASI,gBAAgBA,CAACL,YAAY,EAAEG,QAAQ,EAAE;EACvD,OAAOH,YAAY,IAAIA,YAAY,CAACM,IAAI,CAAC,UAAUT,IAAI,EAAE;IACvD,OAAOU,aAAa,CAACV,IAAI,EAAEM,QAAQ,CAAC;EACtC,CAAC,CAAC;AACJ;AACA,OAAO,SAASI,aAAaA,CAACJ,QAAQ,EAAEK,eAAe,EAAE;EACvD,IAAI,CAACL,QAAQ,IAAI,CAACK,eAAe,IAAIL,QAAQ,CAACM,MAAM,KAAKD,eAAe,CAACC,MAAM,EAAE;IAC/E,OAAO,KAAK;EACd;EACA,OAAON,QAAQ,CAACO,KAAK,CAAC,UAAUC,QAAQ,EAAEC,CAAC,EAAE;IAC3C,OAAOJ,eAAe,CAACI,CAAC,CAAC,KAAKD,QAAQ;EACxC,CAAC,CAAC;AACJ;AACA,OAAO,SAASE,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACxC,IAAID,MAAM,KAAKC,MAAM,EAAE;IACrB,OAAO,IAAI;EACb;EACA,IAAI,CAACD,MAAM,IAAIC,MAAM,IAAID,MAAM,IAAI,CAACC,MAAM,EAAE;IAC1C,OAAO,KAAK;EACd;EACA,IAAI,CAACD,MAAM,IAAI,CAACC,MAAM,IAAIvB,OAAO,CAACsB,MAAM,CAAC,KAAK,QAAQ,IAAItB,OAAO,CAACuB,MAAM,CAAC,KAAK,QAAQ,EAAE;IACtF,OAAO,KAAK;EACd;EACA,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC;EACpC,IAAIK,UAAU,GAAGF,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC;EACpC,IAAIG,IAAI,GAAG,IAAIE,GAAG,CAAC,EAAE,CAACC,MAAM,CAACL,UAAU,EAAEG,UAAU,CAAC,CAAC;EACrD,OAAO5B,kBAAkB,CAAC2B,IAAI,CAAC,CAACR,KAAK,CAAC,UAAUY,GAAG,EAAE;IACnD,IAAIC,WAAW,GAAGT,MAAM,CAACQ,GAAG,CAAC;IAC7B,IAAIE,WAAW,GAAGT,MAAM,CAACO,GAAG,CAAC;IAC7B,IAAI,OAAOC,WAAW,KAAK,UAAU,IAAI,OAAOC,WAAW,KAAK,UAAU,EAAE;MAC1E,OAAO,IAAI;IACb;IACA,OAAOD,WAAW,KAAKC,WAAW;EACpC,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,wBAAwBA,CAACC,aAAa,EAAE;EACtD,IAAIC,KAAK,GAAGC,SAAS,CAACnB,MAAM,IAAI,CAAC,GAAGoB,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC;EAC5D,IAAID,KAAK,IAAIA,KAAK,CAACZ,MAAM,IAAIvB,OAAO,CAACmC,KAAK,CAACZ,MAAM,CAAC,KAAK,QAAQ,IAAIW,aAAa,IAAIC,KAAK,CAACZ,MAAM,EAAE;IAChG,OAAOY,KAAK,CAACZ,MAAM,CAACW,aAAa,CAAC;EACpC;EACA,OAAOC,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,IAAIA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAC9C,IAAIxB,MAAM,GAAGsB,KAAK,CAACtB,MAAM;EACzB,IAAIuB,SAAS,GAAG,CAAC,IAAIA,SAAS,IAAIvB,MAAM,IAAIwB,OAAO,GAAG,CAAC,IAAIA,OAAO,IAAIxB,MAAM,EAAE;IAC5E,OAAOsB,KAAK;EACd;EACA,IAAIG,IAAI,GAAGH,KAAK,CAACC,SAAS,CAAC;EAC3B,IAAIG,IAAI,GAAGH,SAAS,GAAGC,OAAO;EAC9B,IAAIE,IAAI,GAAG,CAAC,EAAE;IACZ;IACA,OAAO,EAAE,CAACd,MAAM,CAAC9B,kBAAkB,CAACwC,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEH,OAAO,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,EAAE3C,kBAAkB,CAACwC,KAAK,CAACK,KAAK,CAACH,OAAO,EAAED,SAAS,CAAC,CAAC,EAAEzC,kBAAkB,CAACwC,KAAK,CAACK,KAAK,CAACJ,SAAS,GAAG,CAAC,EAAEvB,MAAM,CAAC,CAAC,CAAC;EACpL;EACA,IAAI0B,IAAI,GAAG,CAAC,EAAE;IACZ;IACA,OAAO,EAAE,CAACd,MAAM,CAAC9B,kBAAkB,CAACwC,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEJ,SAAS,CAAC,CAAC,EAAEzC,kBAAkB,CAACwC,KAAK,CAACK,KAAK,CAACJ,SAAS,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,EAAE3C,kBAAkB,CAACwC,KAAK,CAACK,KAAK,CAACH,OAAO,GAAG,CAAC,EAAExB,MAAM,CAAC,CAAC,CAAC;EAC5L;EACA,OAAOsB,KAAK;AACd"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}