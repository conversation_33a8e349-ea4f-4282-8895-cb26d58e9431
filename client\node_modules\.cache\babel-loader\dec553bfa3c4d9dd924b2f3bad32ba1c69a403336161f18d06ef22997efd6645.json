{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\PerformanceMonitor.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// Performance monitoring hook\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const usePerformanceMonitor = () => {\n  _s();\n  const [metrics, setMetrics] = useState({\n    loadTime: 0,\n    renderTime: 0,\n    memoryUsage: 0,\n    fps: 0\n  });\n  useEffect(() => {\n    // Measure page load time\n    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;\n\n    // Measure memory usage (if available)\n    const memoryUsage = performance.memory ? performance.memory.usedJSHeapSize / 1048576 : 0; // MB\n\n    setMetrics(prev => ({\n      ...prev,\n      loadTime,\n      memoryUsage\n    }));\n\n    // FPS monitoring\n    let frameCount = 0;\n    let lastTime = performance.now();\n    const measureFPS = () => {\n      frameCount++;\n      const currentTime = performance.now();\n      if (currentTime >= lastTime + 1000) {\n        const fps = Math.round(frameCount * 1000 / (currentTime - lastTime));\n        setMetrics(prev => ({\n          ...prev,\n          fps\n        }));\n        frameCount = 0;\n        lastTime = currentTime;\n      }\n      requestAnimationFrame(measureFPS);\n    };\n    requestAnimationFrame(measureFPS);\n  }, []);\n  return metrics;\n};\n\n// Performance indicator component\n_s(usePerformanceMonitor, \"Ka6RVACHjDSKqpEeay2VaR0hzdc=\");\nconst PerformanceIndicator = ({\n  show = false\n}) => {\n  _s2();\n  const metrics = usePerformanceMonitor();\n  const [isVisible, setIsVisible] = useState(show);\n  useEffect(() => {\n    const handleKeyPress = e => {\n      if (e.ctrlKey && e.shiftKey && e.key === 'P') {\n        setIsVisible(!isVisible);\n      }\n    };\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [isVisible]);\n  if (process.env.NODE_ENV !== 'development' && !show) return null;\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: isVisible && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      exit: {\n        opacity: 0,\n        y: 20\n      },\n      className: \"fixed bottom-4 right-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs font-mono backdrop-blur-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"font-bold text-green-400 mb-2\",\n          children: \"Performance Metrics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Load Time: \", metrics.loadTime, \"ms\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Memory: \", metrics.memoryUsage.toFixed(1), \"MB\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"FPS: \", metrics.fps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-400 mt-2 text-xs\",\n          children: \"Press Ctrl+Shift+P to toggle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n\n// Lazy loading wrapper with intersection observer\n_s2(PerformanceIndicator, \"AVO4kqW/vagHFKIbA5+qTwBu3FU=\", false, function () {\n  return [usePerformanceMonitor];\n});\n_c = PerformanceIndicator;\nexport const LazyWrapper = ({\n  children,\n  threshold = 0.1,\n  rootMargin = '50px'\n}) => {\n  _s3();\n  const [isVisible, setIsVisible] = useState(false);\n  const [ref, setRef] = useState(null);\n  useEffect(() => {\n    if (!ref) return;\n    const observer = new IntersectionObserver(([entry]) => {\n      if (entry.isIntersecting) {\n        setIsVisible(true);\n        observer.disconnect();\n      }\n    }, {\n      threshold,\n      rootMargin\n    });\n    observer.observe(ref);\n    return () => observer.disconnect();\n  }, [ref, threshold, rootMargin]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setRef,\n    children: isVisible ? children : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-32 bg-gray-100 animate-pulse rounded\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 31\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n\n// Optimized image component with WebP support\n_s3(LazyWrapper, \"7+OH+3Hgye9SulaBJN2t+ZYfQPw=\");\n_c2 = LazyWrapper;\nexport const OptimizedImage = ({\n  src,\n  webpSrc,\n  alt,\n  className = '',\n  loading = 'lazy',\n  ...props\n}) => {\n  _s4();\n  const [imageError, setImageError] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n  const handleError = () => {\n    setImageError(true);\n  };\n  const handleLoad = () => {\n    setIsLoaded(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative overflow-hidden ${className}`,\n    children: [!isLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-gray-200 animate-pulse\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 9\n    }, this), !imageError ? /*#__PURE__*/_jsxDEV(\"picture\", {\n      children: [webpSrc && /*#__PURE__*/_jsxDEV(\"source\", {\n        srcSet: webpSrc,\n        type: \"image/webp\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 23\n      }, this), /*#__PURE__*/_jsxDEV(motion.img, {\n        src: src,\n        alt: alt,\n        loading: loading,\n        onError: handleError,\n        onLoad: handleLoad,\n        className: `w-full h-full object-cover transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`,\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full h-full bg-gray-200 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-400 text-sm\",\n        children: \"Image not available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n\n// Debounced search hook\n_s4(OptimizedImage, \"BsaXuvpY10J3ojVczMlsSjGZLnI=\");\n_c3 = OptimizedImage;\nexport const useDebouncedSearch = (searchTerm, delay = 300) => {\n  _s5();\n  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedTerm(searchTerm);\n    }, delay);\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [searchTerm, delay]);\n  return debouncedTerm;\n};\n\n// Virtual scrolling component for large lists\n_s5(useDebouncedSearch, \"yEhuFh+q3lNFWhiodqqgaJgYkZQ=\");\nexport const VirtualList = ({\n  items,\n  itemHeight = 60,\n  containerHeight = 400,\n  renderItem,\n  className = ''\n}) => {\n  _s6();\n  const [scrollTop, setScrollTop] = useState(0);\n  const [containerRef, setContainerRef] = useState(null);\n  const visibleStart = Math.floor(scrollTop / itemHeight);\n  const visibleEnd = Math.min(visibleStart + Math.ceil(containerHeight / itemHeight) + 1, items.length);\n  const visibleItems = items.slice(visibleStart, visibleEnd);\n  const totalHeight = items.length * itemHeight;\n  const offsetY = visibleStart * itemHeight;\n  const handleScroll = e => {\n    setScrollTop(e.target.scrollTop);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: setContainerRef,\n    className: `overflow-auto ${className}`,\n    style: {\n      height: containerHeight\n    },\n    onScroll: handleScroll,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: totalHeight,\n        position: 'relative'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          transform: `translateY(${offsetY}px)`\n        },\n        children: visibleItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: itemHeight\n          },\n          children: renderItem(item, visibleStart + index)\n        }, visibleStart + index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 214,\n    columnNumber: 5\n  }, this);\n};\n_s6(VirtualList, \"19JZY/5RWsyc60vdyJtBhqes8Og=\");\n_c4 = VirtualList;\nexport default PerformanceIndicator;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"PerformanceIndicator\");\n$RefreshReg$(_c2, \"LazyWrapper\");\n$RefreshReg$(_c3, \"OptimizedImage\");\n$RefreshReg$(_c4, \"VirtualList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "usePerformanceMonitor", "_s", "metrics", "setMetrics", "loadTime", "renderTime", "memoryUsage", "fps", "performance", "timing", "loadEventEnd", "navigationStart", "memory", "usedJSHeapSize", "prev", "frameCount", "lastTime", "now", "measureFPS", "currentTime", "Math", "round", "requestAnimationFrame", "PerformanceIndicator", "show", "_s2", "isVisible", "setIsVisible", "handleKeyPress", "e", "ctrl<PERSON>ey", "shift<PERSON>ey", "key", "window", "addEventListener", "removeEventListener", "process", "env", "NODE_ENV", "children", "div", "initial", "opacity", "y", "animate", "exit", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toFixed", "_c", "LazyWrapper", "threshold", "rootMargin", "_s3", "ref", "setRef", "observer", "IntersectionObserver", "entry", "isIntersecting", "disconnect", "observe", "_c2", "OptimizedImage", "src", "webpSrc", "alt", "loading", "props", "_s4", "imageError", "setImageError", "isLoaded", "setIsLoaded", "handleError", "handleLoad", "srcSet", "type", "img", "onError", "onLoad", "_c3", "useDebouncedSearch", "searchTerm", "delay", "_s5", "debouncedTerm", "setDebouncedTerm", "handler", "setTimeout", "clearTimeout", "VirtualList", "items", "itemHeight", "containerHeight", "renderItem", "_s6", "scrollTop", "setScrollTop", "containerRef", "setContainerRef", "visibleStart", "floor", "visibleEnd", "min", "ceil", "length", "visibleItems", "slice", "totalHeight", "offsetY", "handleScroll", "target", "style", "height", "onScroll", "position", "transform", "map", "item", "index", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/PerformanceMonitor.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// Performance monitoring hook\nexport const usePerformanceMonitor = () => {\n  const [metrics, setMetrics] = useState({\n    loadTime: 0,\n    renderTime: 0,\n    memoryUsage: 0,\n    fps: 0,\n  });\n\n  useEffect(() => {\n    // Measure page load time\n    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;\n    \n    // Measure memory usage (if available)\n    const memoryUsage = performance.memory ? performance.memory.usedJSHeapSize / 1048576 : 0; // MB\n\n    setMetrics(prev => ({\n      ...prev,\n      loadTime,\n      memoryUsage,\n    }));\n\n    // FPS monitoring\n    let frameCount = 0;\n    let lastTime = performance.now();\n    \n    const measureFPS = () => {\n      frameCount++;\n      const currentTime = performance.now();\n      \n      if (currentTime >= lastTime + 1000) {\n        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));\n        setMetrics(prev => ({ ...prev, fps }));\n        frameCount = 0;\n        lastTime = currentTime;\n      }\n      \n      requestAnimationFrame(measureFPS);\n    };\n    \n    requestAnimationFrame(measureFPS);\n  }, []);\n\n  return metrics;\n};\n\n// Performance indicator component\nconst PerformanceIndicator = ({ show = false }) => {\n  const metrics = usePerformanceMonitor();\n  const [isVisible, setIsVisible] = useState(show);\n\n  useEffect(() => {\n    const handleKeyPress = (e) => {\n      if (e.ctrlKey && e.shiftKey && e.key === 'P') {\n        setIsVisible(!isVisible);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [isVisible]);\n\n  if (process.env.NODE_ENV !== 'development' && !show) return null;\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: 20 }}\n          className=\"fixed bottom-4 right-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs font-mono backdrop-blur-sm\"\n        >\n          <div className=\"space-y-1\">\n            <div className=\"font-bold text-green-400 mb-2\">Performance Metrics</div>\n            <div>Load Time: {metrics.loadTime}ms</div>\n            <div>Memory: {metrics.memoryUsage.toFixed(1)}MB</div>\n            <div>FPS: {metrics.fps}</div>\n            <div className=\"text-gray-400 mt-2 text-xs\">\n              Press Ctrl+Shift+P to toggle\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\n// Lazy loading wrapper with intersection observer\nexport const LazyWrapper = ({ children, threshold = 0.1, rootMargin = '50px' }) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [ref, setRef] = useState(null);\n\n  useEffect(() => {\n    if (!ref) return;\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n          observer.disconnect();\n        }\n      },\n      { threshold, rootMargin }\n    );\n\n    observer.observe(ref);\n    return () => observer.disconnect();\n  }, [ref, threshold, rootMargin]);\n\n  return (\n    <div ref={setRef}>\n      {isVisible ? children : <div className=\"h-32 bg-gray-100 animate-pulse rounded\" />}\n    </div>\n  );\n};\n\n// Optimized image component with WebP support\nexport const OptimizedImage = ({ \n  src, \n  webpSrc, \n  alt, \n  className = '',\n  loading = 'lazy',\n  ...props \n}) => {\n  const [imageError, setImageError] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  const handleError = () => {\n    setImageError(true);\n  };\n\n  const handleLoad = () => {\n    setIsLoaded(true);\n  };\n\n  return (\n    <div className={`relative overflow-hidden ${className}`}>\n      {!isLoaded && (\n        <div className=\"absolute inset-0 bg-gray-200 animate-pulse\" />\n      )}\n      \n      {!imageError ? (\n        <picture>\n          {webpSrc && <source srcSet={webpSrc} type=\"image/webp\" />}\n          <motion.img\n            src={src}\n            alt={alt}\n            loading={loading}\n            onError={handleError}\n            onLoad={handleLoad}\n            className={`w-full h-full object-cover transition-opacity duration-300 ${\n              isLoaded ? 'opacity-100' : 'opacity-0'\n            }`}\n            {...props}\n          />\n        </picture>\n      ) : (\n        <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n          <span className=\"text-gray-400 text-sm\">Image not available</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Debounced search hook\nexport const useDebouncedSearch = (searchTerm, delay = 300) => {\n  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedTerm(searchTerm);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [searchTerm, delay]);\n\n  return debouncedTerm;\n};\n\n// Virtual scrolling component for large lists\nexport const VirtualList = ({ \n  items, \n  itemHeight = 60, \n  containerHeight = 400,\n  renderItem,\n  className = '' \n}) => {\n  const [scrollTop, setScrollTop] = useState(0);\n  const [containerRef, setContainerRef] = useState(null);\n\n  const visibleStart = Math.floor(scrollTop / itemHeight);\n  const visibleEnd = Math.min(\n    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,\n    items.length\n  );\n\n  const visibleItems = items.slice(visibleStart, visibleEnd);\n  const totalHeight = items.length * itemHeight;\n  const offsetY = visibleStart * itemHeight;\n\n  const handleScroll = (e) => {\n    setScrollTop(e.target.scrollTop);\n  };\n\n  return (\n    <div\n      ref={setContainerRef}\n      className={`overflow-auto ${className}`}\n      style={{ height: containerHeight }}\n      onScroll={handleScroll}\n    >\n      <div style={{ height: totalHeight, position: 'relative' }}>\n        <div style={{ transform: `translateY(${offsetY}px)` }}>\n          {visibleItems.map((item, index) => (\n            <div key={visibleStart + index} style={{ height: itemHeight }}>\n              {renderItem(item, visibleStart + index)}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PerformanceIndicator;\n"], "mappings": ";;;;;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC;IACrCS,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,GAAG,EAAE;EACP,CAAC,CAAC;EAEFb,SAAS,CAAC,MAAM;IACd;IACA,MAAMU,QAAQ,GAAGI,WAAW,CAACC,MAAM,CAACC,YAAY,GAAGF,WAAW,CAACC,MAAM,CAACE,eAAe;;IAErF;IACA,MAAML,WAAW,GAAGE,WAAW,CAACI,MAAM,GAAGJ,WAAW,CAACI,MAAM,CAACC,cAAc,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;;IAE1FV,UAAU,CAACW,IAAI,KAAK;MAClB,GAAGA,IAAI;MACPV,QAAQ;MACRE;IACF,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIS,UAAU,GAAG,CAAC;IAClB,IAAIC,QAAQ,GAAGR,WAAW,CAACS,GAAG,CAAC,CAAC;IAEhC,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvBH,UAAU,EAAE;MACZ,MAAMI,WAAW,GAAGX,WAAW,CAACS,GAAG,CAAC,CAAC;MAErC,IAAIE,WAAW,IAAIH,QAAQ,GAAG,IAAI,EAAE;QAClC,MAAMT,GAAG,GAAGa,IAAI,CAACC,KAAK,CAAEN,UAAU,GAAG,IAAI,IAAKI,WAAW,GAAGH,QAAQ,CAAC,CAAC;QACtEb,UAAU,CAACW,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEP;QAAI,CAAC,CAAC,CAAC;QACtCQ,UAAU,GAAG,CAAC;QACdC,QAAQ,GAAGG,WAAW;MACxB;MAEAG,qBAAqB,CAACJ,UAAU,CAAC;IACnC,CAAC;IAEDI,qBAAqB,CAACJ,UAAU,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOhB,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CA7CaD,qBAAqB;AA8ClC,MAAMuB,oBAAoB,GAAGA,CAAC;EAAEC,IAAI,GAAG;AAAM,CAAC,KAAK;EAAAC,GAAA;EACjD,MAAMvB,OAAO,GAAGF,qBAAqB,CAAC,CAAC;EACvC,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC6B,IAAI,CAAC;EAEhD9B,SAAS,CAAC,MAAM;IACd,MAAMkC,cAAc,GAAIC,CAAC,IAAK;MAC5B,IAAIA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,QAAQ,IAAIF,CAAC,CAACG,GAAG,KAAK,GAAG,EAAE;QAC5CL,YAAY,CAAC,CAACD,SAAS,CAAC;MAC1B;IACF,CAAC;IAEDO,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEN,cAAc,CAAC;IAClD,OAAO,MAAMK,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEP,cAAc,CAAC;EACpE,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,IAAIU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,CAACd,IAAI,EAAE,OAAO,IAAI;EAEhE,oBACEzB,OAAA,CAACF,eAAe;IAAA0C,QAAA,EACbb,SAAS,iBACR3B,OAAA,CAACH,MAAM,CAAC4C,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,IAAI,EAAE;QAAEH,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC5BG,SAAS,EAAC,sGAAsG;MAAAP,QAAA,eAEhHxC,OAAA;QAAK+C,SAAS,EAAC,WAAW;QAAAP,QAAA,gBACxBxC,OAAA;UAAK+C,SAAS,EAAC,+BAA+B;UAAAP,QAAA,EAAC;QAAmB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxEnD,OAAA;UAAAwC,QAAA,GAAK,aAAW,EAACrC,OAAO,CAACE,QAAQ,EAAC,IAAE;QAAA;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1CnD,OAAA;UAAAwC,QAAA,GAAK,UAAQ,EAACrC,OAAO,CAACI,WAAW,CAAC6C,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrDnD,OAAA;UAAAwC,QAAA,GAAK,OAAK,EAACrC,OAAO,CAACK,GAAG;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7BnD,OAAA;UAAK+C,SAAS,EAAC,4BAA4B;UAAAP,QAAA,EAAC;QAE5C;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EACb;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAEtB,CAAC;;AAED;AAAAzB,GAAA,CAzCMF,oBAAoB;EAAA,QACRvB,qBAAqB;AAAA;AAAAoD,EAAA,GADjC7B,oBAAoB;AA0C1B,OAAO,MAAM8B,WAAW,GAAGA,CAAC;EAAEd,QAAQ;EAAEe,SAAS,GAAG,GAAG;EAAEC,UAAU,GAAG;AAAO,CAAC,KAAK;EAAAC,GAAA;EACjF,MAAM,CAAC9B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8D,GAAG,EAAEC,MAAM,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAEpCD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+D,GAAG,EAAE;IAEV,MAAME,QAAQ,GAAG,IAAIC,oBAAoB,CACvC,CAAC,CAACC,KAAK,CAAC,KAAK;MACX,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxBnC,YAAY,CAAC,IAAI,CAAC;QAClBgC,QAAQ,CAACI,UAAU,CAAC,CAAC;MACvB;IACF,CAAC,EACD;MAAET,SAAS;MAAEC;IAAW,CAC1B,CAAC;IAEDI,QAAQ,CAACK,OAAO,CAACP,GAAG,CAAC;IACrB,OAAO,MAAME,QAAQ,CAACI,UAAU,CAAC,CAAC;EACpC,CAAC,EAAE,CAACN,GAAG,EAAEH,SAAS,EAAEC,UAAU,CAAC,CAAC;EAEhC,oBACExD,OAAA;IAAK0D,GAAG,EAAEC,MAAO;IAAAnB,QAAA,EACdb,SAAS,GAAGa,QAAQ,gBAAGxC,OAAA;MAAK+C,SAAS,EAAC;IAAwC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/E,CAAC;AAEV,CAAC;;AAED;AAAAM,GAAA,CA5BaH,WAAW;AAAAY,GAAA,GAAXZ,WAAW;AA6BxB,OAAO,MAAMa,cAAc,GAAGA,CAAC;EAC7BC,GAAG;EACHC,OAAO;EACPC,GAAG;EACHvB,SAAS,GAAG,EAAE;EACdwB,OAAO,GAAG,MAAM;EAChB,GAAGC;AACL,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgF,QAAQ,EAAEC,WAAW,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMkF,WAAW,GAAGA,CAAA,KAAM;IACxBH,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvBF,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,oBACE7E,OAAA;IAAK+C,SAAS,EAAG,4BAA2BA,SAAU,EAAE;IAAAP,QAAA,GACrD,CAACoC,QAAQ,iBACR5E,OAAA;MAAK+C,SAAS,EAAC;IAA4C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC9D,EAEA,CAACuB,UAAU,gBACV1E,OAAA;MAAAwC,QAAA,GACG6B,OAAO,iBAAIrE,OAAA;QAAQgF,MAAM,EAAEX,OAAQ;QAACY,IAAI,EAAC;MAAY;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzDnD,OAAA,CAACH,MAAM,CAACqF,GAAG;QACTd,GAAG,EAAEA,GAAI;QACTE,GAAG,EAAEA,GAAI;QACTC,OAAO,EAAEA,OAAQ;QACjBY,OAAO,EAAEL,WAAY;QACrBM,MAAM,EAAEL,UAAW;QACnBhC,SAAS,EAAG,8DACV6B,QAAQ,GAAG,aAAa,GAAG,WAC5B,EAAE;QAAA,GACCJ;MAAK;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,gBAEVnD,OAAA;MAAK+C,SAAS,EAAC,4DAA4D;MAAAP,QAAA,eACzExC,OAAA;QAAM+C,SAAS,EAAC,uBAAuB;QAAAP,QAAA,EAAC;MAAmB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAsB,GAAA,CAjDaN,cAAc;AAAAkB,GAAA,GAAdlB,cAAc;AAkD3B,OAAO,MAAMmB,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,GAAG,GAAG,KAAK;EAAAC,GAAA;EAC7D,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/F,QAAQ,CAAC2F,UAAU,CAAC;EAE9D5F,SAAS,CAAC,MAAM;IACd,MAAMiG,OAAO,GAAGC,UAAU,CAAC,MAAM;MAC/BF,gBAAgB,CAACJ,UAAU,CAAC;IAC9B,CAAC,EAAEC,KAAK,CAAC;IAET,OAAO,MAAM;MACXM,YAAY,CAACF,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACL,UAAU,EAAEC,KAAK,CAAC,CAAC;EAEvB,OAAOE,aAAa;AACtB,CAAC;;AAED;AAAAD,GAAA,CAhBaH,kBAAkB;AAiB/B,OAAO,MAAMS,WAAW,GAAGA,CAAC;EAC1BC,KAAK;EACLC,UAAU,GAAG,EAAE;EACfC,eAAe,GAAG,GAAG;EACrBC,UAAU;EACVpD,SAAS,GAAG;AACd,CAAC,KAAK;EAAAqD,GAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1G,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC2G,YAAY,EAAEC,eAAe,CAAC,GAAG5G,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAM6G,YAAY,GAAGpF,IAAI,CAACqF,KAAK,CAACL,SAAS,GAAGJ,UAAU,CAAC;EACvD,MAAMU,UAAU,GAAGtF,IAAI,CAACuF,GAAG,CACzBH,YAAY,GAAGpF,IAAI,CAACwF,IAAI,CAACX,eAAe,GAAGD,UAAU,CAAC,GAAG,CAAC,EAC1DD,KAAK,CAACc,MACR,CAAC;EAED,MAAMC,YAAY,GAAGf,KAAK,CAACgB,KAAK,CAACP,YAAY,EAAEE,UAAU,CAAC;EAC1D,MAAMM,WAAW,GAAGjB,KAAK,CAACc,MAAM,GAAGb,UAAU;EAC7C,MAAMiB,OAAO,GAAGT,YAAY,GAAGR,UAAU;EAEzC,MAAMkB,YAAY,GAAIrF,CAAC,IAAK;IAC1BwE,YAAY,CAACxE,CAAC,CAACsF,MAAM,CAACf,SAAS,CAAC;EAClC,CAAC;EAED,oBACErG,OAAA;IACE0D,GAAG,EAAE8C,eAAgB;IACrBzD,SAAS,EAAG,iBAAgBA,SAAU,EAAE;IACxCsE,KAAK,EAAE;MAAEC,MAAM,EAAEpB;IAAgB,CAAE;IACnCqB,QAAQ,EAAEJ,YAAa;IAAA3E,QAAA,eAEvBxC,OAAA;MAAKqH,KAAK,EAAE;QAAEC,MAAM,EAAEL,WAAW;QAAEO,QAAQ,EAAE;MAAW,CAAE;MAAAhF,QAAA,eACxDxC,OAAA;QAAKqH,KAAK,EAAE;UAAEI,SAAS,EAAG,cAAaP,OAAQ;QAAK,CAAE;QAAA1E,QAAA,EACnDuE,YAAY,CAACW,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC5B5H,OAAA;UAAgCqH,KAAK,EAAE;YAAEC,MAAM,EAAErB;UAAW,CAAE;UAAAzD,QAAA,EAC3D2D,UAAU,CAACwB,IAAI,EAAElB,YAAY,GAAGmB,KAAK;QAAC,GAD/BnB,YAAY,GAAGmB,KAAK;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACiD,GAAA,CA1CWL,WAAW;AAAA8B,GAAA,GAAX9B,WAAW;AA4CxB,eAAevE,oBAAoB;AAAC,IAAA6B,EAAA,EAAAa,GAAA,EAAAmB,GAAA,EAAAwC,GAAA;AAAAC,YAAA,CAAAzE,EAAA;AAAAyE,YAAA,CAAA5D,GAAA;AAAA4D,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}