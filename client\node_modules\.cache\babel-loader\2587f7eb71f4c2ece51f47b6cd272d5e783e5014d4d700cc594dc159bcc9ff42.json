{"ast": null, "code": "import { responseImmutable, useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport PerfContext from \"../context/PerfContext\";\nimport TableContext from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nimport BodyRow from \"./BodyRow\";\nimport ExpandedRow from \"./ExpandedRow\";\nimport MeasureRow from \"./MeasureRow\";\nfunction Body(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var data = props.data,\n    getRowKey = props.getRowKey,\n    measureColumnWidth = props.measureColumnWidth,\n    expandedKeys = props.expandedKeys,\n    onRow = props.onRow,\n    rowExpandable = props.rowExpandable,\n    emptyNode = props.emptyNode,\n    childrenColumnName = props.childrenColumnName;\n  var _useContext = useContext(TableContext, ['prefixCls', 'getComponent', 'onColumnResize', 'flattenColumns']),\n    prefixCls = _useContext.prefixCls,\n    getComponent = _useContext.getComponent,\n    onColumnResize = _useContext.onColumnResize,\n    flattenColumns = _useContext.flattenColumns;\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n\n  // =================== Performance ====================\n  var perfRef = React.useRef({\n    renderWithProps: false\n  });\n\n  // ====================== Render ======================\n  var WrapperComponent = getComponent(['body', 'wrapper'], 'tbody');\n  var trComponent = getComponent(['body', 'row'], 'tr');\n  var tdComponent = getComponent(['body', 'cell'], 'td');\n  var thComponent = getComponent(['body', 'cell'], 'th');\n  var rows;\n  if (data.length) {\n    rows = flattenData.map(function (item, idx) {\n      var record = item.record,\n        indent = item.indent,\n        renderIndex = item.index;\n      var key = getRowKey(record, idx);\n      return /*#__PURE__*/React.createElement(BodyRow, {\n        key: key,\n        rowKey: key,\n        record: record,\n        index: idx,\n        renderIndex: renderIndex,\n        rowComponent: trComponent,\n        cellComponent: tdComponent,\n        scopeCellComponent: thComponent,\n        expandedKeys: expandedKeys,\n        onRow: onRow,\n        getRowKey: getRowKey,\n        rowExpandable: rowExpandable,\n        childrenColumnName: childrenColumnName,\n        indent: indent\n      });\n    });\n  } else {\n    rows = /*#__PURE__*/React.createElement(ExpandedRow, {\n      expanded: true,\n      className: \"\".concat(prefixCls, \"-placeholder\"),\n      prefixCls: prefixCls,\n      component: trComponent,\n      cellComponent: tdComponent,\n      colSpan: flattenColumns.length,\n      isEmpty: true\n    }, emptyNode);\n  }\n  var columnsKey = getColumnsKey(flattenColumns);\n  return /*#__PURE__*/React.createElement(PerfContext.Provider, {\n    value: perfRef.current\n  }, /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-tbody\")\n  }, measureColumnWidth && /*#__PURE__*/React.createElement(MeasureRow, {\n    prefixCls: prefixCls,\n    columnsKey: columnsKey,\n    onColumnResize: onColumnResize\n  }), rows));\n}\nBody.displayName = 'Body';\nexport default responseImmutable(Body);", "map": {"version": 3, "names": ["responseImmutable", "useContext", "React", "PerfContext", "TableContext", "useFlattenRecords", "devRenderTimes", "getColumnsKey", "BodyRow", "ExpandedRow", "MeasureRow", "Body", "props", "process", "env", "NODE_ENV", "data", "getRowKey", "measureColumnWidth", "expandedKeys", "onRow", "rowExpandable", "emptyNode", "childrenColumnName", "_useContext", "prefixCls", "getComponent", "onColumnResize", "flattenColumns", "flattenData", "perfRef", "useRef", "renderWithProps", "WrapperComponent", "trComponent", "tdComponent", "thComponent", "rows", "length", "map", "item", "idx", "record", "indent", "renderIndex", "index", "key", "createElement", "<PERSON><PERSON><PERSON>", "rowComponent", "cellComponent", "scopeCellComponent", "expanded", "className", "concat", "component", "colSpan", "isEmpty", "columnsKey", "Provider", "value", "current", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/Body/index.js"], "sourcesContent": ["import { responseImmutable, useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport PerfContext from \"../context/PerfContext\";\nimport TableContext from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nimport BodyRow from \"./BodyRow\";\nimport ExpandedRow from \"./ExpandedRow\";\nimport MeasureRow from \"./MeasureRow\";\nfunction Body(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var data = props.data,\n    getRowKey = props.getRowKey,\n    measureColumnWidth = props.measureColumnWidth,\n    expandedKeys = props.expandedKeys,\n    onRow = props.onRow,\n    rowExpandable = props.rowExpandable,\n    emptyNode = props.emptyNode,\n    childrenColumnName = props.childrenColumnName;\n  var _useContext = useContext(TableContext, ['prefixCls', 'getComponent', 'onColumnResize', 'flattenColumns']),\n    prefixCls = _useContext.prefixCls,\n    getComponent = _useContext.getComponent,\n    onColumnResize = _useContext.onColumnResize,\n    flattenColumns = _useContext.flattenColumns;\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n\n  // =================== Performance ====================\n  var perfRef = React.useRef({\n    renderWithProps: false\n  });\n\n  // ====================== Render ======================\n  var WrapperComponent = getComponent(['body', 'wrapper'], 'tbody');\n  var trComponent = getComponent(['body', 'row'], 'tr');\n  var tdComponent = getComponent(['body', 'cell'], 'td');\n  var thComponent = getComponent(['body', 'cell'], 'th');\n  var rows;\n  if (data.length) {\n    rows = flattenData.map(function (item, idx) {\n      var record = item.record,\n        indent = item.indent,\n        renderIndex = item.index;\n      var key = getRowKey(record, idx);\n      return /*#__PURE__*/React.createElement(BodyRow, {\n        key: key,\n        rowKey: key,\n        record: record,\n        index: idx,\n        renderIndex: renderIndex,\n        rowComponent: trComponent,\n        cellComponent: tdComponent,\n        scopeCellComponent: thComponent,\n        expandedKeys: expandedKeys,\n        onRow: onRow,\n        getRowKey: getRowKey,\n        rowExpandable: rowExpandable,\n        childrenColumnName: childrenColumnName,\n        indent: indent\n      });\n    });\n  } else {\n    rows = /*#__PURE__*/React.createElement(ExpandedRow, {\n      expanded: true,\n      className: \"\".concat(prefixCls, \"-placeholder\"),\n      prefixCls: prefixCls,\n      component: trComponent,\n      cellComponent: tdComponent,\n      colSpan: flattenColumns.length,\n      isEmpty: true\n    }, emptyNode);\n  }\n  var columnsKey = getColumnsKey(flattenColumns);\n  return /*#__PURE__*/React.createElement(PerfContext.Provider, {\n    value: perfRef.current\n  }, /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-tbody\")\n  }, measureColumnWidth && /*#__PURE__*/React.createElement(MeasureRow, {\n    prefixCls: prefixCls,\n    columnsKey: columnsKey,\n    onColumnResize: onColumnResize\n  }), rows));\n}\nBody.displayName = 'Body';\nexport default responseImmutable(Body);"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,UAAU,QAAQ,uBAAuB;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,IAAIA,CAACC,KAAK,EAAE;EACnB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCT,cAAc,CAACM,KAAK,CAAC;EACvB;EACA,IAAII,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACnBC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,kBAAkB,GAAGN,KAAK,CAACM,kBAAkB;IAC7CC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,aAAa,GAAGT,KAAK,CAACS,aAAa;IACnCC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,kBAAkB,GAAGX,KAAK,CAACW,kBAAkB;EAC/C,IAAIC,WAAW,GAAGvB,UAAU,CAACG,YAAY,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;IAC3GqB,SAAS,GAAGD,WAAW,CAACC,SAAS;IACjCC,YAAY,GAAGF,WAAW,CAACE,YAAY;IACvCC,cAAc,GAAGH,WAAW,CAACG,cAAc;IAC3CC,cAAc,GAAGJ,WAAW,CAACI,cAAc;EAC7C,IAAIC,WAAW,GAAGxB,iBAAiB,CAACW,IAAI,EAAEO,kBAAkB,EAAEJ,YAAY,EAAEF,SAAS,CAAC;;EAEtF;EACA,IAAIa,OAAO,GAAG5B,KAAK,CAAC6B,MAAM,CAAC;IACzBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,IAAIC,gBAAgB,GAAGP,YAAY,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC;EACjE,IAAIQ,WAAW,GAAGR,YAAY,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;EACrD,IAAIS,WAAW,GAAGT,YAAY,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;EACtD,IAAIU,WAAW,GAAGV,YAAY,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;EACtD,IAAIW,IAAI;EACR,IAAIrB,IAAI,CAACsB,MAAM,EAAE;IACfD,IAAI,GAAGR,WAAW,CAACU,GAAG,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;MAC1C,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;QACtBC,MAAM,GAAGH,IAAI,CAACG,MAAM;QACpBC,WAAW,GAAGJ,IAAI,CAACK,KAAK;MAC1B,IAAIC,GAAG,GAAG7B,SAAS,CAACyB,MAAM,EAAED,GAAG,CAAC;MAChC,OAAO,aAAavC,KAAK,CAAC6C,aAAa,CAACvC,OAAO,EAAE;QAC/CsC,GAAG,EAAEA,GAAG;QACRE,MAAM,EAAEF,GAAG;QACXJ,MAAM,EAAEA,MAAM;QACdG,KAAK,EAAEJ,GAAG;QACVG,WAAW,EAAEA,WAAW;QACxBK,YAAY,EAAEf,WAAW;QACzBgB,aAAa,EAAEf,WAAW;QAC1BgB,kBAAkB,EAAEf,WAAW;QAC/BjB,YAAY,EAAEA,YAAY;QAC1BC,KAAK,EAAEA,KAAK;QACZH,SAAS,EAAEA,SAAS;QACpBI,aAAa,EAAEA,aAAa;QAC5BE,kBAAkB,EAAEA,kBAAkB;QACtCoB,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLN,IAAI,GAAG,aAAanC,KAAK,CAAC6C,aAAa,CAACtC,WAAW,EAAE;MACnD2C,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC7B,SAAS,EAAE,cAAc,CAAC;MAC/CA,SAAS,EAAEA,SAAS;MACpB8B,SAAS,EAAErB,WAAW;MACtBgB,aAAa,EAAEf,WAAW;MAC1BqB,OAAO,EAAE5B,cAAc,CAACU,MAAM;MAC9BmB,OAAO,EAAE;IACX,CAAC,EAAEnC,SAAS,CAAC;EACf;EACA,IAAIoC,UAAU,GAAGnD,aAAa,CAACqB,cAAc,CAAC;EAC9C,OAAO,aAAa1B,KAAK,CAAC6C,aAAa,CAAC5C,WAAW,CAACwD,QAAQ,EAAE;IAC5DC,KAAK,EAAE9B,OAAO,CAAC+B;EACjB,CAAC,EAAE,aAAa3D,KAAK,CAAC6C,aAAa,CAACd,gBAAgB,EAAE;IACpDoB,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC7B,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEP,kBAAkB,IAAI,aAAahB,KAAK,CAAC6C,aAAa,CAACrC,UAAU,EAAE;IACpEe,SAAS,EAAEA,SAAS;IACpBiC,UAAU,EAAEA,UAAU;IACtB/B,cAAc,EAAEA;EAClB,CAAC,CAAC,EAAEU,IAAI,CAAC,CAAC;AACZ;AACA1B,IAAI,CAACmD,WAAW,GAAG,MAAM;AACzB,eAAe9D,iBAAiB,CAACW,IAAI,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}