{"ast": null, "code": "import * as React from 'react';\nimport { useRef } from 'react';\nimport { animationEndName, transitionEndName } from \"../util/motion\";\nexport default (function (callback) {\n  var cacheElementRef = useRef();\n\n  // Cache callback\n  var callbackRef = useRef(callback);\n  callbackRef.current = callback;\n\n  // Internal motion event handler\n  var onInternalMotionEnd = React.useCallback(function (event) {\n    callbackRef.current(event);\n  }, []);\n\n  // Remove events\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(animationEndName, onInternalMotionEnd);\n    }\n  }\n\n  // Patch events\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(transitionEndName, onInternalMotionEnd);\n      element.addEventListener(animationEndName, onInternalMotionEnd);\n\n      // Save as cache in case dom removed trigger by `motionDeadline`\n      cacheElementRef.current = element;\n    }\n  }\n\n  // Clean up when removed\n  React.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});", "map": {"version": 3, "names": ["React", "useRef", "animationEndName", "transitionEndName", "callback", "cacheElementRef", "callback<PERSON><PERSON>", "current", "onInternalMotionEnd", "useCallback", "event", "removeMotionEvents", "element", "removeEventListener", "patchMotionEvents", "addEventListener", "useEffect"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-motion/es/hooks/useDomMotionEvents.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRef } from 'react';\nimport { animationEndName, transitionEndName } from \"../util/motion\";\nexport default (function (callback) {\n  var cacheElementRef = useRef();\n\n  // Cache callback\n  var callbackRef = useRef(callback);\n  callbackRef.current = callback;\n\n  // Internal motion event handler\n  var onInternalMotionEnd = React.useCallback(function (event) {\n    callbackRef.current(event);\n  }, []);\n\n  // Remove events\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(animationEndName, onInternalMotionEnd);\n    }\n  }\n\n  // Patch events\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(transitionEndName, onInternalMotionEnd);\n      element.addEventListener(animationEndName, onInternalMotionEnd);\n\n      // Save as cache in case dom removed trigger by `motionDeadline`\n      cacheElementRef.current = element;\n    }\n  }\n\n  // Clean up when removed\n  React.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,gBAAgB;AACpE,gBAAgB,UAAUC,QAAQ,EAAE;EAClC,IAAIC,eAAe,GAAGJ,MAAM,CAAC,CAAC;;EAE9B;EACA,IAAIK,WAAW,GAAGL,MAAM,CAACG,QAAQ,CAAC;EAClCE,WAAW,CAACC,OAAO,GAAGH,QAAQ;;EAE9B;EACA,IAAII,mBAAmB,GAAGR,KAAK,CAACS,WAAW,CAAC,UAAUC,KAAK,EAAE;IAC3DJ,WAAW,CAACC,OAAO,CAACG,KAAK,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,SAASC,kBAAkBA,CAACC,OAAO,EAAE;IACnC,IAAIA,OAAO,EAAE;MACXA,OAAO,CAACC,mBAAmB,CAACV,iBAAiB,EAAEK,mBAAmB,CAAC;MACnEI,OAAO,CAACC,mBAAmB,CAACX,gBAAgB,EAAEM,mBAAmB,CAAC;IACpE;EACF;;EAEA;EACA,SAASM,iBAAiBA,CAACF,OAAO,EAAE;IAClC,IAAIP,eAAe,CAACE,OAAO,IAAIF,eAAe,CAACE,OAAO,KAAKK,OAAO,EAAE;MAClED,kBAAkB,CAACN,eAAe,CAACE,OAAO,CAAC;IAC7C;IACA,IAAIK,OAAO,IAAIA,OAAO,KAAKP,eAAe,CAACE,OAAO,EAAE;MAClDK,OAAO,CAACG,gBAAgB,CAACZ,iBAAiB,EAAEK,mBAAmB,CAAC;MAChEI,OAAO,CAACG,gBAAgB,CAACb,gBAAgB,EAAEM,mBAAmB,CAAC;;MAE/D;MACAH,eAAe,CAACE,OAAO,GAAGK,OAAO;IACnC;EACF;;EAEA;EACAZ,KAAK,CAACgB,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBL,kBAAkB,CAACN,eAAe,CAACE,OAAO,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACO,iBAAiB,EAAEH,kBAAkB,CAAC;AAChD,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}