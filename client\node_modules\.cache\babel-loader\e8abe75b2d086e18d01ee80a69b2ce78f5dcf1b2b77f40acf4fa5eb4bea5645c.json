{"ast": null, "code": "'use strict';\n\nmodule.exports = typeof navigator != 'undefined' && String(navigator.userAgent) || '';", "map": {"version": 3, "names": ["module", "exports", "navigator", "String", "userAgent"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/core-js-pure/internals/engine-user-agent.js"], "sourcesContent": ["'use strict';\nmodule.exports = typeof navigator != 'undefined' && String(navigator.userAgent) || '';\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,OAAO,GAAG,OAAOC,SAAS,IAAI,WAAW,IAAIC,MAAM,CAACD,SAAS,CAACE,SAAS,CAAC,IAAI,EAAE"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}