{"ast": null, "code": "import React from 'react';\nexport function useCellRender(_ref) {\n  var cellRender = _ref.cellRender,\n    monthCellRender = _ref.monthCellRender,\n    dateRender = _ref.dateRender;\n  var mergedCellRender = React.useMemo(function () {\n    if (cellRender) return cellRender;\n    if (!monthCellRender && !dateRender) return undefined;\n    return function (current, info) {\n      var date = current;\n      if (dateRender && info.type === 'date') {\n        return dateRender(date, info.today);\n      }\n      if (monthCellRender && info.type === 'month') {\n        return monthCellRender(date, info.locale);\n      }\n      return info.originNode;\n    };\n  }, [cellRender, monthCellRender, dateRender]);\n  return mergedCellRender;\n}", "map": {"version": 3, "names": ["React", "useCellRender", "_ref", "cellRender", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateRender", "mergedCellRender", "useMemo", "undefined", "current", "info", "date", "type", "today", "locale", "originNode"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/hooks/useCellRender.js"], "sourcesContent": ["import React from 'react';\nexport function useCellRender(_ref) {\n  var cellRender = _ref.cellRender,\n    monthCellRender = _ref.monthCellRender,\n    dateRender = _ref.dateRender;\n  var mergedCellRender = React.useMemo(function () {\n    if (cellRender) return cellRender;\n    if (!monthCellRender && !dateRender) return undefined;\n    return function (current, info) {\n      var date = current;\n      if (dateRender && info.type === 'date') {\n        return dateRender(date, info.today);\n      }\n      if (monthCellRender && info.type === 'month') {\n        return monthCellRender(date, info.locale);\n      }\n      return info.originNode;\n    };\n  }, [cellRender, monthCellRender, dateRender]);\n  return mergedCellRender;\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;IAC9BC,eAAe,GAAGF,IAAI,CAACE,eAAe;IACtCC,UAAU,GAAGH,IAAI,CAACG,UAAU;EAC9B,IAAIC,gBAAgB,GAAGN,KAAK,CAACO,OAAO,CAAC,YAAY;IAC/C,IAAIJ,UAAU,EAAE,OAAOA,UAAU;IACjC,IAAI,CAACC,eAAe,IAAI,CAACC,UAAU,EAAE,OAAOG,SAAS;IACrD,OAAO,UAAUC,OAAO,EAAEC,IAAI,EAAE;MAC9B,IAAIC,IAAI,GAAGF,OAAO;MAClB,IAAIJ,UAAU,IAAIK,IAAI,CAACE,IAAI,KAAK,MAAM,EAAE;QACtC,OAAOP,UAAU,CAACM,IAAI,EAAED,IAAI,CAACG,KAAK,CAAC;MACrC;MACA,IAAIT,eAAe,IAAIM,IAAI,CAACE,IAAI,KAAK,OAAO,EAAE;QAC5C,OAAOR,eAAe,CAACO,IAAI,EAAED,IAAI,CAACI,MAAM,CAAC;MAC3C;MACA,OAAOJ,IAAI,CAACK,UAAU;IACxB,CAAC;EACH,CAAC,EAAE,CAACZ,UAAU,EAAEC,eAAe,EAAEC,UAAU,CAAC,CAAC;EAC7C,OAAOC,gBAAgB;AACzB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}