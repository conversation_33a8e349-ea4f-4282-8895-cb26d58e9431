{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\n// Firefox has low performance of map.\nvar CacheMap = /*#__PURE__*/function () {\n  function CacheMap() {\n    _classCallCheck(this, CacheMap);\n    this.maps = void 0;\n    this.maps = Object.create(null);\n  }\n  _createClass(CacheMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      this.maps[key] = value;\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.maps[key];\n    }\n  }]);\n  return CacheMap;\n}();\nexport default CacheMap;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "CacheMap", "maps", "Object", "create", "key", "value", "set", "get"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-virtual-list/es/utils/CacheMap.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\n// Firefox has low performance of map.\nvar CacheMap = /*#__PURE__*/function () {\n  function CacheMap() {\n    _classCallCheck(this, CacheMap);\n    this.maps = void 0;\n    this.maps = Object.create(null);\n  }\n  _createClass(CacheMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      this.maps[key] = value;\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.maps[key];\n    }\n  }]);\n  return CacheMap;\n}();\nexport default CacheMap;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE;AACA,IAAIC,QAAQ,GAAG,aAAa,YAAY;EACtC,SAASA,QAAQA,CAAA,EAAG;IAClBF,eAAe,CAAC,IAAI,EAAEE,QAAQ,CAAC;IAC/B,IAAI,CAACC,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAACA,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACjC;EACAJ,YAAY,CAACC,QAAQ,EAAE,CAAC;IACtBI,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASC,GAAGA,CAACF,GAAG,EAAEC,KAAK,EAAE;MAC9B,IAAI,CAACJ,IAAI,CAACG,GAAG,CAAC,GAAGC,KAAK;IACxB;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASE,GAAGA,CAACH,GAAG,EAAE;MACvB,OAAO,IAAI,CAACH,IAAI,CAACG,GAAG,CAAC;IACvB;EACF,CAAC,CAAC,CAAC;EACH,OAAOJ,QAAQ;AACjB,CAAC,CAAC,CAAC;AACH,eAAeA,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}