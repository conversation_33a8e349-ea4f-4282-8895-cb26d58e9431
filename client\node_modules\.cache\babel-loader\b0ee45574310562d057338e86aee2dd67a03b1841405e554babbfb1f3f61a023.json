{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizStart.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Card, Button, Loading } from '../../../components/modern';\nimport { TbClock, TbQuestionMark, TbTrophy, TbAlertTriangle, TbPlay, TbArrowLeft, TbBrain } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizStart = () => {\n  _s();\n  var _examData$questions, _user$name, _user$name$charAt;\n  const [examData, setExamData] = useState(null);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({\n          examId: id\n        });\n        dispatch(HideLoading());\n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n  if (!examData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(Loading, {\n        fullScreen: true,\n        text: \"Loading quiz details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-6 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mb-6\",\n          children: /*#__PURE__*/_jsxDEV(TbBrain, {\n            className: \"w-8 h-8 text-primary-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"heading-2 text-gradient mb-4\",\n          children: examData.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600\",\n          children: \"Ready to challenge yourself? Let's test your knowledge!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-8 mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid md:grid-cols-2 gap-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"heading-3 mb-6 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                  className: \"w-6 h-6 text-primary-600 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this), \"Quiz Details\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.3\n                  },\n                  className: \"flex items-center justify-between p-4 bg-primary-50 rounded-lg border border-primary-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                      className: \"w-5 h-5 text-primary-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 97,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Questions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 98,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold text-primary-600\",\n                    children: ((_examData$questions = examData.questions) === null || _examData$questions === void 0 ? void 0 : _examData$questions.length) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.4\n                  },\n                  className: \"flex items-center justify-between p-4 bg-success-50 rounded-lg border border-success-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                      className: \"w-5 h-5 text-success-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 110,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Duration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 111,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold text-success-600\",\n                    children: [Math.floor(examData.duration / 60), \" minutes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.5\n                  },\n                  className: \"flex items-center justify-between p-4 bg-warning-50 rounded-lg border border-warning-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                      className: \"w-5 h-5 text-warning-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 123,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Total Marks\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold text-warning-600\",\n                    children: examData.totalMarks\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.6\n                  },\n                  className: \"flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                      className: \"w-5 h-5 text-blue-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-700\",\n                      children: \"Passing Marks\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold text-blue-600\",\n                    children: examData.passingMarks\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"heading-3 mb-6 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n                  className: \"w-6 h-6 text-warning-600 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), \"Instructions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4 text-gray-700\",\n                children: [\"Read each question carefully before answering\", \"You can navigate between questions using Previous/Next buttons\", \"Make sure to answer all questions before submitting\", \"Keep an eye on the timer - the quiz will auto-submit when time runs out\"].map((instruction, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: 0.3 + index * 0.1\n                  },\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-primary-600 font-bold text-sm\",\n                      children: index + 1\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"leading-relaxed\",\n                    children: instruction\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg p-6 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-600 font-bold text-lg\",\n              children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900\",\n              children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || 'Student']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: [\"Level: \", (user === null || user === void 0 ? void 0 : user.level) || 'Primary', \" \\u2022 Class: \", (user === null || user === void 0 ? void 0 : user.class) || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/user/quiz'),\n          className: \"px-8 py-4 bg-gray-100 text-gray-700 rounded-xl font-semibold hover:bg-gray-200 transition-colors duration-200 border-0 outline-none focus:outline-none\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleStartQuiz,\n          className: \"px-8 py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 shadow-lg border-0 outline-none focus:outline-none\",\n          children: \"Start Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizStart, \"f4+KHu+9zCjMDEp5I8yw6ulo0NM=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizStart;\nexport default QuizStart;\nvar _c;\n$RefreshReg$(_c, \"QuizStart\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useDispatch", "useSelector", "motion", "message", "getExamById", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Loading", "TbClock", "TbQuestionMark", "TbTrophy", "TbAlertTriangle", "TbPlay", "TbArrowLeft", "TbBrain", "jsxDEV", "_jsxDEV", "QuizStart", "_s", "_examData$questions", "_user$name", "_user$name$charAt", "examData", "setExamData", "id", "navigate", "dispatch", "user", "state", "fetchExamData", "response", "examId", "success", "data", "error", "handleStartQuiz", "className", "children", "fullScreen", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "name", "transition", "delay", "x", "questions", "length", "Math", "floor", "duration", "totalMarks", "passingMarks", "map", "instruction", "index", "char<PERSON>t", "toUpperCase", "level", "class", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizStart.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Card, Button, Loading } from '../../../components/modern';\nimport { TbClock, TbQuestionMark, TbTrophy, TbAlertTriangle, TbPlay, TbArrowLeft, TbBrain } from 'react-icons/tb';\n\nconst QuizStart = () => {\n  const [examData, setExamData] = useState(null);\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({ examId: id });\n        dispatch(HideLoading());\n        \n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n\n  if (!examData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\">\n        <Loading fullScreen text=\"Loading quiz details...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"max-w-4xl mx-auto px-6 py-12\">\n        {/* Modern Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center mb-12\"\n        >\n          <div className=\"inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mb-6\">\n            <TbBrain className=\"w-8 h-8 text-primary-600\" />\n          </div>\n          <h1 className=\"heading-2 text-gradient mb-4\">\n            {examData.name}\n          </h1>\n          <p className=\"text-xl text-gray-600\">\n            Ready to challenge yourself? Let's test your knowledge!\n          </p>\n        </motion.div>\n\n        {/* Modern Quiz Info Card */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <Card className=\"p-8 mb-8\">\n            <div className=\"grid md:grid-cols-2 gap-8\">\n              {/* Quiz Details */}\n              <div>\n                <h2 className=\"heading-3 mb-6 flex items-center\">\n                  <TbQuestionMark className=\"w-6 h-6 text-primary-600 mr-2\" />\n                  Quiz Details\n                </h2>\n                <div className=\"space-y-4\">\n                  <motion.div\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.3 }}\n                    className=\"flex items-center justify-between p-4 bg-primary-50 rounded-lg border border-primary-100\"\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <TbQuestionMark className=\"w-5 h-5 text-primary-600\" />\n                      <span className=\"font-medium text-gray-700\">Questions</span>\n                    </div>\n                    <span className=\"font-bold text-primary-600\">{examData.questions?.length || 0}</span>\n                  </motion.div>\n\n                  <motion.div\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.4 }}\n                    className=\"flex items-center justify-between p-4 bg-success-50 rounded-lg border border-success-100\"\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <TbClock className=\"w-5 h-5 text-success-600\" />\n                      <span className=\"font-medium text-gray-700\">Duration</span>\n                    </div>\n                    <span className=\"font-bold text-success-600\">{Math.floor(examData.duration / 60)} minutes</span>\n                  </motion.div>\n\n                  <motion.div\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.5 }}\n                    className=\"flex items-center justify-between p-4 bg-warning-50 rounded-lg border border-warning-100\"\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <TbTrophy className=\"w-5 h-5 text-warning-600\" />\n                      <span className=\"font-medium text-gray-700\">Total Marks</span>\n                    </div>\n                    <span className=\"font-bold text-warning-600\">{examData.totalMarks}</span>\n                  </motion.div>\n\n                  <motion.div\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.6 }}\n                    className=\"flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-100\"\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <TbTrophy className=\"w-5 h-5 text-blue-600\" />\n                      <span className=\"font-medium text-gray-700\">Passing Marks</span>\n                    </div>\n                    <span className=\"font-bold text-blue-600\">{examData.passingMarks}</span>\n                  </motion.div>\n                </div>\n              </div>\n\n              {/* Instructions */}\n              <div>\n                <h2 className=\"heading-3 mb-6 flex items-center\">\n                  <TbAlertTriangle className=\"w-6 h-6 text-warning-600 mr-2\" />\n                  Instructions\n                </h2>\n                <div className=\"space-y-4 text-gray-700\">\n                  {[\n                    \"Read each question carefully before answering\",\n                    \"You can navigate between questions using Previous/Next buttons\",\n                    \"Make sure to answer all questions before submitting\",\n                    \"Keep an eye on the timer - the quiz will auto-submit when time runs out\"\n                  ].map((instruction, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, x: 20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: 0.3 + index * 0.1 }}\n                      className=\"flex items-start space-x-3\"\n                    >\n                      <div className=\"w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\n                        <span className=\"text-primary-600 font-bold text-sm\">{index + 1}</span>\n                      </div>\n                      <p className=\"leading-relaxed\">{instruction}</p>\n                    </motion.div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n\n        {/* User Info */}\n        <div className=\"bg-white rounded-xl shadow-lg p-6 mb-8\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\n              <span className=\"text-blue-600 font-bold text-lg\">\n                {user?.name?.charAt(0)?.toUpperCase() || 'U'}\n              </span>\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Welcome, {user?.name || 'Student'}</h3>\n              <p className=\"text-gray-600\">Level: {user?.level || 'Primary'} • Class: {user?.class || 'N/A'}</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <button\n            onClick={() => navigate('/user/quiz')}\n            className=\"px-8 py-4 bg-gray-100 text-gray-700 rounded-xl font-semibold hover:bg-gray-200 transition-colors duration-200 border-0 outline-none focus:outline-none\"\n          >\n            Back to Quizzes\n          </button>\n          <button\n            onClick={handleStartQuiz}\n            className=\"px-8 py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 shadow-lg border-0 outline-none focus:outline-none\"\n          >\n            Start Quiz\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizStart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,4BAA4B;AAClE,SAASC,OAAO,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElH,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,UAAA,EAAAC,iBAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM;IAAE8B;EAAG,CAAC,GAAG5B,SAAS,CAAC,CAAC;EAC1B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B;EAAK,CAAC,GAAG5B,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnDhC,SAAS,CAAC,MAAM;IACd,MAAMkC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAM0B,QAAQ,GAAG,MAAM5B,WAAW,CAAC;UAAE6B,MAAM,EAAEP;QAAG,CAAC,CAAC;QAClDE,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAI2B,QAAQ,CAACE,OAAO,EAAE;UACpBT,WAAW,CAACO,QAAQ,CAACG,IAAI,CAAC;QAC5B,CAAC,MAAM;UACLhC,OAAO,CAACiC,KAAK,CAACJ,QAAQ,CAAC7B,OAAO,CAAC;UAC/BwB,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdR,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;QACvBF,OAAO,CAACiC,KAAK,CAACA,KAAK,CAACjC,OAAO,CAAC;QAC5BwB,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAED,IAAID,EAAE,EAAE;MACNK,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACL,EAAE,EAAEE,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE5B,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5BV,QAAQ,CAAE,SAAQD,EAAG,OAAM,CAAC;EAC9B,CAAC;EAED,IAAI,CAACF,QAAQ,EAAE;IACb,oBACEN,OAAA;MAAKoB,SAAS,EAAC,yFAAyF;MAAAC,QAAA,eACtGrB,OAAA,CAACT,OAAO;QAAC+B,UAAU;QAACC,IAAI,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEV;EAEA,oBACE3B,OAAA;IAAKoB,SAAS,EAAC,wDAAwD;IAAAC,QAAA,eACrErB,OAAA;MAAKoB,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAE3CrB,OAAA,CAAChB,MAAM,CAAC4C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BX,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BrB,OAAA;UAAKoB,SAAS,EAAC,oFAAoF;UAAAC,QAAA,eACjGrB,OAAA,CAACF,OAAO;YAACsB,SAAS,EAAC;UAA0B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACN3B,OAAA;UAAIoB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EACzCf,QAAQ,CAAC2B;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACL3B,OAAA;UAAGoB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb3B,OAAA,CAAChB,MAAM,CAAC4C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAd,QAAA,eAE3BrB,OAAA,CAACX,IAAI;UAAC+B,SAAS,EAAC,UAAU;UAAAC,QAAA,eACxBrB,OAAA;YAAKoB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBAExCrB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAIoB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC9CrB,OAAA,CAACP,cAAc;kBAAC2B,SAAS,EAAC;gBAA+B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE9D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3B,OAAA;gBAAKoB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBrB,OAAA,CAAChB,MAAM,CAAC4C,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCJ,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3Bf,SAAS,EAAC,0FAA0F;kBAAAC,QAAA,gBAEpGrB,OAAA;oBAAKoB,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CrB,OAAA,CAACP,cAAc;sBAAC2B,SAAS,EAAC;oBAA0B;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvD3B,OAAA;sBAAMoB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACN3B,OAAA;oBAAMoB,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAE,EAAAlB,mBAAA,GAAAG,QAAQ,CAAC+B,SAAS,cAAAlC,mBAAA,uBAAlBA,mBAAA,CAAoBmC,MAAM,KAAI;kBAAC;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eAEb3B,OAAA,CAAChB,MAAM,CAAC4C,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCJ,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3Bf,SAAS,EAAC,0FAA0F;kBAAAC,QAAA,gBAEpGrB,OAAA;oBAAKoB,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CrB,OAAA,CAACR,OAAO;sBAAC4B,SAAS,EAAC;oBAA0B;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChD3B,OAAA;sBAAMoB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACN3B,OAAA;oBAAMoB,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GAAEkB,IAAI,CAACC,KAAK,CAAClC,QAAQ,CAACmC,QAAQ,GAAG,EAAE,CAAC,EAAC,UAAQ;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC,eAEb3B,OAAA,CAAChB,MAAM,CAAC4C,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCJ,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3Bf,SAAS,EAAC,0FAA0F;kBAAAC,QAAA,gBAEpGrB,OAAA;oBAAKoB,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CrB,OAAA,CAACN,QAAQ;sBAAC0B,SAAS,EAAC;oBAA0B;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjD3B,OAAA;sBAAMoB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACN3B,OAAA;oBAAMoB,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEf,QAAQ,CAACoC;kBAAU;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC,eAEb3B,OAAA,CAAChB,MAAM,CAAC4C,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCJ,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3Bf,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,gBAE9FrB,OAAA;oBAAKoB,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CrB,OAAA,CAACN,QAAQ;sBAAC0B,SAAS,EAAC;oBAAuB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9C3B,OAAA;sBAAMoB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACN3B,OAAA;oBAAMoB,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAEf,QAAQ,CAACqC;kBAAY;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3B,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAIoB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC9CrB,OAAA,CAACL,eAAe;kBAACyB,SAAS,EAAC;gBAA+B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3B,OAAA;gBAAKoB,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACrC,CACC,+CAA+C,EAC/C,gEAAgE,EAChE,qDAAqD,EACrD,yEAAyE,CAC1E,CAACuB,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACvB9C,OAAA,CAAChB,MAAM,CAAC4C,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAG,CAAE;kBAC/BJ,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9BF,UAAU,EAAE;oBAAEC,KAAK,EAAE,GAAG,GAAGW,KAAK,GAAG;kBAAI,CAAE;kBACzC1B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAEtCrB,OAAA;oBAAKoB,SAAS,EAAC,2FAA2F;oBAAAC,QAAA,eACxGrB,OAAA;sBAAMoB,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAEyB,KAAK,GAAG;oBAAC;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eACN3B,OAAA;oBAAGoB,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAEwB;kBAAW;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA,GAT3CmB,KAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUA,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGb3B,OAAA;QAAKoB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDrB,OAAA;UAAKoB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CrB,OAAA;YAAKoB,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClFrB,OAAA;cAAMoB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC9C,CAAAV,IAAI,aAAJA,IAAI,wBAAAP,UAAA,GAAJO,IAAI,CAAEsB,IAAI,cAAA7B,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAY2C,MAAM,CAAC,CAAC,CAAC,cAAA1C,iBAAA,uBAArBA,iBAAA,CAAuB2C,WAAW,CAAC,CAAC,KAAI;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN3B,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAIoB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GAAC,WAAS,EAAC,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,IAAI,KAAI,SAAS;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnF3B,OAAA;cAAGoB,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,SAAO,EAAC,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,KAAK,KAAI,SAAS,EAAC,iBAAU,EAAC,CAAAtC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuC,KAAK,KAAI,KAAK;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAKoB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DrB,OAAA;UACEmD,OAAO,EAAEA,CAAA,KAAM1C,QAAQ,CAAC,YAAY,CAAE;UACtCW,SAAS,EAAC,wJAAwJ;UAAAC,QAAA,EACnK;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3B,OAAA;UACEmD,OAAO,EAAEhC,eAAgB;UACzBC,SAAS,EAAC,+JAA+J;UAAAC,QAAA,EAC1K;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAtMID,SAAS;EAAA,QAEErB,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAAqE,EAAA,GALxBnD,SAAS;AAwMf,eAAeA,SAAS;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}