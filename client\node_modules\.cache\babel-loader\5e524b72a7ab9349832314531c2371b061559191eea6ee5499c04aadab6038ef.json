{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertChildrenToData } from \"../utils/legacyUtil\";\nfunction parseSimpleTreeData(treeData, _ref) {\n  var id = _ref.id,\n    pId = _ref.pId,\n    rootPId = _ref.rootPId;\n  var keyNodes = {};\n  var rootNodeList = [];\n\n  // Fill in the map\n  var nodeList = treeData.map(function (node) {\n    var clone = _objectSpread({}, node);\n    var key = clone[id];\n    keyNodes[key] = clone;\n    clone.key = clone.key || key;\n    return clone;\n  });\n\n  // Connect tree\n  nodeList.forEach(function (node) {\n    var parentKey = node[pId];\n    var parent = keyNodes[parentKey];\n\n    // Fill parent\n    if (parent) {\n      parent.children = parent.children || [];\n      parent.children.push(node);\n    }\n\n    // Fill root tree node\n    if (parentKey === rootPId || !parent && rootPId === null) {\n      rootNodeList.push(node);\n    }\n  });\n  return rootNodeList;\n}\n\n/**\n * Convert `treeData` or `children` into formatted `treeData`.\n * Will not re-calculate if `treeData` or `children` not change.\n */\nexport default function useTreeData(treeData, children, simpleMode) {\n  return React.useMemo(function () {\n    if (treeData) {\n      return simpleMode ? parseSimpleTreeData(treeData, _objectSpread({\n        id: 'id',\n        pId: 'pId',\n        rootPId: null\n      }, simpleMode !== true ? simpleMode : {})) : treeData;\n    }\n    return convertChildrenToData(children);\n  }, [children, simpleMode, treeData]);\n}", "map": {"version": 3, "names": ["_objectSpread", "React", "convertChildrenToData", "parseSimpleTreeData", "treeData", "_ref", "id", "pId", "rootPId", "keyNodes", "rootNodeList", "nodeList", "map", "node", "clone", "key", "for<PERSON>ach", "parent<PERSON><PERSON>", "parent", "children", "push", "useTreeData", "simpleMode", "useMemo"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tree-select/es/hooks/useTreeData.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertChildrenToData } from \"../utils/legacyUtil\";\nfunction parseSimpleTreeData(treeData, _ref) {\n  var id = _ref.id,\n    pId = _ref.pId,\n    rootPId = _ref.rootPId;\n  var keyNodes = {};\n  var rootNodeList = [];\n\n  // Fill in the map\n  var nodeList = treeData.map(function (node) {\n    var clone = _objectSpread({}, node);\n    var key = clone[id];\n    keyNodes[key] = clone;\n    clone.key = clone.key || key;\n    return clone;\n  });\n\n  // Connect tree\n  nodeList.forEach(function (node) {\n    var parentKey = node[pId];\n    var parent = keyNodes[parentKey];\n\n    // Fill parent\n    if (parent) {\n      parent.children = parent.children || [];\n      parent.children.push(node);\n    }\n\n    // Fill root tree node\n    if (parentKey === rootPId || !parent && rootPId === null) {\n      rootNodeList.push(node);\n    }\n  });\n  return rootNodeList;\n}\n\n/**\n * Convert `treeData` or `children` into formatted `treeData`.\n * Will not re-calculate if `treeData` or `children` not change.\n */\nexport default function useTreeData(treeData, children, simpleMode) {\n  return React.useMemo(function () {\n    if (treeData) {\n      return simpleMode ? parseSimpleTreeData(treeData, _objectSpread({\n        id: 'id',\n        pId: 'pId',\n        rootPId: null\n      }, simpleMode !== true ? simpleMode : {})) : treeData;\n    }\n    return convertChildrenToData(children);\n  }, [children, simpleMode, treeData]);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,qBAAqB;AAC3D,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EAC3C,IAAIC,EAAE,GAAGD,IAAI,CAACC,EAAE;IACdC,GAAG,GAAGF,IAAI,CAACE,GAAG;IACdC,OAAO,GAAGH,IAAI,CAACG,OAAO;EACxB,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIC,YAAY,GAAG,EAAE;;EAErB;EACA,IAAIC,QAAQ,GAAGP,QAAQ,CAACQ,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC1C,IAAIC,KAAK,GAAGd,aAAa,CAAC,CAAC,CAAC,EAAEa,IAAI,CAAC;IACnC,IAAIE,GAAG,GAAGD,KAAK,CAACR,EAAE,CAAC;IACnBG,QAAQ,CAACM,GAAG,CAAC,GAAGD,KAAK;IACrBA,KAAK,CAACC,GAAG,GAAGD,KAAK,CAACC,GAAG,IAAIA,GAAG;IAC5B,OAAOD,KAAK;EACd,CAAC,CAAC;;EAEF;EACAH,QAAQ,CAACK,OAAO,CAAC,UAAUH,IAAI,EAAE;IAC/B,IAAII,SAAS,GAAGJ,IAAI,CAACN,GAAG,CAAC;IACzB,IAAIW,MAAM,GAAGT,QAAQ,CAACQ,SAAS,CAAC;;IAEhC;IACA,IAAIC,MAAM,EAAE;MACVA,MAAM,CAACC,QAAQ,GAAGD,MAAM,CAACC,QAAQ,IAAI,EAAE;MACvCD,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACP,IAAI,CAAC;IAC5B;;IAEA;IACA,IAAII,SAAS,KAAKT,OAAO,IAAI,CAACU,MAAM,IAAIV,OAAO,KAAK,IAAI,EAAE;MACxDE,YAAY,CAACU,IAAI,CAACP,IAAI,CAAC;IACzB;EACF,CAAC,CAAC;EACF,OAAOH,YAAY;AACrB;;AAEA;AACA;AACA;AACA;AACA,eAAe,SAASW,WAAWA,CAACjB,QAAQ,EAAEe,QAAQ,EAAEG,UAAU,EAAE;EAClE,OAAOrB,KAAK,CAACsB,OAAO,CAAC,YAAY;IAC/B,IAAInB,QAAQ,EAAE;MACZ,OAAOkB,UAAU,GAAGnB,mBAAmB,CAACC,QAAQ,EAAEJ,aAAa,CAAC;QAC9DM,EAAE,EAAE,IAAI;QACRC,GAAG,EAAE,KAAK;QACVC,OAAO,EAAE;MACX,CAAC,EAAEc,UAAU,KAAK,IAAI,GAAGA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGlB,QAAQ;IACvD;IACA,OAAOF,qBAAqB,CAACiB,QAAQ,CAAC;EACxC,CAAC,EAAE,CAACA,QAAQ,EAAEG,UAAU,EAAElB,QAAQ,CAAC,CAAC;AACtC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}