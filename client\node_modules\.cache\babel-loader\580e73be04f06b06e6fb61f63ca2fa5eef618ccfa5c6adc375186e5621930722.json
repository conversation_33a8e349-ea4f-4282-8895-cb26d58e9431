{"ast": null, "code": "import getMiniDecimal from \"./MiniDecimal\";\nexport * from \"./MiniDecimal\";\nimport { trimNumber, getNumberPrecision, num2str, validateNumber } from \"./numberUtil\";\nexport { trimNumber, getNumberPrecision, num2str, validateNumber };\nexport default getMiniDecimal;", "map": {"version": 3, "names": ["getMiniDecimal", "trimNumber", "getNumberPrecision", "num2str", "validateNumber"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/mini-decimal/es/index.js"], "sourcesContent": ["import getMiniDecimal from \"./MiniDecimal\";\nexport * from \"./MiniDecimal\";\nimport { trimNumber, getNumberPrecision, num2str, validateNumber } from \"./numberUtil\";\nexport { trimNumber, getNumberPrecision, num2str, validateNumber };\nexport default getMiniDecimal;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,eAAe;AAC1C,cAAc,eAAe;AAC7B,SAASC,UAAU,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,cAAc,QAAQ,cAAc;AACtF,SAASH,UAAU,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,cAAc;AAChE,eAAeJ,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}