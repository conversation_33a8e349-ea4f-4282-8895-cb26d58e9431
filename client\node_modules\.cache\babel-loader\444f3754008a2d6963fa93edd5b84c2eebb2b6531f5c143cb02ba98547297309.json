{"ast": null, "code": "import * as React from 'react';\n/** Similar with `useEffect` but only trigger after mounted */\nconst useUpdatedEffect = (callback, conditions) => {\n  const mountRef = React.useRef(false);\n  React.useEffect(() => {\n    if (mountRef.current) {\n      callback();\n    } else {\n      mountRef.current = true;\n    }\n  }, conditions);\n};\nexport default useUpdatedEffect;", "map": {"version": 3, "names": ["React", "useUpdatedEffect", "callback", "conditions", "mountRef", "useRef", "useEffect", "current"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/typography/hooks/useUpdatedEffect.js"], "sourcesContent": ["import * as React from 'react';\n/** Similar with `useEffect` but only trigger after mounted */\nconst useUpdatedEffect = (callback, conditions) => {\n  const mountRef = React.useRef(false);\n  React.useEffect(() => {\n    if (mountRef.current) {\n      callback();\n    } else {\n      mountRef.current = true;\n    }\n  }, conditions);\n};\nexport default useUpdatedEffect;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA,MAAMC,gBAAgB,GAAGA,CAACC,QAAQ,EAAEC,UAAU,KAAK;EACjD,MAAMC,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAAC,KAAK,CAAC;EACpCL,KAAK,CAACM,SAAS,CAAC,MAAM;IACpB,IAAIF,QAAQ,CAACG,OAAO,EAAE;MACpBL,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACLE,QAAQ,CAACG,OAAO,GAAG,IAAI;IACzB;EACF,CAAC,EAAEJ,UAAU,CAAC;AAChB,CAAC;AACD,eAAeF,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}