{"ast": null, "code": "/*\n.typography-title(@fontSize; @fontWeight; @lineHeight; @headingColor; @headingMarginBottom;) {\n  margin-bottom: @headingMarginBottom;\n  color: @headingColor;\n  font-weight: @fontWeight;\n  fontSize: @fontSize;\n  line-height: @lineHeight;\n}\n*/\nimport { gold } from '@ant-design/colors';\nimport { initInputToken } from '../../input/style';\nimport { operationUnit } from '../../style';\n// eslint-disable-next-line import/prefer-default-export\nconst getTitleStyle = (fontSize, lineHeight, color, token) => {\n  const {\n    titleMarginBottom,\n    fontWeightStrong\n  } = token;\n  return {\n    marginBottom: titleMarginBottom,\n    color,\n    fontWeight: fontWeightStrong,\n    fontSize,\n    lineHeight\n  };\n};\n// eslint-disable-next-line import/prefer-default-export\nexport const getTitleStyles = token => {\n  const headings = [1, 2, 3, 4, 5];\n  const styles = {};\n  headings.forEach(headingLevel => {\n    styles[`\n      h${headingLevel}&,\n      div&-h${headingLevel},\n      div&-h${headingLevel} > textarea,\n      h${headingLevel}\n    `] = getTitleStyle(token[`fontSizeHeading${headingLevel}`], token[`lineHeightHeading${headingLevel}`], token.colorTextHeading, token);\n  });\n  return styles;\n};\nexport const getLinkStyles = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    'a&, a': Object.assign(Object.assign({}, operationUnit(token)), {\n      textDecoration: token.linkDecoration,\n      '&:active, &:hover': {\n        textDecoration: token.linkHoverDecoration\n      },\n      [`&[disabled], &${componentCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed',\n        '&:active, &:hover': {\n          color: token.colorTextDisabled\n        },\n        '&:active': {\n          pointerEvents: 'none'\n        }\n      }\n    })\n  };\n};\nexport const getResetStyles = token => ({\n  code: {\n    margin: '0 0.2em',\n    paddingInline: '0.4em',\n    paddingBlock: '0.2em 0.1em',\n    fontSize: '85%',\n    fontFamily: token.fontFamilyCode,\n    background: 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderRadius: 3\n  },\n  kbd: {\n    margin: '0 0.2em',\n    paddingInline: '0.4em',\n    paddingBlock: '0.15em 0.1em',\n    fontSize: '90%',\n    fontFamily: token.fontFamilyCode,\n    background: 'rgba(150, 150, 150, 0.06)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderBottomWidth: 2,\n    borderRadius: 3\n  },\n  mark: {\n    padding: 0,\n    // FIXME hardcode in v4\n    backgroundColor: gold[2]\n  },\n  'u, ins': {\n    textDecoration: 'underline',\n    textDecorationSkipInk: 'auto'\n  },\n  's, del': {\n    textDecoration: 'line-through'\n  },\n  strong: {\n    fontWeight: 600\n  },\n  // list\n  'ul, ol': {\n    marginInline: 0,\n    marginBlock: '0 1em',\n    padding: 0,\n    li: {\n      marginInline: '20px 0',\n      marginBlock: 0,\n      paddingInline: '4px 0',\n      paddingBlock: 0\n    }\n  },\n  ul: {\n    listStyleType: 'circle',\n    ul: {\n      listStyleType: 'disc'\n    }\n  },\n  ol: {\n    listStyleType: 'decimal'\n  },\n  // pre & block\n  'pre, blockquote': {\n    margin: '1em 0'\n  },\n  pre: {\n    padding: '0.4em 0.6em',\n    whiteSpace: 'pre-wrap',\n    wordWrap: 'break-word',\n    background: 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderRadius: 3,\n    fontFamily: token.fontFamilyCode,\n    // Compatible for marked\n    code: {\n      display: 'inline',\n      margin: 0,\n      padding: 0,\n      fontSize: 'inherit',\n      fontFamily: 'inherit',\n      background: 'transparent',\n      border: 0\n    }\n  },\n  blockquote: {\n    paddingInline: '0.6em 0',\n    paddingBlock: 0,\n    borderInlineStart: '4px solid rgba(100, 100, 100, 0.2)',\n    opacity: 0.85\n  }\n});\nexport const getEditableStyles = token => {\n  const {\n    componentCls\n  } = token;\n  const inputToken = initInputToken(token);\n  const inputShift = inputToken.inputPaddingVertical + 1;\n  return {\n    '&-edit-content': {\n      position: 'relative',\n      'div&': {\n        insetInlineStart: -token.paddingSM,\n        marginTop: -inputShift,\n        marginBottom: `calc(1em - ${inputShift}px)`\n      },\n      [`${componentCls}-edit-content-confirm`]: {\n        position: 'absolute',\n        insetInlineEnd: token.marginXS + 2,\n        insetBlockEnd: token.marginXS,\n        color: token.colorTextDescription,\n        // default style\n        fontWeight: 'normal',\n        fontSize: token.fontSize,\n        fontStyle: 'normal',\n        pointerEvents: 'none'\n      },\n      textarea: {\n        margin: '0!important',\n        // Fix Editable Textarea flash in Firefox\n        MozTransition: 'none',\n        height: '1em'\n      }\n    }\n  };\n};\nexport const getCopyableStyles = token => ({\n  '&-copy-success': {\n    [`\n    &,\n    &:hover,\n    &:focus`]: {\n      color: token.colorSuccess\n    }\n  }\n});\nexport const getEllipsisStyles = () => ({\n  [`\n  a&-ellipsis,\n  span&-ellipsis\n  `]: {\n    display: 'inline-block',\n    maxWidth: '100%'\n  },\n  '&-single-line': {\n    whiteSpace: 'nowrap'\n  },\n  '&-ellipsis-single-line': {\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    // https://blog.csdn.net/iefreer/article/details/50421025\n    'a&, span&': {\n      verticalAlign: 'bottom'\n    }\n  },\n  '&-ellipsis-multiple-line': {\n    display: '-webkit-box',\n    overflow: 'hidden',\n    WebkitLineClamp: 3,\n    WebkitBoxOrient: 'vertical'\n  }\n});", "map": {"version": 3, "names": ["gold", "initInputToken", "operationUnit", "getTitleStyle", "fontSize", "lineHeight", "color", "token", "titleMarginBottom", "fontWeightStrong", "marginBottom", "fontWeight", "getTitleStyles", "headings", "styles", "for<PERSON>ach", "headingLevel", "colorTextHeading", "getLinkStyles", "componentCls", "Object", "assign", "textDecoration", "linkDecoration", "linkHoverDecoration", "colorTextDisabled", "cursor", "pointerEvents", "getResetStyles", "code", "margin", "paddingInline", "paddingBlock", "fontFamily", "fontFamilyCode", "background", "border", "borderRadius", "kbd", "borderBottomWidth", "mark", "padding", "backgroundColor", "textDecorationSkipInk", "strong", "marginInline", "marginBlock", "li", "ul", "listStyleType", "ol", "pre", "whiteSpace", "wordWrap", "display", "blockquote", "borderInlineStart", "opacity", "getEditableStyles", "inputToken", "inputShift", "inputPaddingVertical", "position", "insetInlineStart", "paddingSM", "marginTop", "insetInlineEnd", "marginXS", "insetBlockEnd", "colorTextDescription", "fontStyle", "textarea", "MozTransition", "height", "getCopyableStyles", "colorSuccess", "getEllipsisStyles", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "verticalAlign", "WebkitLineClamp", "WebkitBoxOrient"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/typography/style/mixins.js"], "sourcesContent": ["/*\n.typography-title(@fontSize; @fontWeight; @lineHeight; @headingColor; @headingMarginBottom;) {\n  margin-bottom: @headingMarginBottom;\n  color: @headingColor;\n  font-weight: @fontWeight;\n  fontSize: @fontSize;\n  line-height: @lineHeight;\n}\n*/\nimport { gold } from '@ant-design/colors';\nimport { initInputToken } from '../../input/style';\nimport { operationUnit } from '../../style';\n// eslint-disable-next-line import/prefer-default-export\nconst getTitleStyle = (fontSize, lineHeight, color, token) => {\n  const {\n    titleMarginBottom,\n    fontWeightStrong\n  } = token;\n  return {\n    marginBottom: titleMarginBottom,\n    color,\n    fontWeight: fontWeightStrong,\n    fontSize,\n    lineHeight\n  };\n};\n// eslint-disable-next-line import/prefer-default-export\nexport const getTitleStyles = token => {\n  const headings = [1, 2, 3, 4, 5];\n  const styles = {};\n  headings.forEach(headingLevel => {\n    styles[`\n      h${headingLevel}&,\n      div&-h${headingLevel},\n      div&-h${headingLevel} > textarea,\n      h${headingLevel}\n    `] = getTitleStyle(token[`fontSizeHeading${headingLevel}`], token[`lineHeightHeading${headingLevel}`], token.colorTextHeading, token);\n  });\n  return styles;\n};\nexport const getLinkStyles = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    'a&, a': Object.assign(Object.assign({}, operationUnit(token)), {\n      textDecoration: token.linkDecoration,\n      '&:active, &:hover': {\n        textDecoration: token.linkHoverDecoration\n      },\n      [`&[disabled], &${componentCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed',\n        '&:active, &:hover': {\n          color: token.colorTextDisabled\n        },\n        '&:active': {\n          pointerEvents: 'none'\n        }\n      }\n    })\n  };\n};\nexport const getResetStyles = token => ({\n  code: {\n    margin: '0 0.2em',\n    paddingInline: '0.4em',\n    paddingBlock: '0.2em 0.1em',\n    fontSize: '85%',\n    fontFamily: token.fontFamilyCode,\n    background: 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderRadius: 3\n  },\n  kbd: {\n    margin: '0 0.2em',\n    paddingInline: '0.4em',\n    paddingBlock: '0.15em 0.1em',\n    fontSize: '90%',\n    fontFamily: token.fontFamilyCode,\n    background: 'rgba(150, 150, 150, 0.06)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderBottomWidth: 2,\n    borderRadius: 3\n  },\n  mark: {\n    padding: 0,\n    // FIXME hardcode in v4\n    backgroundColor: gold[2]\n  },\n  'u, ins': {\n    textDecoration: 'underline',\n    textDecorationSkipInk: 'auto'\n  },\n  's, del': {\n    textDecoration: 'line-through'\n  },\n  strong: {\n    fontWeight: 600\n  },\n  // list\n  'ul, ol': {\n    marginInline: 0,\n    marginBlock: '0 1em',\n    padding: 0,\n    li: {\n      marginInline: '20px 0',\n      marginBlock: 0,\n      paddingInline: '4px 0',\n      paddingBlock: 0\n    }\n  },\n  ul: {\n    listStyleType: 'circle',\n    ul: {\n      listStyleType: 'disc'\n    }\n  },\n  ol: {\n    listStyleType: 'decimal'\n  },\n  // pre & block\n  'pre, blockquote': {\n    margin: '1em 0'\n  },\n  pre: {\n    padding: '0.4em 0.6em',\n    whiteSpace: 'pre-wrap',\n    wordWrap: 'break-word',\n    background: 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderRadius: 3,\n    fontFamily: token.fontFamilyCode,\n    // Compatible for marked\n    code: {\n      display: 'inline',\n      margin: 0,\n      padding: 0,\n      fontSize: 'inherit',\n      fontFamily: 'inherit',\n      background: 'transparent',\n      border: 0\n    }\n  },\n  blockquote: {\n    paddingInline: '0.6em 0',\n    paddingBlock: 0,\n    borderInlineStart: '4px solid rgba(100, 100, 100, 0.2)',\n    opacity: 0.85\n  }\n});\nexport const getEditableStyles = token => {\n  const {\n    componentCls\n  } = token;\n  const inputToken = initInputToken(token);\n  const inputShift = inputToken.inputPaddingVertical + 1;\n  return {\n    '&-edit-content': {\n      position: 'relative',\n      'div&': {\n        insetInlineStart: -token.paddingSM,\n        marginTop: -inputShift,\n        marginBottom: `calc(1em - ${inputShift}px)`\n      },\n      [`${componentCls}-edit-content-confirm`]: {\n        position: 'absolute',\n        insetInlineEnd: token.marginXS + 2,\n        insetBlockEnd: token.marginXS,\n        color: token.colorTextDescription,\n        // default style\n        fontWeight: 'normal',\n        fontSize: token.fontSize,\n        fontStyle: 'normal',\n        pointerEvents: 'none'\n      },\n      textarea: {\n        margin: '0!important',\n        // Fix Editable Textarea flash in Firefox\n        MozTransition: 'none',\n        height: '1em'\n      }\n    }\n  };\n};\nexport const getCopyableStyles = token => ({\n  '&-copy-success': {\n    [`\n    &,\n    &:hover,\n    &:focus`]: {\n      color: token.colorSuccess\n    }\n  }\n});\nexport const getEllipsisStyles = () => ({\n  [`\n  a&-ellipsis,\n  span&-ellipsis\n  `]: {\n    display: 'inline-block',\n    maxWidth: '100%'\n  },\n  '&-single-line': {\n    whiteSpace: 'nowrap'\n  },\n  '&-ellipsis-single-line': {\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    // https://blog.csdn.net/iefreer/article/details/50421025\n    'a&, span&': {\n      verticalAlign: 'bottom'\n    }\n  },\n  '&-ellipsis-multiple-line': {\n    display: '-webkit-box',\n    overflow: 'hidden',\n    WebkitLineClamp: 3,\n    WebkitBoxOrient: 'vertical'\n  }\n});"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,QAAQ,oBAAoB;AACzC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,aAAa,QAAQ,aAAa;AAC3C;AACA,MAAMC,aAAa,GAAGA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,KAAK,KAAK;EAC5D,MAAM;IACJC,iBAAiB;IACjBC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACLG,YAAY,EAAEF,iBAAiB;IAC/BF,KAAK;IACLK,UAAU,EAAEF,gBAAgB;IAC5BL,QAAQ;IACRC;EACF,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMO,cAAc,GAAGL,KAAK,IAAI;EACrC,MAAMM,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChC,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBD,QAAQ,CAACE,OAAO,CAACC,YAAY,IAAI;IAC/BF,MAAM,CAAE;AACZ,SAASE,YAAa;AACtB,cAAcA,YAAa;AAC3B,cAAcA,YAAa;AAC3B,SAASA,YAAa;AACtB,KAAK,CAAC,GAAGb,aAAa,CAACI,KAAK,CAAE,kBAAiBS,YAAa,EAAC,CAAC,EAAET,KAAK,CAAE,oBAAmBS,YAAa,EAAC,CAAC,EAAET,KAAK,CAACU,gBAAgB,EAAEV,KAAK,CAAC;EACvI,CAAC,CAAC;EACF,OAAOO,MAAM;AACf,CAAC;AACD,OAAO,MAAMI,aAAa,GAAGX,KAAK,IAAI;EACpC,MAAM;IACJY;EACF,CAAC,GAAGZ,KAAK;EACT,OAAO;IACL,OAAO,EAAEa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnB,aAAa,CAACK,KAAK,CAAC,CAAC,EAAE;MAC9De,cAAc,EAAEf,KAAK,CAACgB,cAAc;MACpC,mBAAmB,EAAE;QACnBD,cAAc,EAAEf,KAAK,CAACiB;MACxB,CAAC;MACD,CAAE,iBAAgBL,YAAa,WAAU,GAAG;QAC1Cb,KAAK,EAAEC,KAAK,CAACkB,iBAAiB;QAC9BC,MAAM,EAAE,aAAa;QACrB,mBAAmB,EAAE;UACnBpB,KAAK,EAAEC,KAAK,CAACkB;QACf,CAAC;QACD,UAAU,EAAE;UACVE,aAAa,EAAE;QACjB;MACF;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,OAAO,MAAMC,cAAc,GAAGrB,KAAK,KAAK;EACtCsB,IAAI,EAAE;IACJC,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,OAAO;IACtBC,YAAY,EAAE,aAAa;IAC3B5B,QAAQ,EAAE,KAAK;IACf6B,UAAU,EAAE1B,KAAK,CAAC2B,cAAc;IAChCC,UAAU,EAAE,0BAA0B;IACtCC,MAAM,EAAE,oCAAoC;IAC5CC,YAAY,EAAE;EAChB,CAAC;EACDC,GAAG,EAAE;IACHR,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,OAAO;IACtBC,YAAY,EAAE,cAAc;IAC5B5B,QAAQ,EAAE,KAAK;IACf6B,UAAU,EAAE1B,KAAK,CAAC2B,cAAc;IAChCC,UAAU,EAAE,2BAA2B;IACvCC,MAAM,EAAE,oCAAoC;IAC5CG,iBAAiB,EAAE,CAAC;IACpBF,YAAY,EAAE;EAChB,CAAC;EACDG,IAAI,EAAE;IACJC,OAAO,EAAE,CAAC;IACV;IACAC,eAAe,EAAE1C,IAAI,CAAC,CAAC;EACzB,CAAC;EACD,QAAQ,EAAE;IACRsB,cAAc,EAAE,WAAW;IAC3BqB,qBAAqB,EAAE;EACzB,CAAC;EACD,QAAQ,EAAE;IACRrB,cAAc,EAAE;EAClB,CAAC;EACDsB,MAAM,EAAE;IACNjC,UAAU,EAAE;EACd,CAAC;EACD;EACA,QAAQ,EAAE;IACRkC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,OAAO;IACpBL,OAAO,EAAE,CAAC;IACVM,EAAE,EAAE;MACFF,YAAY,EAAE,QAAQ;MACtBC,WAAW,EAAE,CAAC;MACdf,aAAa,EAAE,OAAO;MACtBC,YAAY,EAAE;IAChB;EACF,CAAC;EACDgB,EAAE,EAAE;IACFC,aAAa,EAAE,QAAQ;IACvBD,EAAE,EAAE;MACFC,aAAa,EAAE;IACjB;EACF,CAAC;EACDC,EAAE,EAAE;IACFD,aAAa,EAAE;EACjB,CAAC;EACD;EACA,iBAAiB,EAAE;IACjBnB,MAAM,EAAE;EACV,CAAC;EACDqB,GAAG,EAAE;IACHV,OAAO,EAAE,aAAa;IACtBW,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,YAAY;IACtBlB,UAAU,EAAE,0BAA0B;IACtCC,MAAM,EAAE,oCAAoC;IAC5CC,YAAY,EAAE,CAAC;IACfJ,UAAU,EAAE1B,KAAK,CAAC2B,cAAc;IAChC;IACAL,IAAI,EAAE;MACJyB,OAAO,EAAE,QAAQ;MACjBxB,MAAM,EAAE,CAAC;MACTW,OAAO,EAAE,CAAC;MACVrC,QAAQ,EAAE,SAAS;MACnB6B,UAAU,EAAE,SAAS;MACrBE,UAAU,EAAE,aAAa;MACzBC,MAAM,EAAE;IACV;EACF,CAAC;EACDmB,UAAU,EAAE;IACVxB,aAAa,EAAE,SAAS;IACxBC,YAAY,EAAE,CAAC;IACfwB,iBAAiB,EAAE,oCAAoC;IACvDC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,OAAO,MAAMC,iBAAiB,GAAGnD,KAAK,IAAI;EACxC,MAAM;IACJY;EACF,CAAC,GAAGZ,KAAK;EACT,MAAMoD,UAAU,GAAG1D,cAAc,CAACM,KAAK,CAAC;EACxC,MAAMqD,UAAU,GAAGD,UAAU,CAACE,oBAAoB,GAAG,CAAC;EACtD,OAAO;IACL,gBAAgB,EAAE;MAChBC,QAAQ,EAAE,UAAU;MACpB,MAAM,EAAE;QACNC,gBAAgB,EAAE,CAACxD,KAAK,CAACyD,SAAS;QAClCC,SAAS,EAAE,CAACL,UAAU;QACtBlD,YAAY,EAAG,cAAakD,UAAW;MACzC,CAAC;MACD,CAAE,GAAEzC,YAAa,uBAAsB,GAAG;QACxC2C,QAAQ,EAAE,UAAU;QACpBI,cAAc,EAAE3D,KAAK,CAAC4D,QAAQ,GAAG,CAAC;QAClCC,aAAa,EAAE7D,KAAK,CAAC4D,QAAQ;QAC7B7D,KAAK,EAAEC,KAAK,CAAC8D,oBAAoB;QACjC;QACA1D,UAAU,EAAE,QAAQ;QACpBP,QAAQ,EAAEG,KAAK,CAACH,QAAQ;QACxBkE,SAAS,EAAE,QAAQ;QACnB3C,aAAa,EAAE;MACjB,CAAC;MACD4C,QAAQ,EAAE;QACRzC,MAAM,EAAE,aAAa;QACrB;QACA0C,aAAa,EAAE,MAAM;QACrBC,MAAM,EAAE;MACV;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMC,iBAAiB,GAAGnE,KAAK,KAAK;EACzC,gBAAgB,EAAE;IAChB,CAAE;AACN;AACA;AACA,YAAY,GAAG;MACTD,KAAK,EAAEC,KAAK,CAACoE;IACf;EACF;AACF,CAAC,CAAC;AACF,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,MAAO;EACtC,CAAE;AACJ;AACA;AACA,GAAG,GAAG;IACFtB,OAAO,EAAE,cAAc;IACvBuB,QAAQ,EAAE;EACZ,CAAC;EACD,eAAe,EAAE;IACfzB,UAAU,EAAE;EACd,CAAC;EACD,wBAAwB,EAAE;IACxB0B,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,UAAU;IACxB;IACA,WAAW,EAAE;MACXC,aAAa,EAAE;IACjB;EACF,CAAC;EACD,0BAA0B,EAAE;IAC1B1B,OAAO,EAAE,aAAa;IACtBwB,QAAQ,EAAE,QAAQ;IAClBG,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}