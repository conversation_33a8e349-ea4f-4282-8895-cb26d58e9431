{"ast": null, "code": "export function getColumnKey(column, defaultKey) {\n  if ('key' in column && column.key !== undefined && column.key !== null) {\n    return column.key;\n  }\n  if (column.dataIndex) {\n    return Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex;\n  }\n  return defaultKey;\n}\nexport function getColumnPos(index, pos) {\n  return pos ? `${pos}-${index}` : `${index}`;\n}\nexport function renderColumnTitle(title, props) {\n  if (typeof title === 'function') {\n    return title(props);\n  }\n  return title;\n}\n/**\n * Safe get column title\n *\n * Should filter [object Object]\n *\n * @param title\n * @returns\n */\nexport function safeColumnTitle(title, props) {\n  const res = renderColumnTitle(title, props);\n  if (Object.prototype.toString.call(res) === '[object Object]') return '';\n  return res;\n}", "map": {"version": 3, "names": ["getColumnKey", "column", "defaultKey", "key", "undefined", "dataIndex", "Array", "isArray", "join", "getColumnPos", "index", "pos", "renderColumnTitle", "title", "props", "safeColumnTitle", "res", "Object", "prototype", "toString", "call"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/util.js"], "sourcesContent": ["export function getColumnKey(column, defaultKey) {\n  if ('key' in column && column.key !== undefined && column.key !== null) {\n    return column.key;\n  }\n  if (column.dataIndex) {\n    return Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex;\n  }\n  return defaultKey;\n}\nexport function getColumnPos(index, pos) {\n  return pos ? `${pos}-${index}` : `${index}`;\n}\nexport function renderColumnTitle(title, props) {\n  if (typeof title === 'function') {\n    return title(props);\n  }\n  return title;\n}\n/**\n * Safe get column title\n *\n * Should filter [object Object]\n *\n * @param title\n * @returns\n */\nexport function safeColumnTitle(title, props) {\n  const res = renderColumnTitle(title, props);\n  if (Object.prototype.toString.call(res) === '[object Object]') return '';\n  return res;\n}"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,MAAM,EAAEC,UAAU,EAAE;EAC/C,IAAI,KAAK,IAAID,MAAM,IAAIA,MAAM,CAACE,GAAG,KAAKC,SAAS,IAAIH,MAAM,CAACE,GAAG,KAAK,IAAI,EAAE;IACtE,OAAOF,MAAM,CAACE,GAAG;EACnB;EACA,IAAIF,MAAM,CAACI,SAAS,EAAE;IACpB,OAAOC,KAAK,CAACC,OAAO,CAACN,MAAM,CAACI,SAAS,CAAC,GAAGJ,MAAM,CAACI,SAAS,CAACG,IAAI,CAAC,GAAG,CAAC,GAAGP,MAAM,CAACI,SAAS;EACxF;EACA,OAAOH,UAAU;AACnB;AACA,OAAO,SAASO,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvC,OAAOA,GAAG,GAAI,GAAEA,GAAI,IAAGD,KAAM,EAAC,GAAI,GAAEA,KAAM,EAAC;AAC7C;AACA,OAAO,SAASE,iBAAiBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC9C,IAAI,OAAOD,KAAK,KAAK,UAAU,EAAE;IAC/B,OAAOA,KAAK,CAACC,KAAK,CAAC;EACrB;EACA,OAAOD,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,eAAeA,CAACF,KAAK,EAAEC,KAAK,EAAE;EAC5C,MAAME,GAAG,GAAGJ,iBAAiB,CAACC,KAAK,EAAEC,KAAK,CAAC;EAC3C,IAAIG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,GAAG,CAAC,KAAK,iBAAiB,EAAE,OAAO,EAAE;EACxE,OAAOA,GAAG;AACZ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}