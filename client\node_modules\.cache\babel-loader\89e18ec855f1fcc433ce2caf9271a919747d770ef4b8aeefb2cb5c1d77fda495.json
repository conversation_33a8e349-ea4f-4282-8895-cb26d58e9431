{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\Users\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { getAllUsers, blockUserById, deleteUserById } from \"../../../apicalls/users\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\nimport { TbUsers, TbSear<PERSON>, Tb<PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>ser<PERSON>, Tb<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, TbMail, TbUser } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Users() {\n  _s();\n  const navigate = useNavigate();\n  const [users, setUsers] = React.useState([]);\n  const dispatch = useDispatch();\n  const getUsersData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllUsers();\n      dispatch(HideLoading());\n      if (response.success) {\n        setUsers(response.users);\n        console.log(\"users\", response);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const blockUser = async studentId => {\n    try {\n      dispatch(ShowLoading());\n      const response = await blockUserById({\n        studentId\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        message.success(response.message);\n        getUsersData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const deleteUser = async studentId => {\n    try {\n      dispatch(ShowLoading());\n      const response = await deleteUserById({\n        studentId\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        message.success(\"User deleted successfully\");\n        getUsersData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const columns = [{\n    title: \"Name\",\n    dataIndex: \"name\"\n  }, {\n    title: \"School\",\n    dataIndex: \"school\"\n  }, {\n    title: \"Class\",\n    dataIndex: \"class\"\n  }, {\n    title: \"Email\",\n    dataIndex: \"email\"\n  }, {\n    title: \"Action\",\n    dataIndex: \"action\",\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between \",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => blockUser(record.studentId),\n        children: record.isBlocked ? \"Unblock\" : \"Block\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        onClick: () => {\n          if (window.confirm(\"Are you sure you want to delete this user?\")) {\n            deleteUser(record.studentId);\n          }\n        },\n        style: {\n          color: \"red\",\n          cursor: \"pointer\"\n        },\n        className: \"cursor-pointer\",\n        children: /*#__PURE__*/_jsxDEV(MdDelete, {\n          fontSize: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 9\n    }, this)\n  }];\n  useEffect(() => {\n    getUsersData();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between mt-2 items-end\",\n      children: /*#__PURE__*/_jsxDEV(PageTitle, {\n        title: \"Users\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divider\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: users,\n      rowKey: record => record.studentId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n}\n_s(Users, \"g/fUFlwDb3vtM9rcI634YR+3Vj4=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = Users;\nexport default Users;\nvar _c;\n$RefreshReg$(_c, \"Users\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useDispatch", "useNavigate", "motion", "getAllUsers", "blockUserById", "deleteUserById", "Page<PERSON><PERSON>le", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Input", "Loading", "TbUsers", "TbSearch", "Tb<PERSON><PERSON>er", "TbUserCheck", "TbUserX", "TbTrash", "TbEye", "TbSchool", "TbMail", "TbUser", "jsxDEV", "_jsxDEV", "Users", "_s", "navigate", "users", "setUsers", "dispatch", "getUsersData", "response", "success", "console", "log", "error", "blockUser", "studentId", "deleteUser", "columns", "title", "dataIndex", "render", "text", "record", "className", "children", "onClick", "isBlocked", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "window", "confirm", "style", "color", "cursor", "MdDelete", "fontSize", "Table", "dataSource", "<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Users/<USER>"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  getAllUsers,\r\n  blockUserById,\r\n  deleteUserById,\r\n} from \"../../../apicalls/users\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\r\nimport {\r\n  TbUsers,\r\n  TbSearch,\r\n  TbFilter,\r\n  TbUserCheck,\r\n  TbUserX,\r\n  TbTrash,\r\n  TbEye,\r\n  TbSchool,\r\n  TbMail,\r\n  TbUser\r\n} from \"react-icons/tb\";\r\n\r\nfunction Users() {\r\n  const navigate = useNavigate();\r\n  const [users, setUsers] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n\r\n  const getUsersData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllUsers();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setUsers(response.users);\r\n        console.log(\"users\", response);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const blockUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await blockUserById({\r\n        studentId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteUserById({ studentId });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(\"User deleted successfully\");\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      title: \"Name\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"School\",\r\n      dataIndex: \"school\",\r\n    },\r\n    {\r\n      title: \"Class\",\r\n      dataIndex: \"class\",\r\n    },\r\n    {\r\n      title: \"Email\",\r\n      dataIndex: \"email\",\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center justify-between \">\r\n          <button onClick={() => blockUser(record.studentId)}>\r\n            {record.isBlocked ? \"Unblock\" : \"Block\"}\r\n          </button>\r\n\r\n          <span\r\n            onClick={() => {\r\n              if (\r\n                window.confirm(\"Are you sure you want to delete this user?\")\r\n              ) {\r\n                deleteUser(record.studentId);\r\n              }\r\n            }}\r\n            style={{ color: \"red\", cursor: \"pointer\" }}\r\n            className=\"cursor-pointer\"\r\n          >\r\n            <MdDelete fontSize={20} />\r\n          </span>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between mt-2 items-end\">\r\n        <PageTitle title=\"Users\" />\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n\r\n      <Table\r\n        columns={columns}\r\n        dataSource={users}\r\n        rowKey={(record) => record.studentId}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Users;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,WAAW,EACXC,aAAa,EACbC,cAAc,QACT,yBAAyB;AAChC,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,4BAA4B;AACzE,SACEC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACXC,OAAO,EACPC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,MAAM,QACD,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,KAAK,CAACE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM+B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAE9B,MAAM+B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFD,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMwB,QAAQ,GAAG,MAAM7B,WAAW,CAAC,CAAC;MACpC2B,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIyB,QAAQ,CAACC,OAAO,EAAE;QACpBJ,QAAQ,CAACG,QAAQ,CAACJ,KAAK,CAAC;QACxBM,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEH,QAAQ,CAAC;MAChC,CAAC,MAAM;QACLpC,OAAO,CAACwC,KAAK,CAACJ,QAAQ,CAACpC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdN,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACwC,KAAK,CAACA,KAAK,CAACxC,OAAO,CAAC;IAC9B;EACF,CAAC;EACD,MAAMyC,SAAS,GAAG,MAAOC,SAAS,IAAK;IACrC,IAAI;MACFR,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMwB,QAAQ,GAAG,MAAM5B,aAAa,CAAC;QACnCkC;MACF,CAAC,CAAC;MACFR,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIyB,QAAQ,CAACC,OAAO,EAAE;QACpBrC,OAAO,CAACqC,OAAO,CAACD,QAAQ,CAACpC,OAAO,CAAC;QACjCmC,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLnC,OAAO,CAACwC,KAAK,CAACJ,QAAQ,CAACpC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdN,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACwC,KAAK,CAACA,KAAK,CAACxC,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM2C,UAAU,GAAG,MAAOD,SAAS,IAAK;IACtC,IAAI;MACFR,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMwB,QAAQ,GAAG,MAAM3B,cAAc,CAAC;QAAEiC;MAAU,CAAC,CAAC;MACpDR,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIyB,QAAQ,CAACC,OAAO,EAAE;QACpBrC,OAAO,CAACqC,OAAO,CAAC,2BAA2B,CAAC;QAC5CF,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLnC,OAAO,CAACwC,KAAK,CAACJ,QAAQ,CAACpC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdN,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACwC,KAAK,CAACA,KAAK,CAACxC,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM4C,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,QAAQ;IACnBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBrB,OAAA;MAAKsB,SAAS,EAAC,oCAAoC;MAAAC,QAAA,gBACjDvB,OAAA;QAAQwB,OAAO,EAAEA,CAAA,KAAMX,SAAS,CAACQ,MAAM,CAACP,SAAS,CAAE;QAAAS,QAAA,EAChDF,MAAM,CAACI,SAAS,GAAG,SAAS,GAAG;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAET7B,OAAA;QACEwB,OAAO,EAAEA,CAAA,KAAM;UACb,IACEM,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAC5D;YACAhB,UAAU,CAACM,MAAM,CAACP,SAAS,CAAC;UAC9B;QACF,CAAE;QACFkB,KAAK,EAAE;UAAEC,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAU,CAAE;QAC3CZ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAE1BvB,OAAA,CAACmC,QAAQ;UAACC,QAAQ,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,CACF;EACDvD,SAAS,CAAC,MAAM;IACdiC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EACN,oBACEP,OAAA;IAAAuB,QAAA,gBACEvB,OAAA;MAAKsB,SAAS,EAAC,qCAAqC;MAAAC,QAAA,eAClDvB,OAAA,CAAClB,SAAS;QAACmC,KAAK,EAAC;MAAO;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eACN7B,OAAA;MAAKsB,SAAS,EAAC;IAAS;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE/B7B,OAAA,CAACqC,KAAK;MACJrB,OAAO,EAAEA,OAAQ;MACjBsB,UAAU,EAAElC,KAAM;MAClBmC,MAAM,EAAGlB,MAAM,IAAKA,MAAM,CAACP;IAAU;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAC3B,EAAA,CArHQD,KAAK;EAAA,QACKxB,WAAW,EAEXD,WAAW;AAAA;AAAAgE,EAAA,GAHrBvC,KAAK;AAuHd,eAAeA,KAAK;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}