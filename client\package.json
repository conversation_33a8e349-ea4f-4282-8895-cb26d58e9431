{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.8.3", "axios": "^1.4.0", "framer-motion": "^10.18.0", "moment": "^2.29.4", "pdfjs-dist": "^2.15.349", "react": "^18.2.0", "react-confetti": "^6.1.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-katex": "^3.0.1", "react-modal": "^3.16.3", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "react-select": "^5.10.0", "react-use": "^17.4.3", "react-youtube": "^10.1.0", "redux": "^4.2.1", "swiper": "^11.2.8", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}}