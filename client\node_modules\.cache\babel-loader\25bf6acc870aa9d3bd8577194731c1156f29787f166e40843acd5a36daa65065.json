{"ast": null, "code": "const genSizeStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const getSizeStyle = (size, paddingVertical, paddingHorizontal, fontSize) => ({\n    [`${componentCls}${componentCls}-${size}`]: {\n      fontSize,\n      [`\n        ${componentCls}-title,\n        ${componentCls}-footer,\n        ${componentCls}-thead > tr > th,\n        ${componentCls}-tbody > tr > th,\n        ${componentCls}-tbody > tr > td,\n        tfoot > tr > th,\n        tfoot > tr > td\n      `]: {\n        padding: `${paddingVertical}px ${paddingHorizontal}px`\n      },\n      [`${componentCls}-filter-trigger`]: {\n        marginInlineEnd: `-${paddingHorizontal / 2}px`\n      },\n      [`${componentCls}-expanded-row-fixed`]: {\n        margin: `-${paddingVertical}px -${paddingHorizontal}px`\n      },\n      [`${componentCls}-tbody`]: {\n        // ========================= Nest Table ===========================\n        [`${componentCls}-wrapper:only-child ${componentCls}`]: {\n          marginBlock: `-${paddingVertical}px`,\n          marginInline: `${token.tableExpandColumnWidth - paddingHorizontal}px -${paddingHorizontal}px`\n        }\n      },\n      // https://github.com/ant-design/ant-design/issues/35167\n      [`${componentCls}-selection-column`]: {\n        paddingInlineStart: `${paddingHorizontal / 4}px`\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, getSizeStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle, token.tableFontSizeMiddle)), getSizeStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall, token.tableFontSizeSmall))\n  };\n};\nexport default genSizeStyle;", "map": {"version": 3, "names": ["genSizeStyle", "token", "componentCls", "getSizeStyle", "size", "paddingVertical", "paddingHorizontal", "fontSize", "padding", "marginInlineEnd", "margin", "marginBlock", "marginInline", "tableExpandColumnWidth", "paddingInlineStart", "Object", "assign", "tablePaddingVerticalMiddle", "tablePaddingHorizontalMiddle", "tableFontSizeMiddle", "tablePaddingVerticalSmall", "tablePaddingHorizontalSmall", "tableFontSizeSmall"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/size.js"], "sourcesContent": ["const genSizeStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const getSizeStyle = (size, paddingVertical, paddingHorizontal, fontSize) => ({\n    [`${componentCls}${componentCls}-${size}`]: {\n      fontSize,\n      [`\n        ${componentCls}-title,\n        ${componentCls}-footer,\n        ${componentCls}-thead > tr > th,\n        ${componentCls}-tbody > tr > th,\n        ${componentCls}-tbody > tr > td,\n        tfoot > tr > th,\n        tfoot > tr > td\n      `]: {\n        padding: `${paddingVertical}px ${paddingHorizontal}px`\n      },\n      [`${componentCls}-filter-trigger`]: {\n        marginInlineEnd: `-${paddingHorizontal / 2}px`\n      },\n      [`${componentCls}-expanded-row-fixed`]: {\n        margin: `-${paddingVertical}px -${paddingHorizontal}px`\n      },\n      [`${componentCls}-tbody`]: {\n        // ========================= Nest Table ===========================\n        [`${componentCls}-wrapper:only-child ${componentCls}`]: {\n          marginBlock: `-${paddingVertical}px`,\n          marginInline: `${token.tableExpandColumnWidth - paddingHorizontal}px -${paddingHorizontal}px`\n        }\n      },\n      // https://github.com/ant-design/ant-design/issues/35167\n      [`${componentCls}-selection-column`]: {\n        paddingInlineStart: `${paddingHorizontal / 4}px`\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, getSizeStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle, token.tableFontSizeMiddle)), getSizeStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall, token.tableFontSizeSmall))\n  };\n};\nexport default genSizeStyle;"], "mappings": "AAAA,MAAMA,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,YAAY,GAAGA,CAACC,IAAI,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,QAAQ,MAAM;IAC5E,CAAE,GAAEL,YAAa,GAAEA,YAAa,IAAGE,IAAK,EAAC,GAAG;MAC1CG,QAAQ;MACR,CAAE;AACR,UAAUL,YAAa;AACvB,UAAUA,YAAa;AACvB,UAAUA,YAAa;AACvB,UAAUA,YAAa;AACvB,UAAUA,YAAa;AACvB;AACA;AACA,OAAO,GAAG;QACFM,OAAO,EAAG,GAAEH,eAAgB,MAAKC,iBAAkB;MACrD,CAAC;MACD,CAAE,GAAEJ,YAAa,iBAAgB,GAAG;QAClCO,eAAe,EAAG,IAAGH,iBAAiB,GAAG,CAAE;MAC7C,CAAC;MACD,CAAE,GAAEJ,YAAa,qBAAoB,GAAG;QACtCQ,MAAM,EAAG,IAAGL,eAAgB,OAAMC,iBAAkB;MACtD,CAAC;MACD,CAAE,GAAEJ,YAAa,QAAO,GAAG;QACzB;QACA,CAAE,GAAEA,YAAa,uBAAsBA,YAAa,EAAC,GAAG;UACtDS,WAAW,EAAG,IAAGN,eAAgB,IAAG;UACpCO,YAAY,EAAG,GAAEX,KAAK,CAACY,sBAAsB,GAAGP,iBAAkB,OAAMA,iBAAkB;QAC5F;MACF,CAAC;MACD;MACA,CAAE,GAAEJ,YAAa,mBAAkB,GAAG;QACpCY,kBAAkB,EAAG,GAAER,iBAAiB,GAAG,CAAE;MAC/C;IACF;EACF,CAAC,CAAC;EACF,OAAO;IACL,CAAE,GAAEJ,YAAa,UAAS,GAAGa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,YAAY,CAAC,QAAQ,EAAEF,KAAK,CAACgB,0BAA0B,EAAEhB,KAAK,CAACiB,4BAA4B,EAAEjB,KAAK,CAACkB,mBAAmB,CAAC,CAAC,EAAEhB,YAAY,CAAC,OAAO,EAAEF,KAAK,CAACmB,yBAAyB,EAAEnB,KAAK,CAACoB,2BAA2B,EAAEpB,KAAK,CAACqB,kBAAkB,CAAC;EAC5S,CAAC;AACH,CAAC;AACD,eAAetB,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}