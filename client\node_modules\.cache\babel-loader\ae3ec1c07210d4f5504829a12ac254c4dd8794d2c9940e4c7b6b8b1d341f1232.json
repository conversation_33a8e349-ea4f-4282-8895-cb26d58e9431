{"ast": null, "code": "import { responseImmutable, useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport TableContext from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport Summary from \"./Summary\";\nimport SummaryContext from \"./SummaryContext\";\nfunction Footer(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var children = props.children,\n    stickyOffsets = props.stickyOffsets,\n    flattenColumns = props.flattenColumns,\n    columns = props.columns;\n  var prefixCls = useContext(TableContext, 'prefixCls');\n  var lastColumnIndex = flattenColumns.length - 1;\n  var scrollColumn = flattenColumns[lastColumnIndex];\n  var summaryContext = React.useMemo(function () {\n    return {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns,\n      scrollColumnIndex: scrollColumn !== null && scrollColumn !== void 0 && scrollColumn.scrollbar ? lastColumnIndex : null,\n      columns: columns\n    };\n  }, [scrollColumn, flattenColumns, lastColumnIndex, stickyOffsets, columns]);\n  return /*#__PURE__*/React.createElement(SummaryContext.Provider, {\n    value: summaryContext\n  }, /*#__PURE__*/React.createElement(\"tfoot\", {\n    className: \"\".concat(prefixCls, \"-summary\")\n  }, children));\n}\nexport default responseImmutable(Footer);\nexport var FooterComponents = Summary;", "map": {"version": 3, "names": ["responseImmutable", "useContext", "React", "TableContext", "devRenderTimes", "Summary", "SummaryContext", "Footer", "props", "process", "env", "NODE_ENV", "children", "stickyOffsets", "flattenColumns", "columns", "prefixCls", "lastColumnIndex", "length", "scrollColumn", "summaryContext", "useMemo", "scrollColumnIndex", "scrollbar", "createElement", "Provider", "value", "className", "concat", "FooterComponents"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/Footer/index.js"], "sourcesContent": ["import { responseImmutable, useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport TableContext from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport Summary from \"./Summary\";\nimport SummaryContext from \"./SummaryContext\";\nfunction Footer(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var children = props.children,\n    stickyOffsets = props.stickyOffsets,\n    flattenColumns = props.flattenColumns,\n    columns = props.columns;\n  var prefixCls = useContext(TableContext, 'prefixCls');\n  var lastColumnIndex = flattenColumns.length - 1;\n  var scrollColumn = flattenColumns[lastColumnIndex];\n  var summaryContext = React.useMemo(function () {\n    return {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns,\n      scrollColumnIndex: scrollColumn !== null && scrollColumn !== void 0 && scrollColumn.scrollbar ? lastColumnIndex : null,\n      columns: columns\n    };\n  }, [scrollColumn, flattenColumns, lastColumnIndex, stickyOffsets, columns]);\n  return /*#__PURE__*/React.createElement(SummaryContext.Provider, {\n    value: summaryContext\n  }, /*#__PURE__*/React.createElement(\"tfoot\", {\n    className: \"\".concat(prefixCls, \"-summary\")\n  }, children));\n}\nexport default responseImmutable(Footer);\nexport var FooterComponents = Summary;"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,UAAU,QAAQ,uBAAuB;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCP,cAAc,CAACI,KAAK,CAAC;EACvB;EACA,IAAII,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IAC3BC,aAAa,GAAGL,KAAK,CAACK,aAAa;IACnCC,cAAc,GAAGN,KAAK,CAACM,cAAc;IACrCC,OAAO,GAAGP,KAAK,CAACO,OAAO;EACzB,IAAIC,SAAS,GAAGf,UAAU,CAACE,YAAY,EAAE,WAAW,CAAC;EACrD,IAAIc,eAAe,GAAGH,cAAc,CAACI,MAAM,GAAG,CAAC;EAC/C,IAAIC,YAAY,GAAGL,cAAc,CAACG,eAAe,CAAC;EAClD,IAAIG,cAAc,GAAGlB,KAAK,CAACmB,OAAO,CAAC,YAAY;IAC7C,OAAO;MACLR,aAAa,EAAEA,aAAa;MAC5BC,cAAc,EAAEA,cAAc;MAC9BQ,iBAAiB,EAAEH,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACI,SAAS,GAAGN,eAAe,GAAG,IAAI;MACtHF,OAAO,EAAEA;IACX,CAAC;EACH,CAAC,EAAE,CAACI,YAAY,EAAEL,cAAc,EAAEG,eAAe,EAAEJ,aAAa,EAAEE,OAAO,CAAC,CAAC;EAC3E,OAAO,aAAab,KAAK,CAACsB,aAAa,CAAClB,cAAc,CAACmB,QAAQ,EAAE;IAC/DC,KAAK,EAAEN;EACT,CAAC,EAAE,aAAalB,KAAK,CAACsB,aAAa,CAAC,OAAO,EAAE;IAC3CG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACZ,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEJ,QAAQ,CAAC,CAAC;AACf;AACA,eAAeZ,iBAAiB,CAACO,MAAM,CAAC;AACxC,OAAO,IAAIsB,gBAAgB,GAAGxB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}