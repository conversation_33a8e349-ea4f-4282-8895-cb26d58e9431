{"ast": null, "code": "const genDraggerStyle = token => {\n  const {\n    componentCls,\n    iconCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-drag`]: {\n        position: 'relative',\n        width: '100%',\n        height: '100%',\n        textAlign: 'center',\n        background: token.colorFillAlter,\n        border: `${token.lineWidth}px dashed ${token.colorBorder}`,\n        borderRadius: token.borderRadiusLG,\n        cursor: 'pointer',\n        transition: `border-color ${token.motionDurationSlow}`,\n        [componentCls]: {\n          padding: `${token.padding}px 0`\n        },\n        [`${componentCls}-btn`]: {\n          display: 'table',\n          width: '100%',\n          height: '100%',\n          outline: 'none'\n        },\n        [`${componentCls}-drag-container`]: {\n          display: 'table-cell',\n          verticalAlign: 'middle'\n        },\n        [`&:not(${componentCls}-disabled):hover`]: {\n          borderColor: token.colorPrimaryHover\n        },\n        [`p${componentCls}-drag-icon`]: {\n          marginBottom: token.margin,\n          [iconCls]: {\n            color: token.colorPrimary,\n            fontSize: token.uploadThumbnailSize\n          }\n        },\n        [`p${componentCls}-text`]: {\n          margin: `0 0 ${token.marginXXS}px`,\n          color: token.colorTextHeading,\n          fontSize: token.fontSizeLG\n        },\n        [`p${componentCls}-hint`]: {\n          color: token.colorTextDescription,\n          fontSize: token.fontSize\n        },\n        // ===================== Disabled =====================\n        [`&${componentCls}-disabled`]: {\n          cursor: 'not-allowed',\n          [`p${componentCls}-drag-icon ${iconCls},\n            p${componentCls}-text,\n            p${componentCls}-hint\n          `]: {\n            color: token.colorTextDisabled\n          }\n        }\n      }\n    }\n  };\n};\nexport default genDraggerStyle;", "map": {"version": 3, "names": ["genDraggerStyle", "token", "componentCls", "iconCls", "position", "width", "height", "textAlign", "background", "colorFillAlter", "border", "lineWidth", "colorBorder", "borderRadius", "borderRadiusLG", "cursor", "transition", "motionDurationSlow", "padding", "display", "outline", "verticalAlign", "borderColor", "colorPrimaryHover", "marginBottom", "margin", "color", "colorPrimary", "fontSize", "uploadThumbnailSize", "marginXXS", "colorTextHeading", "fontSizeLG", "colorTextDescription", "colorTextDisabled"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/upload/style/dragger.js"], "sourcesContent": ["const genDraggerStyle = token => {\n  const {\n    componentCls,\n    iconCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-drag`]: {\n        position: 'relative',\n        width: '100%',\n        height: '100%',\n        textAlign: 'center',\n        background: token.colorFillAlter,\n        border: `${token.lineWidth}px dashed ${token.colorBorder}`,\n        borderRadius: token.borderRadiusLG,\n        cursor: 'pointer',\n        transition: `border-color ${token.motionDurationSlow}`,\n        [componentCls]: {\n          padding: `${token.padding}px 0`\n        },\n        [`${componentCls}-btn`]: {\n          display: 'table',\n          width: '100%',\n          height: '100%',\n          outline: 'none'\n        },\n        [`${componentCls}-drag-container`]: {\n          display: 'table-cell',\n          verticalAlign: 'middle'\n        },\n        [`&:not(${componentCls}-disabled):hover`]: {\n          borderColor: token.colorPrimaryHover\n        },\n        [`p${componentCls}-drag-icon`]: {\n          marginBottom: token.margin,\n          [iconCls]: {\n            color: token.colorPrimary,\n            fontSize: token.uploadThumbnailSize\n          }\n        },\n        [`p${componentCls}-text`]: {\n          margin: `0 0 ${token.marginXXS}px`,\n          color: token.colorTextHeading,\n          fontSize: token.fontSizeLG\n        },\n        [`p${componentCls}-hint`]: {\n          color: token.colorTextDescription,\n          fontSize: token.fontSize\n        },\n        // ===================== Disabled =====================\n        [`&${componentCls}-disabled`]: {\n          cursor: 'not-allowed',\n          [`p${componentCls}-drag-icon ${iconCls},\n            p${componentCls}-text,\n            p${componentCls}-hint\n          `]: {\n            color: token.colorTextDisabled\n          }\n        }\n      }\n    }\n  };\n};\nexport default genDraggerStyle;"], "mappings": "AAAA,MAAMA,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,CAAE,GAAEC,YAAa,UAAS,GAAG;MAC3B,CAAE,GAAEA,YAAa,OAAM,GAAG;QACxBE,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,QAAQ;QACnBC,UAAU,EAAEP,KAAK,CAACQ,cAAc;QAChCC,MAAM,EAAG,GAAET,KAAK,CAACU,SAAU,aAAYV,KAAK,CAACW,WAAY,EAAC;QAC1DC,YAAY,EAAEZ,KAAK,CAACa,cAAc;QAClCC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAG,gBAAef,KAAK,CAACgB,kBAAmB,EAAC;QACtD,CAACf,YAAY,GAAG;UACdgB,OAAO,EAAG,GAAEjB,KAAK,CAACiB,OAAQ;QAC5B,CAAC;QACD,CAAE,GAAEhB,YAAa,MAAK,GAAG;UACvBiB,OAAO,EAAE,OAAO;UAChBd,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdc,OAAO,EAAE;QACX,CAAC;QACD,CAAE,GAAElB,YAAa,iBAAgB,GAAG;UAClCiB,OAAO,EAAE,YAAY;UACrBE,aAAa,EAAE;QACjB,CAAC;QACD,CAAE,SAAQnB,YAAa,kBAAiB,GAAG;UACzCoB,WAAW,EAAErB,KAAK,CAACsB;QACrB,CAAC;QACD,CAAE,IAAGrB,YAAa,YAAW,GAAG;UAC9BsB,YAAY,EAAEvB,KAAK,CAACwB,MAAM;UAC1B,CAACtB,OAAO,GAAG;YACTuB,KAAK,EAAEzB,KAAK,CAAC0B,YAAY;YACzBC,QAAQ,EAAE3B,KAAK,CAAC4B;UAClB;QACF,CAAC;QACD,CAAE,IAAG3B,YAAa,OAAM,GAAG;UACzBuB,MAAM,EAAG,OAAMxB,KAAK,CAAC6B,SAAU,IAAG;UAClCJ,KAAK,EAAEzB,KAAK,CAAC8B,gBAAgB;UAC7BH,QAAQ,EAAE3B,KAAK,CAAC+B;QAClB,CAAC;QACD,CAAE,IAAG9B,YAAa,OAAM,GAAG;UACzBwB,KAAK,EAAEzB,KAAK,CAACgC,oBAAoB;UACjCL,QAAQ,EAAE3B,KAAK,CAAC2B;QAClB,CAAC;QACD;QACA,CAAE,IAAG1B,YAAa,WAAU,GAAG;UAC7Ba,MAAM,EAAE,aAAa;UACrB,CAAE,IAAGb,YAAa,cAAaC,OAAQ;AACjD,eAAeD,YAAa;AAC5B,eAAeA,YAAa;AAC5B,WAAW,GAAG;YACFwB,KAAK,EAAEzB,KAAK,CAACiC;UACf;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAelC,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}