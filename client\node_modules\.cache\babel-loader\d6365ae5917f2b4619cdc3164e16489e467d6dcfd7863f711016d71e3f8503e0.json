{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n/* eslint react/prop-types: 0 */\nimport classNames from 'classnames';\nimport React from 'react';\nvar Pager = function Pager(props) {\n  var _classNames;\n  var rootPrefixCls = props.rootPrefixCls,\n    page = props.page,\n    active = props.active,\n    className = props.className,\n    showTitle = props.showTitle,\n    onClick = props.onClick,\n    onKeyPress = props.onKeyPress,\n    itemRender = props.itemRender;\n  var prefixCls = \"\".concat(rootPrefixCls, \"-item\");\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(page), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), !page), _defineProperty(_classNames, props.className, className), _classNames));\n  var handleClick = function handleClick() {\n    onClick(page);\n  };\n  var handleKeyPress = function handleKeyPress(e) {\n    onKeyPress(e, onClick, page);\n  };\n  return /*#__PURE__*/React.createElement(\"li\", {\n    title: showTitle ? page.toString() : null,\n    className: cls,\n    onClick: handleClick,\n    onKeyPress: handleKeyPress,\n    tabIndex: 0\n  }, itemRender(page, 'page', /*#__PURE__*/React.createElement(\"a\", {\n    rel: \"nofollow\"\n  }, page)));\n};\nexport default Pager;", "map": {"version": 3, "names": ["_defineProperty", "classNames", "React", "Pager", "props", "_classNames", "rootPrefixCls", "page", "active", "className", "showTitle", "onClick", "onKeyPress", "itemRender", "prefixCls", "concat", "cls", "handleClick", "handleKeyPress", "e", "createElement", "title", "toString", "tabIndex", "rel"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-pagination/es/Pager.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n/* eslint react/prop-types: 0 */\nimport classNames from 'classnames';\nimport React from 'react';\nvar Pager = function Pager(props) {\n  var _classNames;\n  var rootPrefixCls = props.rootPrefixCls,\n    page = props.page,\n    active = props.active,\n    className = props.className,\n    showTitle = props.showTitle,\n    onClick = props.onClick,\n    onKeyPress = props.onKeyPress,\n    itemRender = props.itemRender;\n  var prefixCls = \"\".concat(rootPrefixCls, \"-item\");\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(page), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), !page), _defineProperty(_classNames, props.className, className), _classNames));\n  var handleClick = function handleClick() {\n    onClick(page);\n  };\n  var handleKeyPress = function handleKeyPress(e) {\n    onKeyPress(e, onClick, page);\n  };\n  return /*#__PURE__*/React.createElement(\"li\", {\n    title: showTitle ? page.toString() : null,\n    className: cls,\n    onClick: handleClick,\n    onKeyPress: handleKeyPress,\n    tabIndex: 0\n  }, itemRender(page, 'page', /*#__PURE__*/React.createElement(\"a\", {\n    rel: \"nofollow\"\n  }, page)));\n};\nexport default Pager;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE;AACA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,WAAW;EACf,IAAIC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACrCC,IAAI,GAAGH,KAAK,CAACG,IAAI;IACjBC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,OAAO,GAAGP,KAAK,CAACO,OAAO;IACvBC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,UAAU,GAAGT,KAAK,CAACS,UAAU;EAC/B,IAAIC,SAAS,GAAG,EAAE,CAACC,MAAM,CAACT,aAAa,EAAE,OAAO,CAAC;EACjD,IAAIU,GAAG,GAAGf,UAAU,CAACa,SAAS,EAAE,EAAE,CAACC,MAAM,CAACD,SAAS,EAAE,GAAG,CAAC,CAACC,MAAM,CAACR,IAAI,CAAC,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAEL,eAAe,CAACK,WAAW,EAAE,EAAE,CAACU,MAAM,CAACD,SAAS,EAAE,SAAS,CAAC,EAAEN,MAAM,CAAC,EAAER,eAAe,CAACK,WAAW,EAAE,EAAE,CAACU,MAAM,CAACD,SAAS,EAAE,WAAW,CAAC,EAAE,CAACP,IAAI,CAAC,EAAEP,eAAe,CAACK,WAAW,EAAED,KAAK,CAACK,SAAS,EAAEA,SAAS,CAAC,EAAEJ,WAAW,CAAC,CAAC;EACjT,IAAIY,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvCN,OAAO,CAACJ,IAAI,CAAC;EACf,CAAC;EACD,IAAIW,cAAc,GAAG,SAASA,cAAcA,CAACC,CAAC,EAAE;IAC9CP,UAAU,CAACO,CAAC,EAAER,OAAO,EAAEJ,IAAI,CAAC;EAC9B,CAAC;EACD,OAAO,aAAaL,KAAK,CAACkB,aAAa,CAAC,IAAI,EAAE;IAC5CC,KAAK,EAAEX,SAAS,GAAGH,IAAI,CAACe,QAAQ,CAAC,CAAC,GAAG,IAAI;IACzCb,SAAS,EAAEO,GAAG;IACdL,OAAO,EAAEM,WAAW;IACpBL,UAAU,EAAEM,cAAc;IAC1BK,QAAQ,EAAE;EACZ,CAAC,EAAEV,UAAU,CAACN,IAAI,EAAE,MAAM,EAAE,aAAaL,KAAK,CAACkB,aAAa,CAAC,GAAG,EAAE;IAChEI,GAAG,EAAE;EACP,CAAC,EAAEjB,IAAI,CAAC,CAAC,CAAC;AACZ,CAAC;AACD,eAAeJ,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}