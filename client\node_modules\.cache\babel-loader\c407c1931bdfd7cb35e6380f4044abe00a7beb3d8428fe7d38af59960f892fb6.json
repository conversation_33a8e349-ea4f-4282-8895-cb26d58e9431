{"ast": null, "code": "// =========================== Motion ===========================\nconst genRtlStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    }\n  };\n};\nexport default genRtlStyle;", "map": {"version": 3, "names": ["genRtlStyle", "token", "componentCls", "direction"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/upload/style/rtl.js"], "sourcesContent": ["// =========================== Motion ===========================\nconst genRtlStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    }\n  };\n};\nexport default genRtlStyle;"], "mappings": "AAAA;AACA,MAAMA,WAAW,GAAGC,KAAK,IAAI;EAC3B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAAE,GAAEC,YAAa,MAAK,GAAG;MACvBC,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC;AACD,eAAeH,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}