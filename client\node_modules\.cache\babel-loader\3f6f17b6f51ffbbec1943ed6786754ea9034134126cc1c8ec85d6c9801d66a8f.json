{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\StudyMaterial\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\nimport PDFModal from \"./PDFModal\";\nimport { FaPlayCircle, FaBook, FaVideo, FaFileAlt, FaGraduationCap, FaDownload, FaEye, FaTimes, FaChevronDown, FaSearch, FaExpand, FaCompress } from \"react-icons/fa\";\nimport { TbVideo, TbFileText, TbBook as TbBookIcon, TbGraduationCap, TbSearch, TbFilter, TbSortAscending, TbPlay, TbDownload, TbEye, TbCalendar, TbUser, TbChevronDown as TbChevronDownIcon, TbChevronUp, TbX, TbAlertTriangle, TbInfoCircle, TbCheck, TbSubtitles, TbSchool, TbBooks } from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction StudyMaterial() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = (user === null || user === void 0 ? void 0 : user.level) || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary' ? primarySubjects : userLevelLower === 'secondary' ? secondarySubjects : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary' ? ['1', '2', '3', '4', '5', '6', '7'] : userLevelLower === 'secondary' ? ['Form-1', 'Form-2', 'Form-3', 'Form-4'] : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"videos\");\n  const [selectedClass, setSelectedClass] = useState((user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className) || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className);\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n  const [selectedSubtitle, setSelectedSubtitle] = useState('off');\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className);\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user === null || user === void 0 ? void 0 : user.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" : selectedClass.toString().replace(\"Form-\", \"\");\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject // This can be \"all\" or a specific subject\n      };\n\n      if (userLevel) {\n        data.level = userLevel;\n      }\n      const res = await getStudyMaterial(data);\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = tab => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n  const handleSubjectChange = subject => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n  const handleClassChange = className => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material => material.title.toLowerCase().includes(searchLower) || material.subject.toLowerCase().includes(searchLower) || material.year && material.year.toLowerCase().includes(searchLower));\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n        // For videos (no year field), sort by creation date or reverse order\n        else if (activeTab === \"videos\") {\n          // Since videos are fetched in reverse order from server, maintain that for \"newest\"\n          return 0; // Keep original order (newest first from server)\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;else if (!a.year && b.year) return 1;else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n        // For videos, reverse the order\n        else if (activeTab === \"videos\") {\n          return 0; // Will be reversed after sort\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;else if (!a.year && b.year) return 1;else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n    // For videos with \"oldest\" sort, reverse the array\n    if (activeTab === \"videos\" && sortBy === \"oldest\") {\n      filtered.reverse();\n    }\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = documentUrl => {\n    fetch(documentUrl).then(response => response.blob()).then(blob => {\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = documentUrl.split(\"/\").pop();\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      window.URL.revokeObjectURL(url);\n    }).catch(error => {\n      console.error(\"Error downloading the file:\", error);\n    });\n  };\n  const handleDocumentPreview = documentUrl => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedMaterials[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    setSelectedSubtitle('off');\n    setVideoRef(null);\n  };\n\n  // Handle subtitle selection\n  const handleSubtitleChange = language => {\n    setSelectedSubtitle(language);\n    if (videoRef) {\n      const tracks = videoRef.textTracks;\n\n      // Disable all tracks first\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'disabled';\n      }\n\n      // Enable selected track\n      if (language !== 'off') {\n        for (let i = 0; i < tracks.length; i++) {\n          if (tracks[i].language === language) {\n            tracks[i].mode = 'showing';\n            break;\n          }\n        }\n      }\n    }\n  };\n  const handleExpandVideo = () => {\n    setIsVideoExpanded(true);\n  };\n  const handleCollapseVideo = () => {\n    setIsVideoExpanded(false);\n  };\n\n  // Note: Auto-refresh removed since videos are now uploaded synchronously\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          credentials: 'include'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.warn('⚠️ Failed to get signed URL, using original:', error.message);\n        return videoUrl;\n      }\n    }\n\n    // For other URLs or if signed URL fails, return as-is\n    return videoUrl;\n  };\n\n  // Get appropriate thumbnail URL for video\n  const getThumbnailUrl = material => {\n    // If we have a custom thumbnail, use it\n    if (material.thumbnail && material.thumbnail !== \"\" && material.thumbnail !== \"processing\") {\n      return material.thumbnail;\n    }\n\n    // For YouTube videos, extract video ID and use YouTube thumbnail\n    if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n      // Extract YouTube video ID if it's a full URL\n      let videoId = material.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n\n    // For uploaded videos without thumbnails, use a default placeholder\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n  };\n\n  // Keyboard support for video modal\n  useEffect(() => {\n    const handleKeyPress = event => {\n      if (showVideoIndices.length > 0) {\n        switch (event.key) {\n          case 'Escape':\n            handleHideVideo();\n            break;\n          case 'f':\n          case 'F':\n            if (!isVideoExpanded) {\n              handleExpandVideo();\n            } else {\n              handleCollapseVideo();\n            }\n            break;\n          default:\n            break;\n        }\n      }\n    };\n    document.addEventListener('keydown', handleKeyPress);\n    return () => {\n      document.removeEventListener('keydown', handleKeyPress);\n    };\n  }, [showVideoIndices, isVideoExpanded]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"study-material-modern\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modern-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-main\",\n          children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n            className: \"header-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"Study Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Access comprehensive learning resources for \", userLevel, \" education\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"level-badge\",\n          children: [userLevel === null || userLevel === void 0 ? void 0 : userLevel.toUpperCase(), \" LEVEL\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-tabs\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tabs-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-tab ${activeTab === 'videos' ? 'active' : ''}`,\n          onClick: () => handleTabChange('videos'),\n          children: [/*#__PURE__*/_jsxDEV(FaVideo, {\n            className: \"tab-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Videos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-tab ${activeTab === 'study-notes' ? 'active' : ''}`,\n          onClick: () => handleTabChange('study-notes'),\n          children: [/*#__PURE__*/_jsxDEV(FaFileAlt, {\n            className: \"tab-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Notes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-tab ${activeTab === 'past-papers' ? 'active' : ''}`,\n          onClick: () => handleTabChange('past-papers'),\n          children: [/*#__PURE__*/_jsxDEV(FaFileAlt, {\n            className: \"tab-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Past Papers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-tab ${activeTab === 'books' ? 'active' : ''}`,\n          onClick: () => handleTabChange('books'),\n          children: [/*#__PURE__*/_jsxDEV(FaBook, {\n            className: \"tab-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Books\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filters-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filters-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: [\"Class\", userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"current-class-display\",\n              children: userLevelLower === 'primary' ? `Class ${userCurrentClass}` : `Form ${userCurrentClass}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"class-selector\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"class-display-btn\",\n              onClick: toggleClassSelector,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [selectedClass === 'all' ? 'All Classes' : userLevelLower === 'primary' ? `Class ${selectedClass}` : `Form ${selectedClass}`, selectedClass === userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"current-badge\",\n                  children: \"Current\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FaChevronDown, {\n                className: `chevron ${showClassSelector ? 'open' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this), showClassSelector && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"class-dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `class-option ${selectedClass === 'all' ? 'active' : ''}`,\n                onClick: () => handleClassChange('all'),\n                children: \"All Classes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 19\n              }, this), availableClasses.map((className, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `class-option ${selectedClass === className ? 'active' : ''} ${className === userCurrentClass ? 'user-current' : ''}`,\n                onClick: () => handleClassChange(className),\n                children: [userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`, className === userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"current-badge\",\n                  children: \"Your Class\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Subject\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subject-pills\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `subject-pill ${selectedSubject === 'all' ? 'active' : ''}`,\n              onClick: () => handleSubjectChange('all'),\n              children: \"All Subjects\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), subjectsList.map((subject, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `subject-pill ${selectedSubject === subject ? 'active' : ''}`,\n              onClick: () => handleSubjectChange(subject),\n              children: subject\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-sort-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"filter-label\",\n              children: \"Search Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-box\",\n              children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n                className: \"search-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search by title, subject, or year...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"search-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sort-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"filter-label\",\n              children: \"Sort By\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sort-selector\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: sortBy,\n                onChange: e => setSortBy(e.target.value),\n                className: \"sort-select\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"newest\",\n                  children: \"Newest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"oldest\",\n                  children: \"Oldest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"title\",\n                  children: \"By Title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"materials-section\",\n      children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading materials...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Error Loading Materials\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"retry-btn\",\n          onClick: () => {\n            setError(null);\n            fetchMaterials();\n          },\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 11\n      }, this) : filteredAndSortedMaterials.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"materials-grid\",\n        children: filteredAndSortedMaterials.map((material, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"material-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"material-type\",\n              children: [activeTab === 'videos' && /*#__PURE__*/_jsxDEV(FaVideo, {\n                className: \"type-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 48\n              }, this), activeTab === 'study-notes' && /*#__PURE__*/_jsxDEV(FaFileAlt, {\n                className: \"type-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 53\n              }, this), activeTab === 'past-papers' && /*#__PURE__*/_jsxDEV(FaFileAlt, {\n                className: \"type-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 53\n              }, this), activeTab === 'books' && /*#__PURE__*/_jsxDEV(FaBook, {\n                className: \"type-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 47\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"type-label\",\n                children: activeTab === 'study-notes' ? 'Note' : activeTab === 'past-papers' ? 'Past Paper' : activeTab === 'videos' ? 'Video' : 'Book'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"header-right\",\n              children: [activeTab === 'videos' && material.coreClass && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"class-tags\",\n                children: material.isCore ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"class-tag core-class\",\n                  children: [\"Core Class \", userLevelLower === 'primary' ? material.coreClass : `Form ${material.coreClass}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 27\n                }, this) : material.sharedFromClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"class-tag shared-class\",\n                  children: [\"Shared from \", userLevelLower === 'primary' ? `Class ${material.sharedFromClass}` : `Form ${material.sharedFromClass}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 23\n              }, this), material.year && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-year\",\n                children: material.year\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 17\n          }, this), activeTab === 'videos' && (material.videoUrl || material.videoID) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-thumbnail-container\",\n            onClick: () => handleShowVideo(index),\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: getThumbnailUrl(material),\n              alt: material.title,\n              className: \"video-thumbnail\",\n              onError: e => {\n                // Fallback logic for failed thumbnails\n                if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n                  // For YouTube videos, try different quality thumbnails\n                  let videoId = material.videoID;\n                  if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                  }\n                  if (!e.target.src.includes('youtube.com')) {\n                    e.target.src = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n                  } else if (e.target.src.includes('maxresdefault')) {\n                    e.target.src = `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;\n                  } else if (e.target.src.includes('mqdefault')) {\n                    e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n                  } else {\n                    // Final fallback to default placeholder\n                    e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                  }\n                } else {\n                  // For uploaded videos without thumbnails, use default placeholder\n                  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"play-overlay\",\n              children: [/*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                className: \"play-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"play-text\",\n                children: \"Watch Now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"material-title\",\n              children: material.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"material-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-subject\",\n                children: material.subject\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 21\n              }, this), material.className && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-class\",\n                children: userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-actions\",\n            children: activeTab === 'videos' && (material.videoUrl || material.videoID) ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-info-text\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-duration\",\n                children: \"Click thumbnail to play\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 21\n            }, this) : material.documentUrl ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"action-btn secondary\",\n                onClick: () => handleDocumentPreview(material.documentUrl),\n                children: [/*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 25\n                }, this), \" View\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"action-btn primary\",\n                onClick: () => handleDocumentDownload(material.documentUrl),\n                children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 25\n                }, this), \" Download\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"unavailable\",\n              children: \"Not available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Materials Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No study materials are available for your current selection.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: \"Try selecting a different class or subject.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 767,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 763,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 7\n    }, this), showVideoIndices.length > 0 && currentVideoIndex !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `video-overlay ${isVideoExpanded ? 'expanded' : ''}`,\n      onClick: e => {\n        if (e.target === e.currentTarget) handleHideVideo();\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-modal ${isVideoExpanded ? 'expanded' : ''}`,\n        children: (() => {\n          const video = filteredAndSortedMaterials[currentVideoIndex];\n          if (!video) return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Video not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 34\n          }, this);\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: video.title || 'Untitled Video'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: video.subject || 'Unknown Subject'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: [\"Class \", video.className || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 789,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-controls\",\n                children: [!isVideoExpanded ? /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn expand-btn\",\n                  onClick: handleExpandVideo,\n                  title: \"Expand to fullscreen\",\n                  children: /*#__PURE__*/_jsxDEV(FaExpand, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn collapse-btn\",\n                  onClick: handleCollapseVideo,\n                  title: \"Exit fullscreen\",\n                  children: /*#__PURE__*/_jsxDEV(FaCompress, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn close-btn\",\n                  onClick: handleHideVideo,\n                  title: \"Close video\",\n                  children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 815,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-container\",\n              children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '15px',\n                  background: '#000',\n                  borderRadius: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: ref => setVideoRef(ref),\n                  controls: true,\n                  autoPlay: true,\n                  playsInline: true,\n                  preload: \"metadata\",\n                  width: \"100%\",\n                  height: \"400\",\n                  poster: getThumbnailUrl(video),\n                  style: {\n                    width: '100%',\n                    height: '400px',\n                    backgroundColor: '#000'\n                  },\n                  onError: e => {\n                    setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                  },\n                  onCanPlay: () => {\n                    setVideoError(null);\n                  },\n                  onLoadedMetadata: () => {\n                    // Auto-enable first subtitle if available and none selected\n                    if (video.subtitles && video.subtitles.length > 0 && selectedSubtitle === 'off') {\n                      const defaultSubtitle = video.subtitles.find(sub => sub.isDefault) || video.subtitles[0];\n                      handleSubtitleChange(defaultSubtitle.language);\n                    }\n                  },\n                  crossOrigin: \"anonymous\",\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: video.signedVideoUrl || video.videoUrl,\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 852,\n                    columnNumber: 29\n                  }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                    kind: \"subtitles\",\n                    src: subtitle.url,\n                    srcLang: subtitle.language,\n                    label: subtitle.languageName,\n                    default: subtitle.isDefault || index === 0\n                  }, `${subtitle.language}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 31\n                  }, this)), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      color: 'white',\n                      textAlign: 'center',\n                      padding: '20px'\n                    },\n                    children: [\"Your browser does not support the video tag.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: video.signedVideoUrl || video.videoUrl,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      style: {\n                        color: '#4fc3f7'\n                      },\n                      children: \"Click here to open video in new tab\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 27\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '12px 15px',\n                    background: 'rgba(0,0,0,0.8)',\n                    borderRadius: '0 0 8px 8px',\n                    borderTop: '1px solid #333'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '12px',\n                      flexWrap: 'wrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#fff',\n                        fontSize: '14px',\n                        fontWeight: '500',\n                        minWidth: 'fit-content'\n                      },\n                      children: \"\\uD83D\\uDCDD Choose Language:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 889,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        gap: '8px',\n                        flexWrap: 'wrap',\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleSubtitleChange('off'),\n                        style: {\n                          padding: '6px 12px',\n                          borderRadius: '20px',\n                          border: 'none',\n                          fontSize: '12px',\n                          fontWeight: '500',\n                          cursor: 'pointer',\n                          transition: 'all 0.2s ease',\n                          backgroundColor: selectedSubtitle === 'off' ? '#ff4757' : '#2f3542',\n                          color: '#fff'\n                        },\n                        children: \"OFF\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 905,\n                        columnNumber: 35\n                      }, this), video.subtitles.map(subtitle => /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleSubtitleChange(subtitle.language),\n                        style: {\n                          padding: '6px 12px',\n                          borderRadius: '20px',\n                          border: 'none',\n                          fontSize: '12px',\n                          fontWeight: '500',\n                          cursor: 'pointer',\n                          transition: 'all 0.2s ease',\n                          backgroundColor: selectedSubtitle === subtitle.language ? '#2ed573' : '#57606f',\n                          color: '#fff',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '4px'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          children: subtitle.languageName\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 942,\n                          columnNumber: 39\n                        }, this), subtitle.isAutoGenerated && /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            fontSize: '10px',\n                            opacity: 0.8,\n                            backgroundColor: 'rgba(255,255,255,0.2)',\n                            padding: '1px 4px',\n                            borderRadius: '8px'\n                          },\n                          children: \"AI\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 944,\n                          columnNumber: 41\n                        }, this)]\n                      }, subtitle.language, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 924,\n                        columnNumber: 37\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 883,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 877,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 23\n              }, this) : video.videoID ?\n              /*#__PURE__*/\n              // Fallback to YouTube embed if no videoUrl\n              _jsxDEV(\"iframe\", {\n                src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                title: video.title,\n                frameBorder: \"0\",\n                allowFullScreen: true,\n                className: \"video-iframe\",\n                onLoad: () => console.log('✅ YouTube iframe loaded')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-error\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-icon\",\n                  children: \"\\u26A0\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 973,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Video Unavailable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 974,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: videoError || \"This video cannot be played at the moment.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: video.signedVideoUrl || video.videoUrl,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"external-link-btn\",\n                    children: \"\\uD83D\\uDCF1 Open in New Tab\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 977,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 976,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 19\n            }, this), !isVideoExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-footer\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-description\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Watch this educational video to learn more about \", video.subject, \".\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 25\n                }, this), video.subtitleGenerationStatus === 'processing' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-status\",\n                  style: {\n                    marginTop: '10px',\n                    fontSize: '0.9em',\n                    color: '#2196F3',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '16px',\n                      height: '16px',\n                      border: '2px solid #2196F3',\n                      borderTop: '2px solid transparent',\n                      borderRadius: '50%',\n                      animation: 'spin 1s linear infinite'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1002,\n                    columnNumber: 29\n                  }, this), \"\\uD83E\\uDD16 Generating subtitles...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 991,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 990,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true);\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 774,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(PDFModal, {\n      modalIsOpen: modalIsOpen,\n      closeModal: () => {\n        setModalIsOpen(false);\n        setDocumentUrl(\"\");\n      },\n      documentUrl: documentUrl\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1024,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 461,\n    columnNumber: 5\n  }, this);\n}\n_s(StudyMaterial, \"dZ3Gbn6nY2I1ns1Uyvd5JnsYVa0=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = StudyMaterial;\nexport default StudyMaterial;\nvar _c;\n$RefreshReg$(_c, \"StudyMaterial\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "motion", "AnimatePresence", "getStudyMaterial", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Input", "Loading", "PDFModal", "FaPlayCircle", "FaBook", "FaVideo", "FaFileAlt", "FaGraduationCap", "FaDownload", "FaEye", "FaTimes", "FaChevronDown", "FaSearch", "FaExpand", "FaCompress", "TbVideo", "TbFileText", "TbBook", "TbBookIcon", "TbGraduationCap", "TbSearch", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbPlay", "TbDownload", "TbEye", "TbCalendar", "TbUser", "TbChevronDown", "TbChevronDownIcon", "TbChevronUp", "TbX", "TbAlertTriangle", "TbInfoCircle", "TbCheck", "TbSubtitles", "TbSchool", "TbBooks", "primarySubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StudyMaterial", "_s", "user", "state", "dispatch", "userLevel", "level", "userLevelLower", "toLowerCase", "subjectsList", "console", "log", "allPossibleClasses", "activeTab", "setActiveTab", "selectedClass", "setSelectedClass", "class", "className", "selectedSubject", "setSelectedSubject", "userCurrentClass", "materials", "setMaterials", "isLoading", "setIsLoading", "error", "setError", "showVideoIndices", "setShowVideoIndices", "modalIsOpen", "setModalIsOpen", "documentUrl", "setDocumentUrl", "availableClasses", "setAvailableClasses", "showClassSelector", "setShowClassSelector", "isVideoExpanded", "setIsVideoExpanded", "currentVideoIndex", "setCurrentVideoIndex", "videoError", "setVideoError", "selectedSubtitle", "setSelectedSubtitle", "videoRef", "setVideoRef", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "userClass", "length", "isValidSubject", "includes", "setAvailableClassesForLevel", "fetchMaterials", "normalizedClassName", "toString", "replace", "data", "content", "subject", "res", "status", "success", "handleTabChange", "tab", "handleSubjectChange", "handleClassChange", "toggleClassSelector", "filteredAndSortedMaterials", "filtered", "trim", "searchLower", "filter", "material", "title", "year", "sort", "a", "b", "parseInt", "localeCompare", "reverse", "handleDocumentDownload", "fetch", "then", "response", "blob", "url", "window", "URL", "createObjectURL", "document", "createElement", "href", "download", "split", "pop", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "catch", "handleDocumentPreview", "handleShowVideo", "index", "video", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "handleSubtitleChange", "language", "tracks", "textTracks", "i", "mode", "handleExpandVideo", "handleCollapseVideo", "encodeURIComponent", "method", "headers", "credentials", "ok", "Error", "json", "message", "getThumbnailUrl", "thumbnail", "videoID", "videoId", "match", "handleKeyPress", "event", "key", "addEventListener", "removeEventListener", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toUpperCase", "onClick", "map", "type", "placeholder", "value", "onChange", "e", "target", "coreClass", "isCore", "sharedFromClass", "src", "alt", "onError", "currentTarget", "style", "padding", "background", "borderRadius", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "backgroundColor", "onCanPlay", "onLoadedMetadata", "subtitles", "defaultSubtitle", "find", "sub", "isDefault", "crossOrigin", "subtitle", "kind", "srcLang", "label", "languageName", "default", "color", "textAlign", "rel", "borderTop", "display", "alignItems", "gap", "flexWrap", "fontSize", "fontWeight", "min<PERSON><PERSON><PERSON>", "flex", "border", "cursor", "transition", "isAutoGenerated", "opacity", "frameBorder", "allowFullScreen", "onLoad", "subtitleGenerationStatus", "marginTop", "animation", "closeModal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/StudyMaterial/index.js"], "sourcesContent": ["import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport {\n  FaPlayCircle,\n  FaBook,\n  FaVideo,\n  FaFileAlt,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n} from \"react-icons/fa\";\nimport {\n  TbVideo,\n  TbFileText,\n  TbBook as TbBookIcon,\n  TbGraduationCap,\n  TbSearch,\n  Tb<PERSON><PERSON><PERSON>,\n  Tb<PERSON>ortAscending,\n  Tb<PERSON><PERSON>,\n  TbD<PERSON>load,\n  Tb<PERSON><PERSON>,\n  TbCalendar,\n  Tb<PERSON>ser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  TbAlertTriangle,\n  TbInfoCircle,\n  TbCheck,\n  TbSubtitles,\n  TbSchool,\n  TbBooks\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction StudyMaterial() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = user?.level || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary'\n    ? primarySubjects\n    : userLevelLower === 'secondary'\n      ? secondarySubjects\n      : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary'\n    ? ['1', '2', '3', '4', '5', '6', '7']\n    : userLevelLower === 'secondary'\n      ? ['Form-1', 'Form-2', 'Form-3', 'Form-4']\n      : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"videos\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || user?.className || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = user?.class || user?.className;\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n  const [selectedSubtitle, setSelectedSubtitle] = useState('off');\n  const [videoRef, setVideoRef] = useState(null);\n\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = user?.class || user?.className;\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user?.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user?.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" :\n        selectedClass.toString().replace(\"Form-\", \"\");\n\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject, // This can be \"all\" or a specific subject\n      };\n      if (userLevel) {\n        data.level = userLevel;\n      }\n\n      const res = await getStudyMaterial(data);\n\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = (tab) => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n\n  const handleSubjectChange = (subject) => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n\n  const handleClassChange = (className) => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material =>\n        material.title.toLowerCase().includes(searchLower) ||\n        material.subject.toLowerCase().includes(searchLower) ||\n        (material.year && material.year.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n        // For videos (no year field), sort by creation date or reverse order\n        else if (activeTab === \"videos\") {\n          // Since videos are fetched in reverse order from server, maintain that for \"newest\"\n          return 0; // Keep original order (newest first from server)\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n        // For videos, reverse the order\n        else if (activeTab === \"videos\") {\n          return 0; // Will be reversed after sort\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n    // For videos with \"oldest\" sort, reverse the array\n    if (activeTab === \"videos\" && sortBy === \"oldest\") {\n      filtered.reverse();\n    }\n\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = (documentUrl) => {\n    fetch(documentUrl)\n      .then((response) => response.blob())\n      .then((blob) => {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = documentUrl.split(\"/\").pop();\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      })\n      .catch((error) => {\n        console.error(\"Error downloading the file:\", error);\n      });\n  };\n\n  const handleDocumentPreview = (documentUrl) => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedMaterials[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    setSelectedSubtitle('off');\n    setVideoRef(null);\n  };\n\n  // Handle subtitle selection\n  const handleSubtitleChange = (language) => {\n    setSelectedSubtitle(language);\n\n    if (videoRef) {\n      const tracks = videoRef.textTracks;\n\n      // Disable all tracks first\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'disabled';\n      }\n\n      // Enable selected track\n      if (language !== 'off') {\n        for (let i = 0; i < tracks.length; i++) {\n          if (tracks[i].language === language) {\n            tracks[i].mode = 'showing';\n            break;\n          }\n        }\n      }\n    }\n  };\n\n  const handleExpandVideo = () => {\n    setIsVideoExpanded(true);\n  };\n\n  const handleCollapseVideo = () => {\n    setIsVideoExpanded(false);\n  };\n\n\n\n\n\n\n\n\n\n  // Note: Auto-refresh removed since videos are now uploaded synchronously\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          credentials: 'include'\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.warn('⚠️ Failed to get signed URL, using original:', error.message);\n        return videoUrl;\n      }\n    }\n\n    // For other URLs or if signed URL fails, return as-is\n    return videoUrl;\n  };\n\n\n\n\n\n  // Get appropriate thumbnail URL for video\n  const getThumbnailUrl = (material) => {\n    // If we have a custom thumbnail, use it\n    if (material.thumbnail && material.thumbnail !== \"\" && material.thumbnail !== \"processing\") {\n      return material.thumbnail;\n    }\n\n    // For YouTube videos, extract video ID and use YouTube thumbnail\n    if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n      // Extract YouTube video ID if it's a full URL\n      let videoId = material.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n\n    // For uploaded videos without thumbnails, use a default placeholder\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n  };\n\n  // Keyboard support for video modal\n  useEffect(() => {\n    const handleKeyPress = (event) => {\n      if (showVideoIndices.length > 0) {\n        switch (event.key) {\n          case 'Escape':\n            handleHideVideo();\n            break;\n          case 'f':\n          case 'F':\n            if (!isVideoExpanded) {\n              handleExpandVideo();\n            } else {\n              handleCollapseVideo();\n            }\n            break;\n          default:\n            break;\n        }\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyPress);\n    return () => {\n      document.removeEventListener('keydown', handleKeyPress);\n    };\n  }, [showVideoIndices, isVideoExpanded]);\n\n  return (\n    <div className=\"study-material-modern\">\n      {/* Modern Header */}\n      <div className=\"modern-header\">\n        <div className=\"header-content\">\n          <div className=\"header-main\">\n            <FaGraduationCap className=\"header-icon\" />\n            <div className=\"header-text\">\n              <h1>Study Materials</h1>\n              <p>Access comprehensive learning resources for {userLevel} education</p>\n            </div>\n          </div>\n          <div className=\"level-badge\">\n            {userLevel?.toUpperCase()} LEVEL\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"nav-tabs\">\n        <div className=\"tabs-container\">\n          <button\n            className={`nav-tab ${activeTab === 'videos' ? 'active' : ''}`}\n            onClick={() => handleTabChange('videos')}\n          >\n            <FaVideo className=\"tab-icon\" />\n            <span>Videos</span>\n          </button>\n          <button\n            className={`nav-tab ${activeTab === 'study-notes' ? 'active' : ''}`}\n            onClick={() => handleTabChange('study-notes')}\n          >\n            <FaFileAlt className=\"tab-icon\" />\n            <span>Notes</span>\n          </button>\n          <button\n            className={`nav-tab ${activeTab === 'past-papers' ? 'active' : ''}`}\n            onClick={() => handleTabChange('past-papers')}\n          >\n            <FaFileAlt className=\"tab-icon\" />\n            <span>Past Papers</span>\n          </button>\n          <button\n            className={`nav-tab ${activeTab === 'books' ? 'active' : ''}`}\n            onClick={() => handleTabChange('books')}\n          >\n            <FaBook className=\"tab-icon\" />\n            <span>Books</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Filters Section */}\n      <div className=\"filters-section\">\n        <div className=\"filters-container\">\n          {/* Class Selection */}\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">\n              Class\n              {userCurrentClass && (\n                <span className=\"current-class-display\">\n                  {userLevelLower === 'primary' ? `Class ${userCurrentClass}` : `Form ${userCurrentClass}`}\n                </span>\n              )}\n            </label>\n            <div className=\"class-selector\">\n              <button\n                className=\"class-display-btn\"\n                onClick={toggleClassSelector}\n              >\n                <span>\n                  {selectedClass === 'all' ? 'All Classes' :\n                    userLevelLower === 'primary'\n                      ? `Class ${selectedClass}`\n                      : `Form ${selectedClass}`\n                  }\n                  {selectedClass === userCurrentClass && (\n                    <span className=\"current-badge\">Current</span>\n                  )}\n                </span>\n                <FaChevronDown className={`chevron ${showClassSelector ? 'open' : ''}`} />\n              </button>\n\n              {showClassSelector && (\n                <div className=\"class-dropdown\">\n                  <button\n                    className={`class-option ${selectedClass === 'all' ? 'active' : ''}`}\n                    onClick={() => handleClassChange('all')}\n                  >\n                    All Classes\n                  </button>\n                  {availableClasses.map((className, index) => (\n                    <button\n                      key={index}\n                      className={`class-option ${selectedClass === className ? 'active' : ''} ${className === userCurrentClass ? 'user-current' : ''}`}\n                      onClick={() => handleClassChange(className)}\n                    >\n                      {userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`}\n                      {className === userCurrentClass && (\n                        <span className=\"current-badge\">Your Class</span>\n                      )}\n                    </button>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Subject Filter */}\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">Subject</label>\n            <div className=\"subject-pills\">\n              <button\n                className={`subject-pill ${selectedSubject === 'all' ? 'active' : ''}`}\n                onClick={() => handleSubjectChange('all')}\n              >\n                All Subjects\n              </button>\n              {subjectsList.map((subject, index) => (\n                <button\n                  key={index}\n                  className={`subject-pill ${selectedSubject === subject ? 'active' : ''}`}\n                  onClick={() => handleSubjectChange(subject)}\n                >\n                  {subject}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Search and Sort Section */}\n          <div className=\"search-sort-container\">\n            <div className=\"search-section\">\n              <label className=\"filter-label\">Search Materials</label>\n              <div className=\"search-box\">\n                <FaSearch className=\"search-icon\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search by title, subject, or year...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"search-input\"\n                />\n              </div>\n            </div>\n\n            <div className=\"sort-section\">\n              <label className=\"filter-label\">Sort By</label>\n              <div className=\"sort-selector\">\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  className=\"sort-select\"\n                >\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"oldest\">Oldest First</option>\n                  <option value=\"title\">By Title</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Materials Display */}\n      <div className=\"materials-section\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading materials...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <FaTimes className=\"error-icon\" />\n            <h3>Error Loading Materials</h3>\n            <p>{error}</p>\n            <button\n              className=\"retry-btn\"\n              onClick={() => {\n                setError(null);\n                fetchMaterials();\n              }}\n            >\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedMaterials.length > 0 ? (\n          <div className=\"materials-grid\">\n            {filteredAndSortedMaterials.map((material, index) => (\n              <div key={index} className=\"material-card\">\n                <div className=\"card-header\">\n                  <div className=\"material-type\">\n                    {activeTab === 'videos' && <FaVideo className=\"type-icon\" />}\n                    {activeTab === 'study-notes' && <FaFileAlt className=\"type-icon\" />}\n                    {activeTab === 'past-papers' && <FaFileAlt className=\"type-icon\" />}\n                    {activeTab === 'books' && <FaBook className=\"type-icon\" />}\n                    <span className=\"type-label\">\n                      {activeTab === 'study-notes' ? 'Note' :\n                       activeTab === 'past-papers' ? 'Past Paper' :\n                       activeTab === 'videos' ? 'Video' : 'Book'}\n                    </span>\n                  </div>\n                  <div className=\"header-right\">\n                    {/* Class tags for videos */}\n                    {activeTab === 'videos' && material.coreClass && (\n                      <div className=\"class-tags\">\n                        {material.isCore ? (\n                          <span className=\"class-tag core-class\">\n                            Core Class {userLevelLower === 'primary' ? material.coreClass : `Form ${material.coreClass}`}\n                          </span>\n                        ) : material.sharedFromClass && (\n                          <span className=\"class-tag shared-class\">\n                            Shared from {userLevelLower === 'primary' ? `Class ${material.sharedFromClass}` : `Form ${material.sharedFromClass}`}\n                          </span>\n                        )}\n                      </div>\n                    )}\n                    {material.year && (\n                      <span className=\"material-year\">{material.year}</span>\n                    )}\n                  </div>\n                </div>\n\n                {/* Video Thumbnail for videos */}\n                {activeTab === 'videos' && (material.videoUrl || material.videoID) && (\n                  <div className=\"video-thumbnail-container\" onClick={() => handleShowVideo(index)}>\n                    <img\n                      src={getThumbnailUrl(material)}\n                      alt={material.title}\n                      className=\"video-thumbnail\"\n                      onError={(e) => {\n                        // Fallback logic for failed thumbnails\n                        if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n                          // For YouTube videos, try different quality thumbnails\n                          let videoId = material.videoID;\n                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                          }\n\n                          if (!e.target.src.includes('youtube.com')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n                          } else if (e.target.src.includes('maxresdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;\n                          } else if (e.target.src.includes('mqdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n                          } else {\n                            // Final fallback to default placeholder\n                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                          }\n                        } else {\n                          // For uploaded videos without thumbnails, use default placeholder\n                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                      <span className=\"play-text\">Watch Now</span>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"card-content\">\n                  <h3 className=\"material-title\">{material.title}</h3>\n                  <div className=\"material-meta\">\n                    <span className=\"material-subject\">{material.subject}</span>\n                    {material.className && (\n                      <span className=\"material-class\">\n                        {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"card-actions\">\n                  {activeTab === 'videos' && (material.videoUrl || material.videoID) ? (\n                    <div className=\"video-info-text\">\n                      <span className=\"video-duration\">Click thumbnail to play</span>\n                    </div>\n                  ) : material.documentUrl ? (\n                    <>\n                      <button\n                        className=\"action-btn secondary\"\n                        onClick={() => handleDocumentPreview(material.documentUrl)}\n                      >\n                        <FaEye /> View\n                      </button>\n                      <button\n                        className=\"action-btn primary\"\n                        onClick={() => handleDocumentDownload(material.documentUrl)}\n                      >\n                        <FaDownload /> Download\n                      </button>\n                    </>\n                  ) : (\n                    <span className=\"unavailable\">Not available</span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Materials Found</h3>\n            <p>No study materials are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedMaterials[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <>\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3>{video.title || 'Untitled Video'}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject || 'Unknown Subject'}</span>\n                        <span className=\"video-class\">Class {video.className || 'N/A'}</span>\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      {!isVideoExpanded ? (\n                        <button\n                          className=\"control-btn expand-btn\"\n                          onClick={handleExpandVideo}\n                          title=\"Expand to fullscreen\"\n                        >\n                          <FaExpand />\n                        </button>\n                      ) : (\n                        <button\n                          className=\"control-btn collapse-btn\"\n                          onClick={handleCollapseVideo}\n                          title=\"Exit fullscreen\"\n                        >\n                          <FaCompress />\n                        </button>\n                      )}\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadedMetadata={() => {\n                              // Auto-enable first subtitle if available and none selected\n                              if (video.subtitles && video.subtitles.length > 0 && selectedSubtitle === 'off') {\n                                const defaultSubtitle = video.subtitles.find(sub => sub.isDefault) || video.subtitles[0];\n                                handleSubtitleChange(defaultSubtitle.language);\n                              }\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            <p style={{color: 'white', textAlign: 'center', padding: '20px'}}>\n                              Your browser does not support the video tag.\n                              <br />\n                              <a href={video.signedVideoUrl || video.videoUrl} target=\"_blank\" rel=\"noopener noreferrer\" style={{color: '#4fc3f7'}}>\n                                Click here to open video in new tab\n                              </a>\n                            </p>\n                          </video>\n\n                          {/* Custom Subtitle Controls */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div style={{\n                              padding: '12px 15px',\n                              background: 'rgba(0,0,0,0.8)',\n                              borderRadius: '0 0 8px 8px',\n                              borderTop: '1px solid #333'\n                            }}>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '12px',\n                                flexWrap: 'wrap'\n                              }}>\n                                <span style={{\n                                  color: '#fff',\n                                  fontSize: '14px',\n                                  fontWeight: '500',\n                                  minWidth: 'fit-content'\n                                }}>\n                                  📝 Choose Language:\n                                </span>\n\n                                <div style={{\n                                  display: 'flex',\n                                  gap: '8px',\n                                  flexWrap: 'wrap',\n                                  flex: 1\n                                }}>\n                                  {/* Off Button */}\n                                  <button\n                                    onClick={() => handleSubtitleChange('off')}\n                                    style={{\n                                      padding: '6px 12px',\n                                      borderRadius: '20px',\n                                      border: 'none',\n                                      fontSize: '12px',\n                                      fontWeight: '500',\n                                      cursor: 'pointer',\n                                      transition: 'all 0.2s ease',\n                                      backgroundColor: selectedSubtitle === 'off' ? '#ff4757' : '#2f3542',\n                                      color: '#fff'\n                                    }}\n                                  >\n                                    OFF\n                                  </button>\n\n                                  {/* Language Buttons */}\n                                  {video.subtitles.map((subtitle) => (\n                                    <button\n                                      key={subtitle.language}\n                                      onClick={() => handleSubtitleChange(subtitle.language)}\n                                      style={{\n                                        padding: '6px 12px',\n                                        borderRadius: '20px',\n                                        border: 'none',\n                                        fontSize: '12px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s ease',\n                                        backgroundColor: selectedSubtitle === subtitle.language ? '#2ed573' : '#57606f',\n                                        color: '#fff',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '4px'\n                                      }}\n                                    >\n                                      <span>{subtitle.languageName}</span>\n                                      {subtitle.isAutoGenerated && (\n                                        <span style={{\n                                          fontSize: '10px',\n                                          opacity: 0.8,\n                                          backgroundColor: 'rgba(255,255,255,0.2)',\n                                          padding: '1px 4px',\n                                          borderRadius: '8px'\n                                        }}>\n                                          AI\n                                        </span>\n                                      )}\n                                    </button>\n                                  ))}\n                                </div>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  {!isVideoExpanded && (\n                    <div className=\"video-footer\">\n                      <div className=\"video-description\">\n                        <p>Watch this educational video to learn more about {video.subject}.</p>\n                        {video.subtitleGenerationStatus === 'processing' && (\n                          <div className=\"subtitle-status\" style={{\n                            marginTop: '10px',\n                            fontSize: '0.9em',\n                            color: '#2196F3',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '8px'\n                          }}>\n                            <div style={{\n                              width: '16px',\n                              height: '16px',\n                              border: '2px solid #2196F3',\n                              borderTop: '2px solid transparent',\n                              borderRadius: '50%',\n                              animation: 'spin 1s linear infinite'\n                            }}></div>\n                            🤖 Generating subtitles...\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n\n      {/* PDF Modal */}\n      <PDFModal\n        modalIsOpen={modalIsOpen}\n        closeModal={() => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        }}\n        documentUrl={documentUrl}\n      />\n    </div>\n  );\n}\n\nexport default StudyMaterial;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,4BAA4B;AAEzE,OAAOC,QAAQ,MAAM,YAAY;AACjC,SACEC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,QAAQ,EACRC,UAAU,QACL,gBAAgB;AACvB,SACEC,OAAO,EACPC,UAAU,EACVC,MAAM,IAAIC,UAAU,EACpBC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,aAAa,IAAIC,iBAAiB,EAClCC,WAAW,EACXC,GAAG,EACHC,eAAe,EACfC,YAAY,EACZC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,OAAO,QACF,gBAAgB;AACvB,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjG,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGpD,WAAW,CAAEqD,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGvD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwD,SAAS,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,KAAK,KAAI,SAAS;EAC1C,MAAMC,cAAc,GAAGF,SAAS,CAACG,WAAW,CAAC,CAAC;EAC9C,MAAMC,YAAY,GAAGF,cAAc,KAAK,SAAS,GAC7Cd,eAAe,GACfc,cAAc,KAAK,WAAW,GAC5Bb,iBAAiB,GACjBC,eAAe;;EAErB;EACApD,SAAS,CAAC,MAAM;IACdmE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEN,SAAS,CAAC;IAC1DK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEJ,cAAc,CAAC;IAC3EG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEF,YAAY,CAAC;IAChEC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAET,IAAI,CAAC;EACtD,CAAC,EAAE,CAACG,SAAS,EAAEE,cAAc,EAAEE,YAAY,EAAEP,IAAI,CAAC,CAAC;;EAEnD;EACA,MAAMU,kBAAkB,GAAGL,cAAc,KAAK,SAAS,GACnD,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GACnCA,cAAc,KAAK,WAAW,GAC5B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,GACxC,CAAC,QAAQ,EAAE,QAAQ,CAAC;;EAE1B;EACA,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGxE,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,CAAA4D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS,KAAI,KAAK,CAAC;EAC3F,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM+E,gBAAgB,GAAG,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS;EACvD,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkF,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoF,KAAK,EAAEC,QAAQ,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACsF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACwF,WAAW,EAAEC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0F,WAAW,EAAEC,cAAc,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC8F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnG,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACoG,UAAU,EAAEC,aAAa,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwG,QAAQ,EAAEC,WAAW,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;;EAG9C;EACA,MAAM,CAAC0G,UAAU,EAAEC,aAAa,CAAC,GAAG3G,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4G,MAAM,EAAEC,SAAS,CAAC,GAAG7G,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6G,SAAS,GAAG,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,SAAS;IAChD,IAAIkC,SAAS,IAAIrC,aAAa,KAAK,KAAK,IAAI,CAACmB,gBAAgB,CAACmB,MAAM,EAAE;MACpErC,gBAAgB,CAACoC,SAAS,CAAC;IAC7B;EACF,CAAC,EAAE,CAAClD,IAAI,EAAEa,aAAa,EAAEmB,gBAAgB,CAACmB,MAAM,CAAC,CAAC;;EAElD;EACA9G,SAAS,CAAC,MAAM;IACd,IAAI2D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEI,KAAK,EAAE;MACf;MACA,MAAMgD,cAAc,GAAG7C,YAAY,CAAC8C,QAAQ,CAACpC,eAAe,CAAC;MAC7D,IAAI,CAACmC,cAAc,IAAInC,eAAe,KAAK,KAAK,EAAE;QAChDT,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjES,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAAClB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,KAAK,EAAEG,YAAY,EAAEU,eAAe,CAAC,CAAC;;EAEhD;EACA,MAAMqC,2BAA2B,GAAGhH,WAAW,CAAC,MAAM;IACpD2F,mBAAmB,CAACvB,kBAAkB,CAAC;EACzC,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAM6C,cAAc,GAAGjH,WAAW,CAAC,YAAY;IAC7C,IAAI,CAACqE,SAAS,IAAIE,aAAa,KAAK,SAAS,EAAE;MAC7C;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IACdvB,QAAQ,CAACpD,WAAW,CAAC,CAAC,CAAC;IAEvB,IAAI;MACF;MACA,MAAM0G,mBAAmB,GAAG3C,aAAa,KAAK,KAAK,GAAG,KAAK,GACzDA,aAAa,CAAC4C,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MAE/C,MAAMC,IAAI,GAAG;QACXC,OAAO,EAAEjD,SAAS;QAClBK,SAAS,EAAEwC,mBAAmB;QAC9BK,OAAO,EAAE5C,eAAe,CAAE;MAC5B,CAAC;;MACD,IAAId,SAAS,EAAE;QACbwD,IAAI,CAACvD,KAAK,GAAGD,SAAS;MACxB;MAEA,MAAM2D,GAAG,GAAG,MAAMpH,gBAAgB,CAACiH,IAAI,CAAC;MAExC,IAAIG,GAAG,CAACC,MAAM,KAAK,GAAG,IAAID,GAAG,CAACH,IAAI,CAACK,OAAO,EAAE;QAC1C,MAAM5C,SAAS,GAAG0C,GAAG,CAACH,IAAI,CAACA,IAAI,KAAK,OAAO,GAAG,EAAE,GAAGG,GAAG,CAACH,IAAI,CAACA,IAAI;QAChEtC,YAAY,CAACD,SAAS,CAAC;MACzB,CAAC,MAAM;QACLC,YAAY,CAAC,EAAE,CAAC;QAChBI,QAAQ,CAAE,mBAAkBd,SAAU,qBAAoB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDH,YAAY,CAAC,EAAE,CAAC;MAChBI,QAAQ,CAAE,kBAAiBd,SAAU,+CAA8C,CAAC;IACtF,CAAC,SAAS;MACRY,YAAY,CAAC,KAAK,CAAC;MACnBrB,QAAQ,CAACrD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC8D,SAAS,EAAEE,aAAa,EAAEI,eAAe,EAAEd,SAAS,EAAED,QAAQ,CAAC,CAAC;;EAEpE;EACA7D,SAAS,CAAC,MAAM;IACd,IAAI2D,IAAI,IAAIG,SAAS,EAAE;MACrBmD,2BAA2B,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACtD,IAAI,EAAEG,SAAS,EAAEmD,2BAA2B,CAAC,CAAC;;EAElD;EACAjH,SAAS,CAAC,MAAM;IACd;IACA,IAAI2D,IAAI,IAAIG,SAAS,IAAIQ,SAAS,IAAIE,aAAa,IAAIA,aAAa,KAAK,SAAS,EAAE;MAClF0C,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACvD,IAAI,EAAEG,SAAS,EAAEQ,SAAS,EAAEE,aAAa,EAAEI,eAAe,EAAEsC,cAAc,CAAC,CAAC;;EAEhF;EACA,MAAMU,eAAe,GAAIC,GAAG,IAAK;IAC/B7C,YAAY,CAAC,EAAE,CAAC;IAChBT,YAAY,CAACsD,GAAG,CAAC;IACjBnB,aAAa,CAAC,EAAE,CAAC;IACjBE,SAAS,CAAC,QAAQ,CAAC;EACrB,CAAC;EAED,MAAMkB,mBAAmB,GAAIN,OAAO,IAAK;IACvCxC,YAAY,CAAC,EAAE,CAAC;IAChBH,kBAAkB,CAAC2C,OAAO,CAAC;IAC3Bd,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMqB,iBAAiB,GAAIpD,SAAS,IAAK;IACvCK,YAAY,CAAC,EAAE,CAAC;IAChBP,gBAAgB,CAACE,SAAS,CAAC;IAC3BmB,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMkC,mBAAmB,GAAGA,CAAA,KAAM;IAChClC,oBAAoB,CAAC,CAACD,iBAAiB,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMoC,0BAA0B,GAAG/H,OAAO,CAAC,MAAM;IAC/C,IAAI,CAAC6E,SAAS,IAAIA,SAAS,CAAC+B,MAAM,KAAK,CAAC,EAAE;MACxC,OAAO,EAAE;IACX;IAEA,IAAIoB,QAAQ,GAAGnD,SAAS;;IAExB;IACA,IAAI0B,UAAU,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAG3B,UAAU,CAACxC,WAAW,CAAC,CAAC;MAC5CiE,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IACjCA,QAAQ,CAACC,KAAK,CAACtE,WAAW,CAAC,CAAC,CAAC+C,QAAQ,CAACoB,WAAW,CAAC,IAClDE,QAAQ,CAACd,OAAO,CAACvD,WAAW,CAAC,CAAC,CAAC+C,QAAQ,CAACoB,WAAW,CAAC,IACnDE,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACvE,WAAW,CAAC,CAAC,CAAC+C,QAAQ,CAACoB,WAAW,CACpE,CAAC;IACH;;IAEA;IACAF,QAAQ,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIhC,MAAM,KAAK,QAAQ,EAAE;QACvB;QACA,IAAI+B,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE;UACpB,OAAOI,QAAQ,CAACD,CAAC,CAACH,IAAI,CAAC,GAAGI,QAAQ,CAACF,CAAC,CAACF,IAAI,CAAC;QAC5C;QACA;QAAA,KACK,IAAIlE,SAAS,KAAK,QAAQ,EAAE;UAC/B;UACA,OAAO,CAAC,CAAC,CAAC;QACZ;QACA;QAAA,KACK,IAAIoE,CAAC,CAACF,IAAI,IAAI,CAACG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KACjC,IAAI,CAACE,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,KAChC,OAAO,CAAC;MACf,CAAC,MAAM,IAAI7B,MAAM,KAAK,QAAQ,EAAE;QAC9B;QACA,IAAI+B,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE;UACpB,OAAOI,QAAQ,CAACF,CAAC,CAACF,IAAI,CAAC,GAAGI,QAAQ,CAACD,CAAC,CAACH,IAAI,CAAC;QAC5C;QACA;QAAA,KACK,IAAIlE,SAAS,KAAK,QAAQ,EAAE;UAC/B,OAAO,CAAC,CAAC,CAAC;QACZ;QACA;QAAA,KACK,IAAIoE,CAAC,CAACF,IAAI,IAAI,CAACG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KACjC,IAAI,CAACE,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,KAChC,OAAO,CAAC;MACf,CAAC,MAAM;QACL;QACA,OAAOE,CAAC,CAACH,KAAK,CAACM,aAAa,CAACF,CAAC,CAACJ,KAAK,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACA,IAAIjE,SAAS,KAAK,QAAQ,IAAIqC,MAAM,KAAK,QAAQ,EAAE;MACjDuB,QAAQ,CAACY,OAAO,CAAC,CAAC;IACpB;IAEA,OAAOZ,QAAQ;EACjB,CAAC,EAAE,CAACnD,SAAS,EAAE0B,UAAU,EAAEE,MAAM,EAAErC,SAAS,CAAC,CAAC;;EAE9C;EACA,MAAMyE,sBAAsB,GAAItD,WAAW,IAAK;IAC9CuD,KAAK,CAACvD,WAAW,CAAC,CACfwD,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEE,IAAI,IAAK;MACd,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5C,MAAMT,CAAC,GAAGc,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCf,CAAC,CAACgB,IAAI,GAAGN,GAAG;MACZV,CAAC,CAACiB,QAAQ,GAAGlE,WAAW,CAACmE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MACzCL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACrB,CAAC,CAAC;MAC5BA,CAAC,CAACsB,KAAK,CAAC,CAAC;MACTR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACvB,CAAC,CAAC;MAC5BW,MAAM,CAACC,GAAG,CAACY,eAAe,CAACd,GAAG,CAAC;IACjC,CAAC,CAAC,CACDe,KAAK,CAAEhF,KAAK,IAAK;MAChBhB,OAAO,CAACgB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CAAC;EACN,CAAC;EAED,MAAMiF,qBAAqB,GAAI3E,WAAW,IAAK;IAC7CC,cAAc,CAACD,WAAW,CAAC;IAC3BD,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAM6E,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMC,KAAK,GAAGtC,0BAA0B,CAACqC,KAAK,CAAC;IAE/CpE,oBAAoB,CAACoE,KAAK,CAAC;IAC3BhF,mBAAmB,CAAC,CAACgF,KAAK,CAAC,CAAC;IAC5BtE,kBAAkB,CAAC,KAAK,CAAC;IACzBI,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAImE,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,QAAQ,KAAKD,KAAK,CAACC,QAAQ,CAACxD,QAAQ,CAAC,eAAe,CAAC,IAAIuD,KAAK,CAACC,QAAQ,CAACxD,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAMyD,SAAS,GAAG,MAAMC,iBAAiB,CAACH,KAAK,CAACC,QAAQ,CAAC;QACzDD,KAAK,CAACI,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAOtF,KAAK,EAAE;QACdhB,OAAO,CAACyG,IAAI,CAAC,8CAA8C,CAAC;QAC5DL,KAAK,CAACI,cAAc,GAAGJ,KAAK,CAACC,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BvF,mBAAmB,CAAC,EAAE,CAAC;IACvBY,oBAAoB,CAAC,IAAI,CAAC;IAC1BF,kBAAkB,CAAC,KAAK,CAAC;IACzBI,aAAa,CAAC,IAAI,CAAC;IACnBE,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMsE,oBAAoB,GAAIC,QAAQ,IAAK;IACzCzE,mBAAmB,CAACyE,QAAQ,CAAC;IAE7B,IAAIxE,QAAQ,EAAE;MACZ,MAAMyE,MAAM,GAAGzE,QAAQ,CAAC0E,UAAU;;MAElC;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAClE,MAAM,EAAEoE,CAAC,EAAE,EAAE;QACtCF,MAAM,CAACE,CAAC,CAAC,CAACC,IAAI,GAAG,UAAU;MAC7B;;MAEA;MACA,IAAIJ,QAAQ,KAAK,KAAK,EAAE;QACtB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAClE,MAAM,EAAEoE,CAAC,EAAE,EAAE;UACtC,IAAIF,MAAM,CAACE,CAAC,CAAC,CAACH,QAAQ,KAAKA,QAAQ,EAAE;YACnCC,MAAM,CAACE,CAAC,CAAC,CAACC,IAAI,GAAG,SAAS;YAC1B;UACF;QACF;MACF;IACF;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpF,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMqF,mBAAmB,GAAGA,CAAA,KAAM;IAChCrF,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAUD;;EAEA;EACA,MAAM0E,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAACxD,QAAQ,CAAC,eAAe,CAAC,IAAIwD,QAAQ,CAACxD,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAMkC,QAAQ,GAAG,MAAMF,KAAK,CAAE,6DAA4DsC,kBAAkB,CAACd,QAAQ,CAAE,EAAC,EAAE;UACxHe,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,WAAW,EAAE;QACf,CAAC,CAAC;QAEF,IAAI,CAACvC,QAAQ,CAACwC,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsBzC,QAAQ,CAACxB,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAMJ,IAAI,GAAG,MAAM4B,QAAQ,CAAC0C,IAAI,CAAC,CAAC;QAElC,IAAItE,IAAI,CAACK,OAAO,IAAIL,IAAI,CAACmD,SAAS,EAAE;UAClCtG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAOkD,IAAI,CAACmD,SAAS;QACvB,CAAC,MAAM;UACLtG,OAAO,CAACyG,IAAI,CAAC,+CAA+C,EAAEtD,IAAI,CAAC;UACnE,OAAOkD,QAAQ;QACjB;MACF,CAAC,CAAC,OAAOrF,KAAK,EAAE;QACdhB,OAAO,CAACyG,IAAI,CAAC,8CAA8C,EAAEzF,KAAK,CAAC0G,OAAO,CAAC;QAC3E,OAAOrB,QAAQ;MACjB;IACF;;IAEA;IACA,OAAOA,QAAQ;EACjB,CAAC;;EAMD;EACA,MAAMsB,eAAe,GAAIxD,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,CAACyD,SAAS,IAAIzD,QAAQ,CAACyD,SAAS,KAAK,EAAE,IAAIzD,QAAQ,CAACyD,SAAS,KAAK,YAAY,EAAE;MAC1F,OAAOzD,QAAQ,CAACyD,SAAS;IAC3B;;IAEA;IACA,IAAIzD,QAAQ,CAAC0D,OAAO,IAAI,CAAC1D,QAAQ,CAAC0D,OAAO,CAAChF,QAAQ,CAAC,eAAe,CAAC,EAAE;MACnE;MACA,IAAIiF,OAAO,GAAG3D,QAAQ,CAAC0D,OAAO;MAC9B,IAAIC,OAAO,CAACjF,QAAQ,CAAC,aAAa,CAAC,IAAIiF,OAAO,CAACjF,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAMkF,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;QACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;;IAEA;IACA,OAAO,4cAA4c;EACrd,CAAC;;EAED;EACAjM,SAAS,CAAC,MAAM;IACd,MAAMmM,cAAc,GAAIC,KAAK,IAAK;MAChC,IAAI/G,gBAAgB,CAACyB,MAAM,GAAG,CAAC,EAAE;QAC/B,QAAQsF,KAAK,CAACC,GAAG;UACf,KAAK,QAAQ;YACXxB,eAAe,CAAC,CAAC;YACjB;UACF,KAAK,GAAG;UACR,KAAK,GAAG;YACN,IAAI,CAAC9E,eAAe,EAAE;cACpBqF,iBAAiB,CAAC,CAAC;YACrB,CAAC,MAAM;cACLC,mBAAmB,CAAC,CAAC;YACvB;YACA;UACF;YACE;QACJ;MACF;IACF,CAAC;IAED7B,QAAQ,CAAC8C,gBAAgB,CAAC,SAAS,EAAEH,cAAc,CAAC;IACpD,OAAO,MAAM;MACX3C,QAAQ,CAAC+C,mBAAmB,CAAC,SAAS,EAAEJ,cAAc,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,CAAC9G,gBAAgB,EAAEU,eAAe,CAAC,CAAC;EAEvC,oBACEzC,OAAA;IAAKqB,SAAS,EAAC,uBAAuB;IAAA6H,QAAA,gBAEpClJ,OAAA;MAAKqB,SAAS,EAAC,eAAe;MAAA6H,QAAA,eAC5BlJ,OAAA;QAAKqB,SAAS,EAAC,gBAAgB;QAAA6H,QAAA,gBAC7BlJ,OAAA;UAAKqB,SAAS,EAAC,aAAa;UAAA6H,QAAA,gBAC1BlJ,OAAA,CAACnC,eAAe;YAACwD,SAAS,EAAC;UAAa;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CtJ,OAAA;YAAKqB,SAAS,EAAC,aAAa;YAAA6H,QAAA,gBAC1BlJ,OAAA;cAAAkJ,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBtJ,OAAA;cAAAkJ,QAAA,GAAG,8CAA4C,EAAC1I,SAAS,EAAC,YAAU;YAAA;cAAA2I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtJ,OAAA;UAAKqB,SAAS,EAAC,aAAa;UAAA6H,QAAA,GACzB1I,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE+I,WAAW,CAAC,CAAC,EAAC,QAC5B;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtJ,OAAA;MAAKqB,SAAS,EAAC,UAAU;MAAA6H,QAAA,eACvBlJ,OAAA;QAAKqB,SAAS,EAAC,gBAAgB;QAAA6H,QAAA,gBAC7BlJ,OAAA;UACEqB,SAAS,EAAG,WAAUL,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAG,EAAE;UAC/DwI,OAAO,EAAEA,CAAA,KAAMlF,eAAe,CAAC,QAAQ,CAAE;UAAA4E,QAAA,gBAEzClJ,OAAA,CAACrC,OAAO;YAAC0D,SAAS,EAAC;UAAU;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCtJ,OAAA;YAAAkJ,QAAA,EAAM;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACTtJ,OAAA;UACEqB,SAAS,EAAG,WAAUL,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAG,EAAE;UACpEwI,OAAO,EAAEA,CAAA,KAAMlF,eAAe,CAAC,aAAa,CAAE;UAAA4E,QAAA,gBAE9ClJ,OAAA,CAACpC,SAAS;YAACyD,SAAS,EAAC;UAAU;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClCtJ,OAAA;YAAAkJ,QAAA,EAAM;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACTtJ,OAAA;UACEqB,SAAS,EAAG,WAAUL,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAG,EAAE;UACpEwI,OAAO,EAAEA,CAAA,KAAMlF,eAAe,CAAC,aAAa,CAAE;UAAA4E,QAAA,gBAE9ClJ,OAAA,CAACpC,SAAS;YAACyD,SAAS,EAAC;UAAU;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClCtJ,OAAA;YAAAkJ,QAAA,EAAM;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACTtJ,OAAA;UACEqB,SAAS,EAAG,WAAUL,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG,EAAE;UAC9DwI,OAAO,EAAEA,CAAA,KAAMlF,eAAe,CAAC,OAAO,CAAE;UAAA4E,QAAA,gBAExClJ,OAAA,CAACtC,MAAM;YAAC2D,SAAS,EAAC;UAAU;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BtJ,OAAA;YAAAkJ,QAAA,EAAM;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtJ,OAAA;MAAKqB,SAAS,EAAC,iBAAiB;MAAA6H,QAAA,eAC9BlJ,OAAA;QAAKqB,SAAS,EAAC,mBAAmB;QAAA6H,QAAA,gBAEhClJ,OAAA;UAAKqB,SAAS,EAAC,cAAc;UAAA6H,QAAA,gBAC3BlJ,OAAA;YAAOqB,SAAS,EAAC,cAAc;YAAA6H,QAAA,GAAC,OAE9B,EAAC1H,gBAAgB,iBACfxB,OAAA;cAAMqB,SAAS,EAAC,uBAAuB;cAAA6H,QAAA,EACpCxI,cAAc,KAAK,SAAS,GAAI,SAAQc,gBAAiB,EAAC,GAAI,QAAOA,gBAAiB;YAAC;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACRtJ,OAAA;YAAKqB,SAAS,EAAC,gBAAgB;YAAA6H,QAAA,gBAC7BlJ,OAAA;cACEqB,SAAS,EAAC,mBAAmB;cAC7BmI,OAAO,EAAE9E,mBAAoB;cAAAwE,QAAA,gBAE7BlJ,OAAA;gBAAAkJ,QAAA,GACGhI,aAAa,KAAK,KAAK,GAAG,aAAa,GACtCR,cAAc,KAAK,SAAS,GACvB,SAAQQ,aAAc,EAAC,GACvB,QAAOA,aAAc,EAAC,EAE5BA,aAAa,KAAKM,gBAAgB,iBACjCxB,OAAA;kBAAMqB,SAAS,EAAC,eAAe;kBAAA6H,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACPtJ,OAAA,CAAC/B,aAAa;gBAACoD,SAAS,EAAG,WAAUkB,iBAAiB,GAAG,MAAM,GAAG,EAAG;cAAE;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,EAER/G,iBAAiB,iBAChBvC,OAAA;cAAKqB,SAAS,EAAC,gBAAgB;cAAA6H,QAAA,gBAC7BlJ,OAAA;gBACEqB,SAAS,EAAG,gBAAeH,aAAa,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAG,EAAE;gBACrEsI,OAAO,EAAEA,CAAA,KAAM/E,iBAAiB,CAAC,KAAK,CAAE;gBAAAyE,QAAA,EACzC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACRjH,gBAAgB,CAACoH,GAAG,CAAC,CAACpI,SAAS,EAAE2F,KAAK,kBACrChH,OAAA;gBAEEqB,SAAS,EAAG,gBAAeH,aAAa,KAAKG,SAAS,GAAG,QAAQ,GAAG,EAAG,IAAGA,SAAS,KAAKG,gBAAgB,GAAG,cAAc,GAAG,EAAG,EAAE;gBACjIgI,OAAO,EAAEA,CAAA,KAAM/E,iBAAiB,CAACpD,SAAS,CAAE;gBAAA6H,QAAA,GAE3CxI,cAAc,KAAK,SAAS,GAAI,SAAQW,SAAU,EAAC,GAAI,QAAOA,SAAU,EAAC,EACzEA,SAAS,KAAKG,gBAAgB,iBAC7BxB,OAAA;kBAAMqB,SAAS,EAAC,eAAe;kBAAA6H,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACjD;cAAA,GAPItC,KAAK;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQJ,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtJ,OAAA;UAAKqB,SAAS,EAAC,cAAc;UAAA6H,QAAA,gBAC3BlJ,OAAA;YAAOqB,SAAS,EAAC,cAAc;YAAA6H,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/CtJ,OAAA;YAAKqB,SAAS,EAAC,eAAe;YAAA6H,QAAA,gBAC5BlJ,OAAA;cACEqB,SAAS,EAAG,gBAAeC,eAAe,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAG,EAAE;cACvEkI,OAAO,EAAEA,CAAA,KAAMhF,mBAAmB,CAAC,KAAK,CAAE;cAAA0E,QAAA,EAC3C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACR1I,YAAY,CAAC6I,GAAG,CAAC,CAACvF,OAAO,EAAE8C,KAAK,kBAC/BhH,OAAA;cAEEqB,SAAS,EAAG,gBAAeC,eAAe,KAAK4C,OAAO,GAAG,QAAQ,GAAG,EAAG,EAAE;cACzEsF,OAAO,EAAEA,CAAA,KAAMhF,mBAAmB,CAACN,OAAO,CAAE;cAAAgF,QAAA,EAE3ChF;YAAO,GAJH8C,KAAK;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKJ,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtJ,OAAA;UAAKqB,SAAS,EAAC,uBAAuB;UAAA6H,QAAA,gBACpClJ,OAAA;YAAKqB,SAAS,EAAC,gBAAgB;YAAA6H,QAAA,gBAC7BlJ,OAAA;cAAOqB,SAAS,EAAC,cAAc;cAAA6H,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxDtJ,OAAA;cAAKqB,SAAS,EAAC,YAAY;cAAA6H,QAAA,gBACzBlJ,OAAA,CAAC9B,QAAQ;gBAACmD,SAAS,EAAC;cAAa;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpCtJ,OAAA;gBACE0J,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,sCAAsC;gBAClDC,KAAK,EAAEzG,UAAW;gBAClB0G,QAAQ,EAAGC,CAAC,IAAK1G,aAAa,CAAC0G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CvI,SAAS,EAAC;cAAc;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtJ,OAAA;YAAKqB,SAAS,EAAC,cAAc;YAAA6H,QAAA,gBAC3BlJ,OAAA;cAAOqB,SAAS,EAAC,cAAc;cAAA6H,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/CtJ,OAAA;cAAKqB,SAAS,EAAC,eAAe;cAAA6H,QAAA,eAC5BlJ,OAAA;gBACE4J,KAAK,EAAEvG,MAAO;gBACdwG,QAAQ,EAAGC,CAAC,IAAKxG,SAAS,CAACwG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC3CvI,SAAS,EAAC,aAAa;gBAAA6H,QAAA,gBAEvBlJ,OAAA;kBAAQ4J,KAAK,EAAC,QAAQ;kBAAAV,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CtJ,OAAA;kBAAQ4J,KAAK,EAAC,QAAQ;kBAAAV,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CtJ,OAAA;kBAAQ4J,KAAK,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtJ,OAAA;MAAKqB,SAAS,EAAC,mBAAmB;MAAA6H,QAAA,EAC/BvH,SAAS,gBACR3B,OAAA;QAAKqB,SAAS,EAAC,eAAe;QAAA6H,QAAA,gBAC5BlJ,OAAA;UAAKqB,SAAS,EAAC;QAAiB;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCtJ,OAAA;UAAAkJ,QAAA,EAAG;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,GACJzH,KAAK,gBACP7B,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAA6H,QAAA,gBAC1BlJ,OAAA,CAAChC,OAAO;UAACqD,SAAS,EAAC;QAAY;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClCtJ,OAAA;UAAAkJ,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCtJ,OAAA;UAAAkJ,QAAA,EAAIrH;QAAK;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdtJ,OAAA;UACEqB,SAAS,EAAC,WAAW;UACrBmI,OAAO,EAAEA,CAAA,KAAM;YACb1H,QAAQ,CAAC,IAAI,CAAC;YACd8B,cAAc,CAAC,CAAC;UAClB,CAAE;UAAAsF,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ3E,0BAA0B,CAACnB,MAAM,GAAG,CAAC,gBACvCxD,OAAA;QAAKqB,SAAS,EAAC,gBAAgB;QAAA6H,QAAA,EAC5BvE,0BAA0B,CAAC8E,GAAG,CAAC,CAACzE,QAAQ,EAAEgC,KAAK,kBAC9ChH,OAAA;UAAiBqB,SAAS,EAAC,eAAe;UAAA6H,QAAA,gBACxClJ,OAAA;YAAKqB,SAAS,EAAC,aAAa;YAAA6H,QAAA,gBAC1BlJ,OAAA;cAAKqB,SAAS,EAAC,eAAe;cAAA6H,QAAA,GAC3BlI,SAAS,KAAK,QAAQ,iBAAIhB,OAAA,CAACrC,OAAO;gBAAC0D,SAAS,EAAC;cAAW;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC3DtI,SAAS,KAAK,aAAa,iBAAIhB,OAAA,CAACpC,SAAS;gBAACyD,SAAS,EAAC;cAAW;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAClEtI,SAAS,KAAK,aAAa,iBAAIhB,OAAA,CAACpC,SAAS;gBAACyD,SAAS,EAAC;cAAW;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAClEtI,SAAS,KAAK,OAAO,iBAAIhB,OAAA,CAACtC,MAAM;gBAAC2D,SAAS,EAAC;cAAW;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1DtJ,OAAA;gBAAMqB,SAAS,EAAC,YAAY;gBAAA6H,QAAA,EACzBlI,SAAS,KAAK,aAAa,GAAG,MAAM,GACpCA,SAAS,KAAK,aAAa,GAAG,YAAY,GAC1CA,SAAS,KAAK,QAAQ,GAAG,OAAO,GAAG;cAAM;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtJ,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAA6H,QAAA,GAE1BlI,SAAS,KAAK,QAAQ,IAAIgE,QAAQ,CAACgF,SAAS,iBAC3ChK,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAA6H,QAAA,EACxBlE,QAAQ,CAACiF,MAAM,gBACdjK,OAAA;kBAAMqB,SAAS,EAAC,sBAAsB;kBAAA6H,QAAA,GAAC,aAC1B,EAACxI,cAAc,KAAK,SAAS,GAAGsE,QAAQ,CAACgF,SAAS,GAAI,QAAOhF,QAAQ,CAACgF,SAAU,EAAC;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC,GACLtE,QAAQ,CAACkF,eAAe,iBAC1BlK,OAAA;kBAAMqB,SAAS,EAAC,wBAAwB;kBAAA6H,QAAA,GAAC,cAC3B,EAACxI,cAAc,KAAK,SAAS,GAAI,SAAQsE,QAAQ,CAACkF,eAAgB,EAAC,GAAI,QAAOlF,QAAQ,CAACkF,eAAgB,EAAC;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChH;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EACAtE,QAAQ,CAACE,IAAI,iBACZlF,OAAA;gBAAMqB,SAAS,EAAC,eAAe;gBAAA6H,QAAA,EAAElE,QAAQ,CAACE;cAAI;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CACtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLtI,SAAS,KAAK,QAAQ,KAAKgE,QAAQ,CAACkC,QAAQ,IAAIlC,QAAQ,CAAC0D,OAAO,CAAC,iBAChE1I,OAAA;YAAKqB,SAAS,EAAC,2BAA2B;YAACmI,OAAO,EAAEA,CAAA,KAAMzC,eAAe,CAACC,KAAK,CAAE;YAAAkC,QAAA,gBAC/ElJ,OAAA;cACEmK,GAAG,EAAE3B,eAAe,CAACxD,QAAQ,CAAE;cAC/BoF,GAAG,EAAEpF,QAAQ,CAACC,KAAM;cACpB5D,SAAS,EAAC,iBAAiB;cAC3BgJ,OAAO,EAAGP,CAAC,IAAK;gBACd;gBACA,IAAI9E,QAAQ,CAAC0D,OAAO,IAAI,CAAC1D,QAAQ,CAAC0D,OAAO,CAAChF,QAAQ,CAAC,eAAe,CAAC,EAAE;kBACnE;kBACA,IAAIiF,OAAO,GAAG3D,QAAQ,CAAC0D,OAAO;kBAC9B,IAAIC,OAAO,CAACjF,QAAQ,CAAC,aAAa,CAAC,IAAIiF,OAAO,CAACjF,QAAQ,CAAC,UAAU,CAAC,EAAE;oBACnE,MAAMkF,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;kBACtC;kBAEA,IAAI,CAACmB,CAAC,CAACC,MAAM,CAACI,GAAG,CAACzG,QAAQ,CAAC,aAAa,CAAC,EAAE;oBACzCoG,CAAC,CAACC,MAAM,CAACI,GAAG,GAAI,8BAA6BxB,OAAQ,oBAAmB;kBAC1E,CAAC,MAAM,IAAImB,CAAC,CAACC,MAAM,CAACI,GAAG,CAACzG,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACjDoG,CAAC,CAACC,MAAM,CAACI,GAAG,GAAI,8BAA6BxB,OAAQ,gBAAe;kBACtE,CAAC,MAAM,IAAImB,CAAC,CAACC,MAAM,CAACI,GAAG,CAACzG,QAAQ,CAAC,WAAW,CAAC,EAAE;oBAC7CoG,CAAC,CAACC,MAAM,CAACI,GAAG,GAAI,8BAA6BxB,OAAQ,gBAAe;kBACtE,CAAC,MAAM;oBACL;oBACAmB,CAAC,CAACC,MAAM,CAACI,GAAG,GAAG,4cAA4c;kBAC7d;gBACF,CAAC,MAAM;kBACL;kBACAL,CAAC,CAACC,MAAM,CAACI,GAAG,GAAG,4cAA4c;gBAC7d;cACF;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFtJ,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAA6H,QAAA,gBAC3BlJ,OAAA,CAACvC,YAAY;gBAAC4D,SAAS,EAAC;cAAW;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtCtJ,OAAA;gBAAMqB,SAAS,EAAC,WAAW;gBAAA6H,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDtJ,OAAA;YAAKqB,SAAS,EAAC,cAAc;YAAA6H,QAAA,gBAC3BlJ,OAAA;cAAIqB,SAAS,EAAC,gBAAgB;cAAA6H,QAAA,EAAElE,QAAQ,CAACC;YAAK;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpDtJ,OAAA;cAAKqB,SAAS,EAAC,eAAe;cAAA6H,QAAA,gBAC5BlJ,OAAA;gBAAMqB,SAAS,EAAC,kBAAkB;gBAAA6H,QAAA,EAAElE,QAAQ,CAACd;cAAO;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC3DtE,QAAQ,CAAC3D,SAAS,iBACjBrB,OAAA;gBAAMqB,SAAS,EAAC,gBAAgB;gBAAA6H,QAAA,EAC7BxI,cAAc,KAAK,SAAS,GAAI,SAAQsE,QAAQ,CAAC3D,SAAU,EAAC,GAAI,QAAO2D,QAAQ,CAAC3D,SAAU;cAAC;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtJ,OAAA;YAAKqB,SAAS,EAAC,cAAc;YAAA6H,QAAA,EAC1BlI,SAAS,KAAK,QAAQ,KAAKgE,QAAQ,CAACkC,QAAQ,IAAIlC,QAAQ,CAAC0D,OAAO,CAAC,gBAChE1I,OAAA;cAAKqB,SAAS,EAAC,iBAAiB;cAAA6H,QAAA,eAC9BlJ,OAAA;gBAAMqB,SAAS,EAAC,gBAAgB;gBAAA6H,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,GACJtE,QAAQ,CAAC7C,WAAW,gBACtBnC,OAAA,CAAAE,SAAA;cAAAgJ,QAAA,gBACElJ,OAAA;gBACEqB,SAAS,EAAC,sBAAsB;gBAChCmI,OAAO,EAAEA,CAAA,KAAM1C,qBAAqB,CAAC9B,QAAQ,CAAC7C,WAAW,CAAE;gBAAA+G,QAAA,gBAE3DlJ,OAAA,CAACjC,KAAK;kBAAAoL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SACX;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtJ,OAAA;gBACEqB,SAAS,EAAC,oBAAoB;gBAC9BmI,OAAO,EAAEA,CAAA,KAAM/D,sBAAsB,CAACT,QAAQ,CAAC7C,WAAW,CAAE;gBAAA+G,QAAA,gBAE5DlJ,OAAA,CAAClC,UAAU;kBAAAqL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAChB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT,CAAC,gBAEHtJ,OAAA;cAAMqB,SAAS,EAAC,aAAa;cAAA6H,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAClD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GA7GEtC,KAAK;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8GV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENtJ,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAA6H,QAAA,gBAC1BlJ,OAAA,CAACnC,eAAe;UAACwD,SAAS,EAAC;QAAY;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CtJ,OAAA;UAAAkJ,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BtJ,OAAA;UAAAkJ,QAAA,EAAG;QAA4D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnEtJ,OAAA;UAAGqB,SAAS,EAAC,YAAY;UAAA6H,QAAA,EAAC;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLvH,gBAAgB,CAACyB,MAAM,GAAG,CAAC,IAAIb,iBAAiB,KAAK,IAAI,iBACxD3C,OAAA;MAAKqB,SAAS,EAAG,iBAAgBoB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;MAAC+G,OAAO,EAAGM,CAAC,IAAK;QACpF,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACQ,aAAa,EAAE/C,eAAe,CAAC,CAAC;MACrD,CAAE;MAAA2B,QAAA,eACAlJ,OAAA;QAAKqB,SAAS,EAAG,eAAcoB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;QAAAyG,QAAA,EAChE,CAAC,MAAM;UACN,MAAMjC,KAAK,GAAGtC,0BAA0B,CAAChC,iBAAiB,CAAC;UAC3D,IAAI,CAACsE,KAAK,EAAE,oBAAOjH,OAAA;YAAAkJ,QAAA,EAAK;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;UAE7C,oBACEtJ,OAAA,CAAAE,SAAA;YAAAgJ,QAAA,gBACElJ,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAA6H,QAAA,gBAC3BlJ,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAA6H,QAAA,gBACzBlJ,OAAA;kBAAAkJ,QAAA,EAAKjC,KAAK,CAAChC,KAAK,IAAI;gBAAgB;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CtJ,OAAA;kBAAKqB,SAAS,EAAC,YAAY;kBAAA6H,QAAA,gBACzBlJ,OAAA;oBAAMqB,SAAS,EAAC,eAAe;oBAAA6H,QAAA,EAAEjC,KAAK,CAAC/C,OAAO,IAAI;kBAAiB;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3EtJ,OAAA;oBAAMqB,SAAS,EAAC,aAAa;oBAAA6H,QAAA,GAAC,QAAM,EAACjC,KAAK,CAAC5F,SAAS,IAAI,KAAK;kBAAA;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtJ,OAAA;gBAAKqB,SAAS,EAAC,gBAAgB;gBAAA6H,QAAA,GAC5B,CAACzG,eAAe,gBACfzC,OAAA;kBACEqB,SAAS,EAAC,wBAAwB;kBAClCmI,OAAO,EAAE1B,iBAAkB;kBAC3B7C,KAAK,EAAC,sBAAsB;kBAAAiE,QAAA,eAE5BlJ,OAAA,CAAC7B,QAAQ;oBAAAgL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAETtJ,OAAA;kBACEqB,SAAS,EAAC,0BAA0B;kBACpCmI,OAAO,EAAEzB,mBAAoB;kBAC7B9C,KAAK,EAAC,iBAAiB;kBAAAiE,QAAA,eAEvBlJ,OAAA,CAAC5B,UAAU;oBAAA+K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CACT,eACDtJ,OAAA;kBACEqB,SAAS,EAAC,uBAAuB;kBACjCmI,OAAO,EAAEjC,eAAgB;kBACzBtC,KAAK,EAAC,aAAa;kBAAAiE,QAAA,eAEnBlJ,OAAA,CAAChC,OAAO;oBAAAmL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtJ,OAAA;cAAKqB,SAAS,EAAC,iBAAiB;cAAA6H,QAAA,EAC7BjC,KAAK,CAACC,QAAQ,gBACblH,OAAA;gBAAKuK,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAAxB,QAAA,gBACrElJ,OAAA;kBACE2K,GAAG,EAAGA,GAAG,IAAKzH,WAAW,CAACyH,GAAG,CAAE;kBAC/BC,QAAQ;kBACRC,QAAQ;kBACRC,WAAW;kBACXC,OAAO,EAAC,UAAU;kBAClBC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,KAAK;kBACZC,MAAM,EAAE1C,eAAe,CAACvB,KAAK,CAAE;kBAC/BsD,KAAK,EAAE;oBACLS,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,OAAO;oBACfE,eAAe,EAAE;kBACnB,CAAE;kBACFd,OAAO,EAAGP,CAAC,IAAK;oBACdhH,aAAa,CAAE,yBAAwBmE,KAAK,CAAChC,KAAM,mCAAkC,CAAC;kBACxF,CAAE;kBACFmG,SAAS,EAAEA,CAAA,KAAM;oBACftI,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBACFuI,gBAAgB,EAAEA,CAAA,KAAM;oBACtB;oBACA,IAAIpE,KAAK,CAACqE,SAAS,IAAIrE,KAAK,CAACqE,SAAS,CAAC9H,MAAM,GAAG,CAAC,IAAIT,gBAAgB,KAAK,KAAK,EAAE;sBAC/E,MAAMwI,eAAe,GAAGtE,KAAK,CAACqE,SAAS,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC,IAAIzE,KAAK,CAACqE,SAAS,CAAC,CAAC,CAAC;sBACxF9D,oBAAoB,CAAC+D,eAAe,CAAC9D,QAAQ,CAAC;oBAChD;kBACF,CAAE;kBACFkE,WAAW,EAAC,WAAW;kBAAAzC,QAAA,gBAGvBlJ,OAAA;oBAAQmK,GAAG,EAAElD,KAAK,CAACI,cAAc,IAAIJ,KAAK,CAACC,QAAS;oBAACwC,IAAI,EAAC;kBAAW;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAGvErC,KAAK,CAACqE,SAAS,IAAIrE,KAAK,CAACqE,SAAS,CAAC9H,MAAM,GAAG,CAAC,IAAIyD,KAAK,CAACqE,SAAS,CAAC7B,GAAG,CAAC,CAACmC,QAAQ,EAAE5E,KAAK,kBACpFhH,OAAA;oBAEE6L,IAAI,EAAC,WAAW;oBAChB1B,GAAG,EAAEyB,QAAQ,CAAC9F,GAAI;oBAClBgG,OAAO,EAAEF,QAAQ,CAACnE,QAAS;oBAC3BsE,KAAK,EAAEH,QAAQ,CAACI,YAAa;oBAC7BC,OAAO,EAAEL,QAAQ,CAACF,SAAS,IAAI1E,KAAK,KAAK;kBAAE,GALrC,GAAE4E,QAAQ,CAACnE,QAAS,IAAGT,KAAM,EAAC;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMrC,CACF,CAAC,eAEFtJ,OAAA;oBAAGuK,KAAK,EAAE;sBAAC2B,KAAK,EAAE,OAAO;sBAAEC,SAAS,EAAE,QAAQ;sBAAE3B,OAAO,EAAE;oBAAM,CAAE;oBAAAtB,QAAA,GAAC,8CAEhE,eAAAlJ,OAAA;sBAAAmJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtJ,OAAA;sBAAGoG,IAAI,EAAEa,KAAK,CAACI,cAAc,IAAIJ,KAAK,CAACC,QAAS;sBAAC6C,MAAM,EAAC,QAAQ;sBAACqC,GAAG,EAAC,qBAAqB;sBAAC7B,KAAK,EAAE;wBAAC2B,KAAK,EAAE;sBAAS,CAAE;sBAAAhD,QAAA,EAAC;oBAEtH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAGPrC,KAAK,CAACqE,SAAS,IAAIrE,KAAK,CAACqE,SAAS,CAAC9H,MAAM,GAAG,CAAC,iBAC5CxD,OAAA;kBAAKuK,KAAK,EAAE;oBACVC,OAAO,EAAE,WAAW;oBACpBC,UAAU,EAAE,iBAAiB;oBAC7BC,YAAY,EAAE,aAAa;oBAC3B2B,SAAS,EAAE;kBACb,CAAE;kBAAAnD,QAAA,eACAlJ,OAAA;oBAAKuK,KAAK,EAAE;sBACV+B,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,GAAG,EAAE,MAAM;sBACXC,QAAQ,EAAE;oBACZ,CAAE;oBAAAvD,QAAA,gBACAlJ,OAAA;sBAAMuK,KAAK,EAAE;wBACX2B,KAAK,EAAE,MAAM;wBACbQ,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,KAAK;wBACjBC,QAAQ,EAAE;sBACZ,CAAE;sBAAA1D,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAEPtJ,OAAA;sBAAKuK,KAAK,EAAE;wBACV+B,OAAO,EAAE,MAAM;wBACfE,GAAG,EAAE,KAAK;wBACVC,QAAQ,EAAE,MAAM;wBAChBI,IAAI,EAAE;sBACR,CAAE;sBAAA3D,QAAA,gBAEAlJ,OAAA;wBACEwJ,OAAO,EAAEA,CAAA,KAAMhC,oBAAoB,CAAC,KAAK,CAAE;wBAC3C+C,KAAK,EAAE;0BACLC,OAAO,EAAE,UAAU;0BACnBE,YAAY,EAAE,MAAM;0BACpBoC,MAAM,EAAE,MAAM;0BACdJ,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE,KAAK;0BACjBI,MAAM,EAAE,SAAS;0BACjBC,UAAU,EAAE,eAAe;0BAC3B7B,eAAe,EAAEpI,gBAAgB,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;0BACnEmJ,KAAK,EAAE;wBACT,CAAE;wBAAAhD,QAAA,EACH;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EAGRrC,KAAK,CAACqE,SAAS,CAAC7B,GAAG,CAAEmC,QAAQ,iBAC5B5L,OAAA;wBAEEwJ,OAAO,EAAEA,CAAA,KAAMhC,oBAAoB,CAACoE,QAAQ,CAACnE,QAAQ,CAAE;wBACvD8C,KAAK,EAAE;0BACLC,OAAO,EAAE,UAAU;0BACnBE,YAAY,EAAE,MAAM;0BACpBoC,MAAM,EAAE,MAAM;0BACdJ,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE,KAAK;0BACjBI,MAAM,EAAE,SAAS;0BACjBC,UAAU,EAAE,eAAe;0BAC3B7B,eAAe,EAAEpI,gBAAgB,KAAK6I,QAAQ,CAACnE,QAAQ,GAAG,SAAS,GAAG,SAAS;0BAC/EyE,KAAK,EAAE,MAAM;0BACbI,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,GAAG,EAAE;wBACP,CAAE;wBAAAtD,QAAA,gBAEFlJ,OAAA;0BAAAkJ,QAAA,EAAO0C,QAAQ,CAACI;wBAAY;0BAAA7C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACnCsC,QAAQ,CAACqB,eAAe,iBACvBjN,OAAA;0BAAMuK,KAAK,EAAE;4BACXmC,QAAQ,EAAE,MAAM;4BAChBQ,OAAO,EAAE,GAAG;4BACZ/B,eAAe,EAAE,uBAAuB;4BACxCX,OAAO,EAAE,SAAS;4BAClBE,YAAY,EAAE;0BAChB,CAAE;0BAAAxB,QAAA,EAAC;wBAEH;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA,GA5BIsC,QAAQ,CAACnE,QAAQ;wBAAA0B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA6BhB,CACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,GACNrC,KAAK,CAACyB,OAAO;cAAA;cACf;cACA1I,OAAA;gBACEmK,GAAG,EAAG,iCAAgClD,KAAK,CAACyB,OAAQ,mBAAmB;gBACvEzD,KAAK,EAAEgC,KAAK,CAAChC,KAAM;gBACnBkI,WAAW,EAAC,GAAG;gBACfC,eAAe;gBACf/L,SAAS,EAAC,cAAc;gBACxBgM,MAAM,EAAEA,CAAA,KAAMxM,OAAO,CAACC,GAAG,CAAC,yBAAyB;cAAE;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,gBAEVtJ,OAAA;gBAAKqB,SAAS,EAAC,aAAa;gBAAA6H,QAAA,gBAC1BlJ,OAAA;kBAAKqB,SAAS,EAAC,YAAY;kBAAA6H,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpCtJ,OAAA;kBAAAkJ,QAAA,EAAI;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BtJ,OAAA;kBAAAkJ,QAAA,EAAIrG,UAAU,IAAI;gBAA4C;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEtJ,OAAA;kBAAKqB,SAAS,EAAC,eAAe;kBAAA6H,QAAA,eAC5BlJ,OAAA;oBACEoG,IAAI,EAAEa,KAAK,CAACI,cAAc,IAAIJ,KAAK,CAACC,QAAS;oBAC7C6C,MAAM,EAAC,QAAQ;oBACfqC,GAAG,EAAC,qBAAqB;oBACzB/K,SAAS,EAAC,mBAAmB;oBAAA6H,QAAA,EAC9B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACL,CAAC7G,eAAe,iBACfzC,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAA6H,QAAA,eAC3BlJ,OAAA;gBAAKqB,SAAS,EAAC,mBAAmB;gBAAA6H,QAAA,gBAChClJ,OAAA;kBAAAkJ,QAAA,GAAG,mDAAiD,EAACjC,KAAK,CAAC/C,OAAO,EAAC,GAAC;gBAAA;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EACvErC,KAAK,CAACqG,wBAAwB,KAAK,YAAY,iBAC9CtN,OAAA;kBAAKqB,SAAS,EAAC,iBAAiB;kBAACkJ,KAAK,EAAE;oBACtCgD,SAAS,EAAE,MAAM;oBACjBb,QAAQ,EAAE,OAAO;oBACjBR,KAAK,EAAE,SAAS;oBAChBI,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,GAAG,EAAE;kBACP,CAAE;kBAAAtD,QAAA,gBACAlJ,OAAA;oBAAKuK,KAAK,EAAE;sBACVS,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACd6B,MAAM,EAAE,mBAAmB;sBAC3BT,SAAS,EAAE,uBAAuB;sBAClC3B,YAAY,EAAE,KAAK;sBACnB8C,SAAS,EAAE;oBACb;kBAAE;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,wCAEX;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,eACD,CAAC;QAEP,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDtJ,OAAA,CAACxC,QAAQ;MACPyE,WAAW,EAAEA,WAAY;MACzBwL,UAAU,EAAEA,CAAA,KAAM;QAChBvL,cAAc,CAAC,KAAK,CAAC;QACrBE,cAAc,CAAC,EAAE,CAAC;MACpB,CAAE;MACFD,WAAW,EAAEA;IAAY;MAAAgH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAClJ,EAAA,CAz9BQD,aAAa;EAAA,QACHlD,WAAW,EACXD,WAAW;AAAA;AAAA0Q,EAAA,GAFrBvN,aAAa;AA29BtB,eAAeA,aAAa;AAAC,IAAAuN,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}