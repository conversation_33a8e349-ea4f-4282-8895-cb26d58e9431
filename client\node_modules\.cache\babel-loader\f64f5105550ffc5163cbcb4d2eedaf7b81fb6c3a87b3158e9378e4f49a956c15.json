{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = scopeTab;\nvar _tabbable = require(\"./tabbable\");\nvar _tabbable2 = _interopRequireDefault(_tabbable);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction getActiveElement() {\n  var el = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  return el.activeElement.shadowRoot ? getActiveElement(el.activeElement.shadowRoot) : el.activeElement;\n}\nfunction scopeTab(node, event) {\n  var tabbable = (0, _tabbable2.default)(node);\n  if (!tabbable.length) {\n    // Do nothing, since there are no elements that can receive focus.\n    event.preventDefault();\n    return;\n  }\n  var target = void 0;\n  var shiftKey = event.shiftKey;\n  var head = tabbable[0];\n  var tail = tabbable[tabbable.length - 1];\n  var activeElement = getActiveElement();\n\n  // proceed with default browser behavior on tab.\n  // Focus on last element on shift + tab.\n  if (node === activeElement) {\n    if (!shiftKey) return;\n    target = tail;\n  }\n  if (tail === activeElement && !shiftKey) {\n    target = head;\n  }\n  if (head === activeElement && shiftKey) {\n    target = tail;\n  }\n  if (target) {\n    event.preventDefault();\n    target.focus();\n    return;\n  }\n\n  // Safari radio issue.\n  //\n  // Safari does not move the focus to the radio button,\n  // so we need to force it to really walk through all elements.\n  //\n  // This is very error prone, since we are trying to guess\n  // if it is a safari browser from the first occurence between\n  // chrome or safari.\n  //\n  // The chrome user agent contains the first ocurrence\n  // as the 'chrome/version' and later the 'safari/version'.\n  var checkSafari = /(\\bChrome\\b|\\bSafari\\b)\\//.exec(navigator.userAgent);\n  var isSafariDesktop = checkSafari != null && checkSafari[1] != \"Chrome\" && /\\biPod\\b|\\biPad\\b/g.exec(navigator.userAgent) == null;\n\n  // If we are not in safari desktop, let the browser control\n  // the focus\n  if (!isSafariDesktop) return;\n  var x = tabbable.indexOf(activeElement);\n  if (x > -1) {\n    x += shiftKey ? -1 : 1;\n  }\n  target = tabbable[x];\n\n  // If the tabbable element does not exist,\n  // focus head/tail based on shiftKey\n  if (typeof target === \"undefined\") {\n    event.preventDefault();\n    target = shiftKey ? tail : head;\n    target.focus();\n    return;\n  }\n  event.preventDefault();\n  target.focus();\n}\nmodule.exports = exports[\"default\"];", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "scopeTab", "_tabbable", "require", "_tabbable2", "_interopRequireDefault", "obj", "__esModule", "getActiveElement", "el", "arguments", "length", "undefined", "document", "activeElement", "shadowRoot", "node", "event", "tabbable", "preventDefault", "target", "shift<PERSON>ey", "head", "tail", "focus", "checkSafari", "exec", "navigator", "userAgent", "isSafariDesktop", "x", "indexOf", "module"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/react-modal/lib/helpers/scopeTab.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = scopeTab;\n\nvar _tabbable = require(\"./tabbable\");\n\nvar _tabbable2 = _interopRequireDefault(_tabbable);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction getActiveElement() {\n  var el = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n\n  return el.activeElement.shadowRoot ? getActiveElement(el.activeElement.shadowRoot) : el.activeElement;\n}\n\nfunction scopeTab(node, event) {\n  var tabbable = (0, _tabbable2.default)(node);\n\n  if (!tabbable.length) {\n    // Do nothing, since there are no elements that can receive focus.\n    event.preventDefault();\n    return;\n  }\n\n  var target = void 0;\n\n  var shiftKey = event.shiftKey;\n  var head = tabbable[0];\n  var tail = tabbable[tabbable.length - 1];\n  var activeElement = getActiveElement();\n\n  // proceed with default browser behavior on tab.\n  // Focus on last element on shift + tab.\n  if (node === activeElement) {\n    if (!shiftKey) return;\n    target = tail;\n  }\n\n  if (tail === activeElement && !shiftKey) {\n    target = head;\n  }\n\n  if (head === activeElement && shiftKey) {\n    target = tail;\n  }\n\n  if (target) {\n    event.preventDefault();\n    target.focus();\n    return;\n  }\n\n  // Safari radio issue.\n  //\n  // Safari does not move the focus to the radio button,\n  // so we need to force it to really walk through all elements.\n  //\n  // This is very error prone, since we are trying to guess\n  // if it is a safari browser from the first occurence between\n  // chrome or safari.\n  //\n  // The chrome user agent contains the first ocurrence\n  // as the 'chrome/version' and later the 'safari/version'.\n  var checkSafari = /(\\bChrome\\b|\\bSafari\\b)\\//.exec(navigator.userAgent);\n  var isSafariDesktop = checkSafari != null && checkSafari[1] != \"Chrome\" && /\\biPod\\b|\\biPad\\b/g.exec(navigator.userAgent) == null;\n\n  // If we are not in safari desktop, let the browser control\n  // the focus\n  if (!isSafariDesktop) return;\n\n  var x = tabbable.indexOf(activeElement);\n\n  if (x > -1) {\n    x += shiftKey ? -1 : 1;\n  }\n\n  target = tabbable[x];\n\n  // If the tabbable element does not exist,\n  // focus head/tail based on shiftKey\n  if (typeof target === \"undefined\") {\n    event.preventDefault();\n    target = shiftKey ? tail : head;\n    target.focus();\n    return;\n  }\n\n  event.preventDefault();\n\n  target.focus();\n}\nmodule.exports = exports[\"default\"];"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AAErC,IAAIC,UAAU,GAAGC,sBAAsB,CAACH,SAAS,CAAC;AAElD,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASE,gBAAgBA,CAAA,EAAG;EAC1B,IAAIC,EAAE,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGG,QAAQ;EAErF,OAAOJ,EAAE,CAACK,aAAa,CAACC,UAAU,GAAGP,gBAAgB,CAACC,EAAE,CAACK,aAAa,CAACC,UAAU,CAAC,GAAGN,EAAE,CAACK,aAAa;AACvG;AAEA,SAASb,QAAQA,CAACe,IAAI,EAAEC,KAAK,EAAE;EAC7B,IAAIC,QAAQ,GAAG,CAAC,CAAC,EAAEd,UAAU,CAACJ,OAAO,EAAEgB,IAAI,CAAC;EAE5C,IAAI,CAACE,QAAQ,CAACP,MAAM,EAAE;IACpB;IACAM,KAAK,CAACE,cAAc,CAAC,CAAC;IACtB;EACF;EAEA,IAAIC,MAAM,GAAG,KAAK,CAAC;EAEnB,IAAIC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;EAC7B,IAAIC,IAAI,GAAGJ,QAAQ,CAAC,CAAC,CAAC;EACtB,IAAIK,IAAI,GAAGL,QAAQ,CAACA,QAAQ,CAACP,MAAM,GAAG,CAAC,CAAC;EACxC,IAAIG,aAAa,GAAGN,gBAAgB,CAAC,CAAC;;EAEtC;EACA;EACA,IAAIQ,IAAI,KAAKF,aAAa,EAAE;IAC1B,IAAI,CAACO,QAAQ,EAAE;IACfD,MAAM,GAAGG,IAAI;EACf;EAEA,IAAIA,IAAI,KAAKT,aAAa,IAAI,CAACO,QAAQ,EAAE;IACvCD,MAAM,GAAGE,IAAI;EACf;EAEA,IAAIA,IAAI,KAAKR,aAAa,IAAIO,QAAQ,EAAE;IACtCD,MAAM,GAAGG,IAAI;EACf;EAEA,IAAIH,MAAM,EAAE;IACVH,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBC,MAAM,CAACI,KAAK,CAAC,CAAC;IACd;EACF;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,WAAW,GAAG,2BAA2B,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;EACvE,IAAIC,eAAe,GAAGJ,WAAW,IAAI,IAAI,IAAIA,WAAW,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,oBAAoB,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,IAAI;;EAEjI;EACA;EACA,IAAI,CAACC,eAAe,EAAE;EAEtB,IAAIC,CAAC,GAAGZ,QAAQ,CAACa,OAAO,CAACjB,aAAa,CAAC;EAEvC,IAAIgB,CAAC,GAAG,CAAC,CAAC,EAAE;IACVA,CAAC,IAAIT,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;EACxB;EAEAD,MAAM,GAAGF,QAAQ,CAACY,CAAC,CAAC;;EAEpB;EACA;EACA,IAAI,OAAOV,MAAM,KAAK,WAAW,EAAE;IACjCH,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBC,MAAM,GAAGC,QAAQ,GAAGE,IAAI,GAAGD,IAAI;IAC/BF,MAAM,CAACI,KAAK,CAAC,CAAC;IACd;EACF;EAEAP,KAAK,CAACE,cAAc,CAAC,CAAC;EAEtBC,MAAM,CAACI,KAAK,CAAC,CAAC;AAChB;AACAQ,MAAM,CAAClC,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}