{"ast": null, "code": "import useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nexport default function useFixedInfo(flattenColumns, stickyOffsets, direction, columns) {\n  var fixedInfoList = flattenColumns.map(function (_, colIndex) {\n    return getCellFixedInfo(colIndex, colIndex, flattenColumns, stickyOffsets, direction, columns === null || columns === void 0 ? void 0 : columns[colIndex]);\n  });\n  return useMemo(function () {\n    return fixedInfoList;\n  }, [fixedInfoList], function (prev, next) {\n    return !isEqual(prev, next);\n  });\n}", "map": {"version": 3, "names": ["useMemo", "isEqual", "getCellFixedInfo", "useFixedInfo", "flattenColumns", "stickyOffsets", "direction", "columns", "fixedInfoList", "map", "_", "colIndex", "prev", "next"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/hooks/useFixedInfo.js"], "sourcesContent": ["import useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nexport default function useFixedInfo(flattenColumns, stickyOffsets, direction, columns) {\n  var fixedInfoList = flattenColumns.map(function (_, colIndex) {\n    return getCellFixedInfo(colIndex, colIndex, flattenColumns, stickyOffsets, direction, columns === null || columns === void 0 ? void 0 : columns[colIndex]);\n  });\n  return useMemo(function () {\n    return fixedInfoList;\n  }, [fixedInfoList], function (prev, next) {\n    return !isEqual(prev, next);\n  });\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,eAAe,SAASC,YAAYA,CAACC,cAAc,EAAEC,aAAa,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACtF,IAAIC,aAAa,GAAGJ,cAAc,CAACK,GAAG,CAAC,UAAUC,CAAC,EAAEC,QAAQ,EAAE;IAC5D,OAAOT,gBAAgB,CAACS,QAAQ,EAAEA,QAAQ,EAAEP,cAAc,EAAEC,aAAa,EAAEC,SAAS,EAAEC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACI,QAAQ,CAAC,CAAC;EAC5J,CAAC,CAAC;EACF,OAAOX,OAAO,CAAC,YAAY;IACzB,OAAOQ,aAAa;EACtB,CAAC,EAAE,CAACA,aAAa,CAAC,EAAE,UAAUI,IAAI,EAAEC,IAAI,EAAE;IACxC,OAAO,CAACZ,OAAO,CAACW,IAAI,EAAEC,IAAI,CAAC;EAC7B,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}