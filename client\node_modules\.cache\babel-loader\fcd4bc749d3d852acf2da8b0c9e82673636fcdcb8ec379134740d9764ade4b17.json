{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"label\", \"children\", \"key\", \"type\"];\nimport * as React from 'react';\nimport MenuItemGroup from \"../MenuItemGroup\";\nimport SubMenu from \"../SubMenu\";\nimport Divider from \"../Divider\";\nimport MenuItem from \"../MenuItem\";\nimport { parseChildren } from \"./commonUtil\";\nfunction convertItemsToNodes(list) {\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var _ref = opt,\n        label = _ref.label,\n        children = _ref.children,\n        key = _ref.key,\n        type = _ref.type,\n        restProps = _objectWithoutProperties(_ref, _excluded);\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index);\n\n      // MenuItemGroup & SubMenuItem\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(MenuItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children));\n        }\n\n        // Sub Menu\n        return /*#__PURE__*/React.createElement(SubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children));\n      }\n\n      // MenuItem & Divider\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(Divider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n      return /*#__PURE__*/React.createElement(MenuItem, _extends({\n        key: mergedKey\n      }, restProps), label);\n    }\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n}\nexport function parseItems(children, items, keyPath) {\n  var childNodes = children;\n  if (items) {\n    childNodes = convertItemsToNodes(items);\n  }\n  return parseChildren(childNodes, keyPath);\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_typeof", "_excluded", "React", "MenuItemGroup", "SubMenu", "Divider", "MenuItem", "parse<PERSON><PERSON><PERSON>n", "convertItemsToNodes", "list", "map", "opt", "index", "_ref", "label", "children", "key", "type", "restProps", "mergedKey", "concat", "createElement", "title", "filter", "parseItems", "items", "keyP<PERSON>", "childNodes"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-menu/es/utils/nodeUtil.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"label\", \"children\", \"key\", \"type\"];\nimport * as React from 'react';\nimport MenuItemGroup from \"../MenuItemGroup\";\nimport SubMenu from \"../SubMenu\";\nimport Divider from \"../Divider\";\nimport MenuItem from \"../MenuItem\";\nimport { parseChildren } from \"./commonUtil\";\nfunction convertItemsToNodes(list) {\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var _ref = opt,\n        label = _ref.label,\n        children = _ref.children,\n        key = _ref.key,\n        type = _ref.type,\n        restProps = _objectWithoutProperties(_ref, _excluded);\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index);\n\n      // MenuItemGroup & SubMenuItem\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(MenuItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children));\n        }\n\n        // Sub Menu\n        return /*#__PURE__*/React.createElement(SubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children));\n      }\n\n      // MenuItem & Divider\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(Divider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n      return /*#__PURE__*/React.createElement(MenuItem, _extends({\n        key: mergedKey\n      }, restProps), label);\n    }\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n}\nexport function parseItems(children, items, keyPath) {\n  var childNodes = children;\n  if (items) {\n    childNodes = convertItemsToNodes(items);\n  }\n  return parseChildren(childNodes, keyPath);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,SAAS,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACjC,OAAO,CAACA,IAAI,IAAI,EAAE,EAAEC,GAAG,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAC5C,IAAID,GAAG,IAAIX,OAAO,CAACW,GAAG,CAAC,KAAK,QAAQ,EAAE;MACpC,IAAIE,IAAI,GAAGF,GAAG;QACZG,KAAK,GAAGD,IAAI,CAACC,KAAK;QAClBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;QACxBC,GAAG,GAAGH,IAAI,CAACG,GAAG;QACdC,IAAI,GAAGJ,IAAI,CAACI,IAAI;QAChBC,SAAS,GAAGnB,wBAAwB,CAACc,IAAI,EAAEZ,SAAS,CAAC;MACvD,IAAIkB,SAAS,GAAGH,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,MAAM,CAACI,MAAM,CAACR,KAAK,CAAC;;MAE3E;MACA,IAAIG,QAAQ,IAAIE,IAAI,KAAK,OAAO,EAAE;QAChC,IAAIA,IAAI,KAAK,OAAO,EAAE;UACpB;UACA,OAAO,aAAaf,KAAK,CAACmB,aAAa,CAAClB,aAAa,EAAEL,QAAQ,CAAC;YAC9DkB,GAAG,EAAEG;UACP,CAAC,EAAED,SAAS,EAAE;YACZI,KAAK,EAAER;UACT,CAAC,CAAC,EAAEN,mBAAmB,CAACO,QAAQ,CAAC,CAAC;QACpC;;QAEA;QACA,OAAO,aAAab,KAAK,CAACmB,aAAa,CAACjB,OAAO,EAAEN,QAAQ,CAAC;UACxDkB,GAAG,EAAEG;QACP,CAAC,EAAED,SAAS,EAAE;UACZI,KAAK,EAAER;QACT,CAAC,CAAC,EAAEN,mBAAmB,CAACO,QAAQ,CAAC,CAAC;MACpC;;MAEA;MACA,IAAIE,IAAI,KAAK,SAAS,EAAE;QACtB,OAAO,aAAaf,KAAK,CAACmB,aAAa,CAAChB,OAAO,EAAEP,QAAQ,CAAC;UACxDkB,GAAG,EAAEG;QACP,CAAC,EAAED,SAAS,CAAC,CAAC;MAChB;MACA,OAAO,aAAahB,KAAK,CAACmB,aAAa,CAACf,QAAQ,EAAER,QAAQ,CAAC;QACzDkB,GAAG,EAAEG;MACP,CAAC,EAAED,SAAS,CAAC,EAAEJ,KAAK,CAAC;IACvB;IACA,OAAO,IAAI;EACb,CAAC,CAAC,CAACS,MAAM,CAAC,UAAUZ,GAAG,EAAE;IACvB,OAAOA,GAAG;EACZ,CAAC,CAAC;AACJ;AACA,OAAO,SAASa,UAAUA,CAACT,QAAQ,EAAEU,KAAK,EAAEC,OAAO,EAAE;EACnD,IAAIC,UAAU,GAAGZ,QAAQ;EACzB,IAAIU,KAAK,EAAE;IACTE,UAAU,GAAGnB,mBAAmB,CAACiB,KAAK,CAAC;EACzC;EACA,OAAOlB,aAAa,CAACoB,UAAU,EAAED,OAAO,CAAC;AAC3C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}