{"ast": null, "code": "/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nexport function bound01(n, max) {\n  if (isOnePointZero(n)) {\n    n = '100%';\n  }\n  var isPercent = isPercentage(n);\n  n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n  // Automatically convert percentage into number\n  if (isPercent) {\n    n = parseInt(String(n * max), 10) / 100;\n  }\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n  // Convert into [0, 1] range if it isn't already\n  if (max === 360) {\n    // If n is a hue given in degrees,\n    // wrap around out-of-range values into [0, 360] range\n    // then convert into [0, 1].\n    n = (n < 0 ? n % max + max : n % max) / parseFloat(String(max));\n  } else {\n    // If n not a hue given in degrees\n    // Convert into [0, 1] range if it isn't already.\n    n = n % max / parseFloat(String(max));\n  }\n  return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nexport function clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nexport function isOnePointZero(n) {\n  return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nexport function isPercentage(n) {\n  return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nexport function boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nexport function convertToPercentage(n) {\n  if (n <= 1) {\n    return \"\".concat(Number(n) * 100, \"%\");\n  }\n  return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nexport function pad2(c) {\n  return c.length === 1 ? '0' + c : String(c);\n}", "map": {"version": 3, "names": ["bound01", "n", "max", "isOnePointZero", "isPercent", "isPercentage", "Math", "min", "parseFloat", "parseInt", "String", "abs", "clamp01", "val", "indexOf", "boundAlpha", "a", "isNaN", "convertToPercentage", "concat", "Number", "pad2", "c", "length"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ctrl/tinycolor/dist/module/util.js"], "sourcesContent": ["/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nexport function bound01(n, max) {\n    if (isOnePointZero(n)) {\n        n = '100%';\n    }\n    var isPercent = isPercentage(n);\n    n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n    // Automatically convert percentage into number\n    if (isPercent) {\n        n = parseInt(String(n * max), 10) / 100;\n    }\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n        return 1;\n    }\n    // Convert into [0, 1] range if it isn't already\n    if (max === 360) {\n        // If n is a hue given in degrees,\n        // wrap around out-of-range values into [0, 360] range\n        // then convert into [0, 1].\n        n = (n < 0 ? (n % max) + max : n % max) / parseFloat(String(max));\n    }\n    else {\n        // If n not a hue given in degrees\n        // Convert into [0, 1] range if it isn't already.\n        n = (n % max) / parseFloat(String(max));\n    }\n    return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nexport function clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nexport function isOnePointZero(n) {\n    return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nexport function isPercentage(n) {\n    return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nexport function boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n        a = 1;\n    }\n    return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nexport function convertToPercentage(n) {\n    if (n <= 1) {\n        return \"\".concat(Number(n) * 100, \"%\");\n    }\n    return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nexport function pad2(c) {\n    return c.length === 1 ? '0' + c : String(c);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC5B,IAAIC,cAAc,CAACF,CAAC,CAAC,EAAE;IACnBA,CAAC,GAAG,MAAM;EACd;EACA,IAAIG,SAAS,GAAGC,YAAY,CAACJ,CAAC,CAAC;EAC/BA,CAAC,GAAGC,GAAG,KAAK,GAAG,GAAGD,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACL,GAAG,EAAEI,IAAI,CAACJ,GAAG,CAAC,CAAC,EAAEM,UAAU,CAACP,CAAC,CAAC,CAAC,CAAC;EAC/D;EACA,IAAIG,SAAS,EAAE;IACXH,CAAC,GAAGQ,QAAQ,CAACC,MAAM,CAACT,CAAC,GAAGC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;EAC3C;EACA;EACA,IAAII,IAAI,CAACK,GAAG,CAACV,CAAC,GAAGC,GAAG,CAAC,GAAG,QAAQ,EAAE;IAC9B,OAAO,CAAC;EACZ;EACA;EACA,IAAIA,GAAG,KAAK,GAAG,EAAE;IACb;IACA;IACA;IACAD,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,GAAIA,CAAC,GAAGC,GAAG,GAAIA,GAAG,GAAGD,CAAC,GAAGC,GAAG,IAAIM,UAAU,CAACE,MAAM,CAACR,GAAG,CAAC,CAAC;EACrE,CAAC,MACI;IACD;IACA;IACAD,CAAC,GAAIA,CAAC,GAAGC,GAAG,GAAIM,UAAU,CAACE,MAAM,CAACR,GAAG,CAAC,CAAC;EAC3C;EACA,OAAOD,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,OAAOA,CAACC,GAAG,EAAE;EACzB,OAAOP,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACJ,GAAG,CAAC,CAAC,EAAEW,GAAG,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASV,cAAcA,CAACF,CAAC,EAAE;EAC9B,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACa,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIN,UAAU,CAACP,CAAC,CAAC,KAAK,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,YAAYA,CAACJ,CAAC,EAAE;EAC5B,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACa,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,CAAC,EAAE;EAC1BA,CAAC,GAAGR,UAAU,CAACQ,CAAC,CAAC;EACjB,IAAIC,KAAK,CAACD,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;IAC5BA,CAAC,GAAG,CAAC;EACT;EACA,OAAOA,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,mBAAmBA,CAACjB,CAAC,EAAE;EACnC,IAAIA,CAAC,IAAI,CAAC,EAAE;IACR,OAAO,EAAE,CAACkB,MAAM,CAACC,MAAM,CAACnB,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC;EAC1C;EACA,OAAOA,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoB,IAAIA,CAACC,CAAC,EAAE;EACpB,OAAOA,CAAC,CAACC,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGD,CAAC,GAAGZ,MAAM,CAACY,CAAC,CAAC;AAC/C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}