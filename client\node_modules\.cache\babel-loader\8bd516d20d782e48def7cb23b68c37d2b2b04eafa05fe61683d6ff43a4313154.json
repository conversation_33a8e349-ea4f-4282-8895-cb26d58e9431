{"ast": null, "code": "import EnterOutlined from \"@ant-design/icons/es/icons/EnterOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { cloneElement } from '../_util/reactNode';\nimport TextArea from '../input/TextArea';\nimport useStyle from './style';\nconst Editable = props => {\n  const {\n    prefixCls,\n    'aria-label': ariaLabel,\n    className,\n    style,\n    direction,\n    maxLength,\n    autoSize = true,\n    value,\n    onSave,\n    onCancel,\n    onEnd,\n    component,\n    enterIcon = /*#__PURE__*/React.createElement(EnterOutlined, null)\n  } = props;\n  const ref = React.useRef(null);\n  const inComposition = React.useRef(false);\n  const lastKeyCode = React.useRef();\n  const [current, setCurrent] = React.useState(value);\n  React.useEffect(() => {\n    setCurrent(value);\n  }, [value]);\n  React.useEffect(() => {\n    if (ref.current && ref.current.resizableTextArea) {\n      const {\n        textArea\n      } = ref.current.resizableTextArea;\n      textArea.focus();\n      const {\n        length\n      } = textArea.value;\n      textArea.setSelectionRange(length, length);\n    }\n  }, []);\n  const onChange = _ref => {\n    let {\n      target\n    } = _ref;\n    setCurrent(target.value.replace(/[\\n\\r]/g, ''));\n  };\n  const onCompositionStart = () => {\n    inComposition.current = true;\n  };\n  const onCompositionEnd = () => {\n    inComposition.current = false;\n  };\n  const onKeyDown = _ref2 => {\n    let {\n      keyCode\n    } = _ref2;\n    // We don't record keyCode when IME is using\n    if (inComposition.current) return;\n    lastKeyCode.current = keyCode;\n  };\n  const confirmChange = () => {\n    onSave(current.trim());\n  };\n  const onKeyUp = _ref3 => {\n    let {\n      keyCode,\n      ctrlKey,\n      altKey,\n      metaKey,\n      shiftKey\n    } = _ref3;\n    // Check if it's a real key\n    if (lastKeyCode.current === keyCode && !inComposition.current && !ctrlKey && !altKey && !metaKey && !shiftKey) {\n      if (keyCode === KeyCode.ENTER) {\n        confirmChange();\n        onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n      } else if (keyCode === KeyCode.ESC) {\n        onCancel();\n      }\n    }\n  };\n  const onBlur = () => {\n    confirmChange();\n  };\n  const textClassName = component ? `${prefixCls}-${component}` : '';\n  const [wrapSSR, hashId] = useStyle(prefixCls);\n  const textAreaClassName = classNames(prefixCls, `${prefixCls}-edit-content`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, textClassName, hashId);\n  return wrapSSR( /*#__PURE__*/React.createElement(\"div\", {\n    className: textAreaClassName,\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, {\n    ref: ref,\n    maxLength: maxLength,\n    value: current,\n    onChange: onChange,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBlur: onBlur,\n    \"aria-label\": ariaLabel,\n    rows: 1,\n    autoSize: autoSize\n  }), enterIcon !== null ? cloneElement(enterIcon, {\n    className: `${prefixCls}-edit-content-confirm`\n  }) : null));\n};\nexport default Editable;", "map": {"version": 3, "names": ["EnterOutlined", "classNames", "KeyCode", "React", "cloneElement", "TextArea", "useStyle", "Editable", "props", "prefixCls", "aria<PERSON><PERSON><PERSON>", "className", "style", "direction", "max<PERSON><PERSON><PERSON>", "autoSize", "value", "onSave", "onCancel", "onEnd", "component", "enterIcon", "createElement", "ref", "useRef", "inComposition", "lastKeyCode", "current", "setCurrent", "useState", "useEffect", "resizableTextArea", "textArea", "focus", "length", "setSelectionRange", "onChange", "_ref", "target", "replace", "onCompositionStart", "onCompositionEnd", "onKeyDown", "_ref2", "keyCode", "confirmChange", "trim", "onKeyUp", "_ref3", "ctrl<PERSON>ey", "altKey", "metaKey", "shift<PERSON>ey", "ENTER", "ESC", "onBlur", "textClassName", "wrapSSR", "hashId", "textAreaClassName", "rows"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/typography/Editable.js"], "sourcesContent": ["import EnterOutlined from \"@ant-design/icons/es/icons/EnterOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { cloneElement } from '../_util/reactNode';\nimport TextArea from '../input/TextArea';\nimport useStyle from './style';\nconst Editable = props => {\n  const {\n    prefixCls,\n    'aria-label': ariaLabel,\n    className,\n    style,\n    direction,\n    maxLength,\n    autoSize = true,\n    value,\n    onSave,\n    onCancel,\n    onEnd,\n    component,\n    enterIcon = /*#__PURE__*/React.createElement(EnterOutlined, null)\n  } = props;\n  const ref = React.useRef(null);\n  const inComposition = React.useRef(false);\n  const lastKeyCode = React.useRef();\n  const [current, setCurrent] = React.useState(value);\n  React.useEffect(() => {\n    setCurrent(value);\n  }, [value]);\n  React.useEffect(() => {\n    if (ref.current && ref.current.resizableTextArea) {\n      const {\n        textArea\n      } = ref.current.resizableTextArea;\n      textArea.focus();\n      const {\n        length\n      } = textArea.value;\n      textArea.setSelectionRange(length, length);\n    }\n  }, []);\n  const onChange = _ref => {\n    let {\n      target\n    } = _ref;\n    setCurrent(target.value.replace(/[\\n\\r]/g, ''));\n  };\n  const onCompositionStart = () => {\n    inComposition.current = true;\n  };\n  const onCompositionEnd = () => {\n    inComposition.current = false;\n  };\n  const onKeyDown = _ref2 => {\n    let {\n      keyCode\n    } = _ref2;\n    // We don't record keyCode when IME is using\n    if (inComposition.current) return;\n    lastKeyCode.current = keyCode;\n  };\n  const confirmChange = () => {\n    onSave(current.trim());\n  };\n  const onKeyUp = _ref3 => {\n    let {\n      keyCode,\n      ctrlKey,\n      altKey,\n      metaKey,\n      shiftKey\n    } = _ref3;\n    // Check if it's a real key\n    if (lastKeyCode.current === keyCode && !inComposition.current && !ctrlKey && !altKey && !metaKey && !shiftKey) {\n      if (keyCode === KeyCode.ENTER) {\n        confirmChange();\n        onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n      } else if (keyCode === KeyCode.ESC) {\n        onCancel();\n      }\n    }\n  };\n  const onBlur = () => {\n    confirmChange();\n  };\n  const textClassName = component ? `${prefixCls}-${component}` : '';\n  const [wrapSSR, hashId] = useStyle(prefixCls);\n  const textAreaClassName = classNames(prefixCls, `${prefixCls}-edit-content`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, textClassName, hashId);\n  return wrapSSR( /*#__PURE__*/React.createElement(\"div\", {\n    className: textAreaClassName,\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, {\n    ref: ref,\n    maxLength: maxLength,\n    value: current,\n    onChange: onChange,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBlur: onBlur,\n    \"aria-label\": ariaLabel,\n    rows: 1,\n    autoSize: autoSize\n  }), enterIcon !== null ? cloneElement(enterIcon, {\n    className: `${prefixCls}-edit-content-confirm`\n  }) : null));\n};\nexport default Editable;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC,SAAS;IACT,YAAY,EAAEC,SAAS;IACvBC,SAAS;IACTC,KAAK;IACLC,SAAS;IACTC,SAAS;IACTC,QAAQ,GAAG,IAAI;IACfC,KAAK;IACLC,MAAM;IACNC,QAAQ;IACRC,KAAK;IACLC,SAAS;IACTC,SAAS,GAAG,aAAalB,KAAK,CAACmB,aAAa,CAACtB,aAAa,EAAE,IAAI;EAClE,CAAC,GAAGQ,KAAK;EACT,MAAMe,GAAG,GAAGpB,KAAK,CAACqB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,aAAa,GAAGtB,KAAK,CAACqB,MAAM,CAAC,KAAK,CAAC;EACzC,MAAME,WAAW,GAAGvB,KAAK,CAACqB,MAAM,CAAC,CAAC;EAClC,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGzB,KAAK,CAAC0B,QAAQ,CAACb,KAAK,CAAC;EACnDb,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpBF,UAAU,CAACZ,KAAK,CAAC;EACnB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACXb,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpB,IAAIP,GAAG,CAACI,OAAO,IAAIJ,GAAG,CAACI,OAAO,CAACI,iBAAiB,EAAE;MAChD,MAAM;QACJC;MACF,CAAC,GAAGT,GAAG,CAACI,OAAO,CAACI,iBAAiB;MACjCC,QAAQ,CAACC,KAAK,CAAC,CAAC;MAChB,MAAM;QACJC;MACF,CAAC,GAAGF,QAAQ,CAAChB,KAAK;MAClBgB,QAAQ,CAACG,iBAAiB,CAACD,MAAM,EAAEA,MAAM,CAAC;IAC5C;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAME,QAAQ,GAAGC,IAAI,IAAI;IACvB,IAAI;MACFC;IACF,CAAC,GAAGD,IAAI;IACRT,UAAU,CAACU,MAAM,CAACtB,KAAK,CAACuB,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;EACjD,CAAC;EACD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/Bf,aAAa,CAACE,OAAO,GAAG,IAAI;EAC9B,CAAC;EACD,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhB,aAAa,CAACE,OAAO,GAAG,KAAK;EAC/B,CAAC;EACD,MAAMe,SAAS,GAAGC,KAAK,IAAI;IACzB,IAAI;MACFC;IACF,CAAC,GAAGD,KAAK;IACT;IACA,IAAIlB,aAAa,CAACE,OAAO,EAAE;IAC3BD,WAAW,CAACC,OAAO,GAAGiB,OAAO;EAC/B,CAAC;EACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B5B,MAAM,CAACU,OAAO,CAACmB,IAAI,CAAC,CAAC,CAAC;EACxB,CAAC;EACD,MAAMC,OAAO,GAAGC,KAAK,IAAI;IACvB,IAAI;MACFJ,OAAO;MACPK,OAAO;MACPC,MAAM;MACNC,OAAO;MACPC;IACF,CAAC,GAAGJ,KAAK;IACT;IACA,IAAItB,WAAW,CAACC,OAAO,KAAKiB,OAAO,IAAI,CAACnB,aAAa,CAACE,OAAO,IAAI,CAACsB,OAAO,IAAI,CAACC,MAAM,IAAI,CAACC,OAAO,IAAI,CAACC,QAAQ,EAAE;MAC7G,IAAIR,OAAO,KAAK1C,OAAO,CAACmD,KAAK,EAAE;QAC7BR,aAAa,CAAC,CAAC;QACf1B,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC;MACvD,CAAC,MAAM,IAAIyB,OAAO,KAAK1C,OAAO,CAACoD,GAAG,EAAE;QAClCpC,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;EACD,MAAMqC,MAAM,GAAGA,CAAA,KAAM;IACnBV,aAAa,CAAC,CAAC;EACjB,CAAC;EACD,MAAMW,aAAa,GAAGpC,SAAS,GAAI,GAAEX,SAAU,IAAGW,SAAU,EAAC,GAAG,EAAE;EAClE,MAAM,CAACqC,OAAO,EAAEC,MAAM,CAAC,GAAGpD,QAAQ,CAACG,SAAS,CAAC;EAC7C,MAAMkD,iBAAiB,GAAG1D,UAAU,CAACQ,SAAS,EAAG,GAAEA,SAAU,eAAc,EAAE;IAC3E,CAAE,GAAEA,SAAU,MAAK,GAAGI,SAAS,KAAK;EACtC,CAAC,EAAEF,SAAS,EAAE6C,aAAa,EAAEE,MAAM,CAAC;EACpC,OAAOD,OAAO,EAAE,aAAatD,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACtDX,SAAS,EAAEgD,iBAAiB;IAC5B/C,KAAK,EAAEA;EACT,CAAC,EAAE,aAAaT,KAAK,CAACmB,aAAa,CAACjB,QAAQ,EAAE;IAC5CkB,GAAG,EAAEA,GAAG;IACRT,SAAS,EAAEA,SAAS;IACpBE,KAAK,EAAEW,OAAO;IACdS,QAAQ,EAAEA,QAAQ;IAClBM,SAAS,EAAEA,SAAS;IACpBK,OAAO,EAAEA,OAAO;IAChBP,kBAAkB,EAAEA,kBAAkB;IACtCC,gBAAgB,EAAEA,gBAAgB;IAClCc,MAAM,EAAEA,MAAM;IACd,YAAY,EAAE7C,SAAS;IACvBkD,IAAI,EAAE,CAAC;IACP7C,QAAQ,EAAEA;EACZ,CAAC,CAAC,EAAEM,SAAS,KAAK,IAAI,GAAGjB,YAAY,CAACiB,SAAS,EAAE;IAC/CV,SAAS,EAAG,GAAEF,SAAU;EAC1B,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACb,CAAC;AACD,eAAeF,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}