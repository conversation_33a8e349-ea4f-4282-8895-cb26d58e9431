{"ast": null, "code": "/* eslint-disable no-param-reassign */\n\nvar cached;\nexport default function getScrollBarSize(fresh) {\n  if (typeof document === 'undefined') {\n    return 0;\n  }\n  if (fresh || cached === undefined) {\n    var inner = document.createElement('div');\n    inner.style.width = '100%';\n    inner.style.height = '200px';\n    var outer = document.createElement('div');\n    var outerStyle = outer.style;\n    outerStyle.position = 'absolute';\n    outerStyle.top = '0';\n    outerStyle.left = '0';\n    outerStyle.pointerEvents = 'none';\n    outerStyle.visibility = 'hidden';\n    outerStyle.width = '200px';\n    outerStyle.height = '150px';\n    outerStyle.overflow = 'hidden';\n    outer.appendChild(inner);\n    document.body.appendChild(outer);\n    var widthContained = inner.offsetWidth;\n    outer.style.overflow = 'scroll';\n    var widthScroll = inner.offsetWidth;\n    if (widthContained === widthScroll) {\n      widthScroll = outer.clientWidth;\n    }\n    document.body.removeChild(outer);\n    cached = widthContained - widthScroll;\n  }\n  return cached;\n}\nfunction ensureSize(str) {\n  var match = str.match(/^(.*)px$/);\n  var value = Number(match === null || match === void 0 ? void 0 : match[1]);\n  return Number.isNaN(value) ? getScrollBarSize() : value;\n}\nexport function getTargetScrollBarSize(target) {\n  if (typeof document === 'undefined' || !target || !(target instanceof Element)) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n  var _getComputedStyle = getComputedStyle(target, '::-webkit-scrollbar'),\n    width = _getComputedStyle.width,\n    height = _getComputedStyle.height;\n  return {\n    width: ensureSize(width),\n    height: ensureSize(height)\n  };\n}", "map": {"version": 3, "names": ["cached", "getScrollBarSize", "fresh", "document", "undefined", "inner", "createElement", "style", "width", "height", "outer", "outerStyle", "position", "top", "left", "pointerEvents", "visibility", "overflow", "append<PERSON><PERSON><PERSON>", "body", "widthContained", "offsetWidth", "widthScroll", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "ensureSize", "str", "match", "value", "Number", "isNaN", "getTargetScrollBarSize", "target", "Element", "_getComputedStyle", "getComputedStyle"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-util/es/getScrollBarSize.js"], "sourcesContent": ["/* eslint-disable no-param-reassign */\n\nvar cached;\nexport default function getScrollBarSize(fresh) {\n  if (typeof document === 'undefined') {\n    return 0;\n  }\n  if (fresh || cached === undefined) {\n    var inner = document.createElement('div');\n    inner.style.width = '100%';\n    inner.style.height = '200px';\n    var outer = document.createElement('div');\n    var outerStyle = outer.style;\n    outerStyle.position = 'absolute';\n    outerStyle.top = '0';\n    outerStyle.left = '0';\n    outerStyle.pointerEvents = 'none';\n    outerStyle.visibility = 'hidden';\n    outerStyle.width = '200px';\n    outerStyle.height = '150px';\n    outerStyle.overflow = 'hidden';\n    outer.appendChild(inner);\n    document.body.appendChild(outer);\n    var widthContained = inner.offsetWidth;\n    outer.style.overflow = 'scroll';\n    var widthScroll = inner.offsetWidth;\n    if (widthContained === widthScroll) {\n      widthScroll = outer.clientWidth;\n    }\n    document.body.removeChild(outer);\n    cached = widthContained - widthScroll;\n  }\n  return cached;\n}\nfunction ensureSize(str) {\n  var match = str.match(/^(.*)px$/);\n  var value = Number(match === null || match === void 0 ? void 0 : match[1]);\n  return Number.isNaN(value) ? getScrollBarSize() : value;\n}\nexport function getTargetScrollBarSize(target) {\n  if (typeof document === 'undefined' || !target || !(target instanceof Element)) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n  var _getComputedStyle = getComputedStyle(target, '::-webkit-scrollbar'),\n    width = _getComputedStyle.width,\n    height = _getComputedStyle.height;\n  return {\n    width: ensureSize(width),\n    height: ensureSize(height)\n  };\n}"], "mappings": "AAAA;;AAEA,IAAIA,MAAM;AACV,eAAe,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC9C,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACnC,OAAO,CAAC;EACV;EACA,IAAID,KAAK,IAAIF,MAAM,KAAKI,SAAS,EAAE;IACjC,IAAIC,KAAK,GAAGF,QAAQ,CAACG,aAAa,CAAC,KAAK,CAAC;IACzCD,KAAK,CAACE,KAAK,CAACC,KAAK,GAAG,MAAM;IAC1BH,KAAK,CAACE,KAAK,CAACE,MAAM,GAAG,OAAO;IAC5B,IAAIC,KAAK,GAAGP,QAAQ,CAACG,aAAa,CAAC,KAAK,CAAC;IACzC,IAAIK,UAAU,GAAGD,KAAK,CAACH,KAAK;IAC5BI,UAAU,CAACC,QAAQ,GAAG,UAAU;IAChCD,UAAU,CAACE,GAAG,GAAG,GAAG;IACpBF,UAAU,CAACG,IAAI,GAAG,GAAG;IACrBH,UAAU,CAACI,aAAa,GAAG,MAAM;IACjCJ,UAAU,CAACK,UAAU,GAAG,QAAQ;IAChCL,UAAU,CAACH,KAAK,GAAG,OAAO;IAC1BG,UAAU,CAACF,MAAM,GAAG,OAAO;IAC3BE,UAAU,CAACM,QAAQ,GAAG,QAAQ;IAC9BP,KAAK,CAACQ,WAAW,CAACb,KAAK,CAAC;IACxBF,QAAQ,CAACgB,IAAI,CAACD,WAAW,CAACR,KAAK,CAAC;IAChC,IAAIU,cAAc,GAAGf,KAAK,CAACgB,WAAW;IACtCX,KAAK,CAACH,KAAK,CAACU,QAAQ,GAAG,QAAQ;IAC/B,IAAIK,WAAW,GAAGjB,KAAK,CAACgB,WAAW;IACnC,IAAID,cAAc,KAAKE,WAAW,EAAE;MAClCA,WAAW,GAAGZ,KAAK,CAACa,WAAW;IACjC;IACApB,QAAQ,CAACgB,IAAI,CAACK,WAAW,CAACd,KAAK,CAAC;IAChCV,MAAM,GAAGoB,cAAc,GAAGE,WAAW;EACvC;EACA,OAAOtB,MAAM;AACf;AACA,SAASyB,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIC,KAAK,GAAGD,GAAG,CAACC,KAAK,CAAC,UAAU,CAAC;EACjC,IAAIC,KAAK,GAAGC,MAAM,CAACF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1E,OAAOE,MAAM,CAACC,KAAK,CAACF,KAAK,CAAC,GAAG3B,gBAAgB,CAAC,CAAC,GAAG2B,KAAK;AACzD;AACA,OAAO,SAASG,sBAAsBA,CAACC,MAAM,EAAE;EAC7C,IAAI,OAAO7B,QAAQ,KAAK,WAAW,IAAI,CAAC6B,MAAM,IAAI,EAAEA,MAAM,YAAYC,OAAO,CAAC,EAAE;IAC9E,OAAO;MACLzB,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;EACH;EACA,IAAIyB,iBAAiB,GAAGC,gBAAgB,CAACH,MAAM,EAAE,qBAAqB,CAAC;IACrExB,KAAK,GAAG0B,iBAAiB,CAAC1B,KAAK;IAC/BC,MAAM,GAAGyB,iBAAiB,CAACzB,MAAM;EACnC,OAAO;IACLD,KAAK,EAAEiB,UAAU,CAACjB,KAAK,CAAC;IACxBC,MAAM,EAAEgB,UAAU,CAAChB,MAAM;EAC3B,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}