{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resetState = resetState;\nexports.log = log;\nvar _portalOpenInstances = require(\"./portalOpenInstances\");\nvar _portalOpenInstances2 = _interopRequireDefault(_portalOpenInstances);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\n// Body focus trap see Issue #742\n\nvar before = void 0,\n  after = void 0,\n  instances = [];\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction resetState() {\n  var _arr = [before, after];\n  for (var _i = 0; _i < _arr.length; _i++) {\n    var item = _arr[_i];\n    if (!item) continue;\n    item.parentNode && item.parentNode.removeChild(item);\n  }\n  before = after = null;\n  instances = [];\n}\n\n/* istanbul ignore next */\nfunction log() {\n  console.log(\"bodyTrap ----------\");\n  console.log(instances.length);\n  var _arr2 = [before, after];\n  for (var _i2 = 0; _i2 < _arr2.length; _i2++) {\n    var item = _arr2[_i2];\n    var check = item || {};\n    console.log(check.nodeName, check.className, check.id);\n  }\n  console.log(\"edn bodyTrap ----------\");\n}\n/* eslint-enable no-console */\n\nfunction focusContent() {\n  if (instances.length === 0) {\n    if (process.env.NODE_ENV !== \"production\") {\n      // eslint-disable-next-line no-console\n      console.warn(\"React-Modal: Open instances > 0 expected\");\n    }\n    return;\n  }\n  instances[instances.length - 1].focusContent();\n}\nfunction bodyTrap(eventType, openInstances) {\n  if (!before && !after) {\n    before = document.createElement(\"div\");\n    before.setAttribute(\"data-react-modal-body-trap\", \"\");\n    before.style.position = \"absolute\";\n    before.style.opacity = \"0\";\n    before.setAttribute(\"tabindex\", \"0\");\n    before.addEventListener(\"focus\", focusContent);\n    after = before.cloneNode();\n    after.addEventListener(\"focus\", focusContent);\n  }\n  instances = openInstances;\n  if (instances.length > 0) {\n    // Add focus trap\n    if (document.body.firstChild !== before) {\n      document.body.insertBefore(before, document.body.firstChild);\n    }\n    if (document.body.lastChild !== after) {\n      document.body.appendChild(after);\n    }\n  } else {\n    // Remove focus trap\n    if (before.parentElement) {\n      before.parentElement.removeChild(before);\n    }\n    if (after.parentElement) {\n      after.parentElement.removeChild(after);\n    }\n  }\n}\n_portalOpenInstances2.default.subscribe(bodyTrap);", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "resetState", "log", "_portalOpenInstances", "require", "_portalOpenInstances2", "_interopRequireDefault", "obj", "__esModule", "default", "before", "after", "instances", "_arr", "_i", "length", "item", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "console", "_arr2", "_i2", "check", "nodeName", "className", "id", "focusContent", "process", "env", "NODE_ENV", "warn", "bodyTrap", "eventType", "openInstances", "document", "createElement", "setAttribute", "style", "position", "opacity", "addEventListener", "cloneNode", "body", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "parentElement", "subscribe"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/react-modal/lib/helpers/bodyTrap.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resetState = resetState;\nexports.log = log;\n\nvar _portalOpenInstances = require(\"./portalOpenInstances\");\n\nvar _portalOpenInstances2 = _interopRequireDefault(_portalOpenInstances);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// Body focus trap see Issue #742\n\nvar before = void 0,\n    after = void 0,\n    instances = [];\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction resetState() {\n  var _arr = [before, after];\n\n  for (var _i = 0; _i < _arr.length; _i++) {\n    var item = _arr[_i];\n    if (!item) continue;\n    item.parentNode && item.parentNode.removeChild(item);\n  }\n  before = after = null;\n  instances = [];\n}\n\n/* istanbul ignore next */\nfunction log() {\n  console.log(\"bodyTrap ----------\");\n  console.log(instances.length);\n  var _arr2 = [before, after];\n  for (var _i2 = 0; _i2 < _arr2.length; _i2++) {\n    var item = _arr2[_i2];\n    var check = item || {};\n    console.log(check.nodeName, check.className, check.id);\n  }\n  console.log(\"edn bodyTrap ----------\");\n}\n/* eslint-enable no-console */\n\nfunction focusContent() {\n  if (instances.length === 0) {\n    if (process.env.NODE_ENV !== \"production\") {\n      // eslint-disable-next-line no-console\n      console.warn(\"React-Modal: Open instances > 0 expected\");\n    }\n    return;\n  }\n  instances[instances.length - 1].focusContent();\n}\n\nfunction bodyTrap(eventType, openInstances) {\n  if (!before && !after) {\n    before = document.createElement(\"div\");\n    before.setAttribute(\"data-react-modal-body-trap\", \"\");\n    before.style.position = \"absolute\";\n    before.style.opacity = \"0\";\n    before.setAttribute(\"tabindex\", \"0\");\n    before.addEventListener(\"focus\", focusContent);\n    after = before.cloneNode();\n    after.addEventListener(\"focus\", focusContent);\n  }\n\n  instances = openInstances;\n\n  if (instances.length > 0) {\n    // Add focus trap\n    if (document.body.firstChild !== before) {\n      document.body.insertBefore(before, document.body.firstChild);\n    }\n    if (document.body.lastChild !== after) {\n      document.body.appendChild(after);\n    }\n  } else {\n    // Remove focus trap\n    if (before.parentElement) {\n      before.parentElement.removeChild(before);\n    }\n    if (after.parentElement) {\n      after.parentElement.removeChild(after);\n    }\n  }\n}\n\n_portalOpenInstances2.default.subscribe(bodyTrap);"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/BF,OAAO,CAACG,GAAG,GAAGA,GAAG;AAEjB,IAAIC,oBAAoB,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAE3D,IAAIC,qBAAqB,GAAGC,sBAAsB,CAACH,oBAAoB,CAAC;AAExE,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;;AAE9F;;AAEA,IAAIG,MAAM,GAAG,KAAK,CAAC;EACfC,KAAK,GAAG,KAAK,CAAC;EACdC,SAAS,GAAG,EAAE;;AAElB;AACA;AACA,SAASX,UAAUA,CAAA,EAAG;EACpB,IAAIY,IAAI,GAAG,CAACH,MAAM,EAAEC,KAAK,CAAC;EAE1B,KAAK,IAAIG,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,IAAI,CAACE,MAAM,EAAED,EAAE,EAAE,EAAE;IACvC,IAAIE,IAAI,GAAGH,IAAI,CAACC,EAAE,CAAC;IACnB,IAAI,CAACE,IAAI,EAAE;IACXA,IAAI,CAACC,UAAU,IAAID,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC;EACtD;EACAN,MAAM,GAAGC,KAAK,GAAG,IAAI;EACrBC,SAAS,GAAG,EAAE;AAChB;;AAEA;AACA,SAASV,GAAGA,CAAA,EAAG;EACbiB,OAAO,CAACjB,GAAG,CAAC,qBAAqB,CAAC;EAClCiB,OAAO,CAACjB,GAAG,CAACU,SAAS,CAACG,MAAM,CAAC;EAC7B,IAAIK,KAAK,GAAG,CAACV,MAAM,EAAEC,KAAK,CAAC;EAC3B,KAAK,IAAIU,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGD,KAAK,CAACL,MAAM,EAAEM,GAAG,EAAE,EAAE;IAC3C,IAAIL,IAAI,GAAGI,KAAK,CAACC,GAAG,CAAC;IACrB,IAAIC,KAAK,GAAGN,IAAI,IAAI,CAAC,CAAC;IACtBG,OAAO,CAACjB,GAAG,CAACoB,KAAK,CAACC,QAAQ,EAAED,KAAK,CAACE,SAAS,EAAEF,KAAK,CAACG,EAAE,CAAC;EACxD;EACAN,OAAO,CAACjB,GAAG,CAAC,yBAAyB,CAAC;AACxC;AACA;;AAEA,SAASwB,YAAYA,CAAA,EAAG;EACtB,IAAId,SAAS,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACAV,OAAO,CAACW,IAAI,CAAC,0CAA0C,CAAC;IAC1D;IACA;EACF;EACAlB,SAAS,CAACA,SAAS,CAACG,MAAM,GAAG,CAAC,CAAC,CAACW,YAAY,CAAC,CAAC;AAChD;AAEA,SAASK,QAAQA,CAACC,SAAS,EAAEC,aAAa,EAAE;EAC1C,IAAI,CAACvB,MAAM,IAAI,CAACC,KAAK,EAAE;IACrBD,MAAM,GAAGwB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtCzB,MAAM,CAAC0B,YAAY,CAAC,4BAA4B,EAAE,EAAE,CAAC;IACrD1B,MAAM,CAAC2B,KAAK,CAACC,QAAQ,GAAG,UAAU;IAClC5B,MAAM,CAAC2B,KAAK,CAACE,OAAO,GAAG,GAAG;IAC1B7B,MAAM,CAAC0B,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;IACpC1B,MAAM,CAAC8B,gBAAgB,CAAC,OAAO,EAAEd,YAAY,CAAC;IAC9Cf,KAAK,GAAGD,MAAM,CAAC+B,SAAS,CAAC,CAAC;IAC1B9B,KAAK,CAAC6B,gBAAgB,CAAC,OAAO,EAAEd,YAAY,CAAC;EAC/C;EAEAd,SAAS,GAAGqB,aAAa;EAEzB,IAAIrB,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE;IACxB;IACA,IAAImB,QAAQ,CAACQ,IAAI,CAACC,UAAU,KAAKjC,MAAM,EAAE;MACvCwB,QAAQ,CAACQ,IAAI,CAACE,YAAY,CAAClC,MAAM,EAAEwB,QAAQ,CAACQ,IAAI,CAACC,UAAU,CAAC;IAC9D;IACA,IAAIT,QAAQ,CAACQ,IAAI,CAACG,SAAS,KAAKlC,KAAK,EAAE;MACrCuB,QAAQ,CAACQ,IAAI,CAACI,WAAW,CAACnC,KAAK,CAAC;IAClC;EACF,CAAC,MAAM;IACL;IACA,IAAID,MAAM,CAACqC,aAAa,EAAE;MACxBrC,MAAM,CAACqC,aAAa,CAAC7B,WAAW,CAACR,MAAM,CAAC;IAC1C;IACA,IAAIC,KAAK,CAACoC,aAAa,EAAE;MACvBpC,KAAK,CAACoC,aAAa,CAAC7B,WAAW,CAACP,KAAK,CAAC;IACxC;EACF;AACF;AAEAN,qBAAqB,CAACI,OAAO,CAACuC,SAAS,CAACjB,QAAQ,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}