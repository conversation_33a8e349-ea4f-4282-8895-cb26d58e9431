{"ast": null, "code": "import { useContext } from '@rc-component/context';\nimport TableContext from \"../context/TableContext\";\n/** Check if cell is in hover range */\nfunction inHoverRange(cellStartRow, cellRowSpan, startRow, endRow) {\n  var cellEndRow = cellStartRow + cellRowSpan - 1;\n  return cellStartRow <= endRow && cellEndRow >= startRow;\n}\nexport default function useHoverState(rowIndex, rowSpan) {\n  return useContext(TableContext, function (ctx) {\n    var hovering = inHoverRange(rowIndex, rowSpan || 1, ctx.hoverStartRow, ctx.hoverEndRow);\n    return [hovering, ctx.onHover];\n  });\n}", "map": {"version": 3, "names": ["useContext", "TableContext", "inHoverRange", "cellStartRow", "cellRowSpan", "startRow", "endRow", "cellEndRow", "useHoverState", "rowIndex", "rowSpan", "ctx", "hovering", "hoverStartRow", "hoverEndRow", "onHover"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/Cell/useHoverState.js"], "sourcesContent": ["import { useContext } from '@rc-component/context';\nimport TableContext from \"../context/TableContext\";\n/** Check if cell is in hover range */\nfunction inHoverRange(cellStartRow, cellRowSpan, startRow, endRow) {\n  var cellEndRow = cellStartRow + cellRowSpan - 1;\n  return cellStartRow <= endRow && cellEndRow >= startRow;\n}\nexport default function useHoverState(rowIndex, rowSpan) {\n  return useContext(TableContext, function (ctx) {\n    var hovering = inHoverRange(rowIndex, rowSpan || 1, ctx.hoverStartRow, ctx.hoverEndRow);\n    return [hovering, ctx.onHover];\n  });\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,YAAY,MAAM,yBAAyB;AAClD;AACA,SAASC,YAAYA,CAACC,YAAY,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACjE,IAAIC,UAAU,GAAGJ,YAAY,GAAGC,WAAW,GAAG,CAAC;EAC/C,OAAOD,YAAY,IAAIG,MAAM,IAAIC,UAAU,IAAIF,QAAQ;AACzD;AACA,eAAe,SAASG,aAAaA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACvD,OAAOV,UAAU,CAACC,YAAY,EAAE,UAAUU,GAAG,EAAE;IAC7C,IAAIC,QAAQ,GAAGV,YAAY,CAACO,QAAQ,EAAEC,OAAO,IAAI,CAAC,EAAEC,GAAG,CAACE,aAAa,EAAEF,GAAG,CAACG,WAAW,CAAC;IACvF,OAAO,CAACF,QAAQ,EAAED,GAAG,CAACI,OAAO,CAAC;EAChC,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}