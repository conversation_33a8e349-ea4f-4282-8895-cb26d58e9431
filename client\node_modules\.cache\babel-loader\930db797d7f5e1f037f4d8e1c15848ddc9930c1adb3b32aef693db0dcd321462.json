{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { motion } from \"framer-motion\";\nimport { message } from \"antd\";\nimport { getAllExams } from \"../../../apicalls/exams\";\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useNavigate } from \"react-router-dom\";\nimport Select from \"react-select\";\nimport { QuizGrid, Card, Button, Input, Loading } from \"../../../components/modern\";\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TbQuestionMark, TbBrain } from \"react-icons/tb\";\nimport { BsBookFill } from \"react-icons/bs\";\nimport \"./style.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst primaryClasses = [{\n  value: \"\",\n  label: \"All Classes\"\n}, {\n  value: \"1\",\n  label: \"Class 1\"\n}, {\n  value: \"2\",\n  label: \"Class 2\"\n}, {\n  value: \"3\",\n  label: \"Class 3\"\n}, {\n  value: \"4\",\n  label: \"Class 4\"\n}, {\n  value: \"5\",\n  label: \"Class 5\"\n}, {\n  value: \"6\",\n  label: \"Class 6\"\n}, {\n  value: \"7\",\n  label: \"Class 7\"\n}];\nconst secondaryClasses = [{\n  value: \"\",\n  label: \"All Classes\"\n}, {\n  value: \"Form-1\",\n  label: \"Form 1\"\n}, {\n  value: \"Form-2\",\n  label: \"Form 2\"\n}, {\n  value: \"Form-3\",\n  label: \"Form 3\"\n}, {\n  value: \"Form-4\",\n  label: \"Form 4\"\n}];\nconst advanceClasses = [{\n  value: \"\",\n  label: \"All Classes\"\n}, {\n  value: \"Form-5\",\n  label: \"Form 5\"\n}, {\n  value: \"Form-6\",\n  label: \"Form 6\"\n}];\nfunction Quiz() {\n  _s();\n  var _user$level, _user$level2, _user$class, _user$class2;\n  const [exams, setExams] = useState([]);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [reportsData, setReportsData] = useState([]);\n  const [selectedClass, setSelectedClass] = useState(null);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [lgSize, setLgSize] = useState(8);\n  const availableClasses = (user === null || user === void 0 ? void 0 : (_user$level = user.level) === null || _user$level === void 0 ? void 0 : _user$level.toLowerCase()) === \"primary\" ? primaryClasses : (user === null || user === void 0 ? void 0 : (_user$level2 = user.level) === null || _user$level2 === void 0 ? void 0 : _user$level2.toLowerCase()) === \"secondary\" ? secondaryClasses : advanceClasses;\n  useEffect(() => {\n    if (user && user.class) {\n      const defaultSelectedClass = availableClasses.find(option => option.value === user.class);\n      setSelectedClass(defaultSelectedClass);\n    }\n  }, [user, availableClasses]);\n  useEffect(() => {\n    const updateLgSize = () => {\n      setLgSize(window.innerWidth < 1380 ? 9 : 7);\n    };\n\n    // Set initial lg size\n    updateLgSize();\n\n    // Add event listener for window resize\n    window.addEventListener(\"resize\", updateLgSize);\n\n    // Cleanup event listener on component unmount\n    return () => {\n      window.removeEventListener(\"resize\", updateLgSize);\n    };\n  }, []);\n  const handleClassChange = selectedOption => {\n    setSelectedClass(selectedOption);\n  };\n  const filteredExams = exams.filter(exam => {\n    var _exam$name, _exam$category, _exam$class;\n    // Handle class filtering with format compatibility\n    let classMatches = true;\n    if (selectedClass && selectedClass.value !== \"\") {\n      const selectedValue = selectedClass.value;\n      const examClass = exam.class;\n\n      // Check for exact match first\n      if (examClass === selectedValue) {\n        classMatches = true;\n      }\n      // Check if exam class has \"Class-\" prefix and selected value is just the number\n      else if (examClass === `Class-${selectedValue}`) {\n        classMatches = true;\n      }\n      // Check if selected value has \"Class-\" prefix and exam class is just the number\n      else if (selectedValue === `Class-${examClass}`) {\n        classMatches = true;\n      }\n      // Check for Form classes (secondary)\n      else if (examClass === `Form-${selectedValue.replace('Form-', '')}`) {\n        classMatches = true;\n      } else if (selectedValue === `Form-${examClass.replace('Form-', '')}`) {\n        classMatches = true;\n      } else {\n        classMatches = false;\n      }\n    }\n\n    // Handle search filtering\n    const searchMatches = !searchQuery.trim() || ((_exam$name = exam.name) === null || _exam$name === void 0 ? void 0 : _exam$name.toLowerCase().includes(searchQuery.toLowerCase().trim())) || ((_exam$category = exam.category) === null || _exam$category === void 0 ? void 0 : _exam$category.toLowerCase().includes(searchQuery.toLowerCase().trim())) || ((_exam$class = exam.class) === null || _exam$class === void 0 ? void 0 : _exam$class.toLowerCase().includes(searchQuery.toLowerCase().trim()));\n    return classMatches && searchMatches;\n  });\n\n  // Debug logging\n  if (exams.length > 0) {\n    console.log(`📊 Quiz Debug: ${filteredExams.length}/${exams.length} exams shown | Class: ${(selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.label) || 'None'} | Search: \"${searchQuery}\"`);\n  }\n  const getExams = async () => {\n    try {\n      console.log(\"🔍 Starting to fetch exams...\");\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      console.log(\"📡 API Response:\", response);\n      if (response.success) {\n        console.log(\"✅ Exams fetched successfully:\", response.data.length, \"exams\");\n        setExams(response.data.reverse());\n      } else {\n        console.error(\"❌ API Error:\", response.message);\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      console.error(\"❌ Network Error:\", error);\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const filterReportsData = data => {\n    const reportsMap = {};\n\n    // Iterate over the response data (reports)\n    data.forEach(report => {\n      const examId = report.exam._id;\n      const verdict = report.result.verdict;\n\n      // If the examId is not already in the map, add it\n      if (!reportsMap[examId]) {\n        reportsMap[examId] = report;\n      } else {\n        // If there is already an entry for this exam, keep the one with \"pass\" verdict, or just keep the first one if no \"pass\"\n        if (verdict === \"Pass\" && reportsMap[examId].result.verdict !== \"Pass\") {\n          reportsMap[examId] = report; // Replace with the \"pass\" verdict report\n        }\n      }\n    });\n\n    return Object.values(reportsMap);\n  };\n  const getData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllReportsByUser();\n      if (response.success) {\n        setReportsData(filterReportsData(response.data));\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    getData();\n    getExams();\n  }, []);\n  const verifyRetake = async exam => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllReportsByUser();\n      const retakeCount = response.data.filter(item => item.exam && item.exam._id === exam._id).length;\n      console.log(\"Retake count for exam:\", retakeCount);\n    } catch (error) {\n      message.error(\"Unable to verify retake\");\n      dispatch(HideLoading());\n      return;\n    }\n    dispatch(HideLoading());\n    navigate(`/user/write-exam/${exam._id}`);\n  };\n  const handleSearch = e => {\n    setSearchQuery(e.target.value);\n  };\n  const shouldRenderFilteredExams = filteredExams.length < exams.length;\n  return user && /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: \"80vh\",\n      paddingBottom: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n          title: \"Challenge your brain, Beat the rest\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg text-gray-600 mt-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: \"Current Class: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\",\n            children: (user === null || user === void 0 ? void 0 : user.level) === \"Primary\" ? `Class ${user === null || user === void 0 ? void 0 : user.class}` : (user === null || user === void 0 ? void 0 : user.level) === \"Secondary\" ? `Form ${user === null || user === void 0 ? void 0 : (_user$class = user.class) === null || _user$class === void 0 ? void 0 : _user$class.replace('Form-', '')}` : (user === null || user === void 0 ? void 0 : user.level) === \"Advance\" ? `Form ${user === null || user === void 0 ? void 0 : (_user$class2 = user.class) === null || _user$class2 === void 0 ? void 0 : _user$class2.replace('Form-', '')}` : (user === null || user === void 0 ? void 0 : user.class) || 'Not Set'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divider\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-2 flex-wrap\",\n      style: {\n        marginRight: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Search Quiz Title:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"w-100 mb-2\",\n          placeholder: \"Search quizes\",\n          value: searchQuery,\n          onChange: handleSearch\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Please Select Class:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          options: availableClasses,\n          value: selectedClass,\n          onChange: handleClassChange,\n          placeholder: \"Select Class\",\n          styles: {\n            width: \"300px\"\n          },\n          isSearchable: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 9\n    }, this), shouldRenderFilteredExams && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-2\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: `Filtered ${filteredExams.length} out of ${exams.length}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginLeft: 0,\n        marginRight: 0\n      },\n      children: filteredExams.map((exam, index) => {\n        var _examReport$result, _examReport$result$ve, _examReport$result2, _examReport$result2$v, _examReport$result3, _examReport$result3$v, _examReport$result4, _examReport$result4$v, _examReport$result5, _examReport$result5$v, _examReport$result6, _examReport$result6$v, _examReport$result7, _examReport$result7$v, _examReport$result8, _examReport$result8$v, _examReport$result9, _examReport$result9$v, _examReport$result10, _examReport$result10$, _examReport$result11, _examReport$result11$, _examReport$result12, _examReport$result12$;\n        const examReport = reportsData.find(report => report.exam && report.exam._id === exam._id);\n        return /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 9,\n          lg: lgSize,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: \"100%\",\n              boxSizing: \"border-box\",\n              border: `1px solid ${(examReport === null || examReport === void 0 ? void 0 : (_examReport$result = examReport.result) === null || _examReport$result === void 0 ? void 0 : (_examReport$result$ve = _examReport$result.verdict) === null || _examReport$result$ve === void 0 ? void 0 : _examReport$result$ve.toLowerCase()) === \"fail\" ? \"#FE8267\" : (examReport === null || examReport === void 0 ? void 0 : (_examReport$result2 = examReport.result) === null || _examReport$result2 === void 0 ? void 0 : (_examReport$result2$v = _examReport$result2.verdict) === null || _examReport$result2$v === void 0 ? void 0 : _examReport$result2$v.toLowerCase()) === \"pass\" ? \"#43C46C\" : \"#0E8FE9\"}`\n            },\n            className: `card-lg flex flex-col gap-1 p-2 card-design ${(examReport === null || examReport === void 0 ? void 0 : (_examReport$result3 = examReport.result) === null || _examReport$result3 === void 0 ? void 0 : (_examReport$result3$v = _examReport$result3.verdict) === null || _examReport$result3$v === void 0 ? void 0 : _examReport$result3$v.toLowerCase()) === \"fail\" ? \"fail\" : (examReport === null || examReport === void 0 ? void 0 : (_examReport$result4 = examReport.result) === null || _examReport$result4 === void 0 ? void 0 : (_examReport$result4$v = _examReport$result4.verdict) === null || _examReport$result4$v === void 0 ? void 0 : _examReport$result4$v.toLowerCase()) === \"pass\" ? \"pass\" : \"no-attempts\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl flex items-center gap-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `box-tags-icon  ${(examReport === null || examReport === void 0 ? void 0 : (_examReport$result5 = examReport.result) === null || _examReport$result5 === void 0 ? void 0 : (_examReport$result5$v = _examReport$result5.verdict) === null || _examReport$result5$v === void 0 ? void 0 : _examReport$result5$v.toLowerCase()) === \"fail\" ? \"fail-dark\" : (examReport === null || examReport === void 0 ? void 0 : (_examReport$result6 = examReport.result) === null || _examReport$result6 === void 0 ? void 0 : (_examReport$result6$v = _examReport$result6.verdict) === null || _examReport$result6$v === void 0 ? void 0 : _examReport$result6$v.toLowerCase()) === \"pass\" ? \"pass-dark\" : \"no-attempts-dark\"}`,\n                children: /*#__PURE__*/_jsxDEV(BsBookFill, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this), exam === null || exam === void 0 ? void 0 : exam.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                position: 'absolute',\n                top: '20px',\n                right: '30px',\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: (examReport === null || examReport === void 0 ? void 0 : (_examReport$result7 = examReport.result) === null || _examReport$result7 === void 0 ? void 0 : (_examReport$result7$v = _examReport$result7.verdict) === null || _examReport$result7$v === void 0 ? void 0 : _examReport$result7$v.toLowerCase()) === \"fail\" ? \"#FE8267\" : (examReport === null || examReport === void 0 ? void 0 : (_examReport$result8 = examReport.result) === null || _examReport$result8 === void 0 ? void 0 : (_examReport$result8$v = _examReport$result8.verdict) === null || _examReport$result8$v === void 0 ? void 0 : _examReport$result8$v.toLowerCase()) === \"pass\" ? \"#43C46C\" : \"#0E8FE9\"\n              },\n              children: (examReport === null || examReport === void 0 ? void 0 : (_examReport$result9 = examReport.result) === null || _examReport$result9 === void 0 ? void 0 : (_examReport$result9$v = _examReport$result9.verdict) === null || _examReport$result9$v === void 0 ? void 0 : _examReport$result9$v.toLowerCase()) === \"fail\" ? \"Failed\" : (examReport === null || examReport === void 0 ? void 0 : (_examReport$result10 = examReport.result) === null || _examReport$result10 === void 0 ? void 0 : (_examReport$result10$ = _examReport$result10.verdict) === null || _examReport$result10$ === void 0 ? void 0 : _examReport$result10$.toLowerCase()) === \"pass\" ? \"Passed\" : \"No Attempts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl\",\n              children: [\"Subject: \", exam.category]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-md box-tags\",\n                children: [\"Total Marks: \", exam.totalMarks]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-md box-tags\",\n                children: [\"Passing Marks: \", exam.passingMarks]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-md box-tags\",\n                children: [\"Duration: \", exam.duration]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `box-tags-button text-md ${(examReport === null || examReport === void 0 ? void 0 : (_examReport$result11 = examReport.result) === null || _examReport$result11 === void 0 ? void 0 : (_examReport$result11$ = _examReport$result11.verdict) === null || _examReport$result11$ === void 0 ? void 0 : _examReport$result11$.toLowerCase()) === \"fail\" ? \"fail-dark\" : (examReport === null || examReport === void 0 ? void 0 : (_examReport$result12 = examReport.result) === null || _examReport$result12 === void 0 ? void 0 : (_examReport$result12$ = _examReport$result12.verdict) === null || _examReport$result12$ === void 0 ? void 0 : _examReport$result12$.toLowerCase()) === \"pass\" ? \"pass-dark\" : \"no-attempts-dark\"}`,\n                onClick: () => verifyRetake(exam),\n                children: \"Start Quiz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 226,\n    columnNumber: 7\n  }, this);\n}\n_s(Quiz, \"RCSAwxhf6i4RPRBWUpfB/EChHfk=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = Quiz;\nexport default Quiz;\nvar _c;\n$RefreshReg$(_c, \"Quiz\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "motion", "message", "getAllExams", "getAllReportsByUser", "HideLoading", "ShowLoading", "Page<PERSON><PERSON>le", "useNavigate", "Select", "QuizGrid", "Card", "<PERSON><PERSON>", "Input", "Loading", "TbSearch", "Tb<PERSON><PERSON>er", "TbTrophy", "TbClock", "TbUsers", "TbQuestionMark", "TbBrain", "BsBookFill", "jsxDEV", "_jsxDEV", "primaryClasses", "value", "label", "secondaryClasses", "advanceClasses", "Quiz", "_s", "_user$level", "_user$level2", "_user$class", "_user$class2", "exams", "setExams", "searchQuery", "setSearch<PERSON>uery", "reportsData", "setReportsData", "selectedClass", "setSelectedClass", "navigate", "dispatch", "user", "state", "lgSize", "setLgSize", "availableClasses", "level", "toLowerCase", "class", "defaultSelectedClass", "find", "option", "updateLgSize", "window", "innerWidth", "addEventListener", "removeEventListener", "handleClassChange", "selectedOption", "filteredExams", "filter", "exam", "_exam$name", "_exam$category", "_exam$class", "classMatches", "selected<PERSON><PERSON><PERSON>", "examClass", "replace", "searchMatches", "trim", "name", "includes", "category", "length", "console", "log", "getExams", "response", "success", "data", "reverse", "error", "filterReportsData", "reportsMap", "for<PERSON>ach", "report", "examId", "_id", "verdict", "result", "Object", "values", "getData", "verifyRetake", "retakeCount", "item", "handleSearch", "e", "target", "shouldRenderFilteredExams", "style", "minHeight", "paddingBottom", "children", "className", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginRight", "type", "placeholder", "onChange", "options", "styles", "width", "isSearchable", "Row", "gutter", "marginLeft", "map", "index", "_examReport$result", "_examReport$result$ve", "_examReport$result2", "_examReport$result2$v", "_examReport$result3", "_examReport$result3$v", "_examReport$result4", "_examReport$result4$v", "_examReport$result5", "_examReport$result5$v", "_examReport$result6", "_examReport$result6$v", "_examReport$result7", "_examReport$result7$v", "_examReport$result8", "_examReport$result8$v", "_examReport$result9", "_examReport$result9$v", "_examReport$result10", "_examReport$result10$", "_examReport$result11", "_examReport$result11$", "_examReport$result12", "_examReport$result12$", "examReport", "Col", "xs", "sm", "md", "lg", "height", "boxSizing", "border", "position", "top", "right", "fontSize", "fontWeight", "color", "totalMarks", "passingMarks", "duration", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { motion } from \"framer-motion\";\r\nimport { message } from \"antd\";\r\nimport { getAllExams } from \"../../../apicalls/exams\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport Select from \"react-select\";\r\nimport { QuizGrid, Card, Button, Input, Loading } from \"../../../components/modern\";\r\nimport { TbSearch, TbFilter, TbTrophy, TbClock, TbUsers, TbQuestionMark, TbBrain } from \"react-icons/tb\";\r\nimport { BsBookFill } from \"react-icons/bs\";\r\nimport \"./style.css\";\r\n\r\n\r\nconst primaryClasses = [\r\n  { value: \"\", label: \"All Classes\" },\r\n  { value: \"1\", label: \"Class 1\" },\r\n  { value: \"2\", label: \"Class 2\" },\r\n  { value: \"3\", label: \"Class 3\" },\r\n  { value: \"4\", label: \"Class 4\" },\r\n  { value: \"5\", label: \"Class 5\" },\r\n  { value: \"6\", label: \"Class 6\" },\r\n  { value: \"7\", label: \"Class 7\" },\r\n];\r\n\r\nconst secondaryClasses = [\r\n  { value: \"\", label: \"All Classes\" },\r\n  { value: \"Form-1\", label: \"Form 1\" },\r\n  { value: \"Form-2\", label: \"Form 2\" },\r\n  { value: \"Form-3\", label: \"Form 3\" },\r\n  { value: \"Form-4\", label: \"Form 4\" },\r\n];\r\n\r\nconst advanceClasses = [\r\n  { value: \"\", label: \"All Classes\" },\r\n  { value: \"Form-5\", label: \"Form 5\" },\r\n  { value: \"Form-6\", label: \"Form 6\" },\r\n];\r\n\r\nfunction Quiz() {\r\n  const [exams, setExams] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [reportsData, setReportsData] = useState([]);\r\n  const [selectedClass, setSelectedClass] = useState(null);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n  const [lgSize, setLgSize] = useState(8);\r\n\r\n  const availableClasses =\r\n    user?.level?.toLowerCase() === \"primary\"\r\n      ? primaryClasses\r\n      : user?.level?.toLowerCase() === \"secondary\"\r\n        ? secondaryClasses\r\n        : advanceClasses;\r\n\r\n  useEffect(() => {\r\n    if (user && user.class) {\r\n      const defaultSelectedClass = availableClasses.find(\r\n        (option) => option.value === user.class\r\n      );\r\n      setSelectedClass(defaultSelectedClass);\r\n    }\r\n  }, [user, availableClasses]);\r\n\r\n  useEffect(() => {\r\n    const updateLgSize = () => {\r\n      setLgSize(window.innerWidth < 1380 ? 9 : 7);\r\n    };\r\n\r\n    // Set initial lg size\r\n    updateLgSize();\r\n\r\n    // Add event listener for window resize\r\n    window.addEventListener(\"resize\", updateLgSize);\r\n\r\n    // Cleanup event listener on component unmount\r\n    return () => {\r\n      window.removeEventListener(\"resize\", updateLgSize);\r\n    };\r\n  }, []);\r\n\r\n  const handleClassChange = (selectedOption) => {\r\n    setSelectedClass(selectedOption);\r\n  };\r\n\r\n  const filteredExams = exams.filter(\r\n    (exam) => {\r\n      // Handle class filtering with format compatibility\r\n      let classMatches = true;\r\n      if (selectedClass && selectedClass.value !== \"\") {\r\n        const selectedValue = selectedClass.value;\r\n        const examClass = exam.class;\r\n\r\n        // Check for exact match first\r\n        if (examClass === selectedValue) {\r\n          classMatches = true;\r\n        }\r\n        // Check if exam class has \"Class-\" prefix and selected value is just the number\r\n        else if (examClass === `Class-${selectedValue}`) {\r\n          classMatches = true;\r\n        }\r\n        // Check if selected value has \"Class-\" prefix and exam class is just the number\r\n        else if (selectedValue === `Class-${examClass}`) {\r\n          classMatches = true;\r\n        }\r\n        // Check for Form classes (secondary)\r\n        else if (examClass === `Form-${selectedValue.replace('Form-', '')}`) {\r\n          classMatches = true;\r\n        }\r\n        else if (selectedValue === `Form-${examClass.replace('Form-', '')}`) {\r\n          classMatches = true;\r\n        }\r\n        else {\r\n          classMatches = false;\r\n        }\r\n      }\r\n\r\n      // Handle search filtering\r\n      const searchMatches = !searchQuery.trim() ||\r\n        exam.name?.toLowerCase().includes(searchQuery.toLowerCase().trim()) ||\r\n        exam.category?.toLowerCase().includes(searchQuery.toLowerCase().trim()) ||\r\n        exam.class?.toLowerCase().includes(searchQuery.toLowerCase().trim());\r\n\r\n      return classMatches && searchMatches;\r\n    }\r\n  );\r\n\r\n  // Debug logging\r\n  if (exams.length > 0) {\r\n    console.log(`📊 Quiz Debug: ${filteredExams.length}/${exams.length} exams shown | Class: ${selectedClass?.label || 'None'} | Search: \"${searchQuery}\"`);\r\n  }\r\n\r\n  const getExams = async () => {\r\n    try {\r\n      console.log(\"🔍 Starting to fetch exams...\");\r\n      dispatch(ShowLoading());\r\n      const response = await getAllExams();\r\n      console.log(\"📡 API Response:\", response);\r\n      if (response.success) {\r\n        console.log(\"✅ Exams fetched successfully:\", response.data.length, \"exams\");\r\n        setExams(response.data.reverse());\r\n      } else {\r\n        console.error(\"❌ API Error:\", response.message);\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      console.error(\"❌ Network Error:\", error);\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const filterReportsData = (data) => {\r\n    const reportsMap = {};\r\n\r\n    // Iterate over the response data (reports)\r\n    data.forEach(report => {\r\n      const examId = report.exam._id;\r\n      const verdict = report.result.verdict;\r\n\r\n      // If the examId is not already in the map, add it\r\n      if (!reportsMap[examId]) {\r\n        reportsMap[examId] = report;\r\n      } else {\r\n        // If there is already an entry for this exam, keep the one with \"pass\" verdict, or just keep the first one if no \"pass\"\r\n        if (verdict === \"Pass\" && reportsMap[examId].result.verdict !== \"Pass\") {\r\n          reportsMap[examId] = report; // Replace with the \"pass\" verdict report\r\n        }\r\n      }\r\n    });\r\n\r\n    return Object.values(reportsMap);\r\n  };\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n\r\n        setReportsData(filterReportsData(response.data));\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n    getExams();\r\n  }, []);\r\n\r\n  const verifyRetake = async (exam) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      const retakeCount = response.data.filter(\r\n        (item) => item.exam && item.exam._id === exam._id\r\n      ).length;\r\n      console.log(\"Retake count for exam:\", retakeCount);\r\n    } catch (error) {\r\n      message.error(\"Unable to verify retake\");\r\n      dispatch(HideLoading());\r\n      return;\r\n    }\r\n    dispatch(HideLoading());\r\n    navigate(`/user/write-exam/${exam._id}`);\r\n  };\r\n\r\n  const handleSearch = (e) => {\r\n    setSearchQuery(e.target.value);\r\n  };\r\n\r\n  const shouldRenderFilteredExams = filteredExams.length < exams.length;\r\n\r\n  return (\r\n    user && (\r\n      <div style={{ minHeight: \"80vh\", paddingBottom: '20px' }}>\r\n        <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4\">\r\n          <div>\r\n            <PageTitle title=\"Challenge your brain, Beat the rest\" />\r\n            <div className=\"text-lg text-gray-600 mt-2\">\r\n              <span className=\"font-semibold\">Current Class: </span>\r\n              <span className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\">\r\n                {user?.level === \"Primary\"\r\n                  ? `Class ${user?.class}`\r\n                  : user?.level === \"Secondary\"\r\n                  ? `Form ${user?.class?.replace('Form-', '')}`\r\n                  : user?.level === \"Advance\"\r\n                  ? `Form ${user?.class?.replace('Form-', '')}`\r\n                  : user?.class || 'Not Set'}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"divider\"></div>\r\n\r\n\r\n        <div\r\n          className=\"flex justify-between items-center mb-2 flex-wrap\"\r\n          style={{ marginRight: \"20px\" }}\r\n        >\r\n          <div className=\"flex flex-col gap-1\">\r\n            {/* Search Bar */}\r\n            <div>Search Quiz Title:</div>\r\n            <input\r\n              type=\"text\"\r\n              className=\"w-100 mb-2\"\r\n              placeholder=\"Search quizes\"\r\n              value={searchQuery}\r\n              onChange={handleSearch}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex flex-col gap-1\">\r\n            {/* Class Selector */}\r\n            <div>Please Select Class:</div>\r\n            <Select\r\n              options={availableClasses}\r\n              value={selectedClass}\r\n              onChange={handleClassChange}\r\n              placeholder=\"Select Class\"\r\n              styles={{ width: \"300px\" }}\r\n              isSearchable={false}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {shouldRenderFilteredExams && (\r\n          <div className=\"mb-2\">\r\n            <span>{`Filtered ${filteredExams.length} out of ${exams.length}`}</span>\r\n          </div>\r\n        )}\r\n\r\n        <Row gutter={[16, 16]} style={{ marginLeft: 0, marginRight: 0 }}>\r\n          {filteredExams.map((exam, index) => {\r\n            const examReport = reportsData.find(\r\n              (report) => report.exam && report.exam._id === exam._id\r\n            );\r\n\r\n            return (\r\n              <Col xs={24} sm={12} md={9} lg={lgSize} key={index}>\r\n                <div\r\n                  style={{\r\n                    height: \"100%\",\r\n                    boxSizing: \"border-box\",\r\n                    border: `1px solid ${examReport?.result?.verdict?.toLowerCase() === \"fail\"\r\n                      ? \"#FE8267\"\r\n                      : examReport?.result?.verdict?.toLowerCase() === \"pass\"\r\n                        ? \"#43C46C\"\r\n                        : \"#0E8FE9\"}`\r\n                  }}\r\n                  className={`card-lg flex flex-col gap-1 p-2 card-design ${examReport?.result?.verdict?.toLowerCase() === \"fail\"\r\n                    ? \"fail\"\r\n                    : examReport?.result?.verdict?.toLowerCase() === \"pass\"\r\n                      ? \"pass\"\r\n                      : \"no-attempts\"}`}\r\n                >\r\n                  <h1 className=\"text-2xl flex items-center gap-1\">\r\n                    <span className={`box-tags-icon  ${examReport?.result?.verdict?.toLowerCase() === \"fail\"\r\n                      ? \"fail-dark\"\r\n                      : examReport?.result?.verdict?.toLowerCase() === \"pass\"\r\n                        ? \"pass-dark\"\r\n                        : \"no-attempts-dark\"}`}>\r\n                      <BsBookFill />\r\n                    </span>\r\n                    {exam?.name}\r\n                  </h1>\r\n\r\n                  <span style={{\r\n                    position: 'absolute', top: '20px', right: '30px', fontSize: '14px', fontWeight: 'bold', color: examReport?.result?.verdict?.toLowerCase() === \"fail\"\r\n                      ? \"#FE8267\"\r\n                      : examReport?.result?.verdict?.toLowerCase() === \"pass\"\r\n                        ? \"#43C46C\"\r\n                        : \"#0E8FE9\"\r\n                  }}>\r\n                    {examReport?.result?.verdict?.toLowerCase() === \"fail\"\r\n                      ? \"Failed\"\r\n                      : examReport?.result?.verdict?.toLowerCase() === \"pass\"\r\n                        ? \"Passed\"\r\n                        : \"No Attempts\"}\r\n                  </span>\r\n\r\n                  <h1 className=\"text-xl\">Subject: {exam.category}</h1>\r\n                  <div className=\"flex justify-between\">\r\n                    <h1 className=\"text-md box-tags\">Total Marks: {exam.totalMarks}</h1>\r\n\r\n                    <h1 className=\"text-md box-tags\">\r\n                      Passing Marks: {exam.passingMarks}\r\n                    </h1>\r\n                  </div>\r\n\r\n                  <div className=\"flex justify-between items-center\">\r\n                    <h1 className=\"text-md box-tags\">Duration: {exam.duration}</h1>\r\n\r\n                    <button\r\n                      className={`box-tags-button text-md ${examReport?.result?.verdict?.toLowerCase() === \"fail\"\r\n                        ? \"fail-dark\"\r\n                        : examReport?.result?.verdict?.toLowerCase() === \"pass\"\r\n                          ? \"pass-dark\"\r\n                          : \"no-attempts-dark\"}`}\r\n                      onClick={() => verifyRetake(exam)}\r\n                    >\r\n                      Start Quiz\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </Col>\r\n            );\r\n          })}\r\n        </Row>\r\n      </div>\r\n    )\r\n  );\r\n}\r\n\r\nexport default Quiz;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,4BAA4B;AACnF,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,cAAc,EAAEC,OAAO,QAAQ,gBAAgB;AACxG,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGrB,MAAMC,cAAc,GAAG,CACrB;EAAEC,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAc,CAAC,EACnC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChC;EAAED,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAU,CAAC,CACjC;AAED,MAAMC,gBAAgB,GAAG,CACvB;EAAEF,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAc,CAAC,EACnC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,CACrC;AAED,MAAME,cAAc,GAAG,CACrB;EAAEH,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAc,CAAC,EACnC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,CACrC;AAED,SAASG,IAAIA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,YAAA;EACd,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM+C,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAMqC,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE+C;EAAK,CAAC,GAAG9C,WAAW,CAAE+C,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EAEvC,MAAMqD,gBAAgB,GACpB,CAAAJ,IAAI,aAAJA,IAAI,wBAAAd,WAAA,GAAJc,IAAI,CAAEK,KAAK,cAAAnB,WAAA,uBAAXA,WAAA,CAAaoB,WAAW,CAAC,CAAC,MAAK,SAAS,GACpC3B,cAAc,GACd,CAAAqB,IAAI,aAAJA,IAAI,wBAAAb,YAAA,GAAJa,IAAI,CAAEK,KAAK,cAAAlB,YAAA,uBAAXA,YAAA,CAAamB,WAAW,CAAC,CAAC,MAAK,WAAW,GACxCxB,gBAAgB,GAChBC,cAAc;EAEtB/B,SAAS,CAAC,MAAM;IACd,IAAIgD,IAAI,IAAIA,IAAI,CAACO,KAAK,EAAE;MACtB,MAAMC,oBAAoB,GAAGJ,gBAAgB,CAACK,IAAI,CAC/CC,MAAM,IAAKA,MAAM,CAAC9B,KAAK,KAAKoB,IAAI,CAACO,KACpC,CAAC;MACDV,gBAAgB,CAACW,oBAAoB,CAAC;IACxC;EACF,CAAC,EAAE,CAACR,IAAI,EAAEI,gBAAgB,CAAC,CAAC;EAE5BpD,SAAS,CAAC,MAAM;IACd,MAAM2D,YAAY,GAAGA,CAAA,KAAM;MACzBR,SAAS,CAACS,MAAM,CAACC,UAAU,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;;IAED;IACAF,YAAY,CAAC,CAAC;;IAEd;IACAC,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;;IAE/C;IACA,OAAO,MAAM;MACXC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,iBAAiB,GAAIC,cAAc,IAAK;IAC5CpB,gBAAgB,CAACoB,cAAc,CAAC;EAClC,CAAC;EAED,MAAMC,aAAa,GAAG5B,KAAK,CAAC6B,MAAM,CAC/BC,IAAI,IAAK;IAAA,IAAAC,UAAA,EAAAC,cAAA,EAAAC,WAAA;IACR;IACA,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAI5B,aAAa,IAAIA,aAAa,CAAChB,KAAK,KAAK,EAAE,EAAE;MAC/C,MAAM6C,aAAa,GAAG7B,aAAa,CAAChB,KAAK;MACzC,MAAM8C,SAAS,GAAGN,IAAI,CAACb,KAAK;;MAE5B;MACA,IAAImB,SAAS,KAAKD,aAAa,EAAE;QAC/BD,YAAY,GAAG,IAAI;MACrB;MACA;MAAA,KACK,IAAIE,SAAS,KAAM,SAAQD,aAAc,EAAC,EAAE;QAC/CD,YAAY,GAAG,IAAI;MACrB;MACA;MAAA,KACK,IAAIC,aAAa,KAAM,SAAQC,SAAU,EAAC,EAAE;QAC/CF,YAAY,GAAG,IAAI;MACrB;MACA;MAAA,KACK,IAAIE,SAAS,KAAM,QAAOD,aAAa,CAACE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,EAAC,EAAE;QACnEH,YAAY,GAAG,IAAI;MACrB,CAAC,MACI,IAAIC,aAAa,KAAM,QAAOC,SAAS,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,EAAC,EAAE;QACnEH,YAAY,GAAG,IAAI;MACrB,CAAC,MACI;QACHA,YAAY,GAAG,KAAK;MACtB;IACF;;IAEA;IACA,MAAMI,aAAa,GAAG,CAACpC,WAAW,CAACqC,IAAI,CAAC,CAAC,MAAAR,UAAA,GACvCD,IAAI,CAACU,IAAI,cAAAT,UAAA,uBAATA,UAAA,CAAWf,WAAW,CAAC,CAAC,CAACyB,QAAQ,CAACvC,WAAW,CAACc,WAAW,CAAC,CAAC,CAACuB,IAAI,CAAC,CAAC,CAAC,OAAAP,cAAA,GACnEF,IAAI,CAACY,QAAQ,cAAAV,cAAA,uBAAbA,cAAA,CAAehB,WAAW,CAAC,CAAC,CAACyB,QAAQ,CAACvC,WAAW,CAACc,WAAW,CAAC,CAAC,CAACuB,IAAI,CAAC,CAAC,CAAC,OAAAN,WAAA,GACvEH,IAAI,CAACb,KAAK,cAAAgB,WAAA,uBAAVA,WAAA,CAAYjB,WAAW,CAAC,CAAC,CAACyB,QAAQ,CAACvC,WAAW,CAACc,WAAW,CAAC,CAAC,CAACuB,IAAI,CAAC,CAAC,CAAC;IAEtE,OAAOL,YAAY,IAAII,aAAa;EACtC,CACF,CAAC;;EAED;EACA,IAAItC,KAAK,CAAC2C,MAAM,GAAG,CAAC,EAAE;IACpBC,OAAO,CAACC,GAAG,CAAE,kBAAiBjB,aAAa,CAACe,MAAO,IAAG3C,KAAK,CAAC2C,MAAO,yBAAwB,CAAArC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEf,KAAK,KAAI,MAAO,eAAcW,WAAY,GAAE,CAAC;EACzJ;EAEA,MAAM4C,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFF,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CpC,QAAQ,CAACvC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM6E,QAAQ,GAAG,MAAMhF,WAAW,CAAC,CAAC;MACpC6E,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,QAAQ,CAAC;MACzC,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEE,QAAQ,CAACE,IAAI,CAACN,MAAM,EAAE,OAAO,CAAC;QAC3E1C,QAAQ,CAAC8C,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACLN,OAAO,CAACO,KAAK,CAAC,cAAc,EAAEJ,QAAQ,CAACjF,OAAO,CAAC;QAC/CA,OAAO,CAACqF,KAAK,CAACJ,QAAQ,CAACjF,OAAO,CAAC;MACjC;MACA2C,QAAQ,CAACxC,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOkF,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC1C,QAAQ,CAACxC,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAACqF,KAAK,CAACA,KAAK,CAACrF,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMsF,iBAAiB,GAAIH,IAAI,IAAK;IAClC,MAAMI,UAAU,GAAG,CAAC,CAAC;;IAErB;IACAJ,IAAI,CAACK,OAAO,CAACC,MAAM,IAAI;MACrB,MAAMC,MAAM,GAAGD,MAAM,CAACzB,IAAI,CAAC2B,GAAG;MAC9B,MAAMC,OAAO,GAAGH,MAAM,CAACI,MAAM,CAACD,OAAO;;MAErC;MACA,IAAI,CAACL,UAAU,CAACG,MAAM,CAAC,EAAE;QACvBH,UAAU,CAACG,MAAM,CAAC,GAAGD,MAAM;MAC7B,CAAC,MAAM;QACL;QACA,IAAIG,OAAO,KAAK,MAAM,IAAIL,UAAU,CAACG,MAAM,CAAC,CAACG,MAAM,CAACD,OAAO,KAAK,MAAM,EAAE;UACtEL,UAAU,CAACG,MAAM,CAAC,GAAGD,MAAM,CAAC,CAAC;QAC/B;MACF;IACF,CAAC,CAAC;;IAEF,OAAOK,MAAM,CAACC,MAAM,CAACR,UAAU,CAAC;EAClC,CAAC;EAED,MAAMS,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACFrD,QAAQ,CAACvC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM6E,QAAQ,GAAG,MAAM/E,mBAAmB,CAAC,CAAC;MAC5C,IAAI+E,QAAQ,CAACC,OAAO,EAAE;QAEpB3C,cAAc,CAAC+C,iBAAiB,CAACL,QAAQ,CAACE,IAAI,CAAC,CAAC;MAClD,CAAC,MAAM;QACLnF,OAAO,CAACqF,KAAK,CAACJ,QAAQ,CAACjF,OAAO,CAAC;MACjC;MACA2C,QAAQ,CAACxC,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOkF,KAAK,EAAE;MACd1C,QAAQ,CAACxC,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAACqF,KAAK,CAACA,KAAK,CAACrF,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDJ,SAAS,CAAC,MAAM;IACdoG,OAAO,CAAC,CAAC;IACThB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,YAAY,GAAG,MAAOjC,IAAI,IAAK;IACnC,IAAI;MACFrB,QAAQ,CAACvC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM6E,QAAQ,GAAG,MAAM/E,mBAAmB,CAAC,CAAC;MAC5C,MAAMgG,WAAW,GAAGjB,QAAQ,CAACE,IAAI,CAACpB,MAAM,CACrCoC,IAAI,IAAKA,IAAI,CAACnC,IAAI,IAAImC,IAAI,CAACnC,IAAI,CAAC2B,GAAG,KAAK3B,IAAI,CAAC2B,GAChD,CAAC,CAACd,MAAM;MACRC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,WAAW,CAAC;IACpD,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdrF,OAAO,CAACqF,KAAK,CAAC,yBAAyB,CAAC;MACxC1C,QAAQ,CAACxC,WAAW,CAAC,CAAC,CAAC;MACvB;IACF;IACAwC,QAAQ,CAACxC,WAAW,CAAC,CAAC,CAAC;IACvBuC,QAAQ,CAAE,oBAAmBsB,IAAI,CAAC2B,GAAI,EAAC,CAAC;EAC1C,CAAC;EAED,MAAMS,YAAY,GAAIC,CAAC,IAAK;IAC1BhE,cAAc,CAACgE,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAC;EAChC,CAAC;EAED,MAAM+E,yBAAyB,GAAGzC,aAAa,CAACe,MAAM,GAAG3C,KAAK,CAAC2C,MAAM;EAErE,OACEjC,IAAI,iBACFtB,OAAA;IAAKkF,KAAK,EAAE;MAAEC,SAAS,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACvDrF,OAAA;MAAKsF,SAAS,EAAC,kFAAkF;MAAAD,QAAA,eAC/FrF,OAAA;QAAAqF,QAAA,gBACErF,OAAA,CAACjB,SAAS;UAACwG,KAAK,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzD3F,OAAA;UAAKsF,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzCrF,OAAA;YAAMsF,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtD3F,OAAA;YAAMsF,SAAS,EAAC,sEAAsE;YAAAD,QAAA,EACnF,CAAA/D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,KAAK,MAAK,SAAS,GACrB,SAAQL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAM,EAAC,GACtB,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,KAAK,MAAK,WAAW,GAC1B,QAAOL,IAAI,aAAJA,IAAI,wBAAAZ,WAAA,GAAJY,IAAI,CAAEO,KAAK,cAAAnB,WAAA,uBAAXA,WAAA,CAAauC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,EAAC,GAC3C,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,KAAK,MAAK,SAAS,GACxB,QAAOL,IAAI,aAAJA,IAAI,wBAAAX,YAAA,GAAJW,IAAI,CAAEO,KAAK,cAAAlB,YAAA,uBAAXA,YAAA,CAAasC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,EAAC,GAC3C,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAK,KAAI;UAAS;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN3F,OAAA;MAAKsF,SAAS,EAAC;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAG/B3F,OAAA;MACEsF,SAAS,EAAC,kDAAkD;MAC5DJ,KAAK,EAAE;QAAEU,WAAW,EAAE;MAAO,CAAE;MAAAP,QAAA,gBAE/BrF,OAAA;QAAKsF,SAAS,EAAC,qBAAqB;QAAAD,QAAA,gBAElCrF,OAAA;UAAAqF,QAAA,EAAK;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7B3F,OAAA;UACE6F,IAAI,EAAC,MAAM;UACXP,SAAS,EAAC,YAAY;UACtBQ,WAAW,EAAC,eAAe;UAC3B5F,KAAK,EAAEY,WAAY;UACnBiF,QAAQ,EAAEjB;QAAa;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN3F,OAAA;QAAKsF,SAAS,EAAC,qBAAqB;QAAAD,QAAA,gBAElCrF,OAAA;UAAAqF,QAAA,EAAK;QAAoB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/B3F,OAAA,CAACf,MAAM;UACL+G,OAAO,EAAEtE,gBAAiB;UAC1BxB,KAAK,EAAEgB,aAAc;UACrB6E,QAAQ,EAAEzD,iBAAkB;UAC5BwD,WAAW,EAAC,cAAc;UAC1BG,MAAM,EAAE;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAC3BC,YAAY,EAAE;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELV,yBAAyB,iBACxBjF,OAAA;MAAKsF,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBrF,OAAA;QAAAqF,QAAA,EAAQ,YAAW7C,aAAa,CAACe,MAAO,WAAU3C,KAAK,CAAC2C,MAAO;MAAC;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CACN,eAED3F,OAAA,CAACoG,GAAG;MAACC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACnB,KAAK,EAAE;QAAEoB,UAAU,EAAE,CAAC;QAAEV,WAAW,EAAE;MAAE,CAAE;MAAAP,QAAA,EAC7D7C,aAAa,CAAC+D,GAAG,CAAC,CAAC7D,IAAI,EAAE8D,KAAK,KAAK;QAAA,IAAAC,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA;QAClC,MAAMC,UAAU,GAAGjH,WAAW,CAACe,IAAI,CAChCoC,MAAM,IAAKA,MAAM,CAACzB,IAAI,IAAIyB,MAAM,CAACzB,IAAI,CAAC2B,GAAG,KAAK3B,IAAI,CAAC2B,GACtD,CAAC;QAED,oBACErE,OAAA,CAACkI,GAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE9G,MAAO;UAAA6D,QAAA,eACrCrF,OAAA;YACEkF,KAAK,EAAE;cACLqD,MAAM,EAAE,MAAM;cACdC,SAAS,EAAE,YAAY;cACvBC,MAAM,EAAG,aAAY,CAAAR,UAAU,aAAVA,UAAU,wBAAAxB,kBAAA,GAAVwB,UAAU,CAAE1D,MAAM,cAAAkC,kBAAA,wBAAAC,qBAAA,GAAlBD,kBAAA,CAAoBnC,OAAO,cAAAoC,qBAAA,uBAA3BA,qBAAA,CAA6B9E,WAAW,CAAC,CAAC,MAAK,MAAM,GACtE,SAAS,GACT,CAAAqG,UAAU,aAAVA,UAAU,wBAAAtB,mBAAA,GAAVsB,UAAU,CAAE1D,MAAM,cAAAoC,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBrC,OAAO,cAAAsC,qBAAA,uBAA3BA,qBAAA,CAA6BhF,WAAW,CAAC,CAAC,MAAK,MAAM,GACnD,SAAS,GACT,SAAU;YAClB,CAAE;YACF0D,SAAS,EAAG,+CAA8C,CAAA2C,UAAU,aAAVA,UAAU,wBAAApB,mBAAA,GAAVoB,UAAU,CAAE1D,MAAM,cAAAsC,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBvC,OAAO,cAAAwC,qBAAA,uBAA3BA,qBAAA,CAA6BlF,WAAW,CAAC,CAAC,MAAK,MAAM,GAC3G,MAAM,GACN,CAAAqG,UAAU,aAAVA,UAAU,wBAAAlB,mBAAA,GAAVkB,UAAU,CAAE1D,MAAM,cAAAwC,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBzC,OAAO,cAAA0C,qBAAA,uBAA3BA,qBAAA,CAA6BpF,WAAW,CAAC,CAAC,MAAK,MAAM,GACnD,MAAM,GACN,aAAc,EAAE;YAAAyD,QAAA,gBAEtBrF,OAAA;cAAIsF,SAAS,EAAC,kCAAkC;cAAAD,QAAA,gBAC9CrF,OAAA;gBAAMsF,SAAS,EAAG,kBAAiB,CAAA2C,UAAU,aAAVA,UAAU,wBAAAhB,mBAAA,GAAVgB,UAAU,CAAE1D,MAAM,cAAA0C,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB3C,OAAO,cAAA4C,qBAAA,uBAA3BA,qBAAA,CAA6BtF,WAAW,CAAC,CAAC,MAAK,MAAM,GACpF,WAAW,GACX,CAAAqG,UAAU,aAAVA,UAAU,wBAAAd,mBAAA,GAAVc,UAAU,CAAE1D,MAAM,cAAA4C,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB7C,OAAO,cAAA8C,qBAAA,uBAA3BA,qBAAA,CAA6BxF,WAAW,CAAC,CAAC,MAAK,MAAM,GACnD,WAAW,GACX,kBAAmB,EAAE;gBAAAyD,QAAA,eACzBrF,OAAA,CAACF,UAAU;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,EACNjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEL3F,OAAA;cAAMkF,KAAK,EAAE;gBACXwD,QAAQ,EAAE,UAAU;gBAAEC,GAAG,EAAE,MAAM;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAEC,KAAK,EAAE,CAAAd,UAAU,aAAVA,UAAU,wBAAAZ,mBAAA,GAAVY,UAAU,CAAE1D,MAAM,cAAA8C,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB/C,OAAO,cAAAgD,qBAAA,uBAA3BA,qBAAA,CAA6B1F,WAAW,CAAC,CAAC,MAAK,MAAM,GAChJ,SAAS,GACT,CAAAqG,UAAU,aAAVA,UAAU,wBAAAV,mBAAA,GAAVU,UAAU,CAAE1D,MAAM,cAAAgD,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBjD,OAAO,cAAAkD,qBAAA,uBAA3BA,qBAAA,CAA6B5F,WAAW,CAAC,CAAC,MAAK,MAAM,GACnD,SAAS,GACT;cACR,CAAE;cAAAyD,QAAA,EACC,CAAA4C,UAAU,aAAVA,UAAU,wBAAAR,mBAAA,GAAVQ,UAAU,CAAE1D,MAAM,cAAAkD,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBnD,OAAO,cAAAoD,qBAAA,uBAA3BA,qBAAA,CAA6B9F,WAAW,CAAC,CAAC,MAAK,MAAM,GAClD,QAAQ,GACR,CAAAqG,UAAU,aAAVA,UAAU,wBAAAN,oBAAA,GAAVM,UAAU,CAAE1D,MAAM,cAAAoD,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBrD,OAAO,cAAAsD,qBAAA,uBAA3BA,qBAAA,CAA6BhG,WAAW,CAAC,CAAC,MAAK,MAAM,GACnD,QAAQ,GACR;YAAa;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEP3F,OAAA;cAAIsF,SAAS,EAAC,SAAS;cAAAD,QAAA,GAAC,WAAS,EAAC3C,IAAI,CAACY,QAAQ;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrD3F,OAAA;cAAKsF,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnCrF,OAAA;gBAAIsF,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,GAAC,eAAa,EAAC3C,IAAI,CAACsG,UAAU;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEpE3F,OAAA;gBAAIsF,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,GAAC,iBAChB,EAAC3C,IAAI,CAACuG,YAAY;cAAA;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEN3F,OAAA;cAAKsF,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChDrF,OAAA;gBAAIsF,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,GAAC,YAAU,EAAC3C,IAAI,CAACwG,QAAQ;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAE/D3F,OAAA;gBACEsF,SAAS,EAAG,2BAA0B,CAAA2C,UAAU,aAAVA,UAAU,wBAAAJ,oBAAA,GAAVI,UAAU,CAAE1D,MAAM,cAAAsD,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBvD,OAAO,cAAAwD,qBAAA,uBAA3BA,qBAAA,CAA6BlG,WAAW,CAAC,CAAC,MAAK,MAAM,GACvF,WAAW,GACX,CAAAqG,UAAU,aAAVA,UAAU,wBAAAF,oBAAA,GAAVE,UAAU,CAAE1D,MAAM,cAAAwD,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBzD,OAAO,cAAA0D,qBAAA,uBAA3BA,qBAAA,CAA6BpG,WAAW,CAAC,CAAC,MAAK,MAAM,GACnD,WAAW,GACX,kBAAmB,EAAE;gBAC3BuH,OAAO,EAAEA,CAAA,KAAMxE,YAAY,CAACjC,IAAI,CAAE;gBAAA2C,QAAA,EACnC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjEqCa,KAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkE7C,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;AAEL;AAACpF,EAAA,CAjUQD,IAAI;EAAA,QAKMtB,WAAW,EACXT,WAAW,EACXC,WAAW;AAAA;AAAA4K,EAAA,GAPrB9I,IAAI;AAmUb,eAAeA,IAAI;AAAC,IAAA8I,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}