{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport { useEffect, useRef, useState } from 'react';\nimport { addGlobalMouseDownEvent, getTargetFromEvent } from \"../utils/uiUtil\";\nexport default function usePickerInput(_ref) {\n  var open = _ref.open,\n    value = _ref.value,\n    isClickOutside = _ref.isClickOutside,\n    triggerOpen = _ref.triggerOpen,\n    forwardKeyDown = _ref.forwardKeyDown,\n    _onKeyDown = _ref.onKeyDown,\n    blurToCancel = _ref.blurToCancel,\n    onSubmit = _ref.onSubmit,\n    onCancel = _ref.onCancel,\n    _onFocus = _ref.onFocus,\n    _onBlur = _ref.onBlur,\n    changeOnBlur = _ref.changeOnBlur;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    typing = _useState2[0],\n    setTyping = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    focused = _useState4[0],\n    setFocused = _useState4[1];\n\n  /**\n   * We will prevent blur to handle open event when user click outside,\n   * since this will repeat trigger `onOpenChange` event.\n   */\n  var preventBlurRef = useRef(false);\n  var valueChangedRef = useRef(false);\n  var preventDefaultRef = useRef(false);\n  var inputProps = {\n    onMouseDown: function onMouseDown() {\n      setTyping(true);\n      triggerOpen(true);\n    },\n    onKeyDown: function onKeyDown(e) {\n      var preventDefault = function preventDefault() {\n        preventDefaultRef.current = true;\n      };\n      _onKeyDown(e, preventDefault);\n      if (preventDefaultRef.current) return;\n      switch (e.which) {\n        case KeyCode.ENTER:\n          {\n            if (!open) {\n              triggerOpen(true);\n            } else if (onSubmit() !== false) {\n              setTyping(true);\n            }\n            e.preventDefault();\n            return;\n          }\n        case KeyCode.TAB:\n          {\n            if (typing && open && !e.shiftKey) {\n              setTyping(false);\n              e.preventDefault();\n            } else if (!typing && open) {\n              if (!forwardKeyDown(e) && e.shiftKey) {\n                setTyping(true);\n                e.preventDefault();\n              }\n            }\n            return;\n          }\n        case KeyCode.ESC:\n          {\n            setTyping(true);\n            onCancel();\n            return;\n          }\n      }\n      if (!open && ![KeyCode.SHIFT].includes(e.which)) {\n        triggerOpen(true);\n      } else if (!typing) {\n        // Let popup panel handle keyboard\n        forwardKeyDown(e);\n      }\n    },\n    onFocus: function onFocus(e) {\n      setTyping(true);\n      setFocused(true);\n      if (_onFocus) {\n        _onFocus(e);\n      }\n    },\n    onBlur: function onBlur(e) {\n      if (preventBlurRef.current || !isClickOutside(document.activeElement)) {\n        preventBlurRef.current = false;\n        return;\n      }\n      if (blurToCancel) {\n        setTimeout(function () {\n          var _document = document,\n            activeElement = _document.activeElement;\n          while (activeElement && activeElement.shadowRoot) {\n            activeElement = activeElement.shadowRoot.activeElement;\n          }\n          if (isClickOutside(activeElement)) {\n            onCancel();\n          }\n        }, 0);\n      } else if (open) {\n        triggerOpen(false);\n        if (valueChangedRef.current) {\n          onSubmit();\n        }\n      }\n      setFocused(false);\n      _onBlur === null || _onBlur === void 0 ? void 0 : _onBlur(e);\n    }\n  };\n\n  // check if value changed\n  useEffect(function () {\n    valueChangedRef.current = false;\n  }, [open]);\n  useEffect(function () {\n    valueChangedRef.current = true;\n  }, [value]);\n\n  // Global click handler\n  useEffect(function () {\n    return addGlobalMouseDownEvent(function (e) {\n      var target = getTargetFromEvent(e);\n      var clickedOutside = isClickOutside(target);\n      if (open) {\n        if (!clickedOutside) {\n          preventBlurRef.current = true;\n\n          // Always set back in case `onBlur` prevented by user\n          raf(function () {\n            preventBlurRef.current = false;\n          });\n        } else if (!changeOnBlur && (!focused || clickedOutside)) {\n          triggerOpen(false);\n        }\n      }\n    });\n  });\n  return [inputProps, {\n    focused: focused,\n    typing: typing\n  }];\n}", "map": {"version": 3, "names": ["_slicedToArray", "KeyCode", "raf", "useEffect", "useRef", "useState", "addGlobalMouseDownEvent", "getTargetFromEvent", "usePickerInput", "_ref", "open", "value", "isClickOutside", "triggerOpen", "forwardKeyDown", "_onKeyDown", "onKeyDown", "blurToCancel", "onSubmit", "onCancel", "_onFocus", "onFocus", "_onBlur", "onBlur", "changeOnBlur", "_useState", "_useState2", "typing", "setTyping", "_useState3", "_useState4", "focused", "setFocused", "preventBlurRef", "valueChangedRef", "preventDefaultRef", "inputProps", "onMouseDown", "e", "preventDefault", "current", "which", "ENTER", "TAB", "shift<PERSON>ey", "ESC", "SHIFT", "includes", "document", "activeElement", "setTimeout", "_document", "shadowRoot", "target", "clickedOutside"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/hooks/usePickerInput.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport { useEffect, useRef, useState } from 'react';\nimport { addGlobalMouseDownEvent, getTargetFromEvent } from \"../utils/uiUtil\";\nexport default function usePickerInput(_ref) {\n  var open = _ref.open,\n    value = _ref.value,\n    isClickOutside = _ref.isClickOutside,\n    triggerOpen = _ref.triggerOpen,\n    forwardKeyDown = _ref.forwardKeyDown,\n    _onKeyDown = _ref.onKeyDown,\n    blurToCancel = _ref.blurToCancel,\n    onSubmit = _ref.onSubmit,\n    onCancel = _ref.onCancel,\n    _onFocus = _ref.onFocus,\n    _onBlur = _ref.onBlur,\n    changeOnBlur = _ref.changeOnBlur;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    typing = _useState2[0],\n    setTyping = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    focused = _useState4[0],\n    setFocused = _useState4[1];\n\n  /**\n   * We will prevent blur to handle open event when user click outside,\n   * since this will repeat trigger `onOpenChange` event.\n   */\n  var preventBlurRef = useRef(false);\n  var valueChangedRef = useRef(false);\n  var preventDefaultRef = useRef(false);\n  var inputProps = {\n    onMouseDown: function onMouseDown() {\n      setTyping(true);\n      triggerOpen(true);\n    },\n    onKeyDown: function onKeyDown(e) {\n      var preventDefault = function preventDefault() {\n        preventDefaultRef.current = true;\n      };\n      _onKeyDown(e, preventDefault);\n      if (preventDefaultRef.current) return;\n      switch (e.which) {\n        case KeyCode.ENTER:\n          {\n            if (!open) {\n              triggerOpen(true);\n            } else if (onSubmit() !== false) {\n              setTyping(true);\n            }\n            e.preventDefault();\n            return;\n          }\n        case KeyCode.TAB:\n          {\n            if (typing && open && !e.shiftKey) {\n              setTyping(false);\n              e.preventDefault();\n            } else if (!typing && open) {\n              if (!forwardKeyDown(e) && e.shiftKey) {\n                setTyping(true);\n                e.preventDefault();\n              }\n            }\n            return;\n          }\n        case KeyCode.ESC:\n          {\n            setTyping(true);\n            onCancel();\n            return;\n          }\n      }\n      if (!open && ![KeyCode.SHIFT].includes(e.which)) {\n        triggerOpen(true);\n      } else if (!typing) {\n        // Let popup panel handle keyboard\n        forwardKeyDown(e);\n      }\n    },\n    onFocus: function onFocus(e) {\n      setTyping(true);\n      setFocused(true);\n      if (_onFocus) {\n        _onFocus(e);\n      }\n    },\n    onBlur: function onBlur(e) {\n      if (preventBlurRef.current || !isClickOutside(document.activeElement)) {\n        preventBlurRef.current = false;\n        return;\n      }\n      if (blurToCancel) {\n        setTimeout(function () {\n          var _document = document,\n            activeElement = _document.activeElement;\n          while (activeElement && activeElement.shadowRoot) {\n            activeElement = activeElement.shadowRoot.activeElement;\n          }\n          if (isClickOutside(activeElement)) {\n            onCancel();\n          }\n        }, 0);\n      } else if (open) {\n        triggerOpen(false);\n        if (valueChangedRef.current) {\n          onSubmit();\n        }\n      }\n      setFocused(false);\n      _onBlur === null || _onBlur === void 0 ? void 0 : _onBlur(e);\n    }\n  };\n\n  // check if value changed\n  useEffect(function () {\n    valueChangedRef.current = false;\n  }, [open]);\n  useEffect(function () {\n    valueChangedRef.current = true;\n  }, [value]);\n\n  // Global click handler\n  useEffect(function () {\n    return addGlobalMouseDownEvent(function (e) {\n      var target = getTargetFromEvent(e);\n      var clickedOutside = isClickOutside(target);\n      if (open) {\n        if (!clickedOutside) {\n          preventBlurRef.current = true;\n\n          // Always set back in case `onBlur` prevented by user\n          raf(function () {\n            preventBlurRef.current = false;\n          });\n        } else if (!changeOnBlur && (!focused || clickedOutside)) {\n          triggerOpen(false);\n        }\n      }\n    });\n  });\n  return [inputProps, {\n    focused: focused,\n    typing: typing\n  }];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,uBAAuB,EAAEC,kBAAkB,QAAQ,iBAAiB;AAC7E,eAAe,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC3C,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAClBC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,cAAc,GAAGH,IAAI,CAACG,cAAc;IACpCC,WAAW,GAAGJ,IAAI,CAACI,WAAW;IAC9BC,cAAc,GAAGL,IAAI,CAACK,cAAc;IACpCC,UAAU,GAAGN,IAAI,CAACO,SAAS;IAC3BC,YAAY,GAAGR,IAAI,CAACQ,YAAY;IAChCC,QAAQ,GAAGT,IAAI,CAACS,QAAQ;IACxBC,QAAQ,GAAGV,IAAI,CAACU,QAAQ;IACxBC,QAAQ,GAAGX,IAAI,CAACY,OAAO;IACvBC,OAAO,GAAGb,IAAI,CAACc,MAAM;IACrBC,YAAY,GAAGf,IAAI,CAACe,YAAY;EAClC,IAAIC,SAAS,GAAGpB,QAAQ,CAAC,KAAK,CAAC;IAC7BqB,UAAU,GAAG1B,cAAc,CAACyB,SAAS,EAAE,CAAC,CAAC;IACzCE,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC3B,IAAIG,UAAU,GAAGxB,QAAQ,CAAC,KAAK,CAAC;IAC9ByB,UAAU,GAAG9B,cAAc,CAAC6B,UAAU,EAAE,CAAC,CAAC;IAC1CE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;;EAE5B;AACF;AACA;AACA;EACE,IAAIG,cAAc,GAAG7B,MAAM,CAAC,KAAK,CAAC;EAClC,IAAI8B,eAAe,GAAG9B,MAAM,CAAC,KAAK,CAAC;EACnC,IAAI+B,iBAAiB,GAAG/B,MAAM,CAAC,KAAK,CAAC;EACrC,IAAIgC,UAAU,GAAG;IACfC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;MAClCT,SAAS,CAAC,IAAI,CAAC;MACff,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC;IACDG,SAAS,EAAE,SAASA,SAASA,CAACsB,CAAC,EAAE;MAC/B,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;QAC7CJ,iBAAiB,CAACK,OAAO,GAAG,IAAI;MAClC,CAAC;MACDzB,UAAU,CAACuB,CAAC,EAAEC,cAAc,CAAC;MAC7B,IAAIJ,iBAAiB,CAACK,OAAO,EAAE;MAC/B,QAAQF,CAAC,CAACG,KAAK;QACb,KAAKxC,OAAO,CAACyC,KAAK;UAChB;YACE,IAAI,CAAChC,IAAI,EAAE;cACTG,WAAW,CAAC,IAAI,CAAC;YACnB,CAAC,MAAM,IAAIK,QAAQ,CAAC,CAAC,KAAK,KAAK,EAAE;cAC/BU,SAAS,CAAC,IAAI,CAAC;YACjB;YACAU,CAAC,CAACC,cAAc,CAAC,CAAC;YAClB;UACF;QACF,KAAKtC,OAAO,CAAC0C,GAAG;UACd;YACE,IAAIhB,MAAM,IAAIjB,IAAI,IAAI,CAAC4B,CAAC,CAACM,QAAQ,EAAE;cACjChB,SAAS,CAAC,KAAK,CAAC;cAChBU,CAAC,CAACC,cAAc,CAAC,CAAC;YACpB,CAAC,MAAM,IAAI,CAACZ,MAAM,IAAIjB,IAAI,EAAE;cAC1B,IAAI,CAACI,cAAc,CAACwB,CAAC,CAAC,IAAIA,CAAC,CAACM,QAAQ,EAAE;gBACpChB,SAAS,CAAC,IAAI,CAAC;gBACfU,CAAC,CAACC,cAAc,CAAC,CAAC;cACpB;YACF;YACA;UACF;QACF,KAAKtC,OAAO,CAAC4C,GAAG;UACd;YACEjB,SAAS,CAAC,IAAI,CAAC;YACfT,QAAQ,CAAC,CAAC;YACV;UACF;MACJ;MACA,IAAI,CAACT,IAAI,IAAI,CAAC,CAACT,OAAO,CAAC6C,KAAK,CAAC,CAACC,QAAQ,CAACT,CAAC,CAACG,KAAK,CAAC,EAAE;QAC/C5B,WAAW,CAAC,IAAI,CAAC;MACnB,CAAC,MAAM,IAAI,CAACc,MAAM,EAAE;QAClB;QACAb,cAAc,CAACwB,CAAC,CAAC;MACnB;IACF,CAAC;IACDjB,OAAO,EAAE,SAASA,OAAOA,CAACiB,CAAC,EAAE;MAC3BV,SAAS,CAAC,IAAI,CAAC;MACfI,UAAU,CAAC,IAAI,CAAC;MAChB,IAAIZ,QAAQ,EAAE;QACZA,QAAQ,CAACkB,CAAC,CAAC;MACb;IACF,CAAC;IACDf,MAAM,EAAE,SAASA,MAAMA,CAACe,CAAC,EAAE;MACzB,IAAIL,cAAc,CAACO,OAAO,IAAI,CAAC5B,cAAc,CAACoC,QAAQ,CAACC,aAAa,CAAC,EAAE;QACrEhB,cAAc,CAACO,OAAO,GAAG,KAAK;QAC9B;MACF;MACA,IAAIvB,YAAY,EAAE;QAChBiC,UAAU,CAAC,YAAY;UACrB,IAAIC,SAAS,GAAGH,QAAQ;YACtBC,aAAa,GAAGE,SAAS,CAACF,aAAa;UACzC,OAAOA,aAAa,IAAIA,aAAa,CAACG,UAAU,EAAE;YAChDH,aAAa,GAAGA,aAAa,CAACG,UAAU,CAACH,aAAa;UACxD;UACA,IAAIrC,cAAc,CAACqC,aAAa,CAAC,EAAE;YACjC9B,QAAQ,CAAC,CAAC;UACZ;QACF,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,MAAM,IAAIT,IAAI,EAAE;QACfG,WAAW,CAAC,KAAK,CAAC;QAClB,IAAIqB,eAAe,CAACM,OAAO,EAAE;UAC3BtB,QAAQ,CAAC,CAAC;QACZ;MACF;MACAc,UAAU,CAAC,KAAK,CAAC;MACjBV,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgB,CAAC,CAAC;IAC9D;EACF,CAAC;;EAED;EACAnC,SAAS,CAAC,YAAY;IACpB+B,eAAe,CAACM,OAAO,GAAG,KAAK;EACjC,CAAC,EAAE,CAAC9B,IAAI,CAAC,CAAC;EACVP,SAAS,CAAC,YAAY;IACpB+B,eAAe,CAACM,OAAO,GAAG,IAAI;EAChC,CAAC,EAAE,CAAC7B,KAAK,CAAC,CAAC;;EAEX;EACAR,SAAS,CAAC,YAAY;IACpB,OAAOG,uBAAuB,CAAC,UAAUgC,CAAC,EAAE;MAC1C,IAAIe,MAAM,GAAG9C,kBAAkB,CAAC+B,CAAC,CAAC;MAClC,IAAIgB,cAAc,GAAG1C,cAAc,CAACyC,MAAM,CAAC;MAC3C,IAAI3C,IAAI,EAAE;QACR,IAAI,CAAC4C,cAAc,EAAE;UACnBrB,cAAc,CAACO,OAAO,GAAG,IAAI;;UAE7B;UACAtC,GAAG,CAAC,YAAY;YACd+B,cAAc,CAACO,OAAO,GAAG,KAAK;UAChC,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI,CAAChB,YAAY,KAAK,CAACO,OAAO,IAAIuB,cAAc,CAAC,EAAE;UACxDzC,WAAW,CAAC,KAAK,CAAC;QACpB;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,CAACuB,UAAU,EAAE;IAClBL,OAAO,EAAEA,OAAO;IAChBJ,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}