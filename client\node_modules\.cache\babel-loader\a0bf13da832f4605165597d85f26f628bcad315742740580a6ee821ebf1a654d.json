{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\QuizTimer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { TbClock, TbAlertTriangle } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizTimer = ({\n  duration,\n  // in seconds\n  onTimeUp,\n  isActive = true,\n  showWarning = true,\n  warningThreshold = 300,\n  // 5 minutes\n  className = ''\n}) => {\n  _s();\n  const [timeRemaining, setTimeRemaining] = useState(duration);\n  const [isWarning, setIsWarning] = useState(false);\n  useEffect(() => {\n    setTimeRemaining(duration);\n  }, [duration]);\n  useEffect(() => {\n    if (!isActive) return;\n    const interval = setInterval(() => {\n      setTimeRemaining(prev => {\n        if (prev <= 1) {\n          onTimeUp === null || onTimeUp === void 0 ? void 0 : onTimeUp();\n          return 0;\n        }\n        const newTime = prev - 1;\n\n        // Check if we should show warning\n        if (showWarning && newTime <= warningThreshold && !isWarning) {\n          setIsWarning(true);\n        }\n        return newTime;\n      });\n    }, 1000);\n    return () => clearInterval(interval);\n  }, [isActive, onTimeUp, showWarning, warningThreshold, isWarning]);\n  const formatTime = seconds => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const secs = seconds % 60;\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n  const getProgressPercentage = () => {\n    return (duration - timeRemaining) / duration * 100;\n  };\n  const getTimerColor = () => {\n    if (timeRemaining <= 60) return 'text-red-600'; // Last minute\n    if (timeRemaining <= warningThreshold) return 'text-yellow-600'; // Warning\n    return 'text-primary-600'; // Normal\n  };\n\n  const getProgressColor = () => {\n    if (timeRemaining <= 60) return 'from-red-500 to-red-600';\n    if (timeRemaining <= warningThreshold) return 'from-yellow-500 to-yellow-600';\n    return 'from-primary-500 to-blue-500';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${className}`,\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      animate: isWarning ? {\n        scale: [1, 1.05, 1]\n      } : {},\n      transition: {\n        duration: 1,\n        repeat: isWarning ? Infinity : 0\n      },\n      className: `inline-flex items-center space-x-2 px-4 py-2 rounded-full ${timeRemaining <= 60 ? 'bg-red-100 border border-red-200' : timeRemaining <= warningThreshold ? 'bg-yellow-100 border border-yellow-200' : 'bg-primary-100 border border-primary-200'}`,\n      children: [timeRemaining <= warningThreshold && /*#__PURE__*/_jsxDEV(motion.div, {\n        animate: {\n          rotate: [0, 10, -10, 0]\n        },\n        transition: {\n          duration: 0.5,\n          repeat: Infinity\n        },\n        children: /*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: `w-4 h-4 ${getTimerColor()}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TbClock, {\n        className: `w-4 h-4 ${getTimerColor()}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `font-mono font-bold text-sm ${getTimerColor()}`,\n        children: formatTime(timeRemaining)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 w-full bg-gray-200 rounded-full h-1.5 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          width: 0\n        },\n        animate: {\n          width: `${getProgressPercentage()}%`\n        },\n        transition: {\n          duration: 0.5\n        },\n        className: `h-full bg-gradient-to-r ${getProgressColor()} rounded-full`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), isWarning && timeRemaining > 60 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -10\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"mt-2 text-xs text-yellow-700 font-medium\",\n      children: [\"\\u26A0\\uFE0F \", Math.floor(timeRemaining / 60), \" minutes remaining\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 9\n    }, this), timeRemaining <= 60 && timeRemaining > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -10\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"mt-2 text-xs text-red-700 font-medium\",\n      children: \"\\uD83D\\uDEA8 Less than 1 minute left!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 9\n    }, this), timeRemaining === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        scale: 0.8\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      className: \"mt-2 text-xs text-red-700 font-bold\",\n      children: \"\\u23F0 Time's up!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n\n// Full-screen timer overlay for critical moments\n_s(QuizTimer, \"0cFoYewCebonPy1Gpf1Z84tglI0=\");\n_c = QuizTimer;\nexport const QuizTimerOverlay = ({\n  timeRemaining,\n  onClose\n}) => {\n  if (timeRemaining > 10) return null;\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0\n    },\n    animate: {\n      opacity: 1\n    },\n    exit: {\n      opacity: 0\n    },\n    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        scale: 0.8,\n        opacity: 0\n      },\n      animate: {\n        scale: 1,\n        opacity: 1\n      },\n      className: \"bg-white rounded-2xl p-8 text-center shadow-2xl max-w-sm mx-4\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        animate: {\n          scale: [1, 1.2, 1]\n        },\n        transition: {\n          duration: 1,\n          repeat: Infinity\n        },\n        className: \"text-6xl mb-4\",\n        children: \"\\u23F0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-2xl font-bold text-red-600 mb-2\",\n        children: \"Time Almost Up!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        animate: {\n          scale: [1, 1.1, 1]\n        },\n        transition: {\n          duration: 0.5,\n          repeat: Infinity\n        },\n        className: \"text-4xl font-mono font-bold text-red-600 mb-4\",\n        children: timeRemaining\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-4\",\n        children: \"Submit your answers now!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n        children: \"Continue Quiz\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_c2 = QuizTimerOverlay;\nexport default QuizTimer;\nvar _c, _c2;\n$RefreshReg$(_c, \"QuizTimer\");\n$RefreshReg$(_c2, \"QuizTimerOverlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "TbClock", "TbAlertTriangle", "jsxDEV", "_jsxDEV", "QuizTimer", "duration", "onTimeUp", "isActive", "showWarning", "warningThreshold", "className", "_s", "timeRemaining", "setTimeRemaining", "isWarning", "setIsWarning", "interval", "setInterval", "prev", "newTime", "clearInterval", "formatTime", "seconds", "hours", "Math", "floor", "minutes", "secs", "toString", "padStart", "getProgressPercentage", "getTimerColor", "getProgressColor", "children", "div", "animate", "scale", "transition", "repeat", "Infinity", "rotate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initial", "width", "opacity", "y", "_c", "QuizTimer<PERSON><PERSON>lay", "onClose", "exit", "onClick", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/QuizTimer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { TbClock, TbAlertTriangle } from 'react-icons/tb';\n\nconst QuizTimer = ({\n  duration, // in seconds\n  onTimeUp,\n  isActive = true,\n  showWarning = true,\n  warningThreshold = 300, // 5 minutes\n  className = '',\n}) => {\n  const [timeRemaining, setTimeRemaining] = useState(duration);\n  const [isWarning, setIsWarning] = useState(false);\n\n  useEffect(() => {\n    setTimeRemaining(duration);\n  }, [duration]);\n\n  useEffect(() => {\n    if (!isActive) return;\n\n    const interval = setInterval(() => {\n      setTimeRemaining((prev) => {\n        if (prev <= 1) {\n          onTimeUp?.();\n          return 0;\n        }\n        \n        const newTime = prev - 1;\n        \n        // Check if we should show warning\n        if (showWarning && newTime <= warningThreshold && !isWarning) {\n          setIsWarning(true);\n        }\n        \n        return newTime;\n      });\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, [isActive, onTimeUp, showWarning, warningThreshold, isWarning]);\n\n  const formatTime = (seconds) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getProgressPercentage = () => {\n    return ((duration - timeRemaining) / duration) * 100;\n  };\n\n  const getTimerColor = () => {\n    if (timeRemaining <= 60) return 'text-red-600'; // Last minute\n    if (timeRemaining <= warningThreshold) return 'text-yellow-600'; // Warning\n    return 'text-primary-600'; // Normal\n  };\n\n  const getProgressColor = () => {\n    if (timeRemaining <= 60) return 'from-red-500 to-red-600';\n    if (timeRemaining <= warningThreshold) return 'from-yellow-500 to-yellow-600';\n    return 'from-primary-500 to-blue-500';\n  };\n\n  return (\n    <div className={`${className}`}>\n      {/* Compact Timer Display */}\n      <motion.div\n        animate={isWarning ? { scale: [1, 1.05, 1] } : {}}\n        transition={{ duration: 1, repeat: isWarning ? Infinity : 0 }}\n        className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full ${\n          timeRemaining <= 60 \n            ? 'bg-red-100 border border-red-200' \n            : timeRemaining <= warningThreshold \n            ? 'bg-yellow-100 border border-yellow-200'\n            : 'bg-primary-100 border border-primary-200'\n        }`}\n      >\n        {timeRemaining <= warningThreshold && (\n          <motion.div\n            animate={{ rotate: [0, 10, -10, 0] }}\n            transition={{ duration: 0.5, repeat: Infinity }}\n          >\n            <TbAlertTriangle className={`w-4 h-4 ${getTimerColor()}`} />\n          </motion.div>\n        )}\n        \n        <TbClock className={`w-4 h-4 ${getTimerColor()}`} />\n        \n        <span className={`font-mono font-bold text-sm ${getTimerColor()}`}>\n          {formatTime(timeRemaining)}\n        </span>\n      </motion.div>\n\n      {/* Progress Bar */}\n      <div className=\"mt-2 w-full bg-gray-200 rounded-full h-1.5 overflow-hidden\">\n        <motion.div\n          initial={{ width: 0 }}\n          animate={{ width: `${getProgressPercentage()}%` }}\n          transition={{ duration: 0.5 }}\n          className={`h-full bg-gradient-to-r ${getProgressColor()} rounded-full`}\n        />\n      </div>\n\n      {/* Warning Message */}\n      {isWarning && timeRemaining > 60 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-2 text-xs text-yellow-700 font-medium\"\n        >\n          ⚠️ {Math.floor(timeRemaining / 60)} minutes remaining\n        </motion.div>\n      )}\n\n      {/* Critical Warning */}\n      {timeRemaining <= 60 && timeRemaining > 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-2 text-xs text-red-700 font-medium\"\n        >\n          🚨 Less than 1 minute left!\n        </motion.div>\n      )}\n\n      {/* Time's Up */}\n      {timeRemaining === 0 && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          className=\"mt-2 text-xs text-red-700 font-bold\"\n        >\n          ⏰ Time's up!\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\n// Full-screen timer overlay for critical moments\nexport const QuizTimerOverlay = ({ timeRemaining, onClose }) => {\n  if (timeRemaining > 10) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center\"\n    >\n      <motion.div\n        initial={{ scale: 0.8, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        className=\"bg-white rounded-2xl p-8 text-center shadow-2xl max-w-sm mx-4\"\n      >\n        <motion.div\n          animate={{ scale: [1, 1.2, 1] }}\n          transition={{ duration: 1, repeat: Infinity }}\n          className=\"text-6xl mb-4\"\n        >\n          ⏰\n        </motion.div>\n        \n        <h3 className=\"text-2xl font-bold text-red-600 mb-2\">\n          Time Almost Up!\n        </h3>\n        \n        <motion.div\n          animate={{ scale: [1, 1.1, 1] }}\n          transition={{ duration: 0.5, repeat: Infinity }}\n          className=\"text-4xl font-mono font-bold text-red-600 mb-4\"\n        >\n          {timeRemaining}\n        </motion.div>\n        \n        <p className=\"text-gray-600 mb-4\">\n          Submit your answers now!\n        </p>\n        \n        <button\n          onClick={onClose}\n          className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\n        >\n          Continue Quiz\n        </button>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default QuizTimer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,eAAe,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,SAAS,GAAGA,CAAC;EACjBC,QAAQ;EAAE;EACVC,QAAQ;EACRC,QAAQ,GAAG,IAAI;EACfC,WAAW,GAAG,IAAI;EAClBC,gBAAgB,GAAG,GAAG;EAAE;EACxBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAACQ,QAAQ,CAAC;EAC5D,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACde,gBAAgB,CAACR,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEdP,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,QAAQ,EAAE;IAEf,MAAMS,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCJ,gBAAgB,CAAEK,IAAI,IAAK;QACzB,IAAIA,IAAI,IAAI,CAAC,EAAE;UACbZ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAAC;UACZ,OAAO,CAAC;QACV;QAEA,MAAMa,OAAO,GAAGD,IAAI,GAAG,CAAC;;QAExB;QACA,IAAIV,WAAW,IAAIW,OAAO,IAAIV,gBAAgB,IAAI,CAACK,SAAS,EAAE;UAC5DC,YAAY,CAAC,IAAI,CAAC;QACpB;QAEA,OAAOI,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACT,QAAQ,EAAED,QAAQ,EAAEE,WAAW,EAAEC,gBAAgB,EAAEK,SAAS,CAAC,CAAC;EAElE,MAAMO,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,IAAI,CAAC;IACxC,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEH,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;IACjD,MAAMK,IAAI,GAAGL,OAAO,GAAG,EAAE;IAEzB,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAQ,GAAEA,KAAM,IAAGG,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,IAAGF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;IAC9F;IACA,OAAQ,GAAEH,OAAQ,IAAGC,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACzD,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAQ,CAACzB,QAAQ,GAAGO,aAAa,IAAIP,QAAQ,GAAI,GAAG;EACtD,CAAC;EAED,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAInB,aAAa,IAAI,EAAE,EAAE,OAAO,cAAc,CAAC,CAAC;IAChD,IAAIA,aAAa,IAAIH,gBAAgB,EAAE,OAAO,iBAAiB,CAAC,CAAC;IACjE,OAAO,kBAAkB,CAAC,CAAC;EAC7B,CAAC;;EAED,MAAMuB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpB,aAAa,IAAI,EAAE,EAAE,OAAO,yBAAyB;IACzD,IAAIA,aAAa,IAAIH,gBAAgB,EAAE,OAAO,+BAA+B;IAC7E,OAAO,8BAA8B;EACvC,CAAC;EAED,oBACEN,OAAA;IAAKO,SAAS,EAAG,GAAEA,SAAU,EAAE;IAAAuB,QAAA,gBAE7B9B,OAAA,CAACJ,MAAM,CAACmC,GAAG;MACTC,OAAO,EAAErB,SAAS,GAAG;QAAEsB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;MAAE,CAAC,GAAG,CAAC,CAAE;MAClDC,UAAU,EAAE;QAAEhC,QAAQ,EAAE,CAAC;QAAEiC,MAAM,EAAExB,SAAS,GAAGyB,QAAQ,GAAG;MAAE,CAAE;MAC9D7B,SAAS,EAAG,6DACVE,aAAa,IAAI,EAAE,GACf,kCAAkC,GAClCA,aAAa,IAAIH,gBAAgB,GACjC,wCAAwC,GACxC,0CACL,EAAE;MAAAwB,QAAA,GAEFrB,aAAa,IAAIH,gBAAgB,iBAChCN,OAAA,CAACJ,MAAM,CAACmC,GAAG;QACTC,OAAO,EAAE;UAAEK,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAAE,CAAE;QACrCH,UAAU,EAAE;UAAEhC,QAAQ,EAAE,GAAG;UAAEiC,MAAM,EAAEC;QAAS,CAAE;QAAAN,QAAA,eAEhD9B,OAAA,CAACF,eAAe;UAACS,SAAS,EAAG,WAAUqB,aAAa,CAAC,CAAE;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CACb,eAEDzC,OAAA,CAACH,OAAO;QAACU,SAAS,EAAG,WAAUqB,aAAa,CAAC,CAAE;MAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEpDzC,OAAA;QAAMO,SAAS,EAAG,+BAA8BqB,aAAa,CAAC,CAAE,EAAE;QAAAE,QAAA,EAC/DZ,UAAU,CAACT,aAAa;MAAC;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGbzC,OAAA;MAAKO,SAAS,EAAC,4DAA4D;MAAAuB,QAAA,eACzE9B,OAAA,CAACJ,MAAM,CAACmC,GAAG;QACTW,OAAO,EAAE;UAAEC,KAAK,EAAE;QAAE,CAAE;QACtBX,OAAO,EAAE;UAAEW,KAAK,EAAG,GAAEhB,qBAAqB,CAAC,CAAE;QAAG,CAAE;QAClDO,UAAU,EAAE;UAAEhC,QAAQ,EAAE;QAAI,CAAE;QAC9BK,SAAS,EAAG,2BAA0BsB,gBAAgB,CAAC,CAAE;MAAe;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL9B,SAAS,IAAIF,aAAa,GAAG,EAAE,iBAC9BT,OAAA,CAACJ,MAAM,CAACmC,GAAG;MACTW,OAAO,EAAE;QAAEE,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCb,OAAO,EAAE;QAAEY,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BtC,SAAS,EAAC,0CAA0C;MAAAuB,QAAA,GACrD,eACI,EAACT,IAAI,CAACC,KAAK,CAACb,aAAa,GAAG,EAAE,CAAC,EAAC,oBACrC;IAAA;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CACb,EAGAhC,aAAa,IAAI,EAAE,IAAIA,aAAa,GAAG,CAAC,iBACvCT,OAAA,CAACJ,MAAM,CAACmC,GAAG;MACTW,OAAO,EAAE;QAAEE,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCb,OAAO,EAAE;QAAEY,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BtC,SAAS,EAAC,uCAAuC;MAAAuB,QAAA,EAClD;IAED;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CACb,EAGAhC,aAAa,KAAK,CAAC,iBAClBT,OAAA,CAACJ,MAAM,CAACmC,GAAG;MACTW,OAAO,EAAE;QAAEE,OAAO,EAAE,CAAC;QAAEX,KAAK,EAAE;MAAI,CAAE;MACpCD,OAAO,EAAE;QAAEY,OAAO,EAAE,CAAC;QAAEX,KAAK,EAAE;MAAE,CAAE;MAClC1B,SAAS,EAAC,qCAAqC;MAAAuB,QAAA,EAChD;IAED;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAjC,EAAA,CA9IMP,SAAS;AAAA6C,EAAA,GAAT7C,SAAS;AA+If,OAAO,MAAM8C,gBAAgB,GAAGA,CAAC;EAAEtC,aAAa;EAAEuC;AAAQ,CAAC,KAAK;EAC9D,IAAIvC,aAAa,GAAG,EAAE,EAAE,OAAO,IAAI;EAEnC,oBACET,OAAA,CAACJ,MAAM,CAACmC,GAAG;IACTW,OAAO,EAAE;MAAEE,OAAO,EAAE;IAAE,CAAE;IACxBZ,OAAO,EAAE;MAAEY,OAAO,EAAE;IAAE,CAAE;IACxBK,IAAI,EAAE;MAAEL,OAAO,EAAE;IAAE,CAAE;IACrBrC,SAAS,EAAC,kFAAkF;IAAAuB,QAAA,eAE5F9B,OAAA,CAACJ,MAAM,CAACmC,GAAG;MACTW,OAAO,EAAE;QAAET,KAAK,EAAE,GAAG;QAAEW,OAAO,EAAE;MAAE,CAAE;MACpCZ,OAAO,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEW,OAAO,EAAE;MAAE,CAAE;MAClCrC,SAAS,EAAC,+DAA+D;MAAAuB,QAAA,gBAEzE9B,OAAA,CAACJ,MAAM,CAACmC,GAAG;QACTC,OAAO,EAAE;UAAEC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QAAE,CAAE;QAChCC,UAAU,EAAE;UAAEhC,QAAQ,EAAE,CAAC;UAAEiC,MAAM,EAAEC;QAAS,CAAE;QAC9C7B,SAAS,EAAC,eAAe;QAAAuB,QAAA,EAC1B;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbzC,OAAA;QAAIO,SAAS,EAAC,sCAAsC;QAAAuB,QAAA,EAAC;MAErD;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELzC,OAAA,CAACJ,MAAM,CAACmC,GAAG;QACTC,OAAO,EAAE;UAAEC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QAAE,CAAE;QAChCC,UAAU,EAAE;UAAEhC,QAAQ,EAAE,GAAG;UAAEiC,MAAM,EAAEC;QAAS,CAAE;QAChD7B,SAAS,EAAC,gDAAgD;QAAAuB,QAAA,EAEzDrB;MAAa;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEbzC,OAAA;QAAGO,SAAS,EAAC,oBAAoB;QAAAuB,QAAA,EAAC;MAElC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJzC,OAAA;QACEkD,OAAO,EAAEF,OAAQ;QACjBzC,SAAS,EAAC,+EAA+E;QAAAuB,QAAA,EAC1F;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEjB,CAAC;AAACU,GAAA,GAhDWJ,gBAAgB;AAkD7B,eAAe9C,SAAS;AAAC,IAAA6C,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}