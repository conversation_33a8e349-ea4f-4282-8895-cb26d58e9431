{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CascaderContext from \"../context\";\nimport { useBaseProps } from 'rc-select';\n\n/**\n * Control the active open options path.\n */\nexport default (function () {\n  var _useBaseProps = useBaseProps(),\n    multiple = _useBaseProps.multiple,\n    open = _useBaseProps.open;\n  var _React$useContext = React.useContext(CascaderContext),\n    values = _React$useContext.values;\n\n  // Record current dropdown active options\n  // This also control the open status\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValueCells = _React$useState2[0],\n    setActiveValueCells = _React$useState2[1];\n  React.useEffect(function () {\n    if (open && !multiple) {\n      var firstValueCells = values[0];\n      setActiveValueCells(firstValueCells || []);\n    }\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [open]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n\n  return [activeValueCells, setActiveValueCells];\n});", "map": {"version": 3, "names": ["_slicedToArray", "React", "CascaderContext", "useBaseProps", "_useBaseProps", "multiple", "open", "_React$useContext", "useContext", "values", "_React$useState", "useState", "_React$useState2", "activeValueCells", "setActiveValueCells", "useEffect", "firstValueCells"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-cascader/es/OptionList/useActive.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CascaderContext from \"../context\";\nimport { useBaseProps } from 'rc-select';\n\n/**\n * Control the active open options path.\n */\nexport default (function () {\n  var _useBaseProps = useBaseProps(),\n    multiple = _useBaseProps.multiple,\n    open = _useBaseProps.open;\n  var _React$useContext = React.useContext(CascaderContext),\n    values = _React$useContext.values;\n\n  // Record current dropdown active options\n  // This also control the open status\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValueCells = _React$useState2[0],\n    setActiveValueCells = _React$useState2[1];\n  React.useEffect(function () {\n    if (open && !multiple) {\n      var firstValueCells = values[0];\n      setActiveValueCells(firstValueCells || []);\n    }\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [open]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n\n  return [activeValueCells, setActiveValueCells];\n});"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,YAAY;AACxC,SAASC,YAAY,QAAQ,WAAW;;AAExC;AACA;AACA;AACA,gBAAgB,YAAY;EAC1B,IAAIC,aAAa,GAAGD,YAAY,CAAC,CAAC;IAChCE,QAAQ,GAAGD,aAAa,CAACC,QAAQ;IACjCC,IAAI,GAAGF,aAAa,CAACE,IAAI;EAC3B,IAAIC,iBAAiB,GAAGN,KAAK,CAACO,UAAU,CAACN,eAAe,CAAC;IACvDO,MAAM,GAAGF,iBAAiB,CAACE,MAAM;;EAEnC;EACA;EACA,IAAIC,eAAe,GAAGT,KAAK,CAACU,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAGZ,cAAc,CAACU,eAAe,EAAE,CAAC,CAAC;IACrDG,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC3CX,KAAK,CAACc,SAAS,CAAC,YAAY;IAC1B,IAAIT,IAAI,IAAI,CAACD,QAAQ,EAAE;MACrB,IAAIW,eAAe,GAAGP,MAAM,CAAC,CAAC,CAAC;MAC/BK,mBAAmB,CAACE,eAAe,IAAI,EAAE,CAAC;IAC5C;EACF,CAAC,EAAE;EACH,CAACV,IAAI;EACL,+CAA+C,CAAC;;EAEhD,OAAO,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC;AAChD,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}