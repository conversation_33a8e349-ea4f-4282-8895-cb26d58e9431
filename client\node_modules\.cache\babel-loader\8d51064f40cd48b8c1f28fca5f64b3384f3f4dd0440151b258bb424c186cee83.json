{"ast": null, "code": "import * as React from 'react';\nimport { getQuarter, isSameDate } from \"../utils/dateUtil\";\nimport { getValue } from \"../utils/miscUtil\";\nexport default function useRangeDisabled(_ref, firstTimeOpen) {\n  var picker = _ref.picker,\n    locale = _ref.locale,\n    selectedValue = _ref.selectedValue,\n    disabledDate = _ref.disabledDate,\n    disabled = _ref.disabled,\n    generateConfig = _ref.generateConfig;\n  var startDate = getValue(selectedValue, 0);\n  var endDate = getValue(selectedValue, 1);\n  function weekFirstDate(date) {\n    return generateConfig.locale.getWeekFirstDate(locale.locale, date);\n  }\n  function monthNumber(date) {\n    var year = generateConfig.getYear(date);\n    var month = generateConfig.getMonth(date);\n    return year * 100 + month;\n  }\n  function quarterNumber(date) {\n    var year = generateConfig.getYear(date);\n    var quarter = getQuarter(generateConfig, date);\n    return year * 10 + quarter;\n  }\n  var disabledStartDate = React.useCallback(function (date) {\n    if (disabled[0] || disabledDate && disabledDate(date)) {\n      return true;\n    }\n\n    // Disabled range\n    if (disabled[1] && endDate) {\n      return !isSameDate(generateConfig, date, endDate) && generateConfig.isAfter(date, endDate);\n    }\n\n    // Disabled part\n    if (!firstTimeOpen && endDate) {\n      switch (picker) {\n        case 'quarter':\n          return quarterNumber(date) > quarterNumber(endDate);\n        case 'month':\n          return monthNumber(date) > monthNumber(endDate);\n        case 'week':\n          return weekFirstDate(date) > weekFirstDate(endDate);\n        default:\n          return !isSameDate(generateConfig, date, endDate) && generateConfig.isAfter(date, endDate);\n      }\n    }\n    return false;\n  }, [disabledDate, disabled[1], endDate, firstTimeOpen]);\n  var disabledEndDate = React.useCallback(function (date) {\n    if (disabled[1] || disabledDate && disabledDate(date)) {\n      return true;\n    }\n\n    // Disabled range\n    if (disabled[0] && startDate) {\n      return !isSameDate(generateConfig, date, endDate) && generateConfig.isAfter(startDate, date);\n    }\n\n    // Disabled part\n    if (!firstTimeOpen && startDate) {\n      switch (picker) {\n        case 'quarter':\n          return quarterNumber(date) < quarterNumber(startDate);\n        case 'month':\n          return monthNumber(date) < monthNumber(startDate);\n        case 'week':\n          return weekFirstDate(date) < weekFirstDate(startDate);\n        default:\n          return !isSameDate(generateConfig, date, startDate) && generateConfig.isAfter(startDate, date);\n      }\n    }\n    return false;\n  }, [disabledDate, disabled[0], startDate, firstTimeOpen]);\n  return [disabledStartDate, disabledEndDate];\n}", "map": {"version": 3, "names": ["React", "getQuarter", "isSameDate", "getValue", "useRangeDisabled", "_ref", "firstTimeOpen", "picker", "locale", "selected<PERSON><PERSON><PERSON>", "disabledDate", "disabled", "generateConfig", "startDate", "endDate", "weekFirstDate", "date", "getWeekFirstDate", "monthNumber", "year", "getYear", "month", "getMonth", "quarterNumber", "quarter", "disabledStartDate", "useCallback", "isAfter", "disabledEndDate"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/hooks/useRangeDisabled.js"], "sourcesContent": ["import * as React from 'react';\nimport { getQuarter, isSameDate } from \"../utils/dateUtil\";\nimport { getValue } from \"../utils/miscUtil\";\nexport default function useRangeDisabled(_ref, firstTimeOpen) {\n  var picker = _ref.picker,\n    locale = _ref.locale,\n    selectedValue = _ref.selectedValue,\n    disabledDate = _ref.disabledDate,\n    disabled = _ref.disabled,\n    generateConfig = _ref.generateConfig;\n  var startDate = getValue(selectedValue, 0);\n  var endDate = getValue(selectedValue, 1);\n  function weekFirstDate(date) {\n    return generateConfig.locale.getWeekFirstDate(locale.locale, date);\n  }\n  function monthNumber(date) {\n    var year = generateConfig.getYear(date);\n    var month = generateConfig.getMonth(date);\n    return year * 100 + month;\n  }\n  function quarterNumber(date) {\n    var year = generateConfig.getYear(date);\n    var quarter = getQuarter(generateConfig, date);\n    return year * 10 + quarter;\n  }\n  var disabledStartDate = React.useCallback(function (date) {\n    if (disabled[0] || disabledDate && disabledDate(date)) {\n      return true;\n    }\n\n    // Disabled range\n    if (disabled[1] && endDate) {\n      return !isSameDate(generateConfig, date, endDate) && generateConfig.isAfter(date, endDate);\n    }\n\n    // Disabled part\n    if (!firstTimeOpen && endDate) {\n      switch (picker) {\n        case 'quarter':\n          return quarterNumber(date) > quarterNumber(endDate);\n        case 'month':\n          return monthNumber(date) > monthNumber(endDate);\n        case 'week':\n          return weekFirstDate(date) > weekFirstDate(endDate);\n        default:\n          return !isSameDate(generateConfig, date, endDate) && generateConfig.isAfter(date, endDate);\n      }\n    }\n    return false;\n  }, [disabledDate, disabled[1], endDate, firstTimeOpen]);\n  var disabledEndDate = React.useCallback(function (date) {\n    if (disabled[1] || disabledDate && disabledDate(date)) {\n      return true;\n    }\n\n    // Disabled range\n    if (disabled[0] && startDate) {\n      return !isSameDate(generateConfig, date, endDate) && generateConfig.isAfter(startDate, date);\n    }\n\n    // Disabled part\n    if (!firstTimeOpen && startDate) {\n      switch (picker) {\n        case 'quarter':\n          return quarterNumber(date) < quarterNumber(startDate);\n        case 'month':\n          return monthNumber(date) < monthNumber(startDate);\n        case 'week':\n          return weekFirstDate(date) < weekFirstDate(startDate);\n        default:\n          return !isSameDate(generateConfig, date, startDate) && generateConfig.isAfter(startDate, date);\n      }\n    }\n    return false;\n  }, [disabledDate, disabled[0], startDate, firstTimeOpen]);\n  return [disabledStartDate, disabledEndDate];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,UAAU,QAAQ,mBAAmB;AAC1D,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,eAAe,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,aAAa,EAAE;EAC5D,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACtBC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,aAAa,GAAGJ,IAAI,CAACI,aAAa;IAClCC,YAAY,GAAGL,IAAI,CAACK,YAAY;IAChCC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IACxBC,cAAc,GAAGP,IAAI,CAACO,cAAc;EACtC,IAAIC,SAAS,GAAGV,QAAQ,CAACM,aAAa,EAAE,CAAC,CAAC;EAC1C,IAAIK,OAAO,GAAGX,QAAQ,CAACM,aAAa,EAAE,CAAC,CAAC;EACxC,SAASM,aAAaA,CAACC,IAAI,EAAE;IAC3B,OAAOJ,cAAc,CAACJ,MAAM,CAACS,gBAAgB,CAACT,MAAM,CAACA,MAAM,EAAEQ,IAAI,CAAC;EACpE;EACA,SAASE,WAAWA,CAACF,IAAI,EAAE;IACzB,IAAIG,IAAI,GAAGP,cAAc,CAACQ,OAAO,CAACJ,IAAI,CAAC;IACvC,IAAIK,KAAK,GAAGT,cAAc,CAACU,QAAQ,CAACN,IAAI,CAAC;IACzC,OAAOG,IAAI,GAAG,GAAG,GAAGE,KAAK;EAC3B;EACA,SAASE,aAAaA,CAACP,IAAI,EAAE;IAC3B,IAAIG,IAAI,GAAGP,cAAc,CAACQ,OAAO,CAACJ,IAAI,CAAC;IACvC,IAAIQ,OAAO,GAAGvB,UAAU,CAACW,cAAc,EAAEI,IAAI,CAAC;IAC9C,OAAOG,IAAI,GAAG,EAAE,GAAGK,OAAO;EAC5B;EACA,IAAIC,iBAAiB,GAAGzB,KAAK,CAAC0B,WAAW,CAAC,UAAUV,IAAI,EAAE;IACxD,IAAIL,QAAQ,CAAC,CAAC,CAAC,IAAID,YAAY,IAAIA,YAAY,CAACM,IAAI,CAAC,EAAE;MACrD,OAAO,IAAI;IACb;;IAEA;IACA,IAAIL,QAAQ,CAAC,CAAC,CAAC,IAAIG,OAAO,EAAE;MAC1B,OAAO,CAACZ,UAAU,CAACU,cAAc,EAAEI,IAAI,EAAEF,OAAO,CAAC,IAAIF,cAAc,CAACe,OAAO,CAACX,IAAI,EAAEF,OAAO,CAAC;IAC5F;;IAEA;IACA,IAAI,CAACR,aAAa,IAAIQ,OAAO,EAAE;MAC7B,QAAQP,MAAM;QACZ,KAAK,SAAS;UACZ,OAAOgB,aAAa,CAACP,IAAI,CAAC,GAAGO,aAAa,CAACT,OAAO,CAAC;QACrD,KAAK,OAAO;UACV,OAAOI,WAAW,CAACF,IAAI,CAAC,GAAGE,WAAW,CAACJ,OAAO,CAAC;QACjD,KAAK,MAAM;UACT,OAAOC,aAAa,CAACC,IAAI,CAAC,GAAGD,aAAa,CAACD,OAAO,CAAC;QACrD;UACE,OAAO,CAACZ,UAAU,CAACU,cAAc,EAAEI,IAAI,EAAEF,OAAO,CAAC,IAAIF,cAAc,CAACe,OAAO,CAACX,IAAI,EAAEF,OAAO,CAAC;MAC9F;IACF;IACA,OAAO,KAAK;EACd,CAAC,EAAE,CAACJ,YAAY,EAAEC,QAAQ,CAAC,CAAC,CAAC,EAAEG,OAAO,EAAER,aAAa,CAAC,CAAC;EACvD,IAAIsB,eAAe,GAAG5B,KAAK,CAAC0B,WAAW,CAAC,UAAUV,IAAI,EAAE;IACtD,IAAIL,QAAQ,CAAC,CAAC,CAAC,IAAID,YAAY,IAAIA,YAAY,CAACM,IAAI,CAAC,EAAE;MACrD,OAAO,IAAI;IACb;;IAEA;IACA,IAAIL,QAAQ,CAAC,CAAC,CAAC,IAAIE,SAAS,EAAE;MAC5B,OAAO,CAACX,UAAU,CAACU,cAAc,EAAEI,IAAI,EAAEF,OAAO,CAAC,IAAIF,cAAc,CAACe,OAAO,CAACd,SAAS,EAAEG,IAAI,CAAC;IAC9F;;IAEA;IACA,IAAI,CAACV,aAAa,IAAIO,SAAS,EAAE;MAC/B,QAAQN,MAAM;QACZ,KAAK,SAAS;UACZ,OAAOgB,aAAa,CAACP,IAAI,CAAC,GAAGO,aAAa,CAACV,SAAS,CAAC;QACvD,KAAK,OAAO;UACV,OAAOK,WAAW,CAACF,IAAI,CAAC,GAAGE,WAAW,CAACL,SAAS,CAAC;QACnD,KAAK,MAAM;UACT,OAAOE,aAAa,CAACC,IAAI,CAAC,GAAGD,aAAa,CAACF,SAAS,CAAC;QACvD;UACE,OAAO,CAACX,UAAU,CAACU,cAAc,EAAEI,IAAI,EAAEH,SAAS,CAAC,IAAID,cAAc,CAACe,OAAO,CAACd,SAAS,EAAEG,IAAI,CAAC;MAClG;IACF;IACA,OAAO,KAAK;EACd,CAAC,EAAE,CAACN,YAAY,EAAEC,QAAQ,CAAC,CAAC,CAAC,EAAEE,SAAS,EAAEP,aAAa,CAAC,CAAC;EACzD,OAAO,CAACmB,iBAAiB,EAAEG,eAAe,CAAC;AAC7C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}