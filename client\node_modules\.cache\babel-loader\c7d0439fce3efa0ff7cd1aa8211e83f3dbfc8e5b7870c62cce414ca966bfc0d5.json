{"ast": null, "code": "const genPaginationStyle = token => {\n  const {\n    componentCls,\n    antCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Pagination ==========================\n      [`${componentCls}-pagination${antCls}-pagination`]: {\n        margin: `${token.margin}px 0`\n      },\n      [`${componentCls}-pagination`]: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        rowGap: token.paddingXS,\n        '> *': {\n          flex: 'none'\n        },\n        '&-left': {\n          justifyContent: 'flex-start'\n        },\n        '&-center': {\n          justifyContent: 'center'\n        },\n        '&-right': {\n          justifyContent: 'flex-end'\n        }\n      }\n    }\n  };\n};\nexport default genPaginationStyle;", "map": {"version": 3, "names": ["genPaginationStyle", "token", "componentCls", "antCls", "margin", "display", "flexWrap", "rowGap", "paddingXS", "flex", "justifyContent"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/pagination.js"], "sourcesContent": ["const genPaginationStyle = token => {\n  const {\n    componentCls,\n    antCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Pagination ==========================\n      [`${componentCls}-pagination${antCls}-pagination`]: {\n        margin: `${token.margin}px 0`\n      },\n      [`${componentCls}-pagination`]: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        rowGap: token.paddingXS,\n        '> *': {\n          flex: 'none'\n        },\n        '&-left': {\n          justifyContent: 'flex-start'\n        },\n        '&-center': {\n          justifyContent: 'center'\n        },\n        '&-right': {\n          justifyContent: 'flex-end'\n        }\n      }\n    }\n  };\n};\nexport default genPaginationStyle;"], "mappings": "AAAA,MAAMA,kBAAkB,GAAGC,KAAK,IAAI;EAClC,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,CAAE,GAAEC,YAAa,UAAS,GAAG;MAC3B;MACA,CAAE,GAAEA,YAAa,cAAaC,MAAO,aAAY,GAAG;QAClDC,MAAM,EAAG,GAAEH,KAAK,CAACG,MAAO;MAC1B,CAAC;MACD,CAAE,GAAEF,YAAa,aAAY,GAAG;QAC9BG,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAEN,KAAK,CAACO,SAAS;QACvB,KAAK,EAAE;UACLC,IAAI,EAAE;QACR,CAAC;QACD,QAAQ,EAAE;UACRC,cAAc,EAAE;QAClB,CAAC;QACD,UAAU,EAAE;UACVA,cAAc,EAAE;QAClB,CAAC;QACD,SAAS,EAAE;UACTA,cAAc,EAAE;QAClB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeV,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}