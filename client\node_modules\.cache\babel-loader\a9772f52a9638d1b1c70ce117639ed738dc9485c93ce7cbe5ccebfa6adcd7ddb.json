{"ast": null, "code": "const genCollapseMotion = token => ({\n  [token.componentCls]: {\n    // For common/openAnimation\n    [`${token.antCls}-motion-collapse-legacy`]: {\n      overflow: 'hidden',\n      '&-active': {\n        transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n      }\n    },\n    [`${token.antCls}-motion-collapse`]: {\n      overflow: 'hidden',\n      transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n    }\n  }\n});\nexport default genCollapseMotion;", "map": {"version": 3, "names": ["genCollapseMotion", "token", "componentCls", "antCls", "overflow", "transition", "motionDurationMid", "motionEaseInOut"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/style/motion/collapse.js"], "sourcesContent": ["const genCollapseMotion = token => ({\n  [token.componentCls]: {\n    // For common/openAnimation\n    [`${token.antCls}-motion-collapse-legacy`]: {\n      overflow: 'hidden',\n      '&-active': {\n        transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n      }\n    },\n    [`${token.antCls}-motion-collapse`]: {\n      overflow: 'hidden',\n      transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n    }\n  }\n});\nexport default genCollapseMotion;"], "mappings": "AAAA,MAAMA,iBAAiB,GAAGC,KAAK,KAAK;EAClC,CAACA,KAAK,CAACC,YAAY,GAAG;IACpB;IACA,CAAE,GAAED,KAAK,CAACE,MAAO,yBAAwB,GAAG;MAC1CC,QAAQ,EAAE,QAAQ;MAClB,UAAU,EAAE;QACVC,UAAU,EAAG,UAASJ,KAAK,CAACK,iBAAkB,IAAGL,KAAK,CAACM,eAAgB;AAC/E,kBAAkBN,KAAK,CAACK,iBAAkB,IAAGL,KAAK,CAACM,eAAgB;MAC7D;IACF,CAAC;IACD,CAAE,GAAEN,KAAK,CAACE,MAAO,kBAAiB,GAAG;MACnCC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAG,UAASJ,KAAK,CAACK,iBAAkB,IAAGL,KAAK,CAACM,eAAgB;AAC7E,kBAAkBN,KAAK,CAACK,iBAAkB,IAAGL,KAAK,CAACM,eAAgB;IAC/D;EACF;AACF,CAAC,CAAC;AACF,eAAeP,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}