{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nexport function parseChildren(children, keyPath) {\n  return toArray(children).map(function (child, index) {\n    if ( /*#__PURE__*/React.isValidElement(child)) {\n      var _eventKey, _child$props;\n      var key = child.key;\n      var eventKey = (_eventKey = (_child$props = child.props) === null || _child$props === void 0 ? void 0 : _child$props.eventKey) !== null && _eventKey !== void 0 ? _eventKey : key;\n      var emptyKey = eventKey === null || eventKey === undefined;\n      if (emptyKey) {\n        eventKey = \"tmp_key-\".concat([].concat(_toConsumableArray(keyPath), [index]).join('-'));\n      }\n      var cloneProps = {\n        key: eventKey,\n        eventKey: eventKey\n      };\n      if (process.env.NODE_ENV !== 'production' && emptyKey) {\n        cloneProps.warnKey = true;\n      }\n      return /*#__PURE__*/React.cloneElement(child, cloneProps);\n    }\n    return child;\n  });\n}", "map": {"version": 3, "names": ["_toConsumableArray", "toArray", "React", "parse<PERSON><PERSON><PERSON>n", "children", "keyP<PERSON>", "map", "child", "index", "isValidElement", "_eventKey", "_child$props", "key", "eventKey", "props", "emptyKey", "undefined", "concat", "join", "cloneProps", "process", "env", "NODE_ENV", "<PERSON><PERSON><PERSON>", "cloneElement"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-menu/es/utils/commonUtil.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nexport function parseChildren(children, keyPath) {\n  return toArray(children).map(function (child, index) {\n    if ( /*#__PURE__*/React.isValidElement(child)) {\n      var _eventKey, _child$props;\n      var key = child.key;\n      var eventKey = (_eventKey = (_child$props = child.props) === null || _child$props === void 0 ? void 0 : _child$props.eventKey) !== null && _eventKey !== void 0 ? _eventKey : key;\n      var emptyKey = eventKey === null || eventKey === undefined;\n      if (emptyKey) {\n        eventKey = \"tmp_key-\".concat([].concat(_toConsumableArray(keyPath), [index]).join('-'));\n      }\n      var cloneProps = {\n        key: eventKey,\n        eventKey: eventKey\n      };\n      if (process.env.NODE_ENV !== 'production' && emptyKey) {\n        cloneProps.warnKey = true;\n      }\n      return /*#__PURE__*/React.cloneElement(child, cloneProps);\n    }\n    return child;\n  });\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC/C,OAAOJ,OAAO,CAACG,QAAQ,CAAC,CAACE,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACnD,KAAK,aAAaN,KAAK,CAACO,cAAc,CAACF,KAAK,CAAC,EAAE;MAC7C,IAAIG,SAAS,EAAEC,YAAY;MAC3B,IAAIC,GAAG,GAAGL,KAAK,CAACK,GAAG;MACnB,IAAIC,QAAQ,GAAG,CAACH,SAAS,GAAG,CAACC,YAAY,GAAGJ,KAAK,CAACO,KAAK,MAAM,IAAI,IAAIH,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,QAAQ,MAAM,IAAI,IAAIH,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGE,GAAG;MACjL,IAAIG,QAAQ,GAAGF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKG,SAAS;MAC1D,IAAID,QAAQ,EAAE;QACZF,QAAQ,GAAG,UAAU,CAACI,MAAM,CAAC,EAAE,CAACA,MAAM,CAACjB,kBAAkB,CAACK,OAAO,CAAC,EAAE,CAACG,KAAK,CAAC,CAAC,CAACU,IAAI,CAAC,GAAG,CAAC,CAAC;MACzF;MACA,IAAIC,UAAU,GAAG;QACfP,GAAG,EAAEC,QAAQ;QACbA,QAAQ,EAAEA;MACZ,CAAC;MACD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIP,QAAQ,EAAE;QACrDI,UAAU,CAACI,OAAO,GAAG,IAAI;MAC3B;MACA,OAAO,aAAarB,KAAK,CAACsB,YAAY,CAACjB,KAAK,EAAEY,UAAU,CAAC;IAC3D;IACA,OAAOZ,KAAK;EACd,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}