/* Modern Design System CSS */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* CSS Variables for Theme */
:root {
  /* Primary Blue Theme */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --gradient-blue-sky: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
  --gradient-blue-ocean: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  
  /* Shadows */
  --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-large: 0 10px 40px -10px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-blue: 0 4px 14px 0 rgba(59, 130, 246, 0.15);
  --shadow-blue-lg: 0 10px 40px -10px rgba(59, 130, 246, 0.25);
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Dark Mode Variables */
.dark {
  --color-primary-50: #172554;
  --color-primary-100: #1e3a8a;
  --color-primary-200: #1e40af;
  --color-primary-300: #1d4ed8;
  --color-primary-400: #2563eb;
  --color-primary-500: #3b82f6;
  --color-primary-600: #60a5fa;
  --color-primary-700: #93c5fd;
  --color-primary-800: #bfdbfe;
  --color-primary-900: #dbeafe;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: #1f2937;
  background-color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Component Classes */
@layer components {
  /* Modern Button Styles */
  .btn-modern {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn-modern bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
  }
  
  .btn-secondary {
    @apply btn-modern bg-white text-primary-600 border border-primary-200 hover:bg-primary-50 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }
  
  .btn-ghost {
    @apply btn-modern bg-transparent text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }
  
  /* Modern Card Styles */
  .card-modern {
    @apply bg-white rounded-xl shadow-soft border border-gray-100 transition-all duration-300 hover:shadow-medium;
  }
  
  .card-interactive {
    @apply card-modern cursor-pointer hover:shadow-large hover:-translate-y-1 transform;
  }
  
  .card-glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20 rounded-xl shadow-soft;
  }
  
  /* Modern Input Styles */
  .input-modern {
    @apply w-full px-4 py-3 text-gray-900 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 placeholder-gray-400;
  }
  
  /* Modern Navigation */
  .nav-modern {
    @apply bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50;
  }
  
  .nav-item {
    @apply px-4 py-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-primary-50 transition-all duration-200 font-medium;
  }
  
  .nav-item-active {
    @apply nav-item text-primary-600 bg-primary-50;
  }
  
  /* Modern Sidebar */
  .sidebar-modern {
    @apply bg-gradient-to-b from-primary-600 to-primary-800 text-white shadow-large;
  }
  
  .sidebar-item {
    @apply flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200 cursor-pointer;
  }
  
  .sidebar-item-active {
    @apply sidebar-item text-white bg-white/20 shadow-sm;
  }
  
  /* Modern Layout */
  .layout-modern {
    @apply min-h-screen bg-gradient-to-br from-gray-50 to-blue-50;
  }
  
  .container-modern {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Modern Typography */
  .heading-1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight;
  }
  
  .heading-2 {
    @apply text-3xl md:text-4xl font-bold text-gray-900 leading-tight;
  }
  
  .heading-3 {
    @apply text-2xl md:text-3xl font-semibold text-gray-900 leading-tight;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent;
  }
  
  /* Modern Animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out forwards;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  /* Modern Loading States */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-200 border-t-primary-600;
  }
  
  /* Modern Quiz Styles */
  .quiz-card {
    @apply card-modern p-6 hover:shadow-blue transform hover:scale-105;
  }
  
  .quiz-option {
    @apply p-4 border-2 border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:border-primary-300 hover:bg-primary-50;
  }
  
  .quiz-option-selected {
    @apply quiz-option border-primary-500 bg-primary-50 text-primary-700;
  }
  
  .quiz-option-correct {
    @apply quiz-option border-success-500 bg-success-50 text-success-700;
  }
  
  .quiz-option-incorrect {
    @apply quiz-option border-error-500 bg-error-50 text-error-700;
  }
  
  /* Modern Progress Bar */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
  }
  
  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-500 to-blue-500 rounded-full transition-all duration-500 ease-out;
  }
  
  /* Modern Badge */
  .badge-modern {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge-modern bg-primary-100 text-primary-800;
  }
  
  .badge-success {
    @apply badge-modern bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge-modern bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply badge-modern bg-error-100 text-error-800;
  }
}

/* Responsive Design Utilities */
@layer utilities {
  .text-responsive {
    @apply text-sm sm:text-base md:text-lg;
  }
  
  .padding-responsive {
    @apply p-4 sm:p-6 md:p-8;
  }
  
  .margin-responsive {
    @apply m-4 sm:m-6 md:m-8;
  }
  
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
}
