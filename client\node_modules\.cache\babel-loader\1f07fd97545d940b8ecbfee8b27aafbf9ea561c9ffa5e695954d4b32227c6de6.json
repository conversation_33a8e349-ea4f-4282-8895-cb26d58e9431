{"ast": null, "code": "import * as React from 'react';\nexport default function getRanges(_ref) {\n  var prefixCls = _ref.prefixCls,\n    _ref$components = _ref.components,\n    components = _ref$components === void 0 ? {} : _ref$components,\n    needConfirmButton = _ref.needConfirmButton,\n    onNow = _ref.onNow,\n    onOk = _ref.onOk,\n    okDisabled = _ref.okDisabled,\n    showNow = _ref.showNow,\n    locale = _ref.locale;\n  var presetNode;\n  var okNode;\n  if (needConfirmButton) {\n    var Button = components.button || 'button';\n    if (onNow && showNow !== false) {\n      presetNode = /*#__PURE__*/React.createElement(\"li\", {\n        className: \"\".concat(prefixCls, \"-now\")\n      }, /*#__PURE__*/React.createElement(\"a\", {\n        className: \"\".concat(prefixCls, \"-now-btn\"),\n        onClick: onNow\n      }, locale.now));\n    }\n    okNode = needConfirmButton && /*#__PURE__*/React.createElement(\"li\", {\n      className: \"\".concat(prefixCls, \"-ok\")\n    }, /*#__PURE__*/React.createElement(Button, {\n      disabled: okDisabled,\n      onClick: onOk\n    }, locale.ok));\n  }\n  if (!presetNode && !okNode) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-ranges\")\n  }, presetNode, okNode);\n}", "map": {"version": 3, "names": ["React", "getRanges", "_ref", "prefixCls", "_ref$components", "components", "needConfirmButton", "onNow", "onOk", "okDisabled", "showNow", "locale", "presetNode", "okNode", "<PERSON><PERSON>", "button", "createElement", "className", "concat", "onClick", "now", "disabled", "ok"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/utils/getRanges.js"], "sourcesContent": ["import * as React from 'react';\nexport default function getRanges(_ref) {\n  var prefixCls = _ref.prefixCls,\n    _ref$components = _ref.components,\n    components = _ref$components === void 0 ? {} : _ref$components,\n    needConfirmButton = _ref.needConfirmButton,\n    onNow = _ref.onNow,\n    onOk = _ref.onOk,\n    okDisabled = _ref.okDisabled,\n    showNow = _ref.showNow,\n    locale = _ref.locale;\n  var presetNode;\n  var okNode;\n  if (needConfirmButton) {\n    var Button = components.button || 'button';\n    if (onNow && showNow !== false) {\n      presetNode = /*#__PURE__*/React.createElement(\"li\", {\n        className: \"\".concat(prefixCls, \"-now\")\n      }, /*#__PURE__*/React.createElement(\"a\", {\n        className: \"\".concat(prefixCls, \"-now-btn\"),\n        onClick: onNow\n      }, locale.now));\n    }\n    okNode = needConfirmButton && /*#__PURE__*/React.createElement(\"li\", {\n      className: \"\".concat(prefixCls, \"-ok\")\n    }, /*#__PURE__*/React.createElement(Button, {\n      disabled: okDisabled,\n      onClick: onOk\n    }, locale.ok));\n  }\n  if (!presetNode && !okNode) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"\".concat(prefixCls, \"-ranges\")\n  }, presetNode, okNode);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,SAASA,CAACC,IAAI,EAAE;EACtC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,eAAe,GAAGF,IAAI,CAACG,UAAU;IACjCA,UAAU,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,eAAe;IAC9DE,iBAAiB,GAAGJ,IAAI,CAACI,iBAAiB;IAC1CC,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,IAAI,GAAGN,IAAI,CAACM,IAAI;IAChBC,UAAU,GAAGP,IAAI,CAACO,UAAU;IAC5BC,OAAO,GAAGR,IAAI,CAACQ,OAAO;IACtBC,MAAM,GAAGT,IAAI,CAACS,MAAM;EACtB,IAAIC,UAAU;EACd,IAAIC,MAAM;EACV,IAAIP,iBAAiB,EAAE;IACrB,IAAIQ,MAAM,GAAGT,UAAU,CAACU,MAAM,IAAI,QAAQ;IAC1C,IAAIR,KAAK,IAAIG,OAAO,KAAK,KAAK,EAAE;MAC9BE,UAAU,GAAG,aAAaZ,KAAK,CAACgB,aAAa,CAAC,IAAI,EAAE;QAClDC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACf,SAAS,EAAE,MAAM;MACxC,CAAC,EAAE,aAAaH,KAAK,CAACgB,aAAa,CAAC,GAAG,EAAE;QACvCC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACf,SAAS,EAAE,UAAU,CAAC;QAC3CgB,OAAO,EAAEZ;MACX,CAAC,EAAEI,MAAM,CAACS,GAAG,CAAC,CAAC;IACjB;IACAP,MAAM,GAAGP,iBAAiB,IAAI,aAAaN,KAAK,CAACgB,aAAa,CAAC,IAAI,EAAE;MACnEC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACf,SAAS,EAAE,KAAK;IACvC,CAAC,EAAE,aAAaH,KAAK,CAACgB,aAAa,CAACF,MAAM,EAAE;MAC1CO,QAAQ,EAAEZ,UAAU;MACpBU,OAAO,EAAEX;IACX,CAAC,EAAEG,MAAM,CAACW,EAAE,CAAC,CAAC;EAChB;EACA,IAAI,CAACV,UAAU,IAAI,CAACC,MAAM,EAAE;IAC1B,OAAO,IAAI;EACb;EACA,OAAO,aAAab,KAAK,CAACgB,aAAa,CAAC,IAAI,EAAE;IAC5CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACf,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAES,UAAU,EAAEC,MAAM,CAAC;AACxB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}