{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nvar targetOffset = [0, 0];\nvar basePlacements = {\n  left: {\n    points: ['cr', 'cl'],\n    offset: [-8, 0]\n  },\n  right: {\n    points: ['cl', 'cr'],\n    offset: [8, 0]\n  },\n  top: {\n    points: ['bc', 'tc'],\n    offset: [0, -8]\n  },\n  bottom: {\n    points: ['tc', 'bc'],\n    offset: [0, 8]\n  },\n  topLeft: {\n    points: ['bl', 'tl'],\n    offset: [0, -8]\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    offset: [-8, 0]\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    offset: [0, -8]\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    offset: [8, 0]\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    offset: [0, 8]\n  },\n  rightBottom: {\n    points: ['bl', 'br'],\n    offset: [8, 0]\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    offset: [0, 8]\n  },\n  leftBottom: {\n    points: ['br', 'bl'],\n    offset: [-8, 0]\n  }\n};\nexport function getPlacements() {\n  var arrowPointAtCenter = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  var placements = {};\n  Object.keys(basePlacements).forEach(function (key) {\n    placements[key] = _objectSpread(_objectSpread({}, basePlacements[key]), {}, {\n      autoArrow: arrowPointAtCenter,\n      targetOffset: targetOffset\n    });\n  });\n  return placements;\n}\nexport var placements = getPlacements();", "map": {"version": 3, "names": ["_objectSpread", "autoAdjustOverflow", "adjustX", "adjustY", "targetOffset", "basePlacements", "left", "points", "offset", "right", "top", "bottom", "topLeft", "leftTop", "topRight", "rightTop", "bottomRight", "rightBottom", "bottomLeft", "leftBottom", "getPlacements", "arrowPointAtCenter", "arguments", "length", "undefined", "placements", "Object", "keys", "for<PERSON>ach", "key", "autoArrow"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/tour/es/placements.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nvar targetOffset = [0, 0];\nvar basePlacements = {\n  left: {\n    points: ['cr', 'cl'],\n    offset: [-8, 0]\n  },\n  right: {\n    points: ['cl', 'cr'],\n    offset: [8, 0]\n  },\n  top: {\n    points: ['bc', 'tc'],\n    offset: [0, -8]\n  },\n  bottom: {\n    points: ['tc', 'bc'],\n    offset: [0, 8]\n  },\n  topLeft: {\n    points: ['bl', 'tl'],\n    offset: [0, -8]\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    offset: [-8, 0]\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    offset: [0, -8]\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    offset: [8, 0]\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    offset: [0, 8]\n  },\n  rightBottom: {\n    points: ['bl', 'br'],\n    offset: [8, 0]\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    offset: [0, 8]\n  },\n  leftBottom: {\n    points: ['br', 'bl'],\n    offset: [-8, 0]\n  }\n};\nexport function getPlacements() {\n  var arrowPointAtCenter = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  var placements = {};\n  Object.keys(basePlacements).forEach(function (key) {\n    placements[key] = _objectSpread(_objectSpread({}, basePlacements[key]), {}, {\n      autoArrow: arrowPointAtCenter,\n      targetOffset: targetOffset\n    });\n  });\n  return placements;\n}\nexport var placements = getPlacements();"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,IAAIC,kBAAkB,GAAG;EACvBC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACzB,IAAIC,cAAc,GAAG;EACnBC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EAChB,CAAC;EACDC,KAAK,EAAE;IACLF,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;EACf,CAAC;EACDE,GAAG,EAAE;IACHH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EACDG,MAAM,EAAE;IACNJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;EACf,CAAC;EACDI,OAAO,EAAE;IACPL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EACDK,OAAO,EAAE;IACPN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EAChB,CAAC;EACDM,QAAQ,EAAE;IACRP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EACDO,QAAQ,EAAE;IACRR,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;EACf,CAAC;EACDQ,WAAW,EAAE;IACXT,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;EACf,CAAC;EACDS,WAAW,EAAE;IACXV,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;EACf,CAAC;EACDU,UAAU,EAAE;IACVX,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;EACf,CAAC;EACDW,UAAU,EAAE;IACVZ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EAChB;AACF,CAAC;AACD,OAAO,SAASY,aAAaA,CAAA,EAAG;EAC9B,IAAIC,kBAAkB,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAClG,IAAIG,UAAU,GAAG,CAAC,CAAC;EACnBC,MAAM,CAACC,IAAI,CAACtB,cAAc,CAAC,CAACuB,OAAO,CAAC,UAAUC,GAAG,EAAE;IACjDJ,UAAU,CAACI,GAAG,CAAC,GAAG7B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,cAAc,CAACwB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAC1EC,SAAS,EAAET,kBAAkB;MAC7BjB,YAAY,EAAEA;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOqB,UAAU;AACnB;AACA,OAAO,IAAIA,UAAU,GAAGL,aAAa,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}