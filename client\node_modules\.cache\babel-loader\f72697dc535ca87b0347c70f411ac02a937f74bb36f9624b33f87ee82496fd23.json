{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport React, { useRef } from 'react';\nimport classNames from 'classnames';\nimport MemoChildren from \"./MemoChildren\";\nvar sentinelStyle = {\n  width: 0,\n  height: 0,\n  overflow: 'hidden',\n  outline: 'none'\n};\nvar Panel = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    title = props.title,\n    ariaId = props.ariaId,\n    footer = props.footer,\n    closable = props.closable,\n    closeIcon = props.closeIcon,\n    onClose = props.onClose,\n    children = props.children,\n    bodyStyle = props.bodyStyle,\n    bodyProps = props.bodyProps,\n    modalRender = props.modalRender,\n    onMouseDown = props.onMouseDown,\n    onMouseUp = props.onMouseUp,\n    holderRef = props.holderRef,\n    visible = props.visible,\n    forceRender = props.forceRender,\n    width = props.width,\n    height = props.height;\n\n  // ================================= Refs =================================\n  var sentinelStartRef = useRef();\n  var sentinelEndRef = useRef();\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        var _sentinelStartRef$cur;\n        (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 ? void 0 : _sentinelStartRef$cur.focus();\n      },\n      changeActive: function changeActive(next) {\n        var _document = document,\n          activeElement = _document.activeElement;\n        if (next && activeElement === sentinelEndRef.current) {\n          sentinelStartRef.current.focus();\n        } else if (!next && activeElement === sentinelStartRef.current) {\n          sentinelEndRef.current.focus();\n        }\n      }\n    };\n  });\n\n  // ================================ Style =================================\n  var contentStyle = {};\n  if (width !== undefined) {\n    contentStyle.width = width;\n  }\n  if (height !== undefined) {\n    contentStyle.height = height;\n  }\n  // ================================ Render ================================\n  var footerNode;\n  if (footer) {\n    footerNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, footer);\n  }\n  var headerNode;\n  if (title) {\n    headerNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-header\")\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-title\"),\n      id: ariaId\n    }, title));\n  }\n  var closer;\n  if (closable) {\n    closer = /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: onClose,\n      \"aria-label\": \"Close\",\n      className: \"\".concat(prefixCls, \"-close\")\n    }, closeIcon || /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-close-x\")\n    }));\n  }\n  var content = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, closer, headerNode, /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: \"\".concat(prefixCls, \"-body\"),\n    style: bodyStyle\n  }, bodyProps), children), footerNode);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    key: \"dialog-element\",\n    role: \"dialog\",\n    \"aria-labelledby\": title ? ariaId : null,\n    \"aria-modal\": \"true\",\n    ref: holderRef,\n    style: _objectSpread(_objectSpread({}, style), contentStyle),\n    className: classNames(prefixCls, className),\n    onMouseDown: onMouseDown,\n    onMouseUp: onMouseUp\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelStartRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\"\n  }), /*#__PURE__*/React.createElement(MemoChildren, {\n    shouldUpdate: visible || forceRender\n  }, modalRender ? modalRender(content) : content), /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelEndRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\"\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Panel.displayName = 'Panel';\n}\nexport default Panel;", "map": {"version": 3, "names": ["_objectSpread", "_extends", "React", "useRef", "classNames", "MemoC<PERSON><PERSON>n", "sentinelStyle", "width", "height", "overflow", "outline", "Panel", "forwardRef", "props", "ref", "prefixCls", "className", "style", "title", "ariaId", "footer", "closable", "closeIcon", "onClose", "children", "bodyStyle", "bodyProps", "modalRender", "onMouseDown", "onMouseUp", "holder<PERSON><PERSON>", "visible", "forceRender", "sentinelStartRef", "sentinelEndRef", "useImperativeHandle", "focus", "_sentinelStartRef$cur", "current", "changeActive", "next", "_document", "document", "activeElement", "contentStyle", "undefined", "footerNode", "createElement", "concat", "headerNode", "id", "closer", "type", "onClick", "content", "key", "role", "tabIndex", "shouldUpdate", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-dialog/es/Dialog/Content/Panel.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport React, { useRef } from 'react';\nimport classNames from 'classnames';\nimport MemoChildren from \"./MemoChildren\";\nvar sentinelStyle = {\n  width: 0,\n  height: 0,\n  overflow: 'hidden',\n  outline: 'none'\n};\nvar Panel = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    title = props.title,\n    ariaId = props.ariaId,\n    footer = props.footer,\n    closable = props.closable,\n    closeIcon = props.closeIcon,\n    onClose = props.onClose,\n    children = props.children,\n    bodyStyle = props.bodyStyle,\n    bodyProps = props.bodyProps,\n    modalRender = props.modalRender,\n    onMouseDown = props.onMouseDown,\n    onMouseUp = props.onMouseUp,\n    holderRef = props.holderRef,\n    visible = props.visible,\n    forceRender = props.forceRender,\n    width = props.width,\n    height = props.height;\n\n  // ================================= Refs =================================\n  var sentinelStartRef = useRef();\n  var sentinelEndRef = useRef();\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        var _sentinelStartRef$cur;\n        (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 ? void 0 : _sentinelStartRef$cur.focus();\n      },\n      changeActive: function changeActive(next) {\n        var _document = document,\n          activeElement = _document.activeElement;\n        if (next && activeElement === sentinelEndRef.current) {\n          sentinelStartRef.current.focus();\n        } else if (!next && activeElement === sentinelStartRef.current) {\n          sentinelEndRef.current.focus();\n        }\n      }\n    };\n  });\n\n  // ================================ Style =================================\n  var contentStyle = {};\n  if (width !== undefined) {\n    contentStyle.width = width;\n  }\n  if (height !== undefined) {\n    contentStyle.height = height;\n  }\n  // ================================ Render ================================\n  var footerNode;\n  if (footer) {\n    footerNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, footer);\n  }\n  var headerNode;\n  if (title) {\n    headerNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-header\")\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-title\"),\n      id: ariaId\n    }, title));\n  }\n  var closer;\n  if (closable) {\n    closer = /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: onClose,\n      \"aria-label\": \"Close\",\n      className: \"\".concat(prefixCls, \"-close\")\n    }, closeIcon || /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-close-x\")\n    }));\n  }\n  var content = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, closer, headerNode, /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: \"\".concat(prefixCls, \"-body\"),\n    style: bodyStyle\n  }, bodyProps), children), footerNode);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    key: \"dialog-element\",\n    role: \"dialog\",\n    \"aria-labelledby\": title ? ariaId : null,\n    \"aria-modal\": \"true\",\n    ref: holderRef,\n    style: _objectSpread(_objectSpread({}, style), contentStyle),\n    className: classNames(prefixCls, className),\n    onMouseDown: onMouseDown,\n    onMouseUp: onMouseUp\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelStartRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\"\n  }), /*#__PURE__*/React.createElement(MemoChildren, {\n    shouldUpdate: visible || forceRender\n  }, modalRender ? modalRender(content) : content), /*#__PURE__*/React.createElement(\"div\", {\n    tabIndex: 0,\n    ref: sentinelEndRef,\n    style: sentinelStyle,\n    \"aria-hidden\": \"true\"\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Panel.displayName = 'Panel';\n}\nexport default Panel;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,aAAa,GAAG;EAClBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,KAAK,GAAG,aAAaT,KAAK,CAACU,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC9D,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,OAAO,GAAGV,KAAK,CAACU,OAAO;IACvBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,WAAW,GAAGd,KAAK,CAACc,WAAW;IAC/BC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,SAAS,GAAGhB,KAAK,CAACgB,SAAS;IAC3BC,SAAS,GAAGjB,KAAK,CAACiB,SAAS;IAC3BC,OAAO,GAAGlB,KAAK,CAACkB,OAAO;IACvBC,WAAW,GAAGnB,KAAK,CAACmB,WAAW;IAC/BzB,KAAK,GAAGM,KAAK,CAACN,KAAK;IACnBC,MAAM,GAAGK,KAAK,CAACL,MAAM;;EAEvB;EACA,IAAIyB,gBAAgB,GAAG9B,MAAM,CAAC,CAAC;EAC/B,IAAI+B,cAAc,GAAG/B,MAAM,CAAC,CAAC;EAC7BD,KAAK,CAACiC,mBAAmB,CAACrB,GAAG,EAAE,YAAY;IACzC,OAAO;MACLsB,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;QACtB,IAAIC,qBAAqB;QACzB,CAACA,qBAAqB,GAAGJ,gBAAgB,CAACK,OAAO,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACD,KAAK,CAAC,CAAC;MAC1I,CAAC;MACDG,YAAY,EAAE,SAASA,YAAYA,CAACC,IAAI,EAAE;QACxC,IAAIC,SAAS,GAAGC,QAAQ;UACtBC,aAAa,GAAGF,SAAS,CAACE,aAAa;QACzC,IAAIH,IAAI,IAAIG,aAAa,KAAKT,cAAc,CAACI,OAAO,EAAE;UACpDL,gBAAgB,CAACK,OAAO,CAACF,KAAK,CAAC,CAAC;QAClC,CAAC,MAAM,IAAI,CAACI,IAAI,IAAIG,aAAa,KAAKV,gBAAgB,CAACK,OAAO,EAAE;UAC9DJ,cAAc,CAACI,OAAO,CAACF,KAAK,CAAC,CAAC;QAChC;MACF;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,IAAIQ,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIrC,KAAK,KAAKsC,SAAS,EAAE;IACvBD,YAAY,CAACrC,KAAK,GAAGA,KAAK;EAC5B;EACA,IAAIC,MAAM,KAAKqC,SAAS,EAAE;IACxBD,YAAY,CAACpC,MAAM,GAAGA,MAAM;EAC9B;EACA;EACA,IAAIsC,UAAU;EACd,IAAI1B,MAAM,EAAE;IACV0B,UAAU,GAAG,aAAa5C,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;MACnD/B,SAAS,EAAE,EAAE,CAACgC,MAAM,CAACjC,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAEK,MAAM,CAAC;EACZ;EACA,IAAI6B,UAAU;EACd,IAAI/B,KAAK,EAAE;IACT+B,UAAU,GAAG,aAAa/C,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;MACnD/B,SAAS,EAAE,EAAE,CAACgC,MAAM,CAACjC,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAE,aAAab,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;MACzC/B,SAAS,EAAE,EAAE,CAACgC,MAAM,CAACjC,SAAS,EAAE,QAAQ,CAAC;MACzCmC,EAAE,EAAE/B;IACN,CAAC,EAAED,KAAK,CAAC,CAAC;EACZ;EACA,IAAIiC,MAAM;EACV,IAAI9B,QAAQ,EAAE;IACZ8B,MAAM,GAAG,aAAajD,KAAK,CAAC6C,aAAa,CAAC,QAAQ,EAAE;MAClDK,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE9B,OAAO;MAChB,YAAY,EAAE,OAAO;MACrBP,SAAS,EAAE,EAAE,CAACgC,MAAM,CAACjC,SAAS,EAAE,QAAQ;IAC1C,CAAC,EAAEO,SAAS,IAAI,aAAapB,KAAK,CAAC6C,aAAa,CAAC,MAAM,EAAE;MACvD/B,SAAS,EAAE,EAAE,CAACgC,MAAM,CAACjC,SAAS,EAAE,UAAU;IAC5C,CAAC,CAAC,CAAC;EACL;EACA,IAAIuC,OAAO,GAAG,aAAapD,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;IACpD/B,SAAS,EAAE,EAAE,CAACgC,MAAM,CAACjC,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEoC,MAAM,EAAEF,UAAU,EAAE,aAAa/C,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE9C,QAAQ,CAAC;IACtEe,SAAS,EAAE,EAAE,CAACgC,MAAM,CAACjC,SAAS,EAAE,OAAO,CAAC;IACxCE,KAAK,EAAEQ;EACT,CAAC,EAAEC,SAAS,CAAC,EAAEF,QAAQ,CAAC,EAAEsB,UAAU,CAAC;EACrC,OAAO,aAAa5C,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;IAC7CQ,GAAG,EAAE,gBAAgB;IACrBC,IAAI,EAAE,QAAQ;IACd,iBAAiB,EAAEtC,KAAK,GAAGC,MAAM,GAAG,IAAI;IACxC,YAAY,EAAE,MAAM;IACpBL,GAAG,EAAEgB,SAAS;IACdb,KAAK,EAAEjB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiB,KAAK,CAAC,EAAE2B,YAAY,CAAC;IAC5D5B,SAAS,EAAEZ,UAAU,CAACW,SAAS,EAAEC,SAAS,CAAC;IAC3CY,WAAW,EAAEA,WAAW;IACxBC,SAAS,EAAEA;EACb,CAAC,EAAE,aAAa3B,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;IACzCU,QAAQ,EAAE,CAAC;IACX3C,GAAG,EAAEmB,gBAAgB;IACrBhB,KAAK,EAAEX,aAAa;IACpB,aAAa,EAAE;EACjB,CAAC,CAAC,EAAE,aAAaJ,KAAK,CAAC6C,aAAa,CAAC1C,YAAY,EAAE;IACjDqD,YAAY,EAAE3B,OAAO,IAAIC;EAC3B,CAAC,EAAEL,WAAW,GAAGA,WAAW,CAAC2B,OAAO,CAAC,GAAGA,OAAO,CAAC,EAAE,aAAapD,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;IACxFU,QAAQ,EAAE,CAAC;IACX3C,GAAG,EAAEoB,cAAc;IACnBjB,KAAK,EAAEX,aAAa;IACpB,aAAa,EAAE;EACjB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIqD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzClD,KAAK,CAACmD,WAAW,GAAG,OAAO;AAC7B;AACA,eAAenD,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}