{"ast": null, "code": "import useMemo from \"rc-util/es/hooks/useMemo\";\nimport shallowEqual from \"rc-util/es/isEqual\";\nimport { formatValue, isEqual } from \"../utils/dateUtil\";\nexport default function useValueTexts(value, _ref) {\n  var formatList = _ref.formatList,\n    generateConfig = _ref.generateConfig,\n    locale = _ref.locale;\n  return useMemo(function () {\n    if (!value) {\n      return [[''], ''];\n    }\n\n    // We will convert data format back to first format\n    var firstValueText = '';\n    var fullValueTexts = [];\n    for (var i = 0; i < formatList.length; i += 1) {\n      var format = formatList[i];\n      var formatStr = formatValue(value, {\n        generateConfig: generateConfig,\n        locale: locale,\n        format: format\n      });\n      fullValueTexts.push(formatStr);\n      if (i === 0) {\n        firstValueText = formatStr;\n      }\n    }\n    return [fullValueTexts, firstValueText];\n  }, [value, formatList], function (prev, next) {\n    return (\n      // Not Same Date\n      !isEqual(generateConfig, prev[0], next[0]) ||\n      // Not Same format\n      !shallowEqual(prev[1], next[1], true)\n    );\n  });\n}", "map": {"version": 3, "names": ["useMemo", "shallowEqual", "formatValue", "isEqual", "useValueTexts", "value", "_ref", "formatList", "generateConfig", "locale", "firstValueText", "fullValueTexts", "i", "length", "format", "formatStr", "push", "prev", "next"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/hooks/useValueTexts.js"], "sourcesContent": ["import useMemo from \"rc-util/es/hooks/useMemo\";\nimport shallowEqual from \"rc-util/es/isEqual\";\nimport { formatValue, isEqual } from \"../utils/dateUtil\";\nexport default function useValueTexts(value, _ref) {\n  var formatList = _ref.formatList,\n    generateConfig = _ref.generateConfig,\n    locale = _ref.locale;\n  return useMemo(function () {\n    if (!value) {\n      return [[''], ''];\n    }\n\n    // We will convert data format back to first format\n    var firstValueText = '';\n    var fullValueTexts = [];\n    for (var i = 0; i < formatList.length; i += 1) {\n      var format = formatList[i];\n      var formatStr = formatValue(value, {\n        generateConfig: generateConfig,\n        locale: locale,\n        format: format\n      });\n      fullValueTexts.push(formatStr);\n      if (i === 0) {\n        firstValueText = formatStr;\n      }\n    }\n    return [fullValueTexts, firstValueText];\n  }, [value, formatList], function (prev, next) {\n    return (\n      // Not Same Date\n      !isEqual(generateConfig, prev[0], next[0]) ||\n      // Not Same format\n      !shallowEqual(prev[1], next[1], true)\n    );\n  });\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,SAASC,WAAW,EAAEC,OAAO,QAAQ,mBAAmB;AACxD,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACjD,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;IAC9BC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,MAAM,GAAGH,IAAI,CAACG,MAAM;EACtB,OAAOT,OAAO,CAAC,YAAY;IACzB,IAAI,CAACK,KAAK,EAAE;MACV,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACnB;;IAEA;IACA,IAAIK,cAAc,GAAG,EAAE;IACvB,IAAIC,cAAc,GAAG,EAAE;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,CAACM,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC7C,IAAIE,MAAM,GAAGP,UAAU,CAACK,CAAC,CAAC;MAC1B,IAAIG,SAAS,GAAGb,WAAW,CAACG,KAAK,EAAE;QACjCG,cAAc,EAAEA,cAAc;QAC9BC,MAAM,EAAEA,MAAM;QACdK,MAAM,EAAEA;MACV,CAAC,CAAC;MACFH,cAAc,CAACK,IAAI,CAACD,SAAS,CAAC;MAC9B,IAAIH,CAAC,KAAK,CAAC,EAAE;QACXF,cAAc,GAAGK,SAAS;MAC5B;IACF;IACA,OAAO,CAACJ,cAAc,EAAED,cAAc,CAAC;EACzC,CAAC,EAAE,CAACL,KAAK,EAAEE,UAAU,CAAC,EAAE,UAAUU,IAAI,EAAEC,IAAI,EAAE;IAC5C;MACE;MACA,CAACf,OAAO,CAACK,cAAc,EAAES,IAAI,CAAC,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1C;MACA,CAACjB,YAAY,CAACgB,IAAI,CAAC,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;IAAC;EAEzC,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}