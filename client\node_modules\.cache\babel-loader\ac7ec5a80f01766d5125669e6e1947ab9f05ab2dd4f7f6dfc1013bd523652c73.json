{"ast": null, "code": "import { useEffect, useLayoutEffect } from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\n\n// It's safe to use `useLayoutEffect` but the warning is annoying\nvar useIsomorphicLayoutEffect = canUseDom() ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;", "map": {"version": 3, "names": ["useEffect", "useLayoutEffect", "canUseDom", "useIsomorphicLayoutEffect"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\n\n// It's safe to use `useLayoutEffect` but the warning is annoying\nvar useIsomorphicLayoutEffect = canUseDom() ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,eAAe,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,0BAA0B;;AAEhD;AACA,IAAIC,yBAAyB,GAAGD,SAAS,CAAC,CAAC,GAAGD,eAAe,GAAGD,SAAS;AACzE,eAAeG,yBAAyB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}