{"ast": null, "code": "const genFixedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    colorSplit,\n    motionDurationSlow,\n    zIndexTableFixed,\n    tableBg,\n    zIndexTableSticky\n  } = token;\n  const shadowColor = colorSplit;\n  // Follow style is magic of shadow which should not follow token:\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`\n        ${componentCls}-cell-fix-left,\n        ${componentCls}-cell-fix-right\n      `]: {\n        position: 'sticky !important',\n        zIndex: zIndexTableFixed,\n        background: tableBg\n      },\n      [`\n        ${componentCls}-cell-fix-left-first::after,\n        ${componentCls}-cell-fix-left-last::after\n      `]: {\n        position: 'absolute',\n        top: 0,\n        right: {\n          _skip_check_: true,\n          value: 0\n        },\n        bottom: -lineWidth,\n        width: 30,\n        transform: 'translateX(100%)',\n        transition: `box-shadow ${motionDurationSlow}`,\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [`${componentCls}-cell-fix-left-all::after`]: {\n        display: 'none'\n      },\n      [`\n        ${componentCls}-cell-fix-right-first::after,\n        ${componentCls}-cell-fix-right-last::after\n      `]: {\n        position: 'absolute',\n        top: 0,\n        bottom: -lineWidth,\n        left: {\n          _skip_check_: true,\n          value: 0\n        },\n        width: 30,\n        transform: 'translateX(-100%)',\n        transition: `box-shadow ${motionDurationSlow}`,\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [`${componentCls}-container`]: {\n        '&::before, &::after': {\n          position: 'absolute',\n          top: 0,\n          bottom: 0,\n          zIndex: zIndexTableSticky + 1,\n          width: 30,\n          transition: `box-shadow ${motionDurationSlow}`,\n          content: '\"\"',\n          pointerEvents: 'none'\n        },\n        '&::before': {\n          insetInlineStart: 0\n        },\n        '&::after': {\n          insetInlineEnd: 0\n        }\n      },\n      [`${componentCls}-ping-left`]: {\n        [`&:not(${componentCls}-has-fix-left) ${componentCls}-container`]: {\n          position: 'relative',\n          '&::before': {\n            boxShadow: `inset 10px 0 8px -8px ${shadowColor}`\n          }\n        },\n        [`\n          ${componentCls}-cell-fix-left-first::after,\n          ${componentCls}-cell-fix-left-last::after\n        `]: {\n          boxShadow: `inset 10px 0 8px -8px ${shadowColor}`\n        },\n        [`${componentCls}-cell-fix-left-last::before`]: {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [`${componentCls}-ping-right`]: {\n        [`&:not(${componentCls}-has-fix-right) ${componentCls}-container`]: {\n          position: 'relative',\n          '&::after': {\n            boxShadow: `inset -10px 0 8px -8px ${shadowColor}`\n          }\n        },\n        [`\n          ${componentCls}-cell-fix-right-first::after,\n          ${componentCls}-cell-fix-right-last::after\n        `]: {\n          boxShadow: `inset -10px 0 8px -8px ${shadowColor}`\n        }\n      }\n    }\n  };\n};\nexport default genFixedStyle;", "map": {"version": 3, "names": ["genFixedStyle", "token", "componentCls", "lineWidth", "colorSplit", "motionDurationSlow", "zIndexTableFixed", "tableBg", "zIndexTableSticky", "shadowColor", "position", "zIndex", "background", "top", "right", "_skip_check_", "value", "bottom", "width", "transform", "transition", "content", "pointerEvents", "display", "left", "insetInlineStart", "insetInlineEnd", "boxShadow", "backgroundColor"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/fixed.js"], "sourcesContent": ["const genFixedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    colorSplit,\n    motionDurationSlow,\n    zIndexTableFixed,\n    tableBg,\n    zIndexTableSticky\n  } = token;\n  const shadowColor = colorSplit;\n  // Follow style is magic of shadow which should not follow token:\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`\n        ${componentCls}-cell-fix-left,\n        ${componentCls}-cell-fix-right\n      `]: {\n        position: 'sticky !important',\n        zIndex: zIndexTableFixed,\n        background: tableBg\n      },\n      [`\n        ${componentCls}-cell-fix-left-first::after,\n        ${componentCls}-cell-fix-left-last::after\n      `]: {\n        position: 'absolute',\n        top: 0,\n        right: {\n          _skip_check_: true,\n          value: 0\n        },\n        bottom: -lineWidth,\n        width: 30,\n        transform: 'translateX(100%)',\n        transition: `box-shadow ${motionDurationSlow}`,\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [`${componentCls}-cell-fix-left-all::after`]: {\n        display: 'none'\n      },\n      [`\n        ${componentCls}-cell-fix-right-first::after,\n        ${componentCls}-cell-fix-right-last::after\n      `]: {\n        position: 'absolute',\n        top: 0,\n        bottom: -lineWidth,\n        left: {\n          _skip_check_: true,\n          value: 0\n        },\n        width: 30,\n        transform: 'translateX(-100%)',\n        transition: `box-shadow ${motionDurationSlow}`,\n        content: '\"\"',\n        pointerEvents: 'none'\n      },\n      [`${componentCls}-container`]: {\n        '&::before, &::after': {\n          position: 'absolute',\n          top: 0,\n          bottom: 0,\n          zIndex: zIndexTableSticky + 1,\n          width: 30,\n          transition: `box-shadow ${motionDurationSlow}`,\n          content: '\"\"',\n          pointerEvents: 'none'\n        },\n        '&::before': {\n          insetInlineStart: 0\n        },\n        '&::after': {\n          insetInlineEnd: 0\n        }\n      },\n      [`${componentCls}-ping-left`]: {\n        [`&:not(${componentCls}-has-fix-left) ${componentCls}-container`]: {\n          position: 'relative',\n          '&::before': {\n            boxShadow: `inset 10px 0 8px -8px ${shadowColor}`\n          }\n        },\n        [`\n          ${componentCls}-cell-fix-left-first::after,\n          ${componentCls}-cell-fix-left-last::after\n        `]: {\n          boxShadow: `inset 10px 0 8px -8px ${shadowColor}`\n        },\n        [`${componentCls}-cell-fix-left-last::before`]: {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [`${componentCls}-ping-right`]: {\n        [`&:not(${componentCls}-has-fix-right) ${componentCls}-container`]: {\n          position: 'relative',\n          '&::after': {\n            boxShadow: `inset -10px 0 8px -8px ${shadowColor}`\n          }\n        },\n        [`\n          ${componentCls}-cell-fix-right-first::after,\n          ${componentCls}-cell-fix-right-last::after\n        `]: {\n          boxShadow: `inset -10px 0 8px -8px ${shadowColor}`\n        }\n      }\n    }\n  };\n};\nexport default genFixedStyle;"], "mappings": "AAAA,MAAMA,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,UAAU;IACVC,kBAAkB;IAClBC,gBAAgB;IAChBC,OAAO;IACPC;EACF,CAAC,GAAGP,KAAK;EACT,MAAMQ,WAAW,GAAGL,UAAU;EAC9B;EACA,OAAO;IACL,CAAE,GAAEF,YAAa,UAAS,GAAG;MAC3B,CAAE;AACR,UAAUA,YAAa;AACvB,UAAUA,YAAa;AACvB,OAAO,GAAG;QACFQ,QAAQ,EAAE,mBAAmB;QAC7BC,MAAM,EAAEL,gBAAgB;QACxBM,UAAU,EAAEL;MACd,CAAC;MACD,CAAE;AACR,UAAUL,YAAa;AACvB,UAAUA,YAAa;AACvB,OAAO,GAAG;QACFQ,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,KAAK,EAAE;UACLC,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDC,MAAM,EAAE,CAACd,SAAS;QAClBe,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,kBAAkB;QAC7BC,UAAU,EAAG,cAAaf,kBAAmB,EAAC;QAC9CgB,OAAO,EAAE,IAAI;QACbC,aAAa,EAAE;MACjB,CAAC;MACD,CAAE,GAAEpB,YAAa,2BAA0B,GAAG;QAC5CqB,OAAO,EAAE;MACX,CAAC;MACD,CAAE;AACR,UAAUrB,YAAa;AACvB,UAAUA,YAAa;AACvB,OAAO,GAAG;QACFQ,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNI,MAAM,EAAE,CAACd,SAAS;QAClBqB,IAAI,EAAE;UACJT,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDE,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,mBAAmB;QAC9BC,UAAU,EAAG,cAAaf,kBAAmB,EAAC;QAC9CgB,OAAO,EAAE,IAAI;QACbC,aAAa,EAAE;MACjB,CAAC;MACD,CAAE,GAAEpB,YAAa,YAAW,GAAG;QAC7B,qBAAqB,EAAE;UACrBQ,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNI,MAAM,EAAE,CAAC;UACTN,MAAM,EAAEH,iBAAiB,GAAG,CAAC;UAC7BU,KAAK,EAAE,EAAE;UACTE,UAAU,EAAG,cAAaf,kBAAmB,EAAC;UAC9CgB,OAAO,EAAE,IAAI;UACbC,aAAa,EAAE;QACjB,CAAC;QACD,WAAW,EAAE;UACXG,gBAAgB,EAAE;QACpB,CAAC;QACD,UAAU,EAAE;UACVC,cAAc,EAAE;QAClB;MACF,CAAC;MACD,CAAE,GAAExB,YAAa,YAAW,GAAG;QAC7B,CAAE,SAAQA,YAAa,kBAAiBA,YAAa,YAAW,GAAG;UACjEQ,QAAQ,EAAE,UAAU;UACpB,WAAW,EAAE;YACXiB,SAAS,EAAG,yBAAwBlB,WAAY;UAClD;QACF,CAAC;QACD,CAAE;AACV,YAAYP,YAAa;AACzB,YAAYA,YAAa;AACzB,SAAS,GAAG;UACFyB,SAAS,EAAG,yBAAwBlB,WAAY;QAClD,CAAC;QACD,CAAE,GAAEP,YAAa,6BAA4B,GAAG;UAC9C0B,eAAe,EAAE;QACnB;MACF,CAAC;MACD,CAAE,GAAE1B,YAAa,aAAY,GAAG;QAC9B,CAAE,SAAQA,YAAa,mBAAkBA,YAAa,YAAW,GAAG;UAClEQ,QAAQ,EAAE,UAAU;UACpB,UAAU,EAAE;YACViB,SAAS,EAAG,0BAAyBlB,WAAY;UACnD;QACF,CAAC;QACD,CAAE;AACV,YAAYP,YAAa;AACzB,YAAYA,YAAa;AACzB,SAAS,GAAG;UACFyB,SAAS,EAAG,0BAAyBlB,WAAY;QACnD;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeT,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}