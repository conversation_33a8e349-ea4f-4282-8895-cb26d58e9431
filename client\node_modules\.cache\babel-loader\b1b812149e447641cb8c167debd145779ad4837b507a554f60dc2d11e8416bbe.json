{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { isDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { collectScroller, getVisibleArea, getWin, toNum } from \"../util\";\nfunction getUnitOffset(size) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var offsetStr = \"\".concat(offset);\n  var cells = offsetStr.match(/^(.*)\\%$/);\n  if (cells) {\n    return size * (parseFloat(cells[1]) / 100);\n  }\n  return parseFloat(offsetStr);\n}\nfunction getNumberOffset(rect, offset) {\n  var _ref = offset || [],\n    _ref2 = _slicedToArray(_ref, 2),\n    offsetX = _ref2[0],\n    offsetY = _ref2[1];\n  return [getUnitOffset(rect.width, offsetX), getUnitOffset(rect.height, offsetY)];\n}\nfunction splitPoints() {\n  var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  return [points[0], points[1]];\n}\nfunction getAlignPoint(rect, points) {\n  var topBottom = points[0];\n  var leftRight = points[1];\n  var x;\n  var y;\n\n  // Top & Bottom\n  if (topBottom === 't') {\n    y = rect.y;\n  } else if (topBottom === 'b') {\n    y = rect.y + rect.height;\n  } else {\n    y = rect.y + rect.height / 2;\n  }\n\n  // Left & Right\n  if (leftRight === 'l') {\n    x = rect.x;\n  } else if (leftRight === 'r') {\n    x = rect.x + rect.width;\n  } else {\n    x = rect.x + rect.width / 2;\n  }\n  return {\n    x: x,\n    y: y\n  };\n}\nfunction reversePoints(points, index) {\n  var reverseMap = {\n    t: 'b',\n    b: 't',\n    l: 'r',\n    r: 'l'\n  };\n  return points.map(function (point, i) {\n    if (i === index) {\n      return reverseMap[point] || 'c';\n    }\n    return point;\n  }).join('');\n}\nexport default function useAlign(open, popupEle, target, placement, builtinPlacements, popupAlign, onPopupAlign) {\n  var _React$useState = React.useState({\n      ready: false,\n      offsetX: 0,\n      offsetY: 0,\n      offsetR: 0,\n      offsetB: 0,\n      arrowX: 0,\n      arrowY: 0,\n      scaleX: 1,\n      scaleY: 1,\n      align: builtinPlacements[placement] || {}\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    offsetInfo = _React$useState2[0],\n    setOffsetInfo = _React$useState2[1];\n  var alignCountRef = React.useRef(0);\n  var scrollerList = React.useMemo(function () {\n    if (!popupEle) {\n      return [];\n    }\n    return collectScroller(popupEle);\n  }, [popupEle]);\n\n  // ========================= Flip ==========================\n  // We will memo flip info.\n  // If size change to make flip, it will memo the flip info and use it in next align.\n  var prevFlipRef = React.useRef({});\n  var resetFlipCache = function resetFlipCache() {\n    prevFlipRef.current = {};\n  };\n  if (!open) {\n    resetFlipCache();\n  }\n\n  // ========================= Align =========================\n  var onAlign = useEvent(function () {\n    if (popupEle && target && open) {\n      var _popupElement$parentE, _popupElement$parentE2;\n      var popupElement = popupEle;\n      var doc = popupElement.ownerDocument;\n      var win = getWin(popupElement);\n      var _win$getComputedStyle = win.getComputedStyle(popupElement),\n        width = _win$getComputedStyle.width,\n        height = _win$getComputedStyle.height,\n        popupPosition = _win$getComputedStyle.position;\n      var originLeft = popupElement.style.left;\n      var originTop = popupElement.style.top;\n      var originRight = popupElement.style.right;\n      var originBottom = popupElement.style.bottom;\n\n      // Placement\n      var placementInfo = _objectSpread(_objectSpread({}, builtinPlacements[placement]), popupAlign);\n\n      // placeholder element\n      var placeholderElement = doc.createElement('div');\n      (_popupElement$parentE = popupElement.parentElement) === null || _popupElement$parentE === void 0 ? void 0 : _popupElement$parentE.appendChild(placeholderElement);\n      placeholderElement.style.left = \"\".concat(popupElement.offsetLeft, \"px\");\n      placeholderElement.style.top = \"\".concat(popupElement.offsetTop, \"px\");\n      placeholderElement.style.position = popupPosition;\n      placeholderElement.style.height = \"\".concat(popupElement.offsetHeight, \"px\");\n      placeholderElement.style.width = \"\".concat(popupElement.offsetWidth, \"px\");\n\n      // Reset first\n      popupElement.style.left = '0';\n      popupElement.style.top = '0';\n      popupElement.style.right = 'auto';\n      popupElement.style.bottom = 'auto';\n\n      // Calculate align style, we should consider `transform` case\n      var targetRect;\n      if (Array.isArray(target)) {\n        targetRect = {\n          x: target[0],\n          y: target[1],\n          width: 0,\n          height: 0\n        };\n      } else {\n        var rect = target.getBoundingClientRect();\n        targetRect = {\n          x: rect.x,\n          y: rect.y,\n          width: rect.width,\n          height: rect.height\n        };\n      }\n      var popupRect = popupElement.getBoundingClientRect();\n      var _doc$documentElement = doc.documentElement,\n        clientWidth = _doc$documentElement.clientWidth,\n        clientHeight = _doc$documentElement.clientHeight,\n        scrollWidth = _doc$documentElement.scrollWidth,\n        scrollHeight = _doc$documentElement.scrollHeight,\n        scrollTop = _doc$documentElement.scrollTop,\n        scrollLeft = _doc$documentElement.scrollLeft;\n      var popupHeight = popupRect.height;\n      var popupWidth = popupRect.width;\n      var targetHeight = targetRect.height;\n      var targetWidth = targetRect.width;\n\n      // Get bounding of visible area\n      var visibleRegion = {\n        left: 0,\n        top: 0,\n        right: clientWidth,\n        bottom: clientHeight\n      };\n      var scrollRegion = {\n        left: -scrollLeft,\n        top: -scrollTop,\n        right: scrollWidth - scrollLeft,\n        bottom: scrollHeight - scrollTop\n      };\n      var htmlRegion = placementInfo.htmlRegion;\n      var VISIBLE = 'visible';\n      var VISIBLE_FIRST = 'visibleFirst';\n      if (htmlRegion !== 'scroll' && htmlRegion !== VISIBLE_FIRST) {\n        htmlRegion = VISIBLE;\n      }\n      var isVisibleFirst = htmlRegion === VISIBLE_FIRST;\n      var scrollRegionArea = getVisibleArea(scrollRegion, scrollerList);\n      var visibleRegionArea = getVisibleArea(visibleRegion, scrollerList);\n      var visibleArea = htmlRegion === VISIBLE ? visibleRegionArea : scrollRegionArea;\n\n      // When set to `visibleFirst`,\n      // the check `adjust` logic will use `visibleRegion` for check first.\n      var adjustCheckVisibleArea = isVisibleFirst ? visibleRegionArea : visibleArea;\n\n      // Record right & bottom align data\n      popupElement.style.left = 'auto';\n      popupElement.style.top = 'auto';\n      popupElement.style.right = '0';\n      popupElement.style.bottom = '0';\n      var popupMirrorRect = popupElement.getBoundingClientRect();\n\n      // Reset back\n      popupElement.style.left = originLeft;\n      popupElement.style.top = originTop;\n      popupElement.style.right = originRight;\n      popupElement.style.bottom = originBottom;\n      (_popupElement$parentE2 = popupElement.parentElement) === null || _popupElement$parentE2 === void 0 ? void 0 : _popupElement$parentE2.removeChild(placeholderElement);\n\n      // Calculate scale\n      var _scaleX = toNum(Math.round(popupWidth / parseFloat(width) * 1000) / 1000);\n      var _scaleY = toNum(Math.round(popupHeight / parseFloat(height) * 1000) / 1000);\n\n      // No need to align since it's not visible in view\n      if (_scaleX === 0 || _scaleY === 0 || isDOM(target) && !isVisible(target)) {\n        return;\n      }\n\n      // Offset\n      var offset = placementInfo.offset,\n        targetOffset = placementInfo.targetOffset;\n      var _getNumberOffset = getNumberOffset(popupRect, offset),\n        _getNumberOffset2 = _slicedToArray(_getNumberOffset, 2),\n        popupOffsetX = _getNumberOffset2[0],\n        popupOffsetY = _getNumberOffset2[1];\n      var _getNumberOffset3 = getNumberOffset(targetRect, targetOffset),\n        _getNumberOffset4 = _slicedToArray(_getNumberOffset3, 2),\n        targetOffsetX = _getNumberOffset4[0],\n        targetOffsetY = _getNumberOffset4[1];\n      targetRect.x -= targetOffsetX;\n      targetRect.y -= targetOffsetY;\n\n      // Points\n      var _ref3 = placementInfo.points || [],\n        _ref4 = _slicedToArray(_ref3, 2),\n        popupPoint = _ref4[0],\n        targetPoint = _ref4[1];\n      var targetPoints = splitPoints(targetPoint);\n      var popupPoints = splitPoints(popupPoint);\n      var targetAlignPoint = getAlignPoint(targetRect, targetPoints);\n      var popupAlignPoint = getAlignPoint(popupRect, popupPoints);\n\n      // Real align info may not same as origin one\n      var nextAlignInfo = _objectSpread({}, placementInfo);\n\n      // Next Offset\n      var nextOffsetX = targetAlignPoint.x - popupAlignPoint.x + popupOffsetX;\n      var nextOffsetY = targetAlignPoint.y - popupAlignPoint.y + popupOffsetY;\n\n      // ============== Intersection ===============\n      // Get area by position. Used for check if flip area is better\n      function getIntersectionVisibleArea(offsetX, offsetY) {\n        var area = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : visibleArea;\n        var l = popupRect.x + offsetX;\n        var t = popupRect.y + offsetY;\n        var r = l + popupWidth;\n        var b = t + popupHeight;\n        var visibleL = Math.max(l, area.left);\n        var visibleT = Math.max(t, area.top);\n        var visibleR = Math.min(r, area.right);\n        var visibleB = Math.min(b, area.bottom);\n        return Math.max(0, (visibleR - visibleL) * (visibleB - visibleT));\n      }\n      var originIntersectionVisibleArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY);\n\n      // As `visibleFirst`, we prepare this for check\n      var originIntersectionRecommendArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY, visibleRegionArea);\n\n      // ========================== Overflow ===========================\n      var targetAlignPointTL = getAlignPoint(targetRect, ['t', 'l']);\n      var popupAlignPointTL = getAlignPoint(popupRect, ['t', 'l']);\n      var targetAlignPointBR = getAlignPoint(targetRect, ['b', 'r']);\n      var popupAlignPointBR = getAlignPoint(popupRect, ['b', 'r']);\n      var overflow = placementInfo.overflow || {};\n      var adjustX = overflow.adjustX,\n        adjustY = overflow.adjustY,\n        shiftX = overflow.shiftX,\n        shiftY = overflow.shiftY;\n      var supportAdjust = function supportAdjust(val) {\n        if (typeof val === 'boolean') {\n          return val;\n        }\n        return val >= 0;\n      };\n\n      // Prepare position\n      var nextPopupY;\n      var nextPopupBottom;\n      var nextPopupX;\n      var nextPopupRight;\n      function syncNextPopupPosition() {\n        nextPopupY = popupRect.y + nextOffsetY;\n        nextPopupBottom = nextPopupY + popupHeight;\n        nextPopupX = popupRect.x + nextOffsetX;\n        nextPopupRight = nextPopupX + popupWidth;\n      }\n      syncNextPopupPosition();\n\n      // >>>>>>>>>> Top & Bottom\n      var needAdjustY = supportAdjust(adjustY);\n      var sameTB = popupPoints[0] === targetPoints[0];\n\n      // Bottom to Top\n      if (needAdjustY && popupPoints[0] === 't' && (nextPopupBottom > adjustCheckVisibleArea.bottom || prevFlipRef.current.bt)) {\n        var tmpNextOffsetY = nextOffsetY;\n        if (sameTB) {\n          tmpNextOffsetY -= popupHeight - targetHeight;\n        } else {\n          tmpNextOffsetY = targetAlignPointTL.y - popupAlignPointBR.y - popupOffsetY;\n        }\n        var newVisibleArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY);\n        var newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        newVisibleArea > originIntersectionVisibleArea || newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.bt = true;\n          nextOffsetY = tmpNextOffsetY;\n          popupOffsetY = -popupOffsetY;\n          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];\n        } else {\n          prevFlipRef.current.bt = false;\n        }\n      }\n\n      // Top to Bottom\n      if (needAdjustY && popupPoints[0] === 'b' && (nextPopupY < adjustCheckVisibleArea.top || prevFlipRef.current.tb)) {\n        var _tmpNextOffsetY = nextOffsetY;\n        if (sameTB) {\n          _tmpNextOffsetY += popupHeight - targetHeight;\n        } else {\n          _tmpNextOffsetY = targetAlignPointBR.y - popupAlignPointTL.y - popupOffsetY;\n        }\n        var _newVisibleArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY);\n        var _newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea > originIntersectionVisibleArea || _newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.tb = true;\n          nextOffsetY = _tmpNextOffsetY;\n          popupOffsetY = -popupOffsetY;\n          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];\n        } else {\n          prevFlipRef.current.tb = false;\n        }\n      }\n\n      // >>>>>>>>>> Left & Right\n      var needAdjustX = supportAdjust(adjustX);\n\n      // >>>>> Flip\n      var sameLR = popupPoints[1] === targetPoints[1];\n\n      // Right to Left\n      if (needAdjustX && popupPoints[1] === 'l' && (nextPopupRight > adjustCheckVisibleArea.right || prevFlipRef.current.rl)) {\n        var tmpNextOffsetX = nextOffsetX;\n        if (sameLR) {\n          tmpNextOffsetX -= popupWidth - targetWidth;\n        } else {\n          tmpNextOffsetX = targetAlignPointTL.x - popupAlignPointBR.x - popupOffsetX;\n        }\n        var _newVisibleArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY);\n        var _newVisibleRecommendArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea2 > originIntersectionVisibleArea || _newVisibleArea2 === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea2 >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.rl = true;\n          nextOffsetX = tmpNextOffsetX;\n          popupOffsetX = -popupOffsetX;\n          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];\n        } else {\n          prevFlipRef.current.rl = false;\n        }\n      }\n\n      // Left to Right\n      if (needAdjustX && popupPoints[1] === 'r' && (nextPopupX < adjustCheckVisibleArea.left || prevFlipRef.current.lr)) {\n        var _tmpNextOffsetX = nextOffsetX;\n        if (sameLR) {\n          _tmpNextOffsetX += popupWidth - targetWidth;\n        } else {\n          _tmpNextOffsetX = targetAlignPointBR.x - popupAlignPointTL.x - popupOffsetX;\n        }\n        var _newVisibleArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY);\n        var _newVisibleRecommendArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea3 > originIntersectionVisibleArea || _newVisibleArea3 === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea3 >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.lr = true;\n          nextOffsetX = _tmpNextOffsetX;\n          popupOffsetX = -popupOffsetX;\n          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];\n        } else {\n          prevFlipRef.current.lr = false;\n        }\n      }\n\n      // ============================ Shift ============================\n      syncNextPopupPosition();\n      var numShiftX = shiftX === true ? 0 : shiftX;\n      if (typeof numShiftX === 'number') {\n        // Left\n        if (nextPopupX < visibleRegionArea.left) {\n          nextOffsetX -= nextPopupX - visibleRegionArea.left - popupOffsetX;\n          if (targetRect.x + targetWidth < visibleRegionArea.left + numShiftX) {\n            nextOffsetX += targetRect.x - visibleRegionArea.left + targetWidth - numShiftX;\n          }\n        }\n\n        // Right\n        if (nextPopupRight > visibleRegionArea.right) {\n          nextOffsetX -= nextPopupRight - visibleRegionArea.right - popupOffsetX;\n          if (targetRect.x > visibleRegionArea.right - numShiftX) {\n            nextOffsetX += targetRect.x - visibleRegionArea.right + numShiftX;\n          }\n        }\n      }\n      var numShiftY = shiftY === true ? 0 : shiftY;\n      if (typeof numShiftY === 'number') {\n        // Top\n        if (nextPopupY < visibleRegionArea.top) {\n          nextOffsetY -= nextPopupY - visibleRegionArea.top - popupOffsetY;\n\n          // When target if far away from visible area\n          // Stop shift\n          if (targetRect.y + targetHeight < visibleRegionArea.top + numShiftY) {\n            nextOffsetY += targetRect.y - visibleRegionArea.top + targetHeight - numShiftY;\n          }\n        }\n\n        // Bottom\n        if (nextPopupBottom > visibleRegionArea.bottom) {\n          nextOffsetY -= nextPopupBottom - visibleRegionArea.bottom - popupOffsetY;\n          if (targetRect.y > visibleRegionArea.bottom - numShiftY) {\n            nextOffsetY += targetRect.y - visibleRegionArea.bottom + numShiftY;\n          }\n        }\n      }\n\n      // ============================ Arrow ============================\n      // Arrow center align\n      var popupLeft = popupRect.x + nextOffsetX;\n      var popupRight = popupLeft + popupWidth;\n      var popupTop = popupRect.y + nextOffsetY;\n      var popupBottom = popupTop + popupHeight;\n      var targetLeft = targetRect.x;\n      var targetRight = targetLeft + targetWidth;\n      var targetTop = targetRect.y;\n      var targetBottom = targetTop + targetHeight;\n      var maxLeft = Math.max(popupLeft, targetLeft);\n      var minRight = Math.min(popupRight, targetRight);\n      var xCenter = (maxLeft + minRight) / 2;\n      var nextArrowX = xCenter - popupLeft;\n      var maxTop = Math.max(popupTop, targetTop);\n      var minBottom = Math.min(popupBottom, targetBottom);\n      var yCenter = (maxTop + minBottom) / 2;\n      var nextArrowY = yCenter - popupTop;\n      onPopupAlign === null || onPopupAlign === void 0 ? void 0 : onPopupAlign(popupEle, nextAlignInfo);\n\n      // Additional calculate right & bottom position\n      var offsetX4Right = popupMirrorRect.right - popupRect.x - (nextOffsetX + popupRect.width);\n      var offsetY4Bottom = popupMirrorRect.bottom - popupRect.y - (nextOffsetY + popupRect.height);\n      setOffsetInfo({\n        ready: true,\n        offsetX: nextOffsetX / _scaleX,\n        offsetY: nextOffsetY / _scaleY,\n        offsetR: offsetX4Right / _scaleX,\n        offsetB: offsetY4Bottom / _scaleY,\n        arrowX: nextArrowX / _scaleX,\n        arrowY: nextArrowY / _scaleY,\n        scaleX: _scaleX,\n        scaleY: _scaleY,\n        align: nextAlignInfo\n      });\n    }\n  });\n  var triggerAlign = function triggerAlign() {\n    alignCountRef.current += 1;\n    var id = alignCountRef.current;\n\n    // Merge all align requirement into one frame\n    Promise.resolve().then(function () {\n      if (alignCountRef.current === id) {\n        onAlign();\n      }\n    });\n  };\n\n  // Reset ready status when placement & open changed\n  var resetReady = function resetReady() {\n    setOffsetInfo(function (ori) {\n      return _objectSpread(_objectSpread({}, ori), {}, {\n        ready: false\n      });\n    });\n  };\n  useLayoutEffect(resetReady, [placement]);\n  useLayoutEffect(function () {\n    if (!open) {\n      resetReady();\n    }\n  }, [open]);\n  return [offsetInfo.ready, offsetInfo.offsetX, offsetInfo.offsetY, offsetInfo.offsetR, offsetInfo.offsetB, offsetInfo.arrowX, offsetInfo.arrowY, offsetInfo.scaleX, offsetInfo.scaleY, offsetInfo.align, triggerAlign];\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "isDOM", "isVisible", "useEvent", "useLayoutEffect", "React", "collectScroller", "getVisibleArea", "getWin", "to<PERSON>um", "getUnitOffset", "size", "offset", "arguments", "length", "undefined", "offsetStr", "concat", "cells", "match", "parseFloat", "getNumberOffset", "rect", "_ref", "_ref2", "offsetX", "offsetY", "width", "height", "splitPoints", "points", "getAlignPoint", "topBottom", "leftRight", "x", "y", "reversePoints", "index", "reverseMap", "t", "b", "l", "r", "map", "point", "i", "join", "useAlign", "open", "popup<PERSON>le", "target", "placement", "builtinPlacements", "popupAlign", "onPopupAlign", "_React$useState", "useState", "ready", "offsetR", "offsetB", "arrowX", "arrowY", "scaleX", "scaleY", "align", "_React$useState2", "offsetInfo", "setOffsetInfo", "alignCountRef", "useRef", "scrollerList", "useMemo", "prevFlipRef", "resetFlipCache", "current", "onAlign", "_popupElement$parentE", "_popupElement$parentE2", "popupElement", "doc", "ownerDocument", "win", "_win$getComputedStyle", "getComputedStyle", "popupPosition", "position", "originLeft", "style", "left", "originTop", "top", "originRight", "right", "originBottom", "bottom", "placementInfo", "placeholderElement", "createElement", "parentElement", "append<PERSON><PERSON><PERSON>", "offsetLeft", "offsetTop", "offsetHeight", "offsetWidth", "targetRect", "Array", "isArray", "getBoundingClientRect", "popupRect", "_doc$documentElement", "documentElement", "clientWidth", "clientHeight", "scrollWidth", "scrollHeight", "scrollTop", "scrollLeft", "popupHeight", "popup<PERSON><PERSON><PERSON>", "targetHeight", "targetWidth", "visibleRegion", "scrollRegion", "htmlRegion", "VISIBLE", "VISIBLE_FIRST", "isVisibleFirst", "scrollRegionArea", "visibleRegionArea", "visibleArea", "adjustCheckVisibleArea", "popupMirrorRect", "<PERSON><PERSON><PERSON><PERSON>", "_scaleX", "Math", "round", "_scaleY", "targetOffset", "_getNumberOffset", "_getNumberOffset2", "popupOffsetX", "popupOffsetY", "_getNumberOffset3", "_getNumberOffset4", "targetOffsetX", "targetOffsetY", "_ref3", "_ref4", "popupPoint", "targetPoint", "targetPoints", "popupPoints", "targetAlignPoint", "popupAlignPoint", "nextAlignInfo", "nextOffsetX", "nextOffsetY", "getIntersectionVisibleArea", "area", "visibleL", "max", "visibleT", "visibleR", "min", "visibleB", "originIntersectionVisibleArea", "originIntersectionRecommendArea", "targetAlignPointTL", "popupAlignPointTL", "targetAlignPointBR", "popupAlignPointBR", "overflow", "adjustX", "adjustY", "shiftX", "shiftY", "supportAdjust", "val", "nextPopupY", "nextPopupBottom", "nextPopupX", "nextPopupRight", "syncNextPopupPosition", "needAdjustY", "sameTB", "bt", "tmpNextOffsetY", "newVisibleArea", "newVisibleRecommendArea", "tb", "_tmpNextOffsetY", "_newVisibleArea", "_newVisibleRecommendArea", "needAdjustX", "sameLR", "rl", "tmpNextOffsetX", "_newVisibleArea2", "_newVisibleRecommendArea2", "lr", "_tmpNextOffsetX", "_newVisibleArea3", "_newVisibleRecommendArea3", "numShiftX", "numShiftY", "popupLeft", "popupRight", "popupTop", "popupBottom", "targetLeft", "targetRight", "targetTop", "targetBottom", "maxLeft", "minRight", "xCenter", "nextArrowX", "maxTop", "minBottom", "yCenter", "nextArrowY", "offsetX4Right", "offsetY4Bottom", "triggerAlign", "id", "Promise", "resolve", "then", "resetReady", "ori"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/trigger/es/hooks/useAlign.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { isDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { collectScroller, getVisibleArea, getWin, toNum } from \"../util\";\nfunction getUnitOffset(size) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var offsetStr = \"\".concat(offset);\n  var cells = offsetStr.match(/^(.*)\\%$/);\n  if (cells) {\n    return size * (parseFloat(cells[1]) / 100);\n  }\n  return parseFloat(offsetStr);\n}\nfunction getNumberOffset(rect, offset) {\n  var _ref = offset || [],\n    _ref2 = _slicedToArray(_ref, 2),\n    offsetX = _ref2[0],\n    offsetY = _ref2[1];\n  return [getUnitOffset(rect.width, offsetX), getUnitOffset(rect.height, offsetY)];\n}\nfunction splitPoints() {\n  var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  return [points[0], points[1]];\n}\nfunction getAlignPoint(rect, points) {\n  var topBottom = points[0];\n  var leftRight = points[1];\n  var x;\n  var y;\n\n  // Top & Bottom\n  if (topBottom === 't') {\n    y = rect.y;\n  } else if (topBottom === 'b') {\n    y = rect.y + rect.height;\n  } else {\n    y = rect.y + rect.height / 2;\n  }\n\n  // Left & Right\n  if (leftRight === 'l') {\n    x = rect.x;\n  } else if (leftRight === 'r') {\n    x = rect.x + rect.width;\n  } else {\n    x = rect.x + rect.width / 2;\n  }\n  return {\n    x: x,\n    y: y\n  };\n}\nfunction reversePoints(points, index) {\n  var reverseMap = {\n    t: 'b',\n    b: 't',\n    l: 'r',\n    r: 'l'\n  };\n  return points.map(function (point, i) {\n    if (i === index) {\n      return reverseMap[point] || 'c';\n    }\n    return point;\n  }).join('');\n}\nexport default function useAlign(open, popupEle, target, placement, builtinPlacements, popupAlign, onPopupAlign) {\n  var _React$useState = React.useState({\n      ready: false,\n      offsetX: 0,\n      offsetY: 0,\n      offsetR: 0,\n      offsetB: 0,\n      arrowX: 0,\n      arrowY: 0,\n      scaleX: 1,\n      scaleY: 1,\n      align: builtinPlacements[placement] || {}\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    offsetInfo = _React$useState2[0],\n    setOffsetInfo = _React$useState2[1];\n  var alignCountRef = React.useRef(0);\n  var scrollerList = React.useMemo(function () {\n    if (!popupEle) {\n      return [];\n    }\n    return collectScroller(popupEle);\n  }, [popupEle]);\n\n  // ========================= Flip ==========================\n  // We will memo flip info.\n  // If size change to make flip, it will memo the flip info and use it in next align.\n  var prevFlipRef = React.useRef({});\n  var resetFlipCache = function resetFlipCache() {\n    prevFlipRef.current = {};\n  };\n  if (!open) {\n    resetFlipCache();\n  }\n\n  // ========================= Align =========================\n  var onAlign = useEvent(function () {\n    if (popupEle && target && open) {\n      var _popupElement$parentE, _popupElement$parentE2;\n      var popupElement = popupEle;\n      var doc = popupElement.ownerDocument;\n      var win = getWin(popupElement);\n      var _win$getComputedStyle = win.getComputedStyle(popupElement),\n        width = _win$getComputedStyle.width,\n        height = _win$getComputedStyle.height,\n        popupPosition = _win$getComputedStyle.position;\n      var originLeft = popupElement.style.left;\n      var originTop = popupElement.style.top;\n      var originRight = popupElement.style.right;\n      var originBottom = popupElement.style.bottom;\n\n      // Placement\n      var placementInfo = _objectSpread(_objectSpread({}, builtinPlacements[placement]), popupAlign);\n\n      // placeholder element\n      var placeholderElement = doc.createElement('div');\n      (_popupElement$parentE = popupElement.parentElement) === null || _popupElement$parentE === void 0 ? void 0 : _popupElement$parentE.appendChild(placeholderElement);\n      placeholderElement.style.left = \"\".concat(popupElement.offsetLeft, \"px\");\n      placeholderElement.style.top = \"\".concat(popupElement.offsetTop, \"px\");\n      placeholderElement.style.position = popupPosition;\n      placeholderElement.style.height = \"\".concat(popupElement.offsetHeight, \"px\");\n      placeholderElement.style.width = \"\".concat(popupElement.offsetWidth, \"px\");\n\n      // Reset first\n      popupElement.style.left = '0';\n      popupElement.style.top = '0';\n      popupElement.style.right = 'auto';\n      popupElement.style.bottom = 'auto';\n\n      // Calculate align style, we should consider `transform` case\n      var targetRect;\n      if (Array.isArray(target)) {\n        targetRect = {\n          x: target[0],\n          y: target[1],\n          width: 0,\n          height: 0\n        };\n      } else {\n        var rect = target.getBoundingClientRect();\n        targetRect = {\n          x: rect.x,\n          y: rect.y,\n          width: rect.width,\n          height: rect.height\n        };\n      }\n      var popupRect = popupElement.getBoundingClientRect();\n      var _doc$documentElement = doc.documentElement,\n        clientWidth = _doc$documentElement.clientWidth,\n        clientHeight = _doc$documentElement.clientHeight,\n        scrollWidth = _doc$documentElement.scrollWidth,\n        scrollHeight = _doc$documentElement.scrollHeight,\n        scrollTop = _doc$documentElement.scrollTop,\n        scrollLeft = _doc$documentElement.scrollLeft;\n      var popupHeight = popupRect.height;\n      var popupWidth = popupRect.width;\n      var targetHeight = targetRect.height;\n      var targetWidth = targetRect.width;\n\n      // Get bounding of visible area\n      var visibleRegion = {\n        left: 0,\n        top: 0,\n        right: clientWidth,\n        bottom: clientHeight\n      };\n      var scrollRegion = {\n        left: -scrollLeft,\n        top: -scrollTop,\n        right: scrollWidth - scrollLeft,\n        bottom: scrollHeight - scrollTop\n      };\n      var htmlRegion = placementInfo.htmlRegion;\n      var VISIBLE = 'visible';\n      var VISIBLE_FIRST = 'visibleFirst';\n      if (htmlRegion !== 'scroll' && htmlRegion !== VISIBLE_FIRST) {\n        htmlRegion = VISIBLE;\n      }\n      var isVisibleFirst = htmlRegion === VISIBLE_FIRST;\n      var scrollRegionArea = getVisibleArea(scrollRegion, scrollerList);\n      var visibleRegionArea = getVisibleArea(visibleRegion, scrollerList);\n      var visibleArea = htmlRegion === VISIBLE ? visibleRegionArea : scrollRegionArea;\n\n      // When set to `visibleFirst`,\n      // the check `adjust` logic will use `visibleRegion` for check first.\n      var adjustCheckVisibleArea = isVisibleFirst ? visibleRegionArea : visibleArea;\n\n      // Record right & bottom align data\n      popupElement.style.left = 'auto';\n      popupElement.style.top = 'auto';\n      popupElement.style.right = '0';\n      popupElement.style.bottom = '0';\n      var popupMirrorRect = popupElement.getBoundingClientRect();\n\n      // Reset back\n      popupElement.style.left = originLeft;\n      popupElement.style.top = originTop;\n      popupElement.style.right = originRight;\n      popupElement.style.bottom = originBottom;\n      (_popupElement$parentE2 = popupElement.parentElement) === null || _popupElement$parentE2 === void 0 ? void 0 : _popupElement$parentE2.removeChild(placeholderElement);\n\n      // Calculate scale\n      var _scaleX = toNum(Math.round(popupWidth / parseFloat(width) * 1000) / 1000);\n      var _scaleY = toNum(Math.round(popupHeight / parseFloat(height) * 1000) / 1000);\n\n      // No need to align since it's not visible in view\n      if (_scaleX === 0 || _scaleY === 0 || isDOM(target) && !isVisible(target)) {\n        return;\n      }\n\n      // Offset\n      var offset = placementInfo.offset,\n        targetOffset = placementInfo.targetOffset;\n      var _getNumberOffset = getNumberOffset(popupRect, offset),\n        _getNumberOffset2 = _slicedToArray(_getNumberOffset, 2),\n        popupOffsetX = _getNumberOffset2[0],\n        popupOffsetY = _getNumberOffset2[1];\n      var _getNumberOffset3 = getNumberOffset(targetRect, targetOffset),\n        _getNumberOffset4 = _slicedToArray(_getNumberOffset3, 2),\n        targetOffsetX = _getNumberOffset4[0],\n        targetOffsetY = _getNumberOffset4[1];\n      targetRect.x -= targetOffsetX;\n      targetRect.y -= targetOffsetY;\n\n      // Points\n      var _ref3 = placementInfo.points || [],\n        _ref4 = _slicedToArray(_ref3, 2),\n        popupPoint = _ref4[0],\n        targetPoint = _ref4[1];\n      var targetPoints = splitPoints(targetPoint);\n      var popupPoints = splitPoints(popupPoint);\n      var targetAlignPoint = getAlignPoint(targetRect, targetPoints);\n      var popupAlignPoint = getAlignPoint(popupRect, popupPoints);\n\n      // Real align info may not same as origin one\n      var nextAlignInfo = _objectSpread({}, placementInfo);\n\n      // Next Offset\n      var nextOffsetX = targetAlignPoint.x - popupAlignPoint.x + popupOffsetX;\n      var nextOffsetY = targetAlignPoint.y - popupAlignPoint.y + popupOffsetY;\n\n      // ============== Intersection ===============\n      // Get area by position. Used for check if flip area is better\n      function getIntersectionVisibleArea(offsetX, offsetY) {\n        var area = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : visibleArea;\n        var l = popupRect.x + offsetX;\n        var t = popupRect.y + offsetY;\n        var r = l + popupWidth;\n        var b = t + popupHeight;\n        var visibleL = Math.max(l, area.left);\n        var visibleT = Math.max(t, area.top);\n        var visibleR = Math.min(r, area.right);\n        var visibleB = Math.min(b, area.bottom);\n        return Math.max(0, (visibleR - visibleL) * (visibleB - visibleT));\n      }\n      var originIntersectionVisibleArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY);\n\n      // As `visibleFirst`, we prepare this for check\n      var originIntersectionRecommendArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY, visibleRegionArea);\n\n      // ========================== Overflow ===========================\n      var targetAlignPointTL = getAlignPoint(targetRect, ['t', 'l']);\n      var popupAlignPointTL = getAlignPoint(popupRect, ['t', 'l']);\n      var targetAlignPointBR = getAlignPoint(targetRect, ['b', 'r']);\n      var popupAlignPointBR = getAlignPoint(popupRect, ['b', 'r']);\n      var overflow = placementInfo.overflow || {};\n      var adjustX = overflow.adjustX,\n        adjustY = overflow.adjustY,\n        shiftX = overflow.shiftX,\n        shiftY = overflow.shiftY;\n      var supportAdjust = function supportAdjust(val) {\n        if (typeof val === 'boolean') {\n          return val;\n        }\n        return val >= 0;\n      };\n\n      // Prepare position\n      var nextPopupY;\n      var nextPopupBottom;\n      var nextPopupX;\n      var nextPopupRight;\n      function syncNextPopupPosition() {\n        nextPopupY = popupRect.y + nextOffsetY;\n        nextPopupBottom = nextPopupY + popupHeight;\n        nextPopupX = popupRect.x + nextOffsetX;\n        nextPopupRight = nextPopupX + popupWidth;\n      }\n      syncNextPopupPosition();\n\n      // >>>>>>>>>> Top & Bottom\n      var needAdjustY = supportAdjust(adjustY);\n      var sameTB = popupPoints[0] === targetPoints[0];\n\n      // Bottom to Top\n      if (needAdjustY && popupPoints[0] === 't' && (nextPopupBottom > adjustCheckVisibleArea.bottom || prevFlipRef.current.bt)) {\n        var tmpNextOffsetY = nextOffsetY;\n        if (sameTB) {\n          tmpNextOffsetY -= popupHeight - targetHeight;\n        } else {\n          tmpNextOffsetY = targetAlignPointTL.y - popupAlignPointBR.y - popupOffsetY;\n        }\n        var newVisibleArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY);\n        var newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        newVisibleArea > originIntersectionVisibleArea || newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.bt = true;\n          nextOffsetY = tmpNextOffsetY;\n          popupOffsetY = -popupOffsetY;\n          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];\n        } else {\n          prevFlipRef.current.bt = false;\n        }\n      }\n\n      // Top to Bottom\n      if (needAdjustY && popupPoints[0] === 'b' && (nextPopupY < adjustCheckVisibleArea.top || prevFlipRef.current.tb)) {\n        var _tmpNextOffsetY = nextOffsetY;\n        if (sameTB) {\n          _tmpNextOffsetY += popupHeight - targetHeight;\n        } else {\n          _tmpNextOffsetY = targetAlignPointBR.y - popupAlignPointTL.y - popupOffsetY;\n        }\n        var _newVisibleArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY);\n        var _newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea > originIntersectionVisibleArea || _newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.tb = true;\n          nextOffsetY = _tmpNextOffsetY;\n          popupOffsetY = -popupOffsetY;\n          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];\n        } else {\n          prevFlipRef.current.tb = false;\n        }\n      }\n\n      // >>>>>>>>>> Left & Right\n      var needAdjustX = supportAdjust(adjustX);\n\n      // >>>>> Flip\n      var sameLR = popupPoints[1] === targetPoints[1];\n\n      // Right to Left\n      if (needAdjustX && popupPoints[1] === 'l' && (nextPopupRight > adjustCheckVisibleArea.right || prevFlipRef.current.rl)) {\n        var tmpNextOffsetX = nextOffsetX;\n        if (sameLR) {\n          tmpNextOffsetX -= popupWidth - targetWidth;\n        } else {\n          tmpNextOffsetX = targetAlignPointTL.x - popupAlignPointBR.x - popupOffsetX;\n        }\n        var _newVisibleArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY);\n        var _newVisibleRecommendArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea2 > originIntersectionVisibleArea || _newVisibleArea2 === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea2 >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.rl = true;\n          nextOffsetX = tmpNextOffsetX;\n          popupOffsetX = -popupOffsetX;\n          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];\n        } else {\n          prevFlipRef.current.rl = false;\n        }\n      }\n\n      // Left to Right\n      if (needAdjustX && popupPoints[1] === 'r' && (nextPopupX < adjustCheckVisibleArea.left || prevFlipRef.current.lr)) {\n        var _tmpNextOffsetX = nextOffsetX;\n        if (sameLR) {\n          _tmpNextOffsetX += popupWidth - targetWidth;\n        } else {\n          _tmpNextOffsetX = targetAlignPointBR.x - popupAlignPointTL.x - popupOffsetX;\n        }\n        var _newVisibleArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY);\n        var _newVisibleRecommendArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea3 > originIntersectionVisibleArea || _newVisibleArea3 === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea3 >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.lr = true;\n          nextOffsetX = _tmpNextOffsetX;\n          popupOffsetX = -popupOffsetX;\n          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];\n        } else {\n          prevFlipRef.current.lr = false;\n        }\n      }\n\n      // ============================ Shift ============================\n      syncNextPopupPosition();\n      var numShiftX = shiftX === true ? 0 : shiftX;\n      if (typeof numShiftX === 'number') {\n        // Left\n        if (nextPopupX < visibleRegionArea.left) {\n          nextOffsetX -= nextPopupX - visibleRegionArea.left - popupOffsetX;\n          if (targetRect.x + targetWidth < visibleRegionArea.left + numShiftX) {\n            nextOffsetX += targetRect.x - visibleRegionArea.left + targetWidth - numShiftX;\n          }\n        }\n\n        // Right\n        if (nextPopupRight > visibleRegionArea.right) {\n          nextOffsetX -= nextPopupRight - visibleRegionArea.right - popupOffsetX;\n          if (targetRect.x > visibleRegionArea.right - numShiftX) {\n            nextOffsetX += targetRect.x - visibleRegionArea.right + numShiftX;\n          }\n        }\n      }\n      var numShiftY = shiftY === true ? 0 : shiftY;\n      if (typeof numShiftY === 'number') {\n        // Top\n        if (nextPopupY < visibleRegionArea.top) {\n          nextOffsetY -= nextPopupY - visibleRegionArea.top - popupOffsetY;\n\n          // When target if far away from visible area\n          // Stop shift\n          if (targetRect.y + targetHeight < visibleRegionArea.top + numShiftY) {\n            nextOffsetY += targetRect.y - visibleRegionArea.top + targetHeight - numShiftY;\n          }\n        }\n\n        // Bottom\n        if (nextPopupBottom > visibleRegionArea.bottom) {\n          nextOffsetY -= nextPopupBottom - visibleRegionArea.bottom - popupOffsetY;\n          if (targetRect.y > visibleRegionArea.bottom - numShiftY) {\n            nextOffsetY += targetRect.y - visibleRegionArea.bottom + numShiftY;\n          }\n        }\n      }\n\n      // ============================ Arrow ============================\n      // Arrow center align\n      var popupLeft = popupRect.x + nextOffsetX;\n      var popupRight = popupLeft + popupWidth;\n      var popupTop = popupRect.y + nextOffsetY;\n      var popupBottom = popupTop + popupHeight;\n      var targetLeft = targetRect.x;\n      var targetRight = targetLeft + targetWidth;\n      var targetTop = targetRect.y;\n      var targetBottom = targetTop + targetHeight;\n      var maxLeft = Math.max(popupLeft, targetLeft);\n      var minRight = Math.min(popupRight, targetRight);\n      var xCenter = (maxLeft + minRight) / 2;\n      var nextArrowX = xCenter - popupLeft;\n      var maxTop = Math.max(popupTop, targetTop);\n      var minBottom = Math.min(popupBottom, targetBottom);\n      var yCenter = (maxTop + minBottom) / 2;\n      var nextArrowY = yCenter - popupTop;\n      onPopupAlign === null || onPopupAlign === void 0 ? void 0 : onPopupAlign(popupEle, nextAlignInfo);\n\n      // Additional calculate right & bottom position\n      var offsetX4Right = popupMirrorRect.right - popupRect.x - (nextOffsetX + popupRect.width);\n      var offsetY4Bottom = popupMirrorRect.bottom - popupRect.y - (nextOffsetY + popupRect.height);\n      setOffsetInfo({\n        ready: true,\n        offsetX: nextOffsetX / _scaleX,\n        offsetY: nextOffsetY / _scaleY,\n        offsetR: offsetX4Right / _scaleX,\n        offsetB: offsetY4Bottom / _scaleY,\n        arrowX: nextArrowX / _scaleX,\n        arrowY: nextArrowY / _scaleY,\n        scaleX: _scaleX,\n        scaleY: _scaleY,\n        align: nextAlignInfo\n      });\n    }\n  });\n  var triggerAlign = function triggerAlign() {\n    alignCountRef.current += 1;\n    var id = alignCountRef.current;\n\n    // Merge all align requirement into one frame\n    Promise.resolve().then(function () {\n      if (alignCountRef.current === id) {\n        onAlign();\n      }\n    });\n  };\n\n  // Reset ready status when placement & open changed\n  var resetReady = function resetReady() {\n    setOffsetInfo(function (ori) {\n      return _objectSpread(_objectSpread({}, ori), {}, {\n        ready: false\n      });\n    });\n  };\n  useLayoutEffect(resetReady, [placement]);\n  useLayoutEffect(function () {\n    if (!open) {\n      resetReady();\n    }\n  }, [open]);\n  return [offsetInfo.ready, offsetInfo.offsetX, offsetInfo.offsetY, offsetInfo.offsetR, offsetInfo.offsetB, offsetInfo.arrowX, offsetInfo.arrowY, offsetInfo.scaleX, offsetInfo.scaleY, offsetInfo.align, triggerAlign];\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,KAAK,QAAQ,4BAA4B;AAClD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,EAAEC,cAAc,EAAEC,MAAM,EAAEC,KAAK,QAAQ,SAAS;AACxE,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAClF,IAAIG,SAAS,GAAG,EAAE,CAACC,MAAM,CAACL,MAAM,CAAC;EACjC,IAAIM,KAAK,GAAGF,SAAS,CAACG,KAAK,CAAC,UAAU,CAAC;EACvC,IAAID,KAAK,EAAE;IACT,OAAOP,IAAI,IAAIS,UAAU,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;EAC5C;EACA,OAAOE,UAAU,CAACJ,SAAS,CAAC;AAC9B;AACA,SAASK,eAAeA,CAACC,IAAI,EAAEV,MAAM,EAAE;EACrC,IAAIW,IAAI,GAAGX,MAAM,IAAI,EAAE;IACrBY,KAAK,GAAGxB,cAAc,CAACuB,IAAI,EAAE,CAAC,CAAC;IAC/BE,OAAO,GAAGD,KAAK,CAAC,CAAC,CAAC;IAClBE,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC;EACpB,OAAO,CAACd,aAAa,CAACY,IAAI,CAACK,KAAK,EAAEF,OAAO,CAAC,EAAEf,aAAa,CAACY,IAAI,CAACM,MAAM,EAAEF,OAAO,CAAC,CAAC;AAClF;AACA,SAASG,WAAWA,CAAA,EAAG;EACrB,IAAIC,MAAM,GAAGjB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACnF,OAAO,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA,SAASC,aAAaA,CAACT,IAAI,EAAEQ,MAAM,EAAE;EACnC,IAAIE,SAAS,GAAGF,MAAM,CAAC,CAAC,CAAC;EACzB,IAAIG,SAAS,GAAGH,MAAM,CAAC,CAAC,CAAC;EACzB,IAAII,CAAC;EACL,IAAIC,CAAC;;EAEL;EACA,IAAIH,SAAS,KAAK,GAAG,EAAE;IACrBG,CAAC,GAAGb,IAAI,CAACa,CAAC;EACZ,CAAC,MAAM,IAAIH,SAAS,KAAK,GAAG,EAAE;IAC5BG,CAAC,GAAGb,IAAI,CAACa,CAAC,GAAGb,IAAI,CAACM,MAAM;EAC1B,CAAC,MAAM;IACLO,CAAC,GAAGb,IAAI,CAACa,CAAC,GAAGb,IAAI,CAACM,MAAM,GAAG,CAAC;EAC9B;;EAEA;EACA,IAAIK,SAAS,KAAK,GAAG,EAAE;IACrBC,CAAC,GAAGZ,IAAI,CAACY,CAAC;EACZ,CAAC,MAAM,IAAID,SAAS,KAAK,GAAG,EAAE;IAC5BC,CAAC,GAAGZ,IAAI,CAACY,CAAC,GAAGZ,IAAI,CAACK,KAAK;EACzB,CAAC,MAAM;IACLO,CAAC,GAAGZ,IAAI,CAACY,CAAC,GAAGZ,IAAI,CAACK,KAAK,GAAG,CAAC;EAC7B;EACA,OAAO;IACLO,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA;EACL,CAAC;AACH;AACA,SAASC,aAAaA,CAACN,MAAM,EAAEO,KAAK,EAAE;EACpC,IAAIC,UAAU,GAAG;IACfC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE;EACL,CAAC;EACD,OAAOZ,MAAM,CAACa,GAAG,CAAC,UAAUC,KAAK,EAAEC,CAAC,EAAE;IACpC,IAAIA,CAAC,KAAKR,KAAK,EAAE;MACf,OAAOC,UAAU,CAACM,KAAK,CAAC,IAAI,GAAG;IACjC;IACA,OAAOA,KAAK;EACd,CAAC,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AACb;AACA,eAAe,SAASC,QAAQA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,YAAY,EAAE;EAC/G,IAAIC,eAAe,GAAGlD,KAAK,CAACmD,QAAQ,CAAC;MACjCC,KAAK,EAAE,KAAK;MACZhC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVgC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,KAAK,EAAEZ,iBAAiB,CAACD,SAAS,CAAC,IAAI,CAAC;IAC1C,CAAC,CAAC;IACFc,gBAAgB,GAAGjE,cAAc,CAACuD,eAAe,EAAE,CAAC,CAAC;IACrDW,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,aAAa,GAAG/D,KAAK,CAACgE,MAAM,CAAC,CAAC,CAAC;EACnC,IAAIC,YAAY,GAAGjE,KAAK,CAACkE,OAAO,CAAC,YAAY;IAC3C,IAAI,CAACtB,QAAQ,EAAE;MACb,OAAO,EAAE;IACX;IACA,OAAO3C,eAAe,CAAC2C,QAAQ,CAAC;EAClC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA;EACA;EACA,IAAIuB,WAAW,GAAGnE,KAAK,CAACgE,MAAM,CAAC,CAAC,CAAC,CAAC;EAClC,IAAII,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7CD,WAAW,CAACE,OAAO,GAAG,CAAC,CAAC;EAC1B,CAAC;EACD,IAAI,CAAC1B,IAAI,EAAE;IACTyB,cAAc,CAAC,CAAC;EAClB;;EAEA;EACA,IAAIE,OAAO,GAAGxE,QAAQ,CAAC,YAAY;IACjC,IAAI8C,QAAQ,IAAIC,MAAM,IAAIF,IAAI,EAAE;MAC9B,IAAI4B,qBAAqB,EAAEC,sBAAsB;MACjD,IAAIC,YAAY,GAAG7B,QAAQ;MAC3B,IAAI8B,GAAG,GAAGD,YAAY,CAACE,aAAa;MACpC,IAAIC,GAAG,GAAGzE,MAAM,CAACsE,YAAY,CAAC;MAC9B,IAAII,qBAAqB,GAAGD,GAAG,CAACE,gBAAgB,CAACL,YAAY,CAAC;QAC5DnD,KAAK,GAAGuD,qBAAqB,CAACvD,KAAK;QACnCC,MAAM,GAAGsD,qBAAqB,CAACtD,MAAM;QACrCwD,aAAa,GAAGF,qBAAqB,CAACG,QAAQ;MAChD,IAAIC,UAAU,GAAGR,YAAY,CAACS,KAAK,CAACC,IAAI;MACxC,IAAIC,SAAS,GAAGX,YAAY,CAACS,KAAK,CAACG,GAAG;MACtC,IAAIC,WAAW,GAAGb,YAAY,CAACS,KAAK,CAACK,KAAK;MAC1C,IAAIC,YAAY,GAAGf,YAAY,CAACS,KAAK,CAACO,MAAM;;MAE5C;MACA,IAAIC,aAAa,GAAGhG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqD,iBAAiB,CAACD,SAAS,CAAC,CAAC,EAAEE,UAAU,CAAC;;MAE9F;MACA,IAAI2C,kBAAkB,GAAGjB,GAAG,CAACkB,aAAa,CAAC,KAAK,CAAC;MACjD,CAACrB,qBAAqB,GAAGE,YAAY,CAACoB,aAAa,MAAM,IAAI,IAAItB,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACuB,WAAW,CAACH,kBAAkB,CAAC;MAClKA,kBAAkB,CAACT,KAAK,CAACC,IAAI,GAAG,EAAE,CAACvE,MAAM,CAAC6D,YAAY,CAACsB,UAAU,EAAE,IAAI,CAAC;MACxEJ,kBAAkB,CAACT,KAAK,CAACG,GAAG,GAAG,EAAE,CAACzE,MAAM,CAAC6D,YAAY,CAACuB,SAAS,EAAE,IAAI,CAAC;MACtEL,kBAAkB,CAACT,KAAK,CAACF,QAAQ,GAAGD,aAAa;MACjDY,kBAAkB,CAACT,KAAK,CAAC3D,MAAM,GAAG,EAAE,CAACX,MAAM,CAAC6D,YAAY,CAACwB,YAAY,EAAE,IAAI,CAAC;MAC5EN,kBAAkB,CAACT,KAAK,CAAC5D,KAAK,GAAG,EAAE,CAACV,MAAM,CAAC6D,YAAY,CAACyB,WAAW,EAAE,IAAI,CAAC;;MAE1E;MACAzB,YAAY,CAACS,KAAK,CAACC,IAAI,GAAG,GAAG;MAC7BV,YAAY,CAACS,KAAK,CAACG,GAAG,GAAG,GAAG;MAC5BZ,YAAY,CAACS,KAAK,CAACK,KAAK,GAAG,MAAM;MACjCd,YAAY,CAACS,KAAK,CAACO,MAAM,GAAG,MAAM;;MAElC;MACA,IAAIU,UAAU;MACd,IAAIC,KAAK,CAACC,OAAO,CAACxD,MAAM,CAAC,EAAE;QACzBsD,UAAU,GAAG;UACXtE,CAAC,EAAEgB,MAAM,CAAC,CAAC,CAAC;UACZf,CAAC,EAAEe,MAAM,CAAC,CAAC,CAAC;UACZvB,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,MAAM;QACL,IAAIN,IAAI,GAAG4B,MAAM,CAACyD,qBAAqB,CAAC,CAAC;QACzCH,UAAU,GAAG;UACXtE,CAAC,EAAEZ,IAAI,CAACY,CAAC;UACTC,CAAC,EAAEb,IAAI,CAACa,CAAC;UACTR,KAAK,EAAEL,IAAI,CAACK,KAAK;UACjBC,MAAM,EAAEN,IAAI,CAACM;QACf,CAAC;MACH;MACA,IAAIgF,SAAS,GAAG9B,YAAY,CAAC6B,qBAAqB,CAAC,CAAC;MACpD,IAAIE,oBAAoB,GAAG9B,GAAG,CAAC+B,eAAe;QAC5CC,WAAW,GAAGF,oBAAoB,CAACE,WAAW;QAC9CC,YAAY,GAAGH,oBAAoB,CAACG,YAAY;QAChDC,WAAW,GAAGJ,oBAAoB,CAACI,WAAW;QAC9CC,YAAY,GAAGL,oBAAoB,CAACK,YAAY;QAChDC,SAAS,GAAGN,oBAAoB,CAACM,SAAS;QAC1CC,UAAU,GAAGP,oBAAoB,CAACO,UAAU;MAC9C,IAAIC,WAAW,GAAGT,SAAS,CAAChF,MAAM;MAClC,IAAI0F,UAAU,GAAGV,SAAS,CAACjF,KAAK;MAChC,IAAI4F,YAAY,GAAGf,UAAU,CAAC5E,MAAM;MACpC,IAAI4F,WAAW,GAAGhB,UAAU,CAAC7E,KAAK;;MAElC;MACA,IAAI8F,aAAa,GAAG;QAClBjC,IAAI,EAAE,CAAC;QACPE,GAAG,EAAE,CAAC;QACNE,KAAK,EAAEmB,WAAW;QAClBjB,MAAM,EAAEkB;MACV,CAAC;MACD,IAAIU,YAAY,GAAG;QACjBlC,IAAI,EAAE,CAAC4B,UAAU;QACjB1B,GAAG,EAAE,CAACyB,SAAS;QACfvB,KAAK,EAAEqB,WAAW,GAAGG,UAAU;QAC/BtB,MAAM,EAAEoB,YAAY,GAAGC;MACzB,CAAC;MACD,IAAIQ,UAAU,GAAG5B,aAAa,CAAC4B,UAAU;MACzC,IAAIC,OAAO,GAAG,SAAS;MACvB,IAAIC,aAAa,GAAG,cAAc;MAClC,IAAIF,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAKE,aAAa,EAAE;QAC3DF,UAAU,GAAGC,OAAO;MACtB;MACA,IAAIE,cAAc,GAAGH,UAAU,KAAKE,aAAa;MACjD,IAAIE,gBAAgB,GAAGxH,cAAc,CAACmH,YAAY,EAAEpD,YAAY,CAAC;MACjE,IAAI0D,iBAAiB,GAAGzH,cAAc,CAACkH,aAAa,EAAEnD,YAAY,CAAC;MACnE,IAAI2D,WAAW,GAAGN,UAAU,KAAKC,OAAO,GAAGI,iBAAiB,GAAGD,gBAAgB;;MAE/E;MACA;MACA,IAAIG,sBAAsB,GAAGJ,cAAc,GAAGE,iBAAiB,GAAGC,WAAW;;MAE7E;MACAnD,YAAY,CAACS,KAAK,CAACC,IAAI,GAAG,MAAM;MAChCV,YAAY,CAACS,KAAK,CAACG,GAAG,GAAG,MAAM;MAC/BZ,YAAY,CAACS,KAAK,CAACK,KAAK,GAAG,GAAG;MAC9Bd,YAAY,CAACS,KAAK,CAACO,MAAM,GAAG,GAAG;MAC/B,IAAIqC,eAAe,GAAGrD,YAAY,CAAC6B,qBAAqB,CAAC,CAAC;;MAE1D;MACA7B,YAAY,CAACS,KAAK,CAACC,IAAI,GAAGF,UAAU;MACpCR,YAAY,CAACS,KAAK,CAACG,GAAG,GAAGD,SAAS;MAClCX,YAAY,CAACS,KAAK,CAACK,KAAK,GAAGD,WAAW;MACtCb,YAAY,CAACS,KAAK,CAACO,MAAM,GAAGD,YAAY;MACxC,CAAChB,sBAAsB,GAAGC,YAAY,CAACoB,aAAa,MAAM,IAAI,IAAIrB,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACuD,WAAW,CAACpC,kBAAkB,CAAC;;MAErK;MACA,IAAIqC,OAAO,GAAG5H,KAAK,CAAC6H,IAAI,CAACC,KAAK,CAACjB,UAAU,GAAGlG,UAAU,CAACO,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;MAC7E,IAAI6G,OAAO,GAAG/H,KAAK,CAAC6H,IAAI,CAACC,KAAK,CAAClB,WAAW,GAAGjG,UAAU,CAACQ,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;;MAE/E;MACA,IAAIyG,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,IAAIvI,KAAK,CAACiD,MAAM,CAAC,IAAI,CAAChD,SAAS,CAACgD,MAAM,CAAC,EAAE;QACzE;MACF;;MAEA;MACA,IAAItC,MAAM,GAAGmF,aAAa,CAACnF,MAAM;QAC/B6H,YAAY,GAAG1C,aAAa,CAAC0C,YAAY;MAC3C,IAAIC,gBAAgB,GAAGrH,eAAe,CAACuF,SAAS,EAAEhG,MAAM,CAAC;QACvD+H,iBAAiB,GAAG3I,cAAc,CAAC0I,gBAAgB,EAAE,CAAC,CAAC;QACvDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;QACnCE,YAAY,GAAGF,iBAAiB,CAAC,CAAC,CAAC;MACrC,IAAIG,iBAAiB,GAAGzH,eAAe,CAACmF,UAAU,EAAEiC,YAAY,CAAC;QAC/DM,iBAAiB,GAAG/I,cAAc,CAAC8I,iBAAiB,EAAE,CAAC,CAAC;QACxDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;QACpCE,aAAa,GAAGF,iBAAiB,CAAC,CAAC,CAAC;MACtCvC,UAAU,CAACtE,CAAC,IAAI8G,aAAa;MAC7BxC,UAAU,CAACrE,CAAC,IAAI8G,aAAa;;MAE7B;MACA,IAAIC,KAAK,GAAGnD,aAAa,CAACjE,MAAM,IAAI,EAAE;QACpCqH,KAAK,GAAGnJ,cAAc,CAACkJ,KAAK,EAAE,CAAC,CAAC;QAChCE,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC;QACrBE,WAAW,GAAGF,KAAK,CAAC,CAAC,CAAC;MACxB,IAAIG,YAAY,GAAGzH,WAAW,CAACwH,WAAW,CAAC;MAC3C,IAAIE,WAAW,GAAG1H,WAAW,CAACuH,UAAU,CAAC;MACzC,IAAII,gBAAgB,GAAGzH,aAAa,CAACyE,UAAU,EAAE8C,YAAY,CAAC;MAC9D,IAAIG,eAAe,GAAG1H,aAAa,CAAC6E,SAAS,EAAE2C,WAAW,CAAC;;MAE3D;MACA,IAAIG,aAAa,GAAG3J,aAAa,CAAC,CAAC,CAAC,EAAEgG,aAAa,CAAC;;MAEpD;MACA,IAAI4D,WAAW,GAAGH,gBAAgB,CAACtH,CAAC,GAAGuH,eAAe,CAACvH,CAAC,GAAG0G,YAAY;MACvE,IAAIgB,WAAW,GAAGJ,gBAAgB,CAACrH,CAAC,GAAGsH,eAAe,CAACtH,CAAC,GAAG0G,YAAY;;MAEvE;MACA;MACA,SAASgB,0BAA0BA,CAACpI,OAAO,EAAEC,OAAO,EAAE;QACpD,IAAIoI,IAAI,GAAGjJ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGoH,WAAW;QAC1F,IAAIxF,CAAC,GAAGmE,SAAS,CAAC1E,CAAC,GAAGT,OAAO;QAC7B,IAAIc,CAAC,GAAGqE,SAAS,CAACzE,CAAC,GAAGT,OAAO;QAC7B,IAAIgB,CAAC,GAAGD,CAAC,GAAG6E,UAAU;QACtB,IAAI9E,CAAC,GAAGD,CAAC,GAAG8E,WAAW;QACvB,IAAI0C,QAAQ,GAAGzB,IAAI,CAAC0B,GAAG,CAACvH,CAAC,EAAEqH,IAAI,CAACtE,IAAI,CAAC;QACrC,IAAIyE,QAAQ,GAAG3B,IAAI,CAAC0B,GAAG,CAACzH,CAAC,EAAEuH,IAAI,CAACpE,GAAG,CAAC;QACpC,IAAIwE,QAAQ,GAAG5B,IAAI,CAAC6B,GAAG,CAACzH,CAAC,EAAEoH,IAAI,CAAClE,KAAK,CAAC;QACtC,IAAIwE,QAAQ,GAAG9B,IAAI,CAAC6B,GAAG,CAAC3H,CAAC,EAAEsH,IAAI,CAAChE,MAAM,CAAC;QACvC,OAAOwC,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE,CAACE,QAAQ,GAAGH,QAAQ,KAAKK,QAAQ,GAAGH,QAAQ,CAAC,CAAC;MACnE;MACA,IAAII,6BAA6B,GAAGR,0BAA0B,CAACF,WAAW,EAAEC,WAAW,CAAC;;MAExF;MACA,IAAIU,+BAA+B,GAAGT,0BAA0B,CAACF,WAAW,EAAEC,WAAW,EAAE5B,iBAAiB,CAAC;;MAE7G;MACA,IAAIuC,kBAAkB,GAAGxI,aAAa,CAACyE,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC9D,IAAIgE,iBAAiB,GAAGzI,aAAa,CAAC6E,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC5D,IAAI6D,kBAAkB,GAAG1I,aAAa,CAACyE,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC9D,IAAIkE,iBAAiB,GAAG3I,aAAa,CAAC6E,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC5D,IAAI+D,QAAQ,GAAG5E,aAAa,CAAC4E,QAAQ,IAAI,CAAC,CAAC;MAC3C,IAAIC,OAAO,GAAGD,QAAQ,CAACC,OAAO;QAC5BC,OAAO,GAAGF,QAAQ,CAACE,OAAO;QAC1BC,MAAM,GAAGH,QAAQ,CAACG,MAAM;QACxBC,MAAM,GAAGJ,QAAQ,CAACI,MAAM;MAC1B,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,GAAG,EAAE;QAC9C,IAAI,OAAOA,GAAG,KAAK,SAAS,EAAE;UAC5B,OAAOA,GAAG;QACZ;QACA,OAAOA,GAAG,IAAI,CAAC;MACjB,CAAC;;MAED;MACA,IAAIC,UAAU;MACd,IAAIC,eAAe;MACnB,IAAIC,UAAU;MACd,IAAIC,cAAc;MAClB,SAASC,qBAAqBA,CAAA,EAAG;QAC/BJ,UAAU,GAAGtE,SAAS,CAACzE,CAAC,GAAGyH,WAAW;QACtCuB,eAAe,GAAGD,UAAU,GAAG7D,WAAW;QAC1C+D,UAAU,GAAGxE,SAAS,CAAC1E,CAAC,GAAGyH,WAAW;QACtC0B,cAAc,GAAGD,UAAU,GAAG9D,UAAU;MAC1C;MACAgE,qBAAqB,CAAC,CAAC;;MAEvB;MACA,IAAIC,WAAW,GAAGP,aAAa,CAACH,OAAO,CAAC;MACxC,IAAIW,MAAM,GAAGjC,WAAW,CAAC,CAAC,CAAC,KAAKD,YAAY,CAAC,CAAC,CAAC;;MAE/C;MACA,IAAIiC,WAAW,IAAIhC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK4B,eAAe,GAAGjD,sBAAsB,CAACpC,MAAM,IAAItB,WAAW,CAACE,OAAO,CAAC+G,EAAE,CAAC,EAAE;QACxH,IAAIC,cAAc,GAAG9B,WAAW;QAChC,IAAI4B,MAAM,EAAE;UACVE,cAAc,IAAIrE,WAAW,GAAGE,YAAY;QAC9C,CAAC,MAAM;UACLmE,cAAc,GAAGnB,kBAAkB,CAACpI,CAAC,GAAGuI,iBAAiB,CAACvI,CAAC,GAAG0G,YAAY;QAC5E;QACA,IAAI8C,cAAc,GAAG9B,0BAA0B,CAACF,WAAW,EAAE+B,cAAc,CAAC;QAC5E,IAAIE,uBAAuB,GAAG/B,0BAA0B,CAACF,WAAW,EAAE+B,cAAc,EAAE1D,iBAAiB,CAAC;QACxG;QACA;QACA2D,cAAc,GAAGtB,6BAA6B,IAAIsB,cAAc,KAAKtB,6BAA6B,KAAK,CAACvC,cAAc;QACtH;QACA8D,uBAAuB,IAAItB,+BAA+B,CAAC,EAAE;UAC3D9F,WAAW,CAACE,OAAO,CAAC+G,EAAE,GAAG,IAAI;UAC7B7B,WAAW,GAAG8B,cAAc;UAC5B7C,YAAY,GAAG,CAACA,YAAY;UAC5Ba,aAAa,CAAC5H,MAAM,GAAG,CAACM,aAAa,CAACmH,WAAW,EAAE,CAAC,CAAC,EAAEnH,aAAa,CAACkH,YAAY,EAAE,CAAC,CAAC,CAAC;QACxF,CAAC,MAAM;UACL9E,WAAW,CAACE,OAAO,CAAC+G,EAAE,GAAG,KAAK;QAChC;MACF;;MAEA;MACA,IAAIF,WAAW,IAAIhC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK2B,UAAU,GAAGhD,sBAAsB,CAACxC,GAAG,IAAIlB,WAAW,CAACE,OAAO,CAACmH,EAAE,CAAC,EAAE;QAChH,IAAIC,eAAe,GAAGlC,WAAW;QACjC,IAAI4B,MAAM,EAAE;UACVM,eAAe,IAAIzE,WAAW,GAAGE,YAAY;QAC/C,CAAC,MAAM;UACLuE,eAAe,GAAGrB,kBAAkB,CAACtI,CAAC,GAAGqI,iBAAiB,CAACrI,CAAC,GAAG0G,YAAY;QAC7E;QACA,IAAIkD,eAAe,GAAGlC,0BAA0B,CAACF,WAAW,EAAEmC,eAAe,CAAC;QAC9E,IAAIE,wBAAwB,GAAGnC,0BAA0B,CAACF,WAAW,EAAEmC,eAAe,EAAE9D,iBAAiB,CAAC;QAC1G;QACA;QACA+D,eAAe,GAAG1B,6BAA6B,IAAI0B,eAAe,KAAK1B,6BAA6B,KAAK,CAACvC,cAAc;QACxH;QACAkE,wBAAwB,IAAI1B,+BAA+B,CAAC,EAAE;UAC5D9F,WAAW,CAACE,OAAO,CAACmH,EAAE,GAAG,IAAI;UAC7BjC,WAAW,GAAGkC,eAAe;UAC7BjD,YAAY,GAAG,CAACA,YAAY;UAC5Ba,aAAa,CAAC5H,MAAM,GAAG,CAACM,aAAa,CAACmH,WAAW,EAAE,CAAC,CAAC,EAAEnH,aAAa,CAACkH,YAAY,EAAE,CAAC,CAAC,CAAC;QACxF,CAAC,MAAM;UACL9E,WAAW,CAACE,OAAO,CAACmH,EAAE,GAAG,KAAK;QAChC;MACF;;MAEA;MACA,IAAII,WAAW,GAAGjB,aAAa,CAACJ,OAAO,CAAC;;MAExC;MACA,IAAIsB,MAAM,GAAG3C,WAAW,CAAC,CAAC,CAAC,KAAKD,YAAY,CAAC,CAAC,CAAC;;MAE/C;MACA,IAAI2C,WAAW,IAAI1C,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK8B,cAAc,GAAGnD,sBAAsB,CAACtC,KAAK,IAAIpB,WAAW,CAACE,OAAO,CAACyH,EAAE,CAAC,EAAE;QACtH,IAAIC,cAAc,GAAGzC,WAAW;QAChC,IAAIuC,MAAM,EAAE;UACVE,cAAc,IAAI9E,UAAU,GAAGE,WAAW;QAC5C,CAAC,MAAM;UACL4E,cAAc,GAAG7B,kBAAkB,CAACrI,CAAC,GAAGwI,iBAAiB,CAACxI,CAAC,GAAG0G,YAAY;QAC5E;QACA,IAAIyD,gBAAgB,GAAGxC,0BAA0B,CAACuC,cAAc,EAAExC,WAAW,CAAC;QAC9E,IAAI0C,yBAAyB,GAAGzC,0BAA0B,CAACuC,cAAc,EAAExC,WAAW,EAAE5B,iBAAiB,CAAC;QAC1G;QACA;QACAqE,gBAAgB,GAAGhC,6BAA6B,IAAIgC,gBAAgB,KAAKhC,6BAA6B,KAAK,CAACvC,cAAc;QAC1H;QACAwE,yBAAyB,IAAIhC,+BAA+B,CAAC,EAAE;UAC7D9F,WAAW,CAACE,OAAO,CAACyH,EAAE,GAAG,IAAI;UAC7BxC,WAAW,GAAGyC,cAAc;UAC5BxD,YAAY,GAAG,CAACA,YAAY;UAC5Bc,aAAa,CAAC5H,MAAM,GAAG,CAACM,aAAa,CAACmH,WAAW,EAAE,CAAC,CAAC,EAAEnH,aAAa,CAACkH,YAAY,EAAE,CAAC,CAAC,CAAC;QACxF,CAAC,MAAM;UACL9E,WAAW,CAACE,OAAO,CAACyH,EAAE,GAAG,KAAK;QAChC;MACF;;MAEA;MACA,IAAIF,WAAW,IAAI1C,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK6B,UAAU,GAAGlD,sBAAsB,CAAC1C,IAAI,IAAIhB,WAAW,CAACE,OAAO,CAAC6H,EAAE,CAAC,EAAE;QACjH,IAAIC,eAAe,GAAG7C,WAAW;QACjC,IAAIuC,MAAM,EAAE;UACVM,eAAe,IAAIlF,UAAU,GAAGE,WAAW;QAC7C,CAAC,MAAM;UACLgF,eAAe,GAAG/B,kBAAkB,CAACvI,CAAC,GAAGsI,iBAAiB,CAACtI,CAAC,GAAG0G,YAAY;QAC7E;QACA,IAAI6D,gBAAgB,GAAG5C,0BAA0B,CAAC2C,eAAe,EAAE5C,WAAW,CAAC;QAC/E,IAAI8C,yBAAyB,GAAG7C,0BAA0B,CAAC2C,eAAe,EAAE5C,WAAW,EAAE5B,iBAAiB,CAAC;QAC3G;QACA;QACAyE,gBAAgB,GAAGpC,6BAA6B,IAAIoC,gBAAgB,KAAKpC,6BAA6B,KAAK,CAACvC,cAAc;QAC1H;QACA4E,yBAAyB,IAAIpC,+BAA+B,CAAC,EAAE;UAC7D9F,WAAW,CAACE,OAAO,CAAC6H,EAAE,GAAG,IAAI;UAC7B5C,WAAW,GAAG6C,eAAe;UAC7B5D,YAAY,GAAG,CAACA,YAAY;UAC5Bc,aAAa,CAAC5H,MAAM,GAAG,CAACM,aAAa,CAACmH,WAAW,EAAE,CAAC,CAAC,EAAEnH,aAAa,CAACkH,YAAY,EAAE,CAAC,CAAC,CAAC;QACxF,CAAC,MAAM;UACL9E,WAAW,CAACE,OAAO,CAAC6H,EAAE,GAAG,KAAK;QAChC;MACF;;MAEA;MACAjB,qBAAqB,CAAC,CAAC;MACvB,IAAIqB,SAAS,GAAG7B,MAAM,KAAK,IAAI,GAAG,CAAC,GAAGA,MAAM;MAC5C,IAAI,OAAO6B,SAAS,KAAK,QAAQ,EAAE;QACjC;QACA,IAAIvB,UAAU,GAAGpD,iBAAiB,CAACxC,IAAI,EAAE;UACvCmE,WAAW,IAAIyB,UAAU,GAAGpD,iBAAiB,CAACxC,IAAI,GAAGoD,YAAY;UACjE,IAAIpC,UAAU,CAACtE,CAAC,GAAGsF,WAAW,GAAGQ,iBAAiB,CAACxC,IAAI,GAAGmH,SAAS,EAAE;YACnEhD,WAAW,IAAInD,UAAU,CAACtE,CAAC,GAAG8F,iBAAiB,CAACxC,IAAI,GAAGgC,WAAW,GAAGmF,SAAS;UAChF;QACF;;QAEA;QACA,IAAItB,cAAc,GAAGrD,iBAAiB,CAACpC,KAAK,EAAE;UAC5C+D,WAAW,IAAI0B,cAAc,GAAGrD,iBAAiB,CAACpC,KAAK,GAAGgD,YAAY;UACtE,IAAIpC,UAAU,CAACtE,CAAC,GAAG8F,iBAAiB,CAACpC,KAAK,GAAG+G,SAAS,EAAE;YACtDhD,WAAW,IAAInD,UAAU,CAACtE,CAAC,GAAG8F,iBAAiB,CAACpC,KAAK,GAAG+G,SAAS;UACnE;QACF;MACF;MACA,IAAIC,SAAS,GAAG7B,MAAM,KAAK,IAAI,GAAG,CAAC,GAAGA,MAAM;MAC5C,IAAI,OAAO6B,SAAS,KAAK,QAAQ,EAAE;QACjC;QACA,IAAI1B,UAAU,GAAGlD,iBAAiB,CAACtC,GAAG,EAAE;UACtCkE,WAAW,IAAIsB,UAAU,GAAGlD,iBAAiB,CAACtC,GAAG,GAAGmD,YAAY;;UAEhE;UACA;UACA,IAAIrC,UAAU,CAACrE,CAAC,GAAGoF,YAAY,GAAGS,iBAAiB,CAACtC,GAAG,GAAGkH,SAAS,EAAE;YACnEhD,WAAW,IAAIpD,UAAU,CAACrE,CAAC,GAAG6F,iBAAiB,CAACtC,GAAG,GAAG6B,YAAY,GAAGqF,SAAS;UAChF;QACF;;QAEA;QACA,IAAIzB,eAAe,GAAGnD,iBAAiB,CAAClC,MAAM,EAAE;UAC9C8D,WAAW,IAAIuB,eAAe,GAAGnD,iBAAiB,CAAClC,MAAM,GAAG+C,YAAY;UACxE,IAAIrC,UAAU,CAACrE,CAAC,GAAG6F,iBAAiB,CAAClC,MAAM,GAAG8G,SAAS,EAAE;YACvDhD,WAAW,IAAIpD,UAAU,CAACrE,CAAC,GAAG6F,iBAAiB,CAAClC,MAAM,GAAG8G,SAAS;UACpE;QACF;MACF;;MAEA;MACA;MACA,IAAIC,SAAS,GAAGjG,SAAS,CAAC1E,CAAC,GAAGyH,WAAW;MACzC,IAAImD,UAAU,GAAGD,SAAS,GAAGvF,UAAU;MACvC,IAAIyF,QAAQ,GAAGnG,SAAS,CAACzE,CAAC,GAAGyH,WAAW;MACxC,IAAIoD,WAAW,GAAGD,QAAQ,GAAG1F,WAAW;MACxC,IAAI4F,UAAU,GAAGzG,UAAU,CAACtE,CAAC;MAC7B,IAAIgL,WAAW,GAAGD,UAAU,GAAGzF,WAAW;MAC1C,IAAI2F,SAAS,GAAG3G,UAAU,CAACrE,CAAC;MAC5B,IAAIiL,YAAY,GAAGD,SAAS,GAAG5F,YAAY;MAC3C,IAAI8F,OAAO,GAAG/E,IAAI,CAAC0B,GAAG,CAAC6C,SAAS,EAAEI,UAAU,CAAC;MAC7C,IAAIK,QAAQ,GAAGhF,IAAI,CAAC6B,GAAG,CAAC2C,UAAU,EAAEI,WAAW,CAAC;MAChD,IAAIK,OAAO,GAAG,CAACF,OAAO,GAAGC,QAAQ,IAAI,CAAC;MACtC,IAAIE,UAAU,GAAGD,OAAO,GAAGV,SAAS;MACpC,IAAIY,MAAM,GAAGnF,IAAI,CAAC0B,GAAG,CAAC+C,QAAQ,EAAEI,SAAS,CAAC;MAC1C,IAAIO,SAAS,GAAGpF,IAAI,CAAC6B,GAAG,CAAC6C,WAAW,EAAEI,YAAY,CAAC;MACnD,IAAIO,OAAO,GAAG,CAACF,MAAM,GAAGC,SAAS,IAAI,CAAC;MACtC,IAAIE,UAAU,GAAGD,OAAO,GAAGZ,QAAQ;MACnCzJ,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACL,QAAQ,EAAEyG,aAAa,CAAC;;MAEjG;MACA,IAAImE,aAAa,GAAG1F,eAAe,CAACvC,KAAK,GAAGgB,SAAS,CAAC1E,CAAC,IAAIyH,WAAW,GAAG/C,SAAS,CAACjF,KAAK,CAAC;MACzF,IAAImM,cAAc,GAAG3F,eAAe,CAACrC,MAAM,GAAGc,SAAS,CAACzE,CAAC,IAAIyH,WAAW,GAAGhD,SAAS,CAAChF,MAAM,CAAC;MAC5FuC,aAAa,CAAC;QACZV,KAAK,EAAE,IAAI;QACXhC,OAAO,EAAEkI,WAAW,GAAGtB,OAAO;QAC9B3G,OAAO,EAAEkI,WAAW,GAAGpB,OAAO;QAC9B9E,OAAO,EAAEmK,aAAa,GAAGxF,OAAO;QAChC1E,OAAO,EAAEmK,cAAc,GAAGtF,OAAO;QACjC5E,MAAM,EAAE4J,UAAU,GAAGnF,OAAO;QAC5BxE,MAAM,EAAE+J,UAAU,GAAGpF,OAAO;QAC5B1E,MAAM,EAAEuE,OAAO;QACftE,MAAM,EAAEyE,OAAO;QACfxE,KAAK,EAAE0F;MACT,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,IAAIqE,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC3J,aAAa,CAACM,OAAO,IAAI,CAAC;IAC1B,IAAIsJ,EAAE,GAAG5J,aAAa,CAACM,OAAO;;IAE9B;IACAuJ,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;MACjC,IAAI/J,aAAa,CAACM,OAAO,KAAKsJ,EAAE,EAAE;QAChCrJ,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIyJ,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrCjK,aAAa,CAAC,UAAUkK,GAAG,EAAE;MAC3B,OAAOtO,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsO,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/C5K,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDrD,eAAe,CAACgO,UAAU,EAAE,CAACjL,SAAS,CAAC,CAAC;EACxC/C,eAAe,CAAC,YAAY;IAC1B,IAAI,CAAC4C,IAAI,EAAE;MACToL,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACpL,IAAI,CAAC,CAAC;EACV,OAAO,CAACkB,UAAU,CAACT,KAAK,EAAES,UAAU,CAACzC,OAAO,EAAEyC,UAAU,CAACxC,OAAO,EAAEwC,UAAU,CAACR,OAAO,EAAEQ,UAAU,CAACP,OAAO,EAAEO,UAAU,CAACN,MAAM,EAAEM,UAAU,CAACL,MAAM,EAAEK,UAAU,CAACJ,MAAM,EAAEI,UAAU,CAACH,MAAM,EAAEG,UAAU,CAACF,KAAK,EAAE+J,YAAY,CAAC;AACvN"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}