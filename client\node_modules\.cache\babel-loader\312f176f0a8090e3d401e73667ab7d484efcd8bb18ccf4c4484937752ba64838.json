{"ast": null, "code": "import * as React from 'react';\nimport Mark from './Mark';\nexport default function Marks(props) {\n  var prefixCls = props.prefixCls,\n    marks = props.marks,\n    onClick = props.onClick;\n  var markPrefixCls = \"\".concat(prefixCls, \"-mark\");\n  // Not render mark if empty\n  if (!marks.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: markPrefixCls\n  }, marks.map(function (_ref) {\n    var value = _ref.value,\n      style = _ref.style,\n      label = _ref.label;\n    return /*#__PURE__*/React.createElement(Mark, {\n      key: value,\n      prefixCls: markPrefixCls,\n      style: style,\n      value: value,\n      onClick: onClick\n    }, label);\n  }));\n}", "map": {"version": 3, "names": ["React", "<PERSON>", "Marks", "props", "prefixCls", "marks", "onClick", "markPrefixCls", "concat", "length", "createElement", "className", "map", "_ref", "value", "style", "label", "key"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-slider/es/Marks/index.js"], "sourcesContent": ["import * as React from 'react';\nimport Mark from './Mark';\nexport default function Marks(props) {\n  var prefixCls = props.prefixCls,\n    marks = props.marks,\n    onClick = props.onClick;\n  var markPrefixCls = \"\".concat(prefixCls, \"-mark\");\n  // Not render mark if empty\n  if (!marks.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: markPrefixCls\n  }, marks.map(function (_ref) {\n    var value = _ref.value,\n      style = _ref.style,\n      label = _ref.label;\n    return /*#__PURE__*/React.createElement(Mark, {\n      key: value,\n      prefixCls: markPrefixCls,\n      style: style,\n      value: value,\n      onClick: onClick\n    }, label);\n  }));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,QAAQ;AACzB,eAAe,SAASC,KAAKA,CAACC,KAAK,EAAE;EACnC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,OAAO,GAAGH,KAAK,CAACG,OAAO;EACzB,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,OAAO,CAAC;EACjD;EACA,IAAI,CAACC,KAAK,CAACI,MAAM,EAAE;IACjB,OAAO,IAAI;EACb;EACA,OAAO,aAAaT,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEJ;EACb,CAAC,EAAEF,KAAK,CAACO,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC3B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MACpBC,KAAK,GAAGF,IAAI,CAACE,KAAK;MAClBC,KAAK,GAAGH,IAAI,CAACG,KAAK;IACpB,OAAO,aAAahB,KAAK,CAACU,aAAa,CAACT,IAAI,EAAE;MAC5CgB,GAAG,EAAEH,KAAK;MACVV,SAAS,EAAEG,aAAa;MACxBQ,KAAK,EAAEA,KAAK;MACZD,KAAK,EAAEA,KAAK;MACZR,OAAO,EAAEA;IACX,CAAC,EAAEU,KAAK,CAAC;EACX,CAAC,CAAC,CAAC;AACL"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}