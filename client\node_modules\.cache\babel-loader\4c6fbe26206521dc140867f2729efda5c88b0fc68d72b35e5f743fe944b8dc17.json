{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n\n/* eslint react/prop-types:0 */\nimport React, { Component } from 'react';\nimport AjaxUpload from './AjaxUploader';\nfunction empty() {}\nvar Upload = /*#__PURE__*/function (_Component) {\n  _inherits(Upload, _Component);\n  var _super = _createSuper(Upload);\n  function Upload() {\n    var _this;\n    _classCallCheck(this, Upload);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.uploader = void 0;\n    _this.saveUploader = function (node) {\n      _this.uploader = node;\n    };\n    return _this;\n  }\n  _createClass(Upload, [{\n    key: \"abort\",\n    value: function abort(file) {\n      this.uploader.abort(file);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(AjaxUpload, _extends({}, this.props, {\n        ref: this.saveUploader\n      }));\n    }\n  }]);\n  return Upload;\n}(Component);\nUpload.defaultProps = {\n  component: 'span',\n  prefixCls: 'rc-upload',\n  data: {},\n  headers: {},\n  name: 'file',\n  multipart: false,\n  onStart: empty,\n  onError: empty,\n  onSuccess: empty,\n  multiple: false,\n  beforeUpload: null,\n  customRequest: null,\n  withCredentials: false,\n  openFileDialogOnClick: true\n};\nexport default Upload;", "map": {"version": 3, "names": ["_extends", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "Component", "AjaxUpload", "empty", "Upload", "_Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "uploader", "saveUploader", "node", "key", "value", "abort", "file", "render", "createElement", "props", "ref", "defaultProps", "component", "prefixCls", "data", "headers", "name", "multipart", "onStart", "onError", "onSuccess", "multiple", "beforeUpload", "customRequest", "withCredentials", "openFileDialogOnClick"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-upload/es/Upload.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n\n/* eslint react/prop-types:0 */\nimport React, { Component } from 'react';\nimport AjaxUpload from './AjaxUploader';\n\nfunction empty() {}\n\nvar Upload = /*#__PURE__*/function (_Component) {\n  _inherits(Upload, _Component);\n\n  var _super = _createSuper(Upload);\n\n  function Upload() {\n    var _this;\n\n    _classCallCheck(this, Upload);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.uploader = void 0;\n\n    _this.saveUploader = function (node) {\n      _this.uploader = node;\n    };\n\n    return _this;\n  }\n\n  _createClass(Upload, [{\n    key: \"abort\",\n    value: function abort(file) {\n      this.uploader.abort(file);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(AjaxUpload, _extends({}, this.props, {\n        ref: this.saveUploader\n      }));\n    }\n  }]);\n\n  return Upload;\n}(Component);\n\nUpload.defaultProps = {\n  component: 'span',\n  prefixCls: 'rc-upload',\n  data: {},\n  headers: {},\n  name: 'file',\n  multipart: false,\n  onStart: empty,\n  onError: empty,\n  onSuccess: empty,\n  multiple: false,\n  beforeUpload: null,\n  customRequest: null,\n  withCredentials: false,\n  openFileDialogOnClick: true\n};\nexport default Upload;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;;AAEjE;AACA,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,UAAU,MAAM,gBAAgB;AAEvC,SAASC,KAAKA,CAAA,EAAG,CAAC;AAElB,IAAIC,MAAM,GAAG,aAAa,UAAUC,UAAU,EAAE;EAC9CP,SAAS,CAACM,MAAM,EAAEC,UAAU,CAAC;EAE7B,IAAIC,MAAM,GAAGP,YAAY,CAACK,MAAM,CAAC;EAEjC,SAASA,MAAMA,CAAA,EAAG;IAChB,IAAIG,KAAK;IAETX,eAAe,CAAC,IAAI,EAAEQ,MAAM,CAAC;IAE7B,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDJ,KAAK,CAACU,QAAQ,GAAG,KAAK,CAAC;IAEvBV,KAAK,CAACW,YAAY,GAAG,UAAUC,IAAI,EAAE;MACnCZ,KAAK,CAACU,QAAQ,GAAGE,IAAI;IACvB,CAAC;IAED,OAAOZ,KAAK;EACd;EAEAV,YAAY,CAACO,MAAM,EAAE,CAAC;IACpBgB,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASC,KAAKA,CAACC,IAAI,EAAE;MAC1B,IAAI,CAACN,QAAQ,CAACK,KAAK,CAACC,IAAI,CAAC;IAC3B;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASG,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAaxB,KAAK,CAACyB,aAAa,CAACvB,UAAU,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC+B,KAAK,EAAE;QAC3EC,GAAG,EAAE,IAAI,CAACT;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EAEH,OAAOd,MAAM;AACf,CAAC,CAACH,SAAS,CAAC;AAEZG,MAAM,CAACwB,YAAY,GAAG;EACpBC,SAAS,EAAE,MAAM;EACjBC,SAAS,EAAE,WAAW;EACtBC,IAAI,EAAE,CAAC,CAAC;EACRC,OAAO,EAAE,CAAC,CAAC;EACXC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAEhC,KAAK;EACdiC,OAAO,EAAEjC,KAAK;EACdkC,SAAS,EAAElC,KAAK;EAChBmC,QAAQ,EAAE,KAAK;EACfC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,IAAI;EACnBC,eAAe,EAAE,KAAK;EACtBC,qBAAqB,EAAE;AACzB,CAAC;AACD,eAAetC,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}