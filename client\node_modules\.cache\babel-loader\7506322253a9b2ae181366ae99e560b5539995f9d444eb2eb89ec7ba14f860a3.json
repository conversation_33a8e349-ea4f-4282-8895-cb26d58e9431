{"ast": null, "code": "/* istanbul ignore next */\n/**\n * This is a syntactic sugar for `columns` prop.\n * So HOC will not work on this.\n */ // eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction ColumnGroup(_) {\n  return null;\n}\nexport default ColumnGroup;", "map": {"version": 3, "names": ["ColumnGroup", "_"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/sugar/ColumnGroup.js"], "sourcesContent": ["/* istanbul ignore next */\n/**\n * This is a syntactic sugar for `columns` prop.\n * So HOC will not work on this.\n */ // eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction ColumnGroup(_) {\n  return null;\n}\nexport default ColumnGroup;"], "mappings": "AAAA;AACA;AACA;AACA;AACA,GAHA,CAGI;AACJ,SAASA,WAAWA,CAACC,CAAC,EAAE;EACtB,OAAO,IAAI;AACb;AACA,eAAeD,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}