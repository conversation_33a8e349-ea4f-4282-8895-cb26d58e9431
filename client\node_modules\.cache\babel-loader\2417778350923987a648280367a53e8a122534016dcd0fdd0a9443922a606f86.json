{"ast": null, "code": "import { textEllipsis } from '../../style';\nconst genEllipsisStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-cell-ellipsis`]: Object.assign(Object.assign({}, textEllipsis), {\n        wordBreak: 'keep-all',\n        // Fixed first or last should special process\n        [`\n          &${componentCls}-cell-fix-left-last,\n          &${componentCls}-cell-fix-right-first\n        `]: {\n          overflow: 'visible',\n          [`${componentCls}-cell-content`]: {\n            display: 'block',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          }\n        },\n        [`${componentCls}-column-title`]: {\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          wordBreak: 'keep-all'\n        }\n      })\n    }\n  };\n};\nexport default genEllipsisStyle;", "map": {"version": 3, "names": ["textEllipsis", "genEllipsisStyle", "token", "componentCls", "Object", "assign", "wordBreak", "overflow", "display", "textOverflow"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/ellipsis.js"], "sourcesContent": ["import { textEllipsis } from '../../style';\nconst genEllipsisStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-cell-ellipsis`]: Object.assign(Object.assign({}, textEllipsis), {\n        wordBreak: 'keep-all',\n        // Fixed first or last should special process\n        [`\n          &${componentCls}-cell-fix-left-last,\n          &${componentCls}-cell-fix-right-first\n        `]: {\n          overflow: 'visible',\n          [`${componentCls}-cell-content`]: {\n            display: 'block',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          }\n        },\n        [`${componentCls}-column-title`]: {\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          wordBreak: 'keep-all'\n        }\n      })\n    }\n  };\n};\nexport default genEllipsisStyle;"], "mappings": "AAAA,SAASA,YAAY,QAAQ,aAAa;AAC1C,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAAE,GAAEC,YAAa,UAAS,GAAG;MAC3B,CAAE,GAAEA,YAAa,gBAAe,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,YAAY,CAAC,EAAE;QAChFM,SAAS,EAAE,UAAU;QACrB;QACA,CAAE;AACV,aAAaH,YAAa;AAC1B,aAAaA,YAAa;AAC1B,SAAS,GAAG;UACFI,QAAQ,EAAE,SAAS;UACnB,CAAE,GAAEJ,YAAa,eAAc,GAAG;YAChCK,OAAO,EAAE,OAAO;YAChBD,QAAQ,EAAE,QAAQ;YAClBE,YAAY,EAAE;UAChB;QACF,CAAC;QACD,CAAE,GAAEN,YAAa,eAAc,GAAG;UAChCI,QAAQ,EAAE,QAAQ;UAClBE,YAAY,EAAE,UAAU;UACxBH,SAAS,EAAE;QACb;MACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,eAAeL,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}