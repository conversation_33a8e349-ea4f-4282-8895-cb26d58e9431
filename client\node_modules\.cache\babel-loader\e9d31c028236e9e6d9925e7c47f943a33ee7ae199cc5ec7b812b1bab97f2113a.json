{"ast": null, "code": "const genStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper-rtl`]: {\n      direction: 'rtl',\n      table: {\n        direction: 'rtl'\n      },\n      [`${componentCls}-pagination-left`]: {\n        justifyContent: 'flex-end'\n      },\n      [`${componentCls}-pagination-right`]: {\n        justifyContent: 'flex-start'\n      },\n      [`${componentCls}-row-expand-icon`]: {\n        float: 'right',\n        '&::after': {\n          transform: 'rotate(-90deg)'\n        },\n        '&-collapsed::before': {\n          transform: 'rotate(180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        }\n      },\n      [`${componentCls}-container`]: {\n        '&::before': {\n          insetInlineStart: 'unset',\n          insetInlineEnd: 0\n        },\n        '&::after': {\n          insetInlineStart: 0,\n          insetInlineEnd: 'unset'\n        },\n        [`${componentCls}-row-indent`]: {\n          float: 'right'\n        }\n      }\n    }\n  };\n};\nexport default genStyle;", "map": {"version": 3, "names": ["genStyle", "token", "componentCls", "direction", "table", "justifyContent", "float", "transform", "insetInlineStart", "insetInlineEnd"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/rtl.js"], "sourcesContent": ["const genStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper-rtl`]: {\n      direction: 'rtl',\n      table: {\n        direction: 'rtl'\n      },\n      [`${componentCls}-pagination-left`]: {\n        justifyContent: 'flex-end'\n      },\n      [`${componentCls}-pagination-right`]: {\n        justifyContent: 'flex-start'\n      },\n      [`${componentCls}-row-expand-icon`]: {\n        float: 'right',\n        '&::after': {\n          transform: 'rotate(-90deg)'\n        },\n        '&-collapsed::before': {\n          transform: 'rotate(180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        }\n      },\n      [`${componentCls}-container`]: {\n        '&::before': {\n          insetInlineStart: 'unset',\n          insetInlineEnd: 0\n        },\n        '&::after': {\n          insetInlineStart: 0,\n          insetInlineEnd: 'unset'\n        },\n        [`${componentCls}-row-indent`]: {\n          float: 'right'\n        }\n      }\n    }\n  };\n};\nexport default genStyle;"], "mappings": "AAAA,MAAMA,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAAE,GAAEC,YAAa,cAAa,GAAG;MAC/BC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE;QACLD,SAAS,EAAE;MACb,CAAC;MACD,CAAE,GAAED,YAAa,kBAAiB,GAAG;QACnCG,cAAc,EAAE;MAClB,CAAC;MACD,CAAE,GAAEH,YAAa,mBAAkB,GAAG;QACpCG,cAAc,EAAE;MAClB,CAAC;MACD,CAAE,GAAEH,YAAa,kBAAiB,GAAG;QACnCI,KAAK,EAAE,OAAO;QACd,UAAU,EAAE;UACVC,SAAS,EAAE;QACb,CAAC;QACD,qBAAqB,EAAE;UACrBA,SAAS,EAAE;QACb,CAAC;QACD,oBAAoB,EAAE;UACpBA,SAAS,EAAE;QACb;MACF,CAAC;MACD,CAAE,GAAEL,YAAa,YAAW,GAAG;QAC7B,WAAW,EAAE;UACXM,gBAAgB,EAAE,OAAO;UACzBC,cAAc,EAAE;QAClB,CAAC;QACD,UAAU,EAAE;UACVD,gBAAgB,EAAE,CAAC;UACnBC,cAAc,EAAE;QAClB,CAAC;QACD,CAAE,GAAEP,YAAa,aAAY,GAAG;UAC9BI,KAAK,EAAE;QACT;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeN,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}