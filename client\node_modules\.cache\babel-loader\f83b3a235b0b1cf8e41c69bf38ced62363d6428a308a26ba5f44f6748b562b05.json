{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\ThemeToggle.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { TbSun, TbMoon } from 'react-icons/tb';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeToggle = ({\n  className = '',\n  size = 'md'\n}) => {\n  _s();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  const sizes = {\n    sm: 'w-8 h-8',\n    md: 'w-10 h-10',\n    lg: 'w-12 h-12'\n  };\n  const iconSizes = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6'\n  };\n  return /*#__PURE__*/_jsxDEV(motion.button, {\n    whileHover: {\n      scale: 1.05\n    },\n    whileTap: {\n      scale: 0.95\n    },\n    onClick: toggleTheme,\n    className: `\n        ${sizes[size]} \n        relative rounded-full p-2 \n        bg-gray-200 dark:bg-gray-700 \n        hover:bg-gray-300 dark:hover:bg-gray-600 \n        transition-all duration-300 \n        focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\n        ${className}\n      `,\n    \"aria-label\": isDarkMode ? 'Switch to light mode' : 'Switch to dark mode',\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-full h-full flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        initial: false,\n        children: isDarkMode ? /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            y: -20,\n            opacity: 0,\n            rotate: -90\n          },\n          animate: {\n            y: 0,\n            opacity: 1,\n            rotate: 0\n          },\n          exit: {\n            y: 20,\n            opacity: 0,\n            rotate: 90\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"absolute\",\n          children: /*#__PURE__*/_jsxDEV(TbSun, {\n            className: `${iconSizes[size]} text-yellow-500`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this)\n        }, \"sun\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            y: -20,\n            opacity: 0,\n            rotate: -90\n          },\n          animate: {\n            y: 0,\n            opacity: 1,\n            rotate: 0\n          },\n          exit: {\n            y: 20,\n            opacity: 0,\n            rotate: 90\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"absolute\",\n          children: /*#__PURE__*/_jsxDEV(TbMoon, {\n            className: `${iconSizes[size]} text-blue-600`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this)\n        }, \"moon\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n\n// Advanced Theme Toggle with Switch Design\n_s(ThemeToggle, \"MY/fJVj7pNG84xK2IRXuobEs7Rg=\", false, function () {\n  return [useTheme];\n});\n_c = ThemeToggle;\nexport const ThemeSwitch = ({\n  className = ''\n}) => {\n  _s2();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(motion.button, {\n    whileHover: {\n      scale: 1.02\n    },\n    whileTap: {\n      scale: 0.98\n    },\n    onClick: toggleTheme,\n    className: `\n        relative inline-flex h-6 w-11 items-center rounded-full \n        transition-colors duration-300 focus:outline-none focus:ring-2 \n        focus:ring-primary-500 focus:ring-offset-2\n        ${isDarkMode ? 'bg-primary-600' : 'bg-gray-200'}\n        ${className}\n      `,\n    \"aria-label\": isDarkMode ? 'Switch to light mode' : 'Switch to dark mode',\n    children: /*#__PURE__*/_jsxDEV(motion.span, {\n      layout: true,\n      className: `\n          inline-block h-4 w-4 transform rounded-full bg-white shadow-lg \n          transition-transform duration-300 flex items-center justify-center\n          ${isDarkMode ? 'translate-x-6' : 'translate-x-1'}\n        `,\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: false,\n        animate: {\n          rotate: isDarkMode ? 0 : 180\n        },\n        transition: {\n          duration: 0.3\n        },\n        children: isDarkMode ? /*#__PURE__*/_jsxDEV(TbMoon, {\n          className: \"w-2.5 h-2.5 text-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(TbSun, {\n          className: \"w-2.5 h-2.5 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n\n// Theme Toggle with Label\n_s2(ThemeSwitch, \"MY/fJVj7pNG84xK2IRXuobEs7Rg=\", false, function () {\n  return [useTheme];\n});\n_c2 = ThemeSwitch;\nexport const ThemeToggleWithLabel = ({\n  className = '',\n  showLabel = true\n}) => {\n  _s3();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex items-center space-x-3 ${className}`,\n    children: [showLabel && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n      children: isDarkMode ? 'Dark Mode' : 'Light Mode'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ThemeSwitch, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s3(ThemeToggleWithLabel, \"MY/fJVj7pNG84xK2IRXuobEs7Rg=\", false, function () {\n  return [useTheme];\n});\n_c3 = ThemeToggleWithLabel;\nexport default ThemeToggle;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ThemeToggle\");\n$RefreshReg$(_c2, \"ThemeSwitch\");\n$RefreshReg$(_c3, \"ThemeToggleWithLabel\");", "map": {"version": 3, "names": ["React", "motion", "TbSun", "TbMoon", "useTheme", "jsxDEV", "_jsxDEV", "ThemeToggle", "className", "size", "_s", "isDarkMode", "toggleTheme", "sizes", "sm", "md", "lg", "iconSizes", "button", "whileHover", "scale", "whileTap", "onClick", "children", "AnimatePresence", "mode", "initial", "div", "y", "opacity", "rotate", "animate", "exit", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ThemeSwitch", "_s2", "span", "layout", "_c2", "ThemeToggleWithLabel", "showLabel", "_s3", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/ThemeToggle.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { TbSun, TbMoon } from 'react-icons/tb';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nconst ThemeToggle = ({ className = '', size = 'md' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  const sizes = {\n    sm: 'w-8 h-8',\n    md: 'w-10 h-10',\n    lg: 'w-12 h-12',\n  };\n\n  const iconSizes = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6',\n  };\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      onClick={toggleTheme}\n      className={`\n        ${sizes[size]} \n        relative rounded-full p-2 \n        bg-gray-200 dark:bg-gray-700 \n        hover:bg-gray-300 dark:hover:bg-gray-600 \n        transition-all duration-300 \n        focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\n        ${className}\n      `}\n      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      <div className=\"relative w-full h-full flex items-center justify-center\">\n        <AnimatePresence mode=\"wait\" initial={false}>\n          {isDarkMode ? (\n            <motion.div\n              key=\"sun\"\n              initial={{ y: -20, opacity: 0, rotate: -90 }}\n              animate={{ y: 0, opacity: 1, rotate: 0 }}\n              exit={{ y: 20, opacity: 0, rotate: 90 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute\"\n            >\n              <TbSun className={`${iconSizes[size]} text-yellow-500`} />\n            </motion.div>\n          ) : (\n            <motion.div\n              key=\"moon\"\n              initial={{ y: -20, opacity: 0, rotate: -90 }}\n              animate={{ y: 0, opacity: 1, rotate: 0 }}\n              exit={{ y: 20, opacity: 0, rotate: 90 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute\"\n            >\n              <TbMoon className={`${iconSizes[size]} text-blue-600`} />\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.button>\n  );\n};\n\n// Advanced Theme Toggle with Switch Design\nexport const ThemeSwitch = ({ className = '' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={toggleTheme}\n      className={`\n        relative inline-flex h-6 w-11 items-center rounded-full \n        transition-colors duration-300 focus:outline-none focus:ring-2 \n        focus:ring-primary-500 focus:ring-offset-2\n        ${isDarkMode ? 'bg-primary-600' : 'bg-gray-200'}\n        ${className}\n      `}\n      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      <motion.span\n        layout\n        className={`\n          inline-block h-4 w-4 transform rounded-full bg-white shadow-lg \n          transition-transform duration-300 flex items-center justify-center\n          ${isDarkMode ? 'translate-x-6' : 'translate-x-1'}\n        `}\n      >\n        <motion.div\n          initial={false}\n          animate={{ rotate: isDarkMode ? 0 : 180 }}\n          transition={{ duration: 0.3 }}\n        >\n          {isDarkMode ? (\n            <TbMoon className=\"w-2.5 h-2.5 text-primary-600\" />\n          ) : (\n            <TbSun className=\"w-2.5 h-2.5 text-yellow-500\" />\n          )}\n        </motion.div>\n      </motion.span>\n    </motion.button>\n  );\n};\n\n// Theme Toggle with Label\nexport const ThemeToggleWithLabel = ({ className = '', showLabel = true }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <div className={`flex items-center space-x-3 ${className}`}>\n      {showLabel && (\n        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n          {isDarkMode ? 'Dark Mode' : 'Light Mode'}\n        </span>\n      )}\n      <ThemeSwitch />\n    </div>\n  );\n};\n\nexport default ThemeToggle;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,KAAK,EAAEC,MAAM,QAAQ,gBAAgB;AAC9C,SAASC,QAAQ,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACvD,MAAM;IAAEC,UAAU;IAAEC;EAAY,CAAC,GAAGR,QAAQ,CAAC,CAAC;EAE9C,MAAMS,KAAK,GAAG;IACZC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,SAAS,GAAG;IAChBH,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE;EACN,CAAC;EAED,oBACEV,OAAA,CAACL,MAAM,CAACiB,MAAM;IACZC,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAE;IAC5BC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAC1BE,OAAO,EAAEV,WAAY;IACrBJ,SAAS,EAAG;AAClB,UAAUK,KAAK,CAACJ,IAAI,CAAE;AACtB;AACA;AACA;AACA;AACA;AACA,UAAUD,SAAU;AACpB,OAAQ;IACF,cAAYG,UAAU,GAAG,sBAAsB,GAAG,qBAAsB;IAAAY,QAAA,eAExEjB,OAAA;MAAKE,SAAS,EAAC,yDAAyD;MAAAe,QAAA,eACtEjB,OAAA,CAACkB,eAAe;QAACC,IAAI,EAAC,MAAM;QAACC,OAAO,EAAE,KAAM;QAAAH,QAAA,EACzCZ,UAAU,gBACTL,OAAA,CAACL,MAAM,CAAC0B,GAAG;UAETD,OAAO,EAAE;YAAEE,CAAC,EAAE,CAAC,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE,CAAC;UAAG,CAAE;UAC7CC,OAAO,EAAE;YAAEH,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UACzCE,IAAI,EAAE;YAAEJ,CAAC,EAAE,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAG,CAAE;UACxCG,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B1B,SAAS,EAAC,UAAU;UAAAe,QAAA,eAEpBjB,OAAA,CAACJ,KAAK;YAACM,SAAS,EAAG,GAAES,SAAS,CAACR,IAAI,CAAE;UAAkB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC,GAPtD,KAAK;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQC,CAAC,gBAEbhC,OAAA,CAACL,MAAM,CAAC0B,GAAG;UAETD,OAAO,EAAE;YAAEE,CAAC,EAAE,CAAC,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE,CAAC;UAAG,CAAE;UAC7CC,OAAO,EAAE;YAAEH,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UACzCE,IAAI,EAAE;YAAEJ,CAAC,EAAE,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAG,CAAE;UACxCG,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B1B,SAAS,EAAC,UAAU;UAAAe,QAAA,eAEpBjB,OAAA,CAACH,MAAM;YAACK,SAAS,EAAG,GAAES,SAAS,CAACR,IAAI,CAAE;UAAgB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC,GAPrD,MAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQA;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;;AAED;AAAA5B,EAAA,CA9DMH,WAAW;EAAA,QACqBH,QAAQ;AAAA;AAAAmC,EAAA,GADxChC,WAAW;AA+DjB,OAAO,MAAMiC,WAAW,GAAGA,CAAC;EAAEhC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAiC,GAAA;EACjD,MAAM;IAAE9B,UAAU;IAAEC;EAAY,CAAC,GAAGR,QAAQ,CAAC,CAAC;EAE9C,oBACEE,OAAA,CAACL,MAAM,CAACiB,MAAM;IACZC,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAE;IAC5BC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAC1BE,OAAO,EAAEV,WAAY;IACrBJ,SAAS,EAAG;AAClB;AACA;AACA;AACA,UAAUG,UAAU,GAAG,gBAAgB,GAAG,aAAc;AACxD,UAAUH,SAAU;AACpB,OAAQ;IACF,cAAYG,UAAU,GAAG,sBAAsB,GAAG,qBAAsB;IAAAY,QAAA,eAExEjB,OAAA,CAACL,MAAM,CAACyC,IAAI;MACVC,MAAM;MACNnC,SAAS,EAAG;AACpB;AACA;AACA,YAAYG,UAAU,GAAG,eAAe,GAAG,eAAgB;AAC3D,SAAU;MAAAY,QAAA,eAEFjB,OAAA,CAACL,MAAM,CAAC0B,GAAG;QACTD,OAAO,EAAE,KAAM;QACfK,OAAO,EAAE;UAAED,MAAM,EAAEnB,UAAU,GAAG,CAAC,GAAG;QAAI,CAAE;QAC1CsB,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAX,QAAA,EAE7BZ,UAAU,gBACTL,OAAA,CAACH,MAAM;UAACK,SAAS,EAAC;QAA8B;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEnDhC,OAAA,CAACJ,KAAK;UAACM,SAAS,EAAC;QAA6B;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MACjD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB,CAAC;;AAED;AAAAG,GAAA,CAzCaD,WAAW;EAAA,QACcpC,QAAQ;AAAA;AAAAwC,GAAA,GADjCJ,WAAW;AA0CxB,OAAO,MAAMK,oBAAoB,GAAGA,CAAC;EAAErC,SAAS,GAAG,EAAE;EAAEsC,SAAS,GAAG;AAAK,CAAC,KAAK;EAAAC,GAAA;EAC5E,MAAM;IAAEpC,UAAU;IAAEC;EAAY,CAAC,GAAGR,QAAQ,CAAC,CAAC;EAE9C,oBACEE,OAAA;IAAKE,SAAS,EAAG,+BAA8BA,SAAU,EAAE;IAAAe,QAAA,GACxDuB,SAAS,iBACRxC,OAAA;MAAME,SAAS,EAAC,sDAAsD;MAAAe,QAAA,EACnEZ,UAAU,GAAG,WAAW,GAAG;IAAY;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACP,eACDhC,OAAA,CAACkC,WAAW;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACS,GAAA,CAbWF,oBAAoB;EAAA,QACKzC,QAAQ;AAAA;AAAA4C,GAAA,GADjCH,oBAAoB;AAejC,eAAetC,WAAW;AAAC,IAAAgC,EAAA,EAAAK,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}