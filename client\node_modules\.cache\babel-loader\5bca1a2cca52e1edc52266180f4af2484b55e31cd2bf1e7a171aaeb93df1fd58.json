{"ast": null, "code": "const genStickyStyle = token => {\n  const {\n    componentCls,\n    opacityLoading,\n    tableScrollThumbBg,\n    tableScrollThumbBgHover,\n    tableScrollThumbSize,\n    tableScrollBg,\n    zIndexTableSticky\n  } = token;\n  const tableBorder = `${token.lineWidth}px ${token.lineType} ${token.tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-sticky`]: {\n        '&-holder': {\n          position: 'sticky',\n          zIndex: zIndexTableSticky,\n          background: token.colorBgContainer\n        },\n        '&-scroll': {\n          position: 'sticky',\n          bottom: 0,\n          height: `${tableScrollThumbSize}px !important`,\n          zIndex: zIndexTableSticky,\n          display: 'flex',\n          alignItems: 'center',\n          background: tableScrollBg,\n          borderTop: tableBorder,\n          opacity: opacityLoading,\n          '&:hover': {\n            transformOrigin: 'center bottom'\n          },\n          // fake scrollbar style of sticky\n          '&-bar': {\n            height: tableScrollThumbSize,\n            backgroundColor: tableScrollThumbBg,\n            borderRadius: 100,\n            transition: `all ${token.motionDurationSlow}, transform none`,\n            position: 'absolute',\n            bottom: 0,\n            '&:hover, &-active': {\n              backgroundColor: tableScrollThumbBgHover\n            }\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStickyStyle;", "map": {"version": 3, "names": ["genStickyStyle", "token", "componentCls", "opacityLoading", "tableScrollThumbBg", "tableScrollThumbBgHover", "tableScrollThumbSize", "tableScrollBg", "zIndexTableSticky", "tableBorder", "lineWidth", "lineType", "tableBorderColor", "position", "zIndex", "background", "colorBgContainer", "bottom", "height", "display", "alignItems", "borderTop", "opacity", "transform<PERSON><PERSON>in", "backgroundColor", "borderRadius", "transition", "motionDurationSlow"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/sticky.js"], "sourcesContent": ["const genStickyStyle = token => {\n  const {\n    componentCls,\n    opacityLoading,\n    tableScrollThumbBg,\n    tableScrollThumbBgHover,\n    tableScrollThumbSize,\n    tableScrollBg,\n    zIndexTableSticky\n  } = token;\n  const tableBorder = `${token.lineWidth}px ${token.lineType} ${token.tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-sticky`]: {\n        '&-holder': {\n          position: 'sticky',\n          zIndex: zIndexTableSticky,\n          background: token.colorBgContainer\n        },\n        '&-scroll': {\n          position: 'sticky',\n          bottom: 0,\n          height: `${tableScrollThumbSize}px !important`,\n          zIndex: zIndexTableSticky,\n          display: 'flex',\n          alignItems: 'center',\n          background: tableScrollBg,\n          borderTop: tableBorder,\n          opacity: opacityLoading,\n          '&:hover': {\n            transformOrigin: 'center bottom'\n          },\n          // fake scrollbar style of sticky\n          '&-bar': {\n            height: tableScrollThumbSize,\n            backgroundColor: tableScrollThumbBg,\n            borderRadius: 100,\n            transition: `all ${token.motionDurationSlow}, transform none`,\n            position: 'absolute',\n            bottom: 0,\n            '&:hover, &-active': {\n              backgroundColor: tableScrollThumbBgHover\n            }\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStickyStyle;"], "mappings": "AAAA,MAAMA,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,cAAc;IACdC,kBAAkB;IAClBC,uBAAuB;IACvBC,oBAAoB;IACpBC,aAAa;IACbC;EACF,CAAC,GAAGP,KAAK;EACT,MAAMQ,WAAW,GAAI,GAAER,KAAK,CAACS,SAAU,MAAKT,KAAK,CAACU,QAAS,IAAGV,KAAK,CAACW,gBAAiB,EAAC;EACtF,OAAO;IACL,CAAE,GAAEV,YAAa,UAAS,GAAG;MAC3B,CAAE,GAAEA,YAAa,SAAQ,GAAG;QAC1B,UAAU,EAAE;UACVW,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAEN,iBAAiB;UACzBO,UAAU,EAAEd,KAAK,CAACe;QACpB,CAAC;QACD,UAAU,EAAE;UACVH,QAAQ,EAAE,QAAQ;UAClBI,MAAM,EAAE,CAAC;UACTC,MAAM,EAAG,GAAEZ,oBAAqB,eAAc;UAC9CQ,MAAM,EAAEN,iBAAiB;UACzBW,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBL,UAAU,EAAER,aAAa;UACzBc,SAAS,EAAEZ,WAAW;UACtBa,OAAO,EAAEnB,cAAc;UACvB,SAAS,EAAE;YACToB,eAAe,EAAE;UACnB,CAAC;UACD;UACA,OAAO,EAAE;YACPL,MAAM,EAAEZ,oBAAoB;YAC5BkB,eAAe,EAAEpB,kBAAkB;YACnCqB,YAAY,EAAE,GAAG;YACjBC,UAAU,EAAG,OAAMzB,KAAK,CAAC0B,kBAAmB,kBAAiB;YAC7Dd,QAAQ,EAAE,UAAU;YACpBI,MAAM,EAAE,CAAC;YACT,mBAAmB,EAAE;cACnBO,eAAe,EAAEnB;YACnB;UACF;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeL,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}