{"ast": null, "code": "import _defineProperty from '@babel/runtime/helpers/esm/defineProperty';\nimport _slicedToArray from '@babel/runtime/helpers/esm/slicedToArray';\nimport classnames from 'classnames';\nimport React from 'react';\nvar PanelContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classnames;\n  var prefixCls = props.prefixCls,\n    forceRender = props.forceRender,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    isActive = props.isActive,\n    role = props.role;\n  var _React$useState = React.useState(isActive || forceRender),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    rendered = _React$useState2[0],\n    setRendered = _React$useState2[1];\n  React.useEffect(function () {\n    if (forceRender || isActive) {\n      setRendered(true);\n    }\n  }, [forceRender, isActive]);\n  if (!rendered) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement('div', {\n    ref: ref,\n    className: classnames(''.concat(prefixCls, '-content'), (_classnames = {}, _defineProperty(_classnames, ''.concat(prefixCls, '-content-active'), isActive), _defineProperty(_classnames, ''.concat(prefixCls, '-content-inactive'), !isActive), _classnames), className),\n    style: style,\n    role: role\n  }, /*#__PURE__*/React.createElement('div', {\n    className: ''.concat(prefixCls, '-content-box')\n  }, children));\n});\nPanelContent.displayName = 'PanelContent';\nexport default PanelContent;", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "classnames", "React", "PanelContent", "forwardRef", "props", "ref", "_classnames", "prefixCls", "forceRender", "className", "style", "children", "isActive", "role", "_React$useState", "useState", "_React$useState2", "rendered", "setRendered", "useEffect", "createElement", "concat", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-collapse/es/PanelContent.js"], "sourcesContent": ["import _defineProperty from '@babel/runtime/helpers/esm/defineProperty';\nimport _slicedToArray from '@babel/runtime/helpers/esm/slicedToArray';\nimport classnames from 'classnames';\nimport React from 'react';\nvar PanelContent = /*#__PURE__*/ React.forwardRef(function (props, ref) {\n  var _classnames;\n  var prefixCls = props.prefixCls,\n    forceRender = props.forceRender,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    isActive = props.isActive,\n    role = props.role;\n  var _React$useState = React.useState(isActive || forceRender),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    rendered = _React$useState2[0],\n    setRendered = _React$useState2[1];\n  React.useEffect(\n    function () {\n      if (forceRender || isActive) {\n        setRendered(true);\n      }\n    },\n    [forceRender, isActive],\n  );\n  if (!rendered) {\n    return null;\n  }\n  return /*#__PURE__*/ React.createElement(\n    'div',\n    {\n      ref: ref,\n      className: classnames(\n        ''.concat(prefixCls, '-content'),\n        ((_classnames = {}),\n        _defineProperty(_classnames, ''.concat(prefixCls, '-content-active'), isActive),\n        _defineProperty(_classnames, ''.concat(prefixCls, '-content-inactive'), !isActive),\n        _classnames),\n        className,\n      ),\n      style: style,\n      role: role,\n    },\n    /*#__PURE__*/ React.createElement(\n      'div',\n      {\n        className: ''.concat(prefixCls, '-content-box'),\n      },\n      children,\n    ),\n  );\n});\nPanelContent.displayName = 'PanelContent';\nexport default PanelContent;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,IAAIC,YAAY,GAAG,aAAcD,KAAK,CAACE,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAIC,WAAW;EACf,IAAIC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC7BC,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,IAAI,GAAGT,KAAK,CAACS,IAAI;EACnB,IAAIC,eAAe,GAAGb,KAAK,CAACc,QAAQ,CAACH,QAAQ,IAAIJ,WAAW,CAAC;IAC3DQ,gBAAgB,GAAGjB,cAAc,CAACe,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnCf,KAAK,CAACkB,SAAS,CACb,YAAY;IACV,IAAIX,WAAW,IAAII,QAAQ,EAAE;MAC3BM,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EACD,CAACV,WAAW,EAAEI,QAAQ,CACxB,CAAC;EACD,IAAI,CAACK,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,OAAO,aAAchB,KAAK,CAACmB,aAAa,CACtC,KAAK,EACL;IACEf,GAAG,EAAEA,GAAG;IACRI,SAAS,EAAET,UAAU,CACnB,EAAE,CAACqB,MAAM,CAACd,SAAS,EAAE,UAAU,CAAC,GAC9BD,WAAW,GAAG,CAAC,CAAC,EAClBR,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACe,MAAM,CAACd,SAAS,EAAE,iBAAiB,CAAC,EAAEK,QAAQ,CAAC,EAC/Ed,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACe,MAAM,CAACd,SAAS,EAAE,mBAAmB,CAAC,EAAE,CAACK,QAAQ,CAAC,EAClFN,WAAW,GACXG,SACF,CAAC;IACDC,KAAK,EAAEA,KAAK;IACZG,IAAI,EAAEA;EACR,CAAC,EACD,aAAcZ,KAAK,CAACmB,aAAa,CAC/B,KAAK,EACL;IACEX,SAAS,EAAE,EAAE,CAACY,MAAM,CAACd,SAAS,EAAE,cAAc;EAChD,CAAC,EACDI,QACF,CACF,CAAC;AACH,CAAC,CAAC;AACFT,YAAY,CAACoB,WAAW,GAAG,cAAc;AACzC,eAAepB,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}