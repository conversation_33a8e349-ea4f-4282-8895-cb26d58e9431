{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"value\", \"valueIndex\", \"onStartMove\", \"style\", \"render\", \"dragging\", \"onOffsetChange\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport SliderContext from '../context';\nimport { getDirectionStyle, getIndex } from '../util';\nvar Handle = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames, _getIndex;\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    valueIndex = props.valueIndex,\n    onStartMove = props.onStartMove,\n    style = props.style,\n    render = props.render,\n    dragging = props.dragging,\n    onOffsetChange = props.onOffsetChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    disabled = _React$useContext.disabled,\n    keyboard = _React$useContext.keyboard,\n    range = _React$useContext.range,\n    tabIndex = _React$useContext.tabIndex,\n    ariaLabelForHandle = _React$useContext.ariaLabelForHandle,\n    ariaLabelledByForHandle = _React$useContext.ariaLabelledByForHandle,\n    ariaValueTextFormatterForHandle = _React$useContext.ariaValueTextFormatterForHandle;\n  var handlePrefixCls = \"\".concat(prefixCls, \"-handle\");\n  // ============================ Events ============================\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled) {\n      onStartMove(e, valueIndex);\n    }\n  };\n  // =========================== Keyboard ===========================\n  var onKeyDown = function onKeyDown(e) {\n    if (!disabled && keyboard) {\n      var offset = null;\n      // Change the value\n      switch (e.which || e.keyCode) {\n        case KeyCode.LEFT:\n          offset = direction === 'ltr' || direction === 'btt' ? -1 : 1;\n          break;\n        case KeyCode.RIGHT:\n          offset = direction === 'ltr' || direction === 'btt' ? 1 : -1;\n          break;\n        // Up is plus\n        case KeyCode.UP:\n          offset = direction !== 'ttb' ? 1 : -1;\n          break;\n        // Down is minus\n        case KeyCode.DOWN:\n          offset = direction !== 'ttb' ? -1 : 1;\n          break;\n        case KeyCode.HOME:\n          offset = 'min';\n          break;\n        case KeyCode.END:\n          offset = 'max';\n          break;\n        case KeyCode.PAGE_UP:\n          offset = 2;\n          break;\n        case KeyCode.PAGE_DOWN:\n          offset = -2;\n          break;\n      }\n      if (offset !== null) {\n        e.preventDefault();\n        onOffsetChange(offset, valueIndex);\n      }\n    }\n  };\n  // ============================ Offset ============================\n  var positionStyle = getDirectionStyle(direction, value, min, max);\n  // ============================ Render ============================\n  var handleNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    className: classNames(handlePrefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(handlePrefixCls, \"-\").concat(valueIndex + 1), range), _defineProperty(_classNames, \"\".concat(handlePrefixCls, \"-dragging\"), dragging), _classNames)),\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove,\n    onKeyDown: onKeyDown,\n    tabIndex: disabled ? null : getIndex(tabIndex, valueIndex),\n    role: \"slider\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": value,\n    \"aria-disabled\": disabled,\n    \"aria-label\": getIndex(ariaLabelForHandle, valueIndex),\n    \"aria-labelledby\": getIndex(ariaLabelledByForHandle, valueIndex),\n    \"aria-valuetext\": (_getIndex = getIndex(ariaValueTextFormatterForHandle, valueIndex)) === null || _getIndex === void 0 ? void 0 : _getIndex(value)\n  }, restProps));\n  // Customize\n  if (render) {\n    handleNode = render(handleNode, {\n      index: valueIndex,\n      prefixCls: prefixCls,\n      value: value,\n      dragging: dragging\n    });\n  }\n  return handleNode;\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handle.displayName = 'Handle';\n}\nexport default Handle;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_defineProperty", "_objectWithoutProperties", "_excluded", "React", "classNames", "KeyCode", "SliderContext", "getDirectionStyle", "getIndex", "<PERSON><PERSON>", "forwardRef", "props", "ref", "_classNames", "_getIndex", "prefixCls", "value", "valueIndex", "onStartMove", "style", "render", "dragging", "onOffsetChange", "restProps", "_React$useContext", "useContext", "min", "max", "direction", "disabled", "keyboard", "range", "tabIndex", "ariaLabelFor<PERSON>andle", "ariaLabelledByForHandle", "ariaValueTextFormatterForHandle", "handlePrefixCls", "concat", "onInternalStartMove", "e", "onKeyDown", "offset", "which", "keyCode", "LEFT", "RIGHT", "UP", "DOWN", "HOME", "END", "PAGE_UP", "PAGE_DOWN", "preventDefault", "positionStyle", "handleNode", "createElement", "className", "onMouseDown", "onTouchStart", "role", "index", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-slider/es/Handles/Handle.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"value\", \"valueIndex\", \"onStartMove\", \"style\", \"render\", \"dragging\", \"onOffsetChange\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport SliderContext from '../context';\nimport { getDirectionStyle, getIndex } from '../util';\nvar Handle = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames, _getIndex;\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    valueIndex = props.valueIndex,\n    onStartMove = props.onStartMove,\n    style = props.style,\n    render = props.render,\n    dragging = props.dragging,\n    onOffsetChange = props.onOffsetChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    disabled = _React$useContext.disabled,\n    keyboard = _React$useContext.keyboard,\n    range = _React$useContext.range,\n    tabIndex = _React$useContext.tabIndex,\n    ariaLabelForHandle = _React$useContext.ariaLabelForHandle,\n    ariaLabelledByForHandle = _React$useContext.ariaLabelledByForHandle,\n    ariaValueTextFormatterForHandle = _React$useContext.ariaValueTextFormatterForHandle;\n  var handlePrefixCls = \"\".concat(prefixCls, \"-handle\");\n  // ============================ Events ============================\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled) {\n      onStartMove(e, valueIndex);\n    }\n  };\n  // =========================== Keyboard ===========================\n  var onKeyDown = function onKeyDown(e) {\n    if (!disabled && keyboard) {\n      var offset = null;\n      // Change the value\n      switch (e.which || e.keyCode) {\n        case KeyCode.LEFT:\n          offset = direction === 'ltr' || direction === 'btt' ? -1 : 1;\n          break;\n        case KeyCode.RIGHT:\n          offset = direction === 'ltr' || direction === 'btt' ? 1 : -1;\n          break;\n        // Up is plus\n        case KeyCode.UP:\n          offset = direction !== 'ttb' ? 1 : -1;\n          break;\n        // Down is minus\n        case KeyCode.DOWN:\n          offset = direction !== 'ttb' ? -1 : 1;\n          break;\n        case KeyCode.HOME:\n          offset = 'min';\n          break;\n        case KeyCode.END:\n          offset = 'max';\n          break;\n        case KeyCode.PAGE_UP:\n          offset = 2;\n          break;\n        case KeyCode.PAGE_DOWN:\n          offset = -2;\n          break;\n      }\n      if (offset !== null) {\n        e.preventDefault();\n        onOffsetChange(offset, valueIndex);\n      }\n    }\n  };\n  // ============================ Offset ============================\n  var positionStyle = getDirectionStyle(direction, value, min, max);\n  // ============================ Render ============================\n  var handleNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    className: classNames(handlePrefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(handlePrefixCls, \"-\").concat(valueIndex + 1), range), _defineProperty(_classNames, \"\".concat(handlePrefixCls, \"-dragging\"), dragging), _classNames)),\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove,\n    onKeyDown: onKeyDown,\n    tabIndex: disabled ? null : getIndex(tabIndex, valueIndex),\n    role: \"slider\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": value,\n    \"aria-disabled\": disabled,\n    \"aria-label\": getIndex(ariaLabelForHandle, valueIndex),\n    \"aria-labelledby\": getIndex(ariaLabelledByForHandle, valueIndex),\n    \"aria-valuetext\": (_getIndex = getIndex(ariaValueTextFormatterForHandle, valueIndex)) === null || _getIndex === void 0 ? void 0 : _getIndex(value)\n  }, restProps));\n  // Customize\n  if (render) {\n    handleNode = render(handleNode, {\n      index: valueIndex,\n      prefixCls: prefixCls,\n      value: value,\n      dragging: dragging\n    });\n  }\n  return handleNode;\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handle.displayName = 'Handle';\n}\nexport default Handle;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,CAAC;AACpH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,aAAa,MAAM,YAAY;AACtC,SAASC,iBAAiB,EAAEC,QAAQ,QAAQ,SAAS;AACrD,IAAIC,MAAM,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC/D,IAAIC,WAAW,EAAEC,SAAS;EAC1B,IAAIC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC7BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,MAAM,GAAGT,KAAK,CAACS,MAAM;IACrBC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,cAAc,GAAGX,KAAK,CAACW,cAAc;IACrCC,SAAS,GAAGtB,wBAAwB,CAACU,KAAK,EAAET,SAAS,CAAC;EACxD,IAAIsB,iBAAiB,GAAGrB,KAAK,CAACsB,UAAU,CAACnB,aAAa,CAAC;IACrDoB,GAAG,GAAGF,iBAAiB,CAACE,GAAG;IAC3BC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,SAAS,GAAGJ,iBAAiB,CAACI,SAAS;IACvCC,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACrCC,QAAQ,GAAGN,iBAAiB,CAACM,QAAQ;IACrCC,KAAK,GAAGP,iBAAiB,CAACO,KAAK;IAC/BC,QAAQ,GAAGR,iBAAiB,CAACQ,QAAQ;IACrCC,kBAAkB,GAAGT,iBAAiB,CAACS,kBAAkB;IACzDC,uBAAuB,GAAGV,iBAAiB,CAACU,uBAAuB;IACnEC,+BAA+B,GAAGX,iBAAiB,CAACW,+BAA+B;EACrF,IAAIC,eAAe,GAAG,EAAE,CAACC,MAAM,CAACtB,SAAS,EAAE,SAAS,CAAC;EACrD;EACA,IAAIuB,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,CAAC,EAAE;IACxD,IAAI,CAACV,QAAQ,EAAE;MACbX,WAAW,CAACqB,CAAC,EAAEtB,UAAU,CAAC;IAC5B;EACF,CAAC;EACD;EACA,IAAIuB,SAAS,GAAG,SAASA,SAASA,CAACD,CAAC,EAAE;IACpC,IAAI,CAACV,QAAQ,IAAIC,QAAQ,EAAE;MACzB,IAAIW,MAAM,GAAG,IAAI;MACjB;MACA,QAAQF,CAAC,CAACG,KAAK,IAAIH,CAAC,CAACI,OAAO;QAC1B,KAAKtC,OAAO,CAACuC,IAAI;UACfH,MAAM,GAAGb,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;UAC5D;QACF,KAAKvB,OAAO,CAACwC,KAAK;UAChBJ,MAAM,GAAGb,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;UAC5D;QACF;QACA,KAAKvB,OAAO,CAACyC,EAAE;UACbL,MAAM,GAAGb,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;UACrC;QACF;QACA,KAAKvB,OAAO,CAAC0C,IAAI;UACfN,MAAM,GAAGb,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;UACrC;QACF,KAAKvB,OAAO,CAAC2C,IAAI;UACfP,MAAM,GAAG,KAAK;UACd;QACF,KAAKpC,OAAO,CAAC4C,GAAG;UACdR,MAAM,GAAG,KAAK;UACd;QACF,KAAKpC,OAAO,CAAC6C,OAAO;UAClBT,MAAM,GAAG,CAAC;UACV;QACF,KAAKpC,OAAO,CAAC8C,SAAS;UACpBV,MAAM,GAAG,CAAC,CAAC;UACX;MACJ;MACA,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnBF,CAAC,CAACa,cAAc,CAAC,CAAC;QAClB9B,cAAc,CAACmB,MAAM,EAAExB,UAAU,CAAC;MACpC;IACF;EACF,CAAC;EACD;EACA,IAAIoC,aAAa,GAAG9C,iBAAiB,CAACqB,SAAS,EAAEZ,KAAK,EAAEU,GAAG,EAAEC,GAAG,CAAC;EACjE;EACA,IAAI2B,UAAU,GAAG,aAAanD,KAAK,CAACoD,aAAa,CAAC,KAAK,EAAEzD,QAAQ,CAAC;IAChEc,GAAG,EAAEA,GAAG;IACR4C,SAAS,EAAEpD,UAAU,CAACgC,eAAe,GAAGvB,WAAW,GAAG,CAAC,CAAC,EAAEb,eAAe,CAACa,WAAW,EAAE,EAAE,CAACwB,MAAM,CAACD,eAAe,EAAE,GAAG,CAAC,CAACC,MAAM,CAACpB,UAAU,GAAG,CAAC,CAAC,EAAEc,KAAK,CAAC,EAAE/B,eAAe,CAACa,WAAW,EAAE,EAAE,CAACwB,MAAM,CAACD,eAAe,EAAE,WAAW,CAAC,EAAEf,QAAQ,CAAC,EAAER,WAAW,CAAC,CAAC;IACrPM,KAAK,EAAEpB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsD,aAAa,CAAC,EAAElC,KAAK,CAAC;IAC7DsC,WAAW,EAAEnB,mBAAmB;IAChCoB,YAAY,EAAEpB,mBAAmB;IACjCE,SAAS,EAAEA,SAAS;IACpBR,QAAQ,EAAEH,QAAQ,GAAG,IAAI,GAAGrB,QAAQ,CAACwB,QAAQ,EAAEf,UAAU,CAAC;IAC1D0C,IAAI,EAAE,QAAQ;IACd,eAAe,EAAEjC,GAAG;IACpB,eAAe,EAAEC,GAAG;IACpB,eAAe,EAAEX,KAAK;IACtB,eAAe,EAAEa,QAAQ;IACzB,YAAY,EAAErB,QAAQ,CAACyB,kBAAkB,EAAEhB,UAAU,CAAC;IACtD,iBAAiB,EAAET,QAAQ,CAAC0B,uBAAuB,EAAEjB,UAAU,CAAC;IAChE,gBAAgB,EAAE,CAACH,SAAS,GAAGN,QAAQ,CAAC2B,+BAA+B,EAAElB,UAAU,CAAC,MAAM,IAAI,IAAIH,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,KAAK;EACnJ,CAAC,EAAEO,SAAS,CAAC,CAAC;EACd;EACA,IAAIH,MAAM,EAAE;IACVkC,UAAU,GAAGlC,MAAM,CAACkC,UAAU,EAAE;MAC9BM,KAAK,EAAE3C,UAAU;MACjBF,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEA,KAAK;MACZK,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ;EACA,OAAOiC,UAAU;AACnB,CAAC,CAAC;AACF,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCtD,MAAM,CAACuD,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAevD,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}