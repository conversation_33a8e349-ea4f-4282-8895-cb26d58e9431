{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport classNames from 'classnames';\n/**\n * Fill component to provided the scroll content real height.\n */\nvar Filler = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var height = _ref.height,\n    offset = _ref.offset,\n    children = _ref.children,\n    prefixCls = _ref.prefixCls,\n    onInnerResize = _ref.onInnerResize,\n    innerProps = _ref.innerProps;\n  var outerStyle = {};\n  var innerStyle = {\n    display: 'flex',\n    flexDirection: 'column'\n  };\n  if (offset !== undefined) {\n    outerStyle = {\n      height: height,\n      position: 'relative',\n      overflow: 'hidden'\n    };\n    innerStyle = _objectSpread(_objectSpread({}, innerStyle), {}, {\n      transform: \"translateY(\".concat(offset, \"px)\"),\n      position: 'absolute',\n      left: 0,\n      right: 0,\n      top: 0\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: outerStyle\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: function onResize(_ref2) {\n      var offsetHeight = _ref2.offsetHeight;\n      if (offsetHeight && onInnerResize) {\n        onInnerResize();\n      }\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    style: innerStyle,\n    className: classNames(_defineProperty({}, \"\".concat(prefixCls, \"-holder-inner\"), prefixCls)),\n    ref: ref\n  }, innerProps), children)));\n});\nFiller.displayName = 'Filler';\nexport default Filler;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "React", "ResizeObserver", "classNames", "Filler", "forwardRef", "_ref", "ref", "height", "offset", "children", "prefixCls", "onInnerResize", "innerProps", "outerStyle", "innerStyle", "display", "flexDirection", "undefined", "position", "overflow", "transform", "concat", "left", "right", "top", "createElement", "style", "onResize", "_ref2", "offsetHeight", "className", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-virtual-list/es/Filler.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport classNames from 'classnames';\n/**\n * Fill component to provided the scroll content real height.\n */\nvar Filler = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var height = _ref.height,\n    offset = _ref.offset,\n    children = _ref.children,\n    prefixCls = _ref.prefixCls,\n    onInnerResize = _ref.onInnerResize,\n    innerProps = _ref.innerProps;\n  var outerStyle = {};\n  var innerStyle = {\n    display: 'flex',\n    flexDirection: 'column'\n  };\n  if (offset !== undefined) {\n    outerStyle = {\n      height: height,\n      position: 'relative',\n      overflow: 'hidden'\n    };\n    innerStyle = _objectSpread(_objectSpread({}, innerStyle), {}, {\n      transform: \"translateY(\".concat(offset, \"px)\"),\n      position: 'absolute',\n      left: 0,\n      right: 0,\n      top: 0\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: outerStyle\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: function onResize(_ref2) {\n      var offsetHeight = _ref2.offsetHeight;\n      if (offsetHeight && onInnerResize) {\n        onInnerResize();\n      }\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    style: innerStyle,\n    className: classNames(_defineProperty({}, \"\".concat(prefixCls, \"-holder-inner\"), prefixCls)),\n    ref: ref\n  }, innerProps), children)));\n});\nFiller.displayName = 'Filler';\nexport default Filler;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,UAAU,MAAM,YAAY;AACnC;AACA;AACA;AACA,IAAIC,MAAM,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;EAC9D,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACtBC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;IACxBC,SAAS,GAAGL,IAAI,CAACK,SAAS;IAC1BC,aAAa,GAAGN,IAAI,CAACM,aAAa;IAClCC,UAAU,GAAGP,IAAI,CAACO,UAAU;EAC9B,IAAIC,UAAU,GAAG,CAAC,CAAC;EACnB,IAAIC,UAAU,GAAG;IACfC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE;EACjB,CAAC;EACD,IAAIR,MAAM,KAAKS,SAAS,EAAE;IACxBJ,UAAU,GAAG;MACXN,MAAM,EAAEA,MAAM;MACdW,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;IACZ,CAAC;IACDL,UAAU,GAAGf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5DM,SAAS,EAAE,aAAa,CAACC,MAAM,CAACb,MAAM,EAAE,KAAK,CAAC;MAC9CU,QAAQ,EAAE,UAAU;MACpBI,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ;EACA,OAAO,aAAaxB,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;IAC7CC,KAAK,EAAEb;EACT,CAAC,EAAE,aAAab,KAAK,CAACyB,aAAa,CAACxB,cAAc,EAAE;IAClD0B,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;MACjC,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;MACrC,IAAIA,YAAY,IAAIlB,aAAa,EAAE;QACjCA,aAAa,CAAC,CAAC;MACjB;IACF;EACF,CAAC,EAAE,aAAaX,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE5B,QAAQ,CAAC;IAClD6B,KAAK,EAAEZ,UAAU;IACjBgB,SAAS,EAAE5B,UAAU,CAACJ,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuB,MAAM,CAACX,SAAS,EAAE,eAAe,CAAC,EAAEA,SAAS,CAAC,CAAC;IAC5FJ,GAAG,EAAEA;EACP,CAAC,EAAEM,UAAU,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC;AAC7B,CAAC,CAAC;AACFN,MAAM,CAAC4B,WAAW,GAAG,QAAQ;AAC7B,eAAe5B,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}