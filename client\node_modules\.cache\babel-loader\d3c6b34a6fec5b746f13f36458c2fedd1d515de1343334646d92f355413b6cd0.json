{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"b\"],\n  _excluded2 = [\"v\"];\nimport { TinyColor } from '@ctrl/tinycolor';\nexport var getRoundNumber = function getRoundNumber(value) {\n  return Math.round(Number(value || 0));\n};\nvar convertHsb2Hsv = function convertHsb2Hsv(color) {\n  if (color && _typeof(color) === 'object' && 'h' in color && 'b' in color) {\n    var _ref = color,\n      b = _ref.b,\n      resets = _objectWithoutProperties(_ref, _excluded);\n    return _objectSpread(_objectSpread({}, resets), {}, {\n      v: b\n    });\n  }\n  if (typeof color === 'string' && /hsb/.test(color)) {\n    return color.replace(/hsb/, 'hsv');\n  }\n  return color;\n};\nexport var Color = /*#__PURE__*/function (_TinyColor) {\n  _inherits(Color, _TinyColor);\n  var _super = _createSuper(Color);\n  function Color(color) {\n    _classCallCheck(this, Color);\n    return _super.call(this, convertHsb2Hsv(color));\n  }\n  _createClass(Color, [{\n    key: \"toHsbString\",\n    value: function toHsbString() {\n      var hsb = this.toHsb();\n      var saturation = getRoundNumber(hsb.s * 100);\n      var lightness = getRoundNumber(hsb.b * 100);\n      var hue = getRoundNumber(hsb.h);\n      var alpha = hsb.a;\n      var hsbString = \"hsb(\".concat(hue, \", \").concat(saturation, \"%, \").concat(lightness, \"%)\");\n      var hsbaString = \"hsba(\".concat(hue, \", \").concat(saturation, \"%, \").concat(lightness, \"%, \").concat(alpha.toFixed(alpha === 0 ? 0 : 2), \")\");\n      return alpha === 1 ? hsbString : hsbaString;\n    }\n  }, {\n    key: \"toHsb\",\n    value: function toHsb() {\n      var hsv = this.toHsv();\n      if (_typeof(this.originalInput) === 'object' && this.originalInput) {\n        if ('h' in this.originalInput) {\n          hsv = this.originalInput;\n        }\n      }\n      var _hsv = hsv,\n        v = _hsv.v,\n        resets = _objectWithoutProperties(_hsv, _excluded2);\n      return _objectSpread(_objectSpread({}, resets), {}, {\n        b: hsv.v\n      });\n    }\n  }]);\n  return Color;\n}(TinyColor);", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_inherits", "_createSuper", "_objectSpread", "_objectWithoutProperties", "_typeof", "_excluded", "_excluded2", "TinyColor", "getRoundNumber", "value", "Math", "round", "Number", "convertHsb2Hsv", "color", "_ref", "b", "resets", "v", "test", "replace", "Color", "_TinyColor", "_super", "call", "key", "toHsbString", "hsb", "toHsb", "saturation", "s", "lightness", "hue", "h", "alpha", "a", "hsbString", "concat", "hsbaString", "toFixed", "hsv", "toHsv", "originalInput", "_hsv"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/color-picker/es/color.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"b\"],\n  _excluded2 = [\"v\"];\nimport { TinyColor } from '@ctrl/tinycolor';\nexport var getRoundNumber = function getRoundNumber(value) {\n  return Math.round(Number(value || 0));\n};\nvar convertHsb2Hsv = function convertHsb2Hsv(color) {\n  if (color && _typeof(color) === 'object' && 'h' in color && 'b' in color) {\n    var _ref = color,\n      b = _ref.b,\n      resets = _objectWithoutProperties(_ref, _excluded);\n    return _objectSpread(_objectSpread({}, resets), {}, {\n      v: b\n    });\n  }\n  if (typeof color === 'string' && /hsb/.test(color)) {\n    return color.replace(/hsb/, 'hsv');\n  }\n  return color;\n};\nexport var Color = /*#__PURE__*/function (_TinyColor) {\n  _inherits(Color, _TinyColor);\n  var _super = _createSuper(Color);\n  function Color(color) {\n    _classCallCheck(this, Color);\n    return _super.call(this, convertHsb2Hsv(color));\n  }\n  _createClass(Color, [{\n    key: \"toHsbString\",\n    value: function toHsbString() {\n      var hsb = this.toHsb();\n      var saturation = getRoundNumber(hsb.s * 100);\n      var lightness = getRoundNumber(hsb.b * 100);\n      var hue = getRoundNumber(hsb.h);\n      var alpha = hsb.a;\n      var hsbString = \"hsb(\".concat(hue, \", \").concat(saturation, \"%, \").concat(lightness, \"%)\");\n      var hsbaString = \"hsba(\".concat(hue, \", \").concat(saturation, \"%, \").concat(lightness, \"%, \").concat(alpha.toFixed(alpha === 0 ? 0 : 2), \")\");\n      return alpha === 1 ? hsbString : hsbaString;\n    }\n  }, {\n    key: \"toHsb\",\n    value: function toHsb() {\n      var hsv = this.toHsv();\n      if (_typeof(this.originalInput) === 'object' && this.originalInput) {\n        if ('h' in this.originalInput) {\n          hsv = this.originalInput;\n        }\n      }\n      var _hsv = hsv,\n        v = _hsv.v,\n        resets = _objectWithoutProperties(_hsv, _excluded2);\n      return _objectSpread(_objectSpread({}, resets), {}, {\n        b: hsv.v\n      });\n    }\n  }]);\n  return Color;\n}(TinyColor);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,SAAS,GAAG,CAAC,GAAG,CAAC;EACnBC,UAAU,GAAG,CAAC,GAAG,CAAC;AACpB,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EACzD,OAAOC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACH,KAAK,IAAI,CAAC,CAAC,CAAC;AACvC,CAAC;AACD,IAAII,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIA,KAAK,IAAIV,OAAO,CAACU,KAAK,CAAC,KAAK,QAAQ,IAAI,GAAG,IAAIA,KAAK,IAAI,GAAG,IAAIA,KAAK,EAAE;IACxE,IAAIC,IAAI,GAAGD,KAAK;MACdE,CAAC,GAAGD,IAAI,CAACC,CAAC;MACVC,MAAM,GAAGd,wBAAwB,CAACY,IAAI,EAAEV,SAAS,CAAC;IACpD,OAAOH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MAClDC,CAAC,EAAEF;IACL,CAAC,CAAC;EACJ;EACA,IAAI,OAAOF,KAAK,KAAK,QAAQ,IAAI,KAAK,CAACK,IAAI,CAACL,KAAK,CAAC,EAAE;IAClD,OAAOA,KAAK,CAACM,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;EACpC;EACA,OAAON,KAAK;AACd,CAAC;AACD,OAAO,IAAIO,KAAK,GAAG,aAAa,UAAUC,UAAU,EAAE;EACpDtB,SAAS,CAACqB,KAAK,EAAEC,UAAU,CAAC;EAC5B,IAAIC,MAAM,GAAGtB,YAAY,CAACoB,KAAK,CAAC;EAChC,SAASA,KAAKA,CAACP,KAAK,EAAE;IACpBhB,eAAe,CAAC,IAAI,EAAEuB,KAAK,CAAC;IAC5B,OAAOE,MAAM,CAACC,IAAI,CAAC,IAAI,EAAEX,cAAc,CAACC,KAAK,CAAC,CAAC;EACjD;EACAf,YAAY,CAACsB,KAAK,EAAE,CAAC;IACnBI,GAAG,EAAE,aAAa;IAClBhB,KAAK,EAAE,SAASiB,WAAWA,CAAA,EAAG;MAC5B,IAAIC,GAAG,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;MACtB,IAAIC,UAAU,GAAGrB,cAAc,CAACmB,GAAG,CAACG,CAAC,GAAG,GAAG,CAAC;MAC5C,IAAIC,SAAS,GAAGvB,cAAc,CAACmB,GAAG,CAACX,CAAC,GAAG,GAAG,CAAC;MAC3C,IAAIgB,GAAG,GAAGxB,cAAc,CAACmB,GAAG,CAACM,CAAC,CAAC;MAC/B,IAAIC,KAAK,GAAGP,GAAG,CAACQ,CAAC;MACjB,IAAIC,SAAS,GAAG,MAAM,CAACC,MAAM,CAACL,GAAG,EAAE,IAAI,CAAC,CAACK,MAAM,CAACR,UAAU,EAAE,KAAK,CAAC,CAACQ,MAAM,CAACN,SAAS,EAAE,IAAI,CAAC;MAC1F,IAAIO,UAAU,GAAG,OAAO,CAACD,MAAM,CAACL,GAAG,EAAE,IAAI,CAAC,CAACK,MAAM,CAACR,UAAU,EAAE,KAAK,CAAC,CAACQ,MAAM,CAACN,SAAS,EAAE,KAAK,CAAC,CAACM,MAAM,CAACH,KAAK,CAACK,OAAO,CAACL,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;MAC7I,OAAOA,KAAK,KAAK,CAAC,GAAGE,SAAS,GAAGE,UAAU;IAC7C;EACF,CAAC,EAAE;IACDb,GAAG,EAAE,OAAO;IACZhB,KAAK,EAAE,SAASmB,KAAKA,CAAA,EAAG;MACtB,IAAIY,GAAG,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;MACtB,IAAIrC,OAAO,CAAC,IAAI,CAACsC,aAAa,CAAC,KAAK,QAAQ,IAAI,IAAI,CAACA,aAAa,EAAE;QAClE,IAAI,GAAG,IAAI,IAAI,CAACA,aAAa,EAAE;UAC7BF,GAAG,GAAG,IAAI,CAACE,aAAa;QAC1B;MACF;MACA,IAAIC,IAAI,GAAGH,GAAG;QACZtB,CAAC,GAAGyB,IAAI,CAACzB,CAAC;QACVD,MAAM,GAAGd,wBAAwB,CAACwC,IAAI,EAAErC,UAAU,CAAC;MACrD,OAAOJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAClDD,CAAC,EAAEwB,GAAG,CAACtB;MACT,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;EACH,OAAOG,KAAK;AACd,CAAC,CAACd,SAAS,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}