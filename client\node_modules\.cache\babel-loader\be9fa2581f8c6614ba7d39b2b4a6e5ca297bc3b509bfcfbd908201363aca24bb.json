{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useMergedState } from 'rc-util';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\n\n/**\n * 1. Click input to show picker\n * 2. Calculate next open index\n *\n * If click `confirm`:\n * 3. Hide current picker\n * 4. Open next index picker if exist\n *\n * If not `changeOnBlur` and click outside:\n * 3. Hide picker\n *\n * If `changeOnBlur` and click outside:\n * 3. Hide current picker\n * 4. Open next index picker if exist\n */\n\n/**\n * Auto control of open state\n */\nexport default function useRangeOpen(defaultOpen, open, activePickerIndex, changeOnBlur, startInputRef, endInputRef, startSelectedValue, endSelectedValue, disabled, onOpenChange) {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    firstTimeOpen = _React$useState2[0],\n    setFirstTimeOpen = _React$useState2[1];\n  var _useMergedState = useMergedState(defaultOpen || false, {\n      value: open\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    afferentOpen = _useMergedState2[0],\n    setAfferentOpen = _useMergedState2[1];\n  var _useMergedState3 = useMergedState(defaultOpen || false, {\n      value: open,\n      onChange: function onChange(nextOpen) {\n        onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(nextOpen);\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedOpen = _useMergedState4[0],\n    setMergedOpen = _useMergedState4[1];\n  var _useMergedState5 = useMergedState(0, {\n      value: activePickerIndex\n    }),\n    _useMergedState6 = _slicedToArray(_useMergedState5, 2),\n    mergedActivePickerIndex = _useMergedState6[0],\n    setMergedActivePickerIndex = _useMergedState6[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    nextActiveIndex = _React$useState4[0],\n    setNextActiveIndex = _React$useState4[1];\n  React.useEffect(function () {\n    if (mergedOpen) {\n      setFirstTimeOpen(true);\n    }\n  }, [mergedOpen]);\n  var queryNextIndex = function queryNextIndex(index) {\n    return index === 0 ? 1 : 0;\n  };\n  var triggerOpen = useEvent(function (nextOpen, index, source) {\n    if (index === false) {\n      // Only when `nextOpen` is false and no need open to next index\n      setMergedOpen(nextOpen);\n    } else if (nextOpen) {\n      setMergedActivePickerIndex(index);\n      setMergedOpen(nextOpen);\n      var nextIndex = queryNextIndex(index);\n\n      // Record next open index\n      if (!mergedOpen ||\n      // Also set next index if next is empty\n      ![startSelectedValue, endSelectedValue][nextIndex]) {\n        setNextActiveIndex(nextIndex);\n      } else {\n        setFirstTimeOpen(false);\n        if (nextActiveIndex !== null) {\n          setNextActiveIndex(null);\n        }\n      }\n    } else if (source === 'confirm' || source === 'blur' && changeOnBlur) {\n      var customNextActiveIndex = afferentOpen ? queryNextIndex(index) : nextActiveIndex;\n      if (customNextActiveIndex !== null) {\n        setFirstTimeOpen(false);\n        setMergedActivePickerIndex(customNextActiveIndex);\n      }\n      setNextActiveIndex(null);\n\n      // Focus back\n      if (customNextActiveIndex !== null && !disabled[customNextActiveIndex]) {\n        raf(function () {\n          var _ref$current;\n          var ref = [startInputRef, endInputRef][customNextActiveIndex];\n          (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.focus();\n        });\n      } else {\n        setMergedOpen(false);\n      }\n    } else {\n      setMergedOpen(false);\n      setAfferentOpen(false);\n    }\n  });\n  return [mergedOpen, mergedActivePickerIndex, firstTimeOpen, triggerOpen];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useMergedState", "useEvent", "raf", "React", "useRangeOpen", "defaultOpen", "open", "activePickerIndex", "changeOnBlur", "startInputRef", "endInputRef", "startSelectedValue", "endSelectedValue", "disabled", "onOpenChange", "_React$useState", "useState", "_React$useState2", "firstTimeOpen", "setFirstTimeOpen", "_useMergedState", "value", "_useMergedState2", "afferentOpen", "setAfferentOpen", "_useMergedState3", "onChange", "nextOpen", "_useMergedState4", "mergedOpen", "setMergedOpen", "_useMergedState5", "_useMergedState6", "mergedActivePickerIndex", "setMergedActivePickerIndex", "_React$useState3", "_React$useState4", "nextActiveIndex", "setNextActiveIndex", "useEffect", "queryNextIndex", "index", "triggerOpen", "source", "nextIndex", "customNextActiveIndex", "_ref$current", "ref", "current", "focus"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/hooks/useRangeOpen.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useMergedState } from 'rc-util';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\n\n/**\n * 1. Click input to show picker\n * 2. Calculate next open index\n *\n * If click `confirm`:\n * 3. Hide current picker\n * 4. Open next index picker if exist\n *\n * If not `changeOnBlur` and click outside:\n * 3. Hide picker\n *\n * If `changeOnBlur` and click outside:\n * 3. Hide current picker\n * 4. Open next index picker if exist\n */\n\n/**\n * Auto control of open state\n */\nexport default function useRangeOpen(defaultOpen, open, activePickerIndex, changeOnBlur, startInputRef, endInputRef, startSelectedValue, endSelectedValue, disabled, onOpenChange) {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    firstTimeOpen = _React$useState2[0],\n    setFirstTimeOpen = _React$useState2[1];\n  var _useMergedState = useMergedState(defaultOpen || false, {\n      value: open\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    afferentOpen = _useMergedState2[0],\n    setAfferentOpen = _useMergedState2[1];\n  var _useMergedState3 = useMergedState(defaultOpen || false, {\n      value: open,\n      onChange: function onChange(nextOpen) {\n        onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(nextOpen);\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedOpen = _useMergedState4[0],\n    setMergedOpen = _useMergedState4[1];\n  var _useMergedState5 = useMergedState(0, {\n      value: activePickerIndex\n    }),\n    _useMergedState6 = _slicedToArray(_useMergedState5, 2),\n    mergedActivePickerIndex = _useMergedState6[0],\n    setMergedActivePickerIndex = _useMergedState6[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    nextActiveIndex = _React$useState4[0],\n    setNextActiveIndex = _React$useState4[1];\n  React.useEffect(function () {\n    if (mergedOpen) {\n      setFirstTimeOpen(true);\n    }\n  }, [mergedOpen]);\n  var queryNextIndex = function queryNextIndex(index) {\n    return index === 0 ? 1 : 0;\n  };\n  var triggerOpen = useEvent(function (nextOpen, index, source) {\n    if (index === false) {\n      // Only when `nextOpen` is false and no need open to next index\n      setMergedOpen(nextOpen);\n    } else if (nextOpen) {\n      setMergedActivePickerIndex(index);\n      setMergedOpen(nextOpen);\n      var nextIndex = queryNextIndex(index);\n\n      // Record next open index\n      if (!mergedOpen ||\n      // Also set next index if next is empty\n      ![startSelectedValue, endSelectedValue][nextIndex]) {\n        setNextActiveIndex(nextIndex);\n      } else {\n        setFirstTimeOpen(false);\n        if (nextActiveIndex !== null) {\n          setNextActiveIndex(null);\n        }\n      }\n    } else if (source === 'confirm' || source === 'blur' && changeOnBlur) {\n      var customNextActiveIndex = afferentOpen ? queryNextIndex(index) : nextActiveIndex;\n      if (customNextActiveIndex !== null) {\n        setFirstTimeOpen(false);\n        setMergedActivePickerIndex(customNextActiveIndex);\n      }\n      setNextActiveIndex(null);\n\n      // Focus back\n      if (customNextActiveIndex !== null && !disabled[customNextActiveIndex]) {\n        raf(function () {\n          var _ref$current;\n          var ref = [startInputRef, endInputRef][customNextActiveIndex];\n          (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.focus();\n        });\n      } else {\n        setMergedOpen(false);\n      }\n    } else {\n      setMergedOpen(false);\n      setAfferentOpen(false);\n    }\n  });\n  return [mergedOpen, mergedActivePickerIndex, firstTimeOpen, triggerOpen];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,cAAc,QAAQ,SAAS;AACxC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,WAAW,EAAEC,IAAI,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EACjL,IAAIC,eAAe,GAAGZ,KAAK,CAACa,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGlB,cAAc,CAACgB,eAAe,EAAE,CAAC,CAAC;IACrDG,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIG,eAAe,GAAGpB,cAAc,CAACK,WAAW,IAAI,KAAK,EAAE;MACvDgB,KAAK,EAAEf;IACT,CAAC,CAAC;IACFgB,gBAAgB,GAAGvB,cAAc,CAACqB,eAAe,EAAE,CAAC,CAAC;IACrDG,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIG,gBAAgB,GAAGzB,cAAc,CAACK,WAAW,IAAI,KAAK,EAAE;MACxDgB,KAAK,EAAEf,IAAI;MACXoB,QAAQ,EAAE,SAASA,QAAQA,CAACC,QAAQ,EAAE;QACpCb,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACa,QAAQ,CAAC;MACpF;IACF,CAAC,CAAC;IACFC,gBAAgB,GAAG7B,cAAc,CAAC0B,gBAAgB,EAAE,CAAC,CAAC;IACtDI,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,gBAAgB,GAAG/B,cAAc,CAAC,CAAC,EAAE;MACrCqB,KAAK,EAAEd;IACT,CAAC,CAAC;IACFyB,gBAAgB,GAAGjC,cAAc,CAACgC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,uBAAuB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7CE,0BAA0B,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClD,IAAIG,gBAAgB,GAAGhC,KAAK,CAACa,QAAQ,CAAC,IAAI,CAAC;IACzCoB,gBAAgB,GAAGrC,cAAc,CAACoC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1CjC,KAAK,CAACoC,SAAS,CAAC,YAAY;IAC1B,IAAIV,UAAU,EAAE;MACdV,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC,EAAE,CAACU,UAAU,CAAC,CAAC;EAChB,IAAIW,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;IAClD,OAAOA,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;EAC5B,CAAC;EACD,IAAIC,WAAW,GAAGzC,QAAQ,CAAC,UAAU0B,QAAQ,EAAEc,KAAK,EAAEE,MAAM,EAAE;IAC5D,IAAIF,KAAK,KAAK,KAAK,EAAE;MACnB;MACAX,aAAa,CAACH,QAAQ,CAAC;IACzB,CAAC,MAAM,IAAIA,QAAQ,EAAE;MACnBO,0BAA0B,CAACO,KAAK,CAAC;MACjCX,aAAa,CAACH,QAAQ,CAAC;MACvB,IAAIiB,SAAS,GAAGJ,cAAc,CAACC,KAAK,CAAC;;MAErC;MACA,IAAI,CAACZ,UAAU;MACf;MACA,CAAC,CAAClB,kBAAkB,EAAEC,gBAAgB,CAAC,CAACgC,SAAS,CAAC,EAAE;QAClDN,kBAAkB,CAACM,SAAS,CAAC;MAC/B,CAAC,MAAM;QACLzB,gBAAgB,CAAC,KAAK,CAAC;QACvB,IAAIkB,eAAe,KAAK,IAAI,EAAE;UAC5BC,kBAAkB,CAAC,IAAI,CAAC;QAC1B;MACF;IACF,CAAC,MAAM,IAAIK,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,MAAM,IAAInC,YAAY,EAAE;MACpE,IAAIqC,qBAAqB,GAAGtB,YAAY,GAAGiB,cAAc,CAACC,KAAK,CAAC,GAAGJ,eAAe;MAClF,IAAIQ,qBAAqB,KAAK,IAAI,EAAE;QAClC1B,gBAAgB,CAAC,KAAK,CAAC;QACvBe,0BAA0B,CAACW,qBAAqB,CAAC;MACnD;MACAP,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,IAAIO,qBAAqB,KAAK,IAAI,IAAI,CAAChC,QAAQ,CAACgC,qBAAqB,CAAC,EAAE;QACtE3C,GAAG,CAAC,YAAY;UACd,IAAI4C,YAAY;UAChB,IAAIC,GAAG,GAAG,CAACtC,aAAa,EAAEC,WAAW,CAAC,CAACmC,qBAAqB,CAAC;UAC7D,CAACC,YAAY,GAAGC,GAAG,CAACC,OAAO,MAAM,IAAI,IAAIF,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACG,KAAK,CAAC,CAAC;QAClG,CAAC,CAAC;MACJ,CAAC,MAAM;QACLnB,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC,MAAM;MACLA,aAAa,CAAC,KAAK,CAAC;MACpBN,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,CAAC;EACF,OAAO,CAACK,UAAU,EAAEI,uBAAuB,EAAEf,aAAa,EAAEwB,WAAW,CAAC;AAC1E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}