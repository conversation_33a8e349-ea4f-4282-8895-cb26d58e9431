{"ast": null, "code": "import React, { forwardRef, useMemo } from 'react';\nimport { composeRef, supportRef } from \"rc-util/es/ref\";\nvar Overlay = /*#__PURE__*/forwardRef(function (props, ref) {\n  var overlay = props.overlay,\n    arrow = props.arrow,\n    prefixCls = props.prefixCls;\n  var overlayNode = useMemo(function () {\n    var overlayElement;\n    if (typeof overlay === 'function') {\n      overlayElement = overlay();\n    } else {\n      overlayElement = overlay;\n    }\n    return overlayElement;\n  }, [overlay]);\n  var composedRef = composeRef(ref, overlayNode === null || overlayNode === void 0 ? void 0 : overlayNode.ref);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, arrow && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-arrow\")\n  }), /*#__PURE__*/React.cloneElement(overlayNode, {\n    ref: supportRef(overlayNode) ? composedRef : undefined\n  }));\n});\nexport default Overlay;", "map": {"version": 3, "names": ["React", "forwardRef", "useMemo", "composeRef", "supportRef", "Overlay", "props", "ref", "overlay", "arrow", "prefixCls", "overlayNode", "overlayElement", "composedRef", "createElement", "Fragment", "className", "concat", "cloneElement", "undefined"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-dropdown/es/Overlay.js"], "sourcesContent": ["import React, { forwardRef, useMemo } from 'react';\nimport { composeRef, supportRef } from \"rc-util/es/ref\";\nvar Overlay = /*#__PURE__*/forwardRef(function (props, ref) {\n  var overlay = props.overlay,\n    arrow = props.arrow,\n    prefixCls = props.prefixCls;\n  var overlayNode = useMemo(function () {\n    var overlayElement;\n    if (typeof overlay === 'function') {\n      overlayElement = overlay();\n    } else {\n      overlayElement = overlay;\n    }\n    return overlayElement;\n  }, [overlay]);\n  var composedRef = composeRef(ref, overlayNode === null || overlayNode === void 0 ? void 0 : overlayNode.ref);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, arrow && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-arrow\")\n  }), /*#__PURE__*/React.cloneElement(overlayNode, {\n    ref: supportRef(overlayNode) ? composedRef : undefined\n  }));\n});\nexport default Overlay;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,UAAU,QAAQ,gBAAgB;AACvD,IAAIC,OAAO,GAAG,aAAaJ,UAAU,CAAC,UAAUK,KAAK,EAAEC,GAAG,EAAE;EAC1D,IAAIC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACzBC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;EAC7B,IAAIC,WAAW,GAAGT,OAAO,CAAC,YAAY;IACpC,IAAIU,cAAc;IAClB,IAAI,OAAOJ,OAAO,KAAK,UAAU,EAAE;MACjCI,cAAc,GAAGJ,OAAO,CAAC,CAAC;IAC5B,CAAC,MAAM;MACLI,cAAc,GAAGJ,OAAO;IAC1B;IACA,OAAOI,cAAc;EACvB,CAAC,EAAE,CAACJ,OAAO,CAAC,CAAC;EACb,IAAIK,WAAW,GAAGV,UAAU,CAACI,GAAG,EAAEI,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACJ,GAAG,CAAC;EAC5G,OAAO,aAAaP,KAAK,CAACc,aAAa,CAACd,KAAK,CAACe,QAAQ,EAAE,IAAI,EAAEN,KAAK,IAAI,aAAaT,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IAC7GE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACP,SAAS,EAAE,QAAQ;EAC1C,CAAC,CAAC,EAAE,aAAaV,KAAK,CAACkB,YAAY,CAACP,WAAW,EAAE;IAC/CJ,GAAG,EAAEH,UAAU,CAACO,WAAW,CAAC,GAAGE,WAAW,GAAGM;EAC/C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAed,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}