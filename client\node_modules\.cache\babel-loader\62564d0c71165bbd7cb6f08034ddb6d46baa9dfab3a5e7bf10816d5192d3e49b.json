{"ast": null, "code": "const genSelectionStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSizeIcon,\n    padding,\n    paddingXS,\n    tableHeaderIconColor,\n    tableHeaderIconColorHover,\n    tableSelectionColumnWidth\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Selections ==========================\n      [`${componentCls}-selection-col`]: {\n        width: tableSelectionColumnWidth,\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: tableSelectionColumnWidth + fontSizeIcon + padding / 4\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-selection-col`]: {\n        width: tableSelectionColumnWidth + paddingXS * 2,\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: tableSelectionColumnWidth + fontSizeIcon + padding / 4 + paddingXS * 2\n        }\n      },\n      [`\n        table tr th${componentCls}-selection-column,\n        table tr td${componentCls}-selection-column\n      `]: {\n        paddingInlineEnd: token.paddingXS,\n        paddingInlineStart: token.paddingXS,\n        textAlign: 'center',\n        [`${antCls}-radio-wrapper`]: {\n          marginInlineEnd: 0\n        }\n      },\n      [`table tr th${componentCls}-selection-column${componentCls}-cell-fix-left`]: {\n        zIndex: token.zIndexTableFixed + 1\n      },\n      [`table tr th${componentCls}-selection-column::after`]: {\n        backgroundColor: 'transparent !important'\n      },\n      [`${componentCls}-selection`]: {\n        position: 'relative',\n        display: 'inline-flex',\n        flexDirection: 'column'\n      },\n      [`${componentCls}-selection-extra`]: {\n        position: 'absolute',\n        top: 0,\n        zIndex: 1,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        marginInlineStart: '100%',\n        paddingInlineStart: `${token.tablePaddingHorizontal / 4}px`,\n        [iconCls]: {\n          color: tableHeaderIconColor,\n          fontSize: fontSizeIcon,\n          verticalAlign: 'baseline',\n          '&:hover': {\n            color: tableHeaderIconColorHover\n          }\n        }\n      }\n    }\n  };\n};\nexport default genSelectionStyle;", "map": {"version": 3, "names": ["genSelectionStyle", "token", "componentCls", "antCls", "iconCls", "fontSizeIcon", "padding", "paddingXS", "tableHeaderIconColor", "tableHeaderIconColorHover", "tableSelectionColumnWidth", "width", "paddingInlineEnd", "paddingInlineStart", "textAlign", "marginInlineEnd", "zIndex", "zIndexTableFixed", "backgroundColor", "position", "display", "flexDirection", "top", "cursor", "transition", "motionDurationSlow", "marginInlineStart", "tablePaddingHorizontal", "color", "fontSize", "verticalAlign"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/selection.js"], "sourcesContent": ["const genSelectionStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSizeIcon,\n    padding,\n    paddingXS,\n    tableHeaderIconColor,\n    tableHeaderIconColorHover,\n    tableSelectionColumnWidth\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Selections ==========================\n      [`${componentCls}-selection-col`]: {\n        width: tableSelectionColumnWidth,\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: tableSelectionColumnWidth + fontSizeIcon + padding / 4\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-selection-col`]: {\n        width: tableSelectionColumnWidth + paddingXS * 2,\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: tableSelectionColumnWidth + fontSizeIcon + padding / 4 + paddingXS * 2\n        }\n      },\n      [`\n        table tr th${componentCls}-selection-column,\n        table tr td${componentCls}-selection-column\n      `]: {\n        paddingInlineEnd: token.paddingXS,\n        paddingInlineStart: token.paddingXS,\n        textAlign: 'center',\n        [`${antCls}-radio-wrapper`]: {\n          marginInlineEnd: 0\n        }\n      },\n      [`table tr th${componentCls}-selection-column${componentCls}-cell-fix-left`]: {\n        zIndex: token.zIndexTableFixed + 1\n      },\n      [`table tr th${componentCls}-selection-column::after`]: {\n        backgroundColor: 'transparent !important'\n      },\n      [`${componentCls}-selection`]: {\n        position: 'relative',\n        display: 'inline-flex',\n        flexDirection: 'column'\n      },\n      [`${componentCls}-selection-extra`]: {\n        position: 'absolute',\n        top: 0,\n        zIndex: 1,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        marginInlineStart: '100%',\n        paddingInlineStart: `${token.tablePaddingHorizontal / 4}px`,\n        [iconCls]: {\n          color: tableHeaderIconColor,\n          fontSize: fontSizeIcon,\n          verticalAlign: 'baseline',\n          '&:hover': {\n            color: tableHeaderIconColorHover\n          }\n        }\n      }\n    }\n  };\n};\nexport default genSelectionStyle;"], "mappings": "AAAA,MAAMA,iBAAiB,GAAGC,KAAK,IAAI;EACjC,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,OAAO;IACPC,YAAY;IACZC,OAAO;IACPC,SAAS;IACTC,oBAAoB;IACpBC,yBAAyB;IACzBC;EACF,CAAC,GAAGT,KAAK;EACT,OAAO;IACL,CAAE,GAAEC,YAAa,UAAS,GAAG;MAC3B;MACA,CAAE,GAAEA,YAAa,gBAAe,GAAG;QACjCS,KAAK,EAAED,yBAAyB;QAChC,CAAE,IAAGR,YAAa,8BAA6B,GAAG;UAChDS,KAAK,EAAED,yBAAyB,GAAGL,YAAY,GAAGC,OAAO,GAAG;QAC9D;MACF,CAAC;MACD,CAAE,GAAEJ,YAAa,aAAYA,YAAa,gBAAe,GAAG;QAC1DS,KAAK,EAAED,yBAAyB,GAAGH,SAAS,GAAG,CAAC;QAChD,CAAE,IAAGL,YAAa,8BAA6B,GAAG;UAChDS,KAAK,EAAED,yBAAyB,GAAGL,YAAY,GAAGC,OAAO,GAAG,CAAC,GAAGC,SAAS,GAAG;QAC9E;MACF,CAAC;MACD,CAAE;AACR,qBAAqBL,YAAa;AAClC,qBAAqBA,YAAa;AAClC,OAAO,GAAG;QACFU,gBAAgB,EAAEX,KAAK,CAACM,SAAS;QACjCM,kBAAkB,EAAEZ,KAAK,CAACM,SAAS;QACnCO,SAAS,EAAE,QAAQ;QACnB,CAAE,GAAEX,MAAO,gBAAe,GAAG;UAC3BY,eAAe,EAAE;QACnB;MACF,CAAC;MACD,CAAE,cAAab,YAAa,oBAAmBA,YAAa,gBAAe,GAAG;QAC5Ec,MAAM,EAAEf,KAAK,CAACgB,gBAAgB,GAAG;MACnC,CAAC;MACD,CAAE,cAAaf,YAAa,0BAAyB,GAAG;QACtDgB,eAAe,EAAE;MACnB,CAAC;MACD,CAAE,GAAEhB,YAAa,YAAW,GAAG;QAC7BiB,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,aAAa;QACtBC,aAAa,EAAE;MACjB,CAAC;MACD,CAAE,GAAEnB,YAAa,kBAAiB,GAAG;QACnCiB,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNN,MAAM,EAAE,CAAC;QACTO,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAG,OAAMvB,KAAK,CAACwB,kBAAmB,EAAC;QAC7CC,iBAAiB,EAAE,MAAM;QACzBb,kBAAkB,EAAG,GAAEZ,KAAK,CAAC0B,sBAAsB,GAAG,CAAE,IAAG;QAC3D,CAACvB,OAAO,GAAG;UACTwB,KAAK,EAAEpB,oBAAoB;UAC3BqB,QAAQ,EAAExB,YAAY;UACtByB,aAAa,EAAE,UAAU;UACzB,SAAS,EAAE;YACTF,KAAK,EAAEnB;UACT;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeT,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}