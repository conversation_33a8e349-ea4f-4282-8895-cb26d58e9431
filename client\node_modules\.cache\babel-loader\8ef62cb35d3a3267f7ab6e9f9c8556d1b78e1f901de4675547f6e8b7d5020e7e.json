{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React from \"react\";\nexport function getClearIcon(prefixCls, allowClear, clearIcon) {\n  var mergedClearIcon = _typeof(allowClear) === \"object\" ? allowClear.clearIcon : clearIcon;\n  return mergedClearIcon || /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-clear-btn\")\n  });\n}", "map": {"version": 3, "names": ["_typeof", "React", "getClearIcon", "prefixCls", "allowClear", "clearIcon", "mergedClearIcon", "createElement", "className", "concat"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/utils/getClearIcon.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React from \"react\";\nexport function getClearIcon(prefixCls, allowClear, clearIcon) {\n  var mergedClearIcon = _typeof(allowClear) === \"object\" ? allowClear.clearIcon : clearIcon;\n  return mergedClearIcon || /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-clear-btn\")\n  });\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,SAASC,YAAYA,CAACC,SAAS,EAAEC,UAAU,EAAEC,SAAS,EAAE;EAC7D,IAAIC,eAAe,GAAGN,OAAO,CAACI,UAAU,CAAC,KAAK,QAAQ,GAAGA,UAAU,CAACC,SAAS,GAAGA,SAAS;EACzF,OAAOC,eAAe,IAAI,aAAaL,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IACjEC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,YAAY;EAC9C,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}