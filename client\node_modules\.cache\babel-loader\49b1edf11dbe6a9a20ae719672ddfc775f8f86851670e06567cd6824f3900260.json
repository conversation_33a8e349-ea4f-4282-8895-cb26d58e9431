{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\LazyImage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LazyImage = ({\n  src,\n  alt,\n  className = '',\n  placeholder = null,\n  fallback = null,\n  onLoad = () => {},\n  onError = () => {},\n  ...props\n}) => {\n  _s();\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [isInView, setIsInView] = useState(false);\n  const [hasError, setHasError] = useState(false);\n  const imgRef = useRef();\n  useEffect(() => {\n    const observer = new IntersectionObserver(([entry]) => {\n      if (entry.isIntersecting) {\n        setIsInView(true);\n        observer.disconnect();\n      }\n    }, {\n      threshold: 0.1\n    });\n    if (imgRef.current) {\n      observer.observe(imgRef.current);\n    }\n    return () => observer.disconnect();\n  }, []);\n  const handleLoad = () => {\n    setIsLoaded(true);\n    onLoad();\n  };\n  const handleError = () => {\n    setHasError(true);\n    onError();\n  };\n  const defaultPlaceholder = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-full bg-gray-200 animate-pulse flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-8 h-8 text-gray-400\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 20 20\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n        clipRule: \"evenodd\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n  const defaultFallback = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-full bg-gray-100 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-8 h-8 text-gray-400\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 20 20\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n        clipRule: \"evenodd\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: imgRef,\n    className: `relative overflow-hidden ${className}`,\n    ...props,\n    children: [!isInView && (placeholder || defaultPlaceholder), isInView && !hasError && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [!isLoaded && (placeholder || defaultPlaceholder), /*#__PURE__*/_jsxDEV(motion.img, {\n        src: src,\n        alt: alt,\n        className: `absolute inset-0 w-full h-full object-cover transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`,\n        onLoad: handleLoad,\n        onError: handleError,\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: isLoaded ? 1 : 0\n        },\n        transition: {\n          duration: 0.3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), hasError && (fallback || defaultFallback)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(LazyImage, \"lD2QpFOlG/O2pb/qx+bApBIkHaM=\");\n_c = LazyImage;\nexport default LazyImage;\nvar _c;\n$RefreshReg$(_c, \"LazyImage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LazyImage", "src", "alt", "className", "placeholder", "fallback", "onLoad", "onError", "props", "_s", "isLoaded", "setIsLoaded", "isInView", "setIsInView", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "imgRef", "observer", "IntersectionObserver", "entry", "isIntersecting", "disconnect", "threshold", "current", "observe", "handleLoad", "handleError", "defaultPlaceholder", "children", "fill", "viewBox", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "defaultFallback", "ref", "img", "initial", "opacity", "animate", "transition", "duration", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/LazyImage.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\nconst LazyImage = ({\n  src,\n  alt,\n  className = '',\n  placeholder = null,\n  fallback = null,\n  onLoad = () => {},\n  onError = () => {},\n  ...props\n}) => {\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [isInView, setIsInView] = useState(false);\n  const [hasError, setHasError] = useState(false);\n  const imgRef = useRef();\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsInView(true);\n          observer.disconnect();\n        }\n      },\n      { threshold: 0.1 }\n    );\n\n    if (imgRef.current) {\n      observer.observe(imgRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  const handleLoad = () => {\n    setIsLoaded(true);\n    onLoad();\n  };\n\n  const handleError = () => {\n    setHasError(true);\n    onError();\n  };\n\n  const defaultPlaceholder = (\n    <div className=\"w-full h-full bg-gray-200 animate-pulse flex items-center justify-center\">\n      <svg className=\"w-8 h-8 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n        <path fillRule=\"evenodd\" d=\"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\" clipRule=\"evenodd\" />\n      </svg>\n    </div>\n  );\n\n  const defaultFallback = (\n    <div className=\"w-full h-full bg-gray-100 flex items-center justify-center\">\n      <svg className=\"w-8 h-8 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n        <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n      </svg>\n    </div>\n  );\n\n  return (\n    <div ref={imgRef} className={`relative overflow-hidden ${className}`} {...props}>\n      {!isInView && (placeholder || defaultPlaceholder)}\n      \n      {isInView && !hasError && (\n        <>\n          {!isLoaded && (placeholder || defaultPlaceholder)}\n          <motion.img\n            src={src}\n            alt={alt}\n            className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-300 ${\n              isLoaded ? 'opacity-100' : 'opacity-0'\n            }`}\n            onLoad={handleLoad}\n            onError={handleError}\n            initial={{ opacity: 0 }}\n            animate={{ opacity: isLoaded ? 1 : 0 }}\n            transition={{ duration: 0.3 }}\n          />\n        </>\n      )}\n      \n      {hasError && (fallback || defaultFallback)}\n    </div>\n  );\n};\n\nexport default LazyImage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,SAAS,GAAGA,CAAC;EACjBC,GAAG;EACHC,GAAG;EACHC,SAAS,GAAG,EAAE;EACdC,WAAW,GAAG,IAAI;EAClBC,QAAQ,GAAG,IAAI;EACfC,MAAM,GAAGA,CAAA,KAAM,CAAC,CAAC;EACjBC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClB,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMwB,MAAM,GAAGvB,MAAM,CAAC,CAAC;EAEvBC,SAAS,CAAC,MAAM;IACd,MAAMuB,QAAQ,GAAG,IAAIC,oBAAoB,CACvC,CAAC,CAACC,KAAK,CAAC,KAAK;MACX,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxBP,WAAW,CAAC,IAAI,CAAC;QACjBI,QAAQ,CAACI,UAAU,CAAC,CAAC;MACvB;IACF,CAAC,EACD;MAAEC,SAAS,EAAE;IAAI,CACnB,CAAC;IAED,IAAIN,MAAM,CAACO,OAAO,EAAE;MAClBN,QAAQ,CAACO,OAAO,CAACR,MAAM,CAACO,OAAO,CAAC;IAClC;IAEA,OAAO,MAAMN,QAAQ,CAACI,UAAU,CAAC,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvBd,WAAW,CAAC,IAAI,CAAC;IACjBL,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAMoB,WAAW,GAAGA,CAAA,KAAM;IACxBX,WAAW,CAAC,IAAI,CAAC;IACjBR,OAAO,CAAC,CAAC;EACX,CAAC;EAED,MAAMoB,kBAAkB,gBACtB9B,OAAA;IAAKM,SAAS,EAAC,0EAA0E;IAAAyB,QAAA,eACvF/B,OAAA;MAAKM,SAAS,EAAC,uBAAuB;MAAC0B,IAAI,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAF,QAAA,eAC5E/B,OAAA;QAAMkC,QAAQ,EAAC,SAAS;QAACC,CAAC,EAAC,4FAA4F;QAACC,QAAQ,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1I;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMC,eAAe,gBACnBzC,OAAA;IAAKM,SAAS,EAAC,4DAA4D;IAAAyB,QAAA,eACzE/B,OAAA;MAAKM,SAAS,EAAC,uBAAuB;MAAC0B,IAAI,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAF,QAAA,eAC5E/B,OAAA;QAAMkC,QAAQ,EAAC,SAAS;QAACC,CAAC,EAAC,mHAAmH;QAACC,QAAQ,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACExC,OAAA;IAAK0C,GAAG,EAAEvB,MAAO;IAACb,SAAS,EAAG,4BAA2BA,SAAU,EAAE;IAAA,GAAKK,KAAK;IAAAoB,QAAA,GAC5E,CAAChB,QAAQ,KAAKR,WAAW,IAAIuB,kBAAkB,CAAC,EAEhDf,QAAQ,IAAI,CAACE,QAAQ,iBACpBjB,OAAA,CAAAE,SAAA;MAAA6B,QAAA,GACG,CAAClB,QAAQ,KAAKN,WAAW,IAAIuB,kBAAkB,CAAC,eACjD9B,OAAA,CAACF,MAAM,CAAC6C,GAAG;QACTvC,GAAG,EAAEA,GAAI;QACTC,GAAG,EAAEA,GAAI;QACTC,SAAS,EAAG,+EACVO,QAAQ,GAAG,aAAa,GAAG,WAC5B,EAAE;QACHJ,MAAM,EAAEmB,UAAW;QACnBlB,OAAO,EAAEmB,WAAY;QACrBe,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAEhC,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QACvCkC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA,eACF,CACH,EAEAvB,QAAQ,KAAKT,QAAQ,IAAIiC,eAAe,CAAC;EAAA;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAEV,CAAC;AAAC5B,EAAA,CApFIT,SAAS;AAAA8C,EAAA,GAAT9C,SAAS;AAsFf,eAAeA,SAAS;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}