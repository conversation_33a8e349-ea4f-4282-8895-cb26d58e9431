{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport toArray from 'rc-util/es/Children/toArray';\nimport React from 'react';\nimport CollapsePanel from '../Panel';\nvar _excluded = ['children', 'label', 'key', 'collapsible', 'onItemClick', 'destroyInactivePanel'];\nvar convertItemsToNodes = function convertItemsToNodes(items, props) {\n  var prefixCls = props.prefixCls,\n    accordion = props.accordion,\n    collapsible = props.collapsible,\n    destroyInactivePanel = props.destroyInactivePanel,\n    onItemClick = props.onItemClick,\n    activeKey = props.activeKey,\n    openMotion = props.openMotion,\n    expandIcon = props.expandIcon;\n  return items.map(function (item, index) {\n    var children = item.children,\n      label = item.label,\n      rawKey = item.key,\n      rawCollapsible = item.collapsible,\n      rawOnItemClick = item.onItemClick,\n      rawDestroyInactivePanel = item.destroyInactivePanel,\n      restProps = _objectWithoutProperties(item, _excluded);\n\n    // You may be puzzled why you want to convert them all into strings, me too.\n    // Maybe: https://github.com/react-component/collapse/blob/aac303a8b6ff30e35060b4f8fecde6f4556fcbe2/src/Collapse.tsx#L15\n    var key = String(rawKey !== null && rawKey !== void 0 ? rawKey : index);\n    var mergeCollapsible = rawCollapsible !== null && rawCollapsible !== void 0 ? rawCollapsible : collapsible;\n    var mergeDestroyInactivePanel = rawDestroyInactivePanel !== null && rawDestroyInactivePanel !== void 0 ? rawDestroyInactivePanel : destroyInactivePanel;\n    var handleItemClick = function handleItemClick(value) {\n      if (mergeCollapsible === 'disabled') return;\n      onItemClick(value);\n      rawOnItemClick === null || rawOnItemClick === void 0 ? void 0 : rawOnItemClick(value);\n    };\n    var isActive = false;\n    if (accordion) {\n      isActive = activeKey[0] === key;\n    } else {\n      isActive = activeKey.indexOf(key) > -1;\n    }\n    return /*#__PURE__*/React.createElement(CollapsePanel, _extends({}, restProps, {\n      prefixCls: prefixCls,\n      key: key,\n      panelKey: key,\n      isActive: isActive,\n      accordion: accordion,\n      openMotion: openMotion,\n      expandIcon: expandIcon,\n      header: label,\n      collapsible: mergeCollapsible,\n      onItemClick: handleItemClick,\n      destroyInactivePanel: mergeDestroyInactivePanel\n    }), children);\n  });\n};\n\n/**\n * @deprecated The next major version will be removed\n */\nvar getNewChild = function getNewChild(child, index, props) {\n  if (!child) return null;\n  var prefixCls = props.prefixCls,\n    accordion = props.accordion,\n    collapsible = props.collapsible,\n    destroyInactivePanel = props.destroyInactivePanel,\n    onItemClick = props.onItemClick,\n    activeKey = props.activeKey,\n    openMotion = props.openMotion,\n    expandIcon = props.expandIcon;\n  var key = child.key || String(index);\n  var _child$props = child.props,\n    header = _child$props.header,\n    headerClass = _child$props.headerClass,\n    childDestroyInactivePanel = _child$props.destroyInactivePanel,\n    childCollapsible = _child$props.collapsible,\n    childOnItemClick = _child$props.onItemClick;\n  var isActive = false;\n  if (accordion) {\n    isActive = activeKey[0] === key;\n  } else {\n    isActive = activeKey.indexOf(key) > -1;\n  }\n  var mergeCollapsible = childCollapsible !== null && childCollapsible !== void 0 ? childCollapsible : collapsible;\n  var handleItemClick = function handleItemClick(value) {\n    if (mergeCollapsible === 'disabled') return;\n    onItemClick(value);\n    childOnItemClick === null || childOnItemClick === void 0 ? void 0 : childOnItemClick(value);\n  };\n  var childProps = {\n    key: key,\n    panelKey: key,\n    header: header,\n    headerClass: headerClass,\n    isActive: isActive,\n    prefixCls: prefixCls,\n    destroyInactivePanel: childDestroyInactivePanel !== null && childDestroyInactivePanel !== void 0 ? childDestroyInactivePanel : destroyInactivePanel,\n    openMotion: openMotion,\n    accordion: accordion,\n    children: child.props.children,\n    onItemClick: handleItemClick,\n    expandIcon: expandIcon,\n    collapsible: mergeCollapsible\n  };\n\n  // https://github.com/ant-design/ant-design/issues/20479\n  if (typeof child.type === 'string') {\n    return child;\n  }\n  Object.keys(childProps).forEach(function (propName) {\n    if (typeof childProps[propName] === 'undefined') {\n      delete childProps[propName];\n    }\n  });\n  return /*#__PURE__*/React.cloneElement(child, childProps);\n};\nfunction useItems(items, rawChildren, props) {\n  if (Array.isArray(items)) {\n    return convertItemsToNodes(items, props);\n  }\n  return toArray(rawChildren).map(function (child, index) {\n    return getNewChild(child, index, props);\n  });\n}\nexport default useItems;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "toArray", "React", "CollapsePanel", "_excluded", "convertItemsToNodes", "items", "props", "prefixCls", "accordion", "collapsible", "destroyInactivePanel", "onItemClick", "active<PERSON><PERSON>", "openMotion", "expandIcon", "map", "item", "index", "children", "label", "<PERSON><PERSON><PERSON>", "key", "rawCollapsible", "rawOnItemClick", "rawDestroyInactivePanel", "restProps", "String", "mergeCollapsible", "mergeDestroyInactivePanel", "handleItemClick", "value", "isActive", "indexOf", "createElement", "<PERSON><PERSON><PERSON>", "header", "get<PERSON>ew<PERSON><PERSON><PERSON>", "child", "_child$props", "headerClass", "childDestroyInactivePanel", "childCollapsible", "childOnItemClick", "childProps", "type", "Object", "keys", "for<PERSON>ach", "propName", "cloneElement", "useItems", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-collapse/es/hooks/useItems.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport toArray from 'rc-util/es/Children/toArray';\nimport React from 'react';\nimport CollapsePanel from '../Panel';\nvar _excluded = ['children', 'label', 'key', 'collapsible', 'onItemClick', 'destroyInactivePanel'];\nvar convertItemsToNodes = function convertItemsToNodes(items, props) {\n  var prefixCls = props.prefixCls,\n    accordion = props.accordion,\n    collapsible = props.collapsible,\n    destroyInactivePanel = props.destroyInactivePanel,\n    onItemClick = props.onItemClick,\n    activeKey = props.activeKey,\n    openMotion = props.openMotion,\n    expandIcon = props.expandIcon;\n  return items.map(function (item, index) {\n    var children = item.children,\n      label = item.label,\n      rawKey = item.key,\n      rawCollapsible = item.collapsible,\n      rawOnItemClick = item.onItemClick,\n      rawDestroyInactivePanel = item.destroyInactivePanel,\n      restProps = _objectWithoutProperties(item, _excluded);\n\n    // You may be puzzled why you want to convert them all into strings, me too.\n    // Maybe: https://github.com/react-component/collapse/blob/aac303a8b6ff30e35060b4f8fecde6f4556fcbe2/src/Collapse.tsx#L15\n    var key = String(rawKey !== null && rawKey !== void 0 ? rawKey : index);\n    var mergeCollapsible =\n      rawCollapsible !== null && rawCollapsible !== void 0 ? rawCollapsible : collapsible;\n    var mergeDestroyInactivePanel =\n      rawDestroyInactivePanel !== null && rawDestroyInactivePanel !== void 0\n        ? rawDestroyInactivePanel\n        : destroyInactivePanel;\n    var handleItemClick = function handleItemClick(value) {\n      if (mergeCollapsible === 'disabled') return;\n      onItemClick(value);\n      rawOnItemClick === null || rawOnItemClick === void 0 ? void 0 : rawOnItemClick(value);\n    };\n    var isActive = false;\n    if (accordion) {\n      isActive = activeKey[0] === key;\n    } else {\n      isActive = activeKey.indexOf(key) > -1;\n    }\n    return /*#__PURE__*/ React.createElement(\n      CollapsePanel,\n      _extends({}, restProps, {\n        prefixCls: prefixCls,\n        key: key,\n        panelKey: key,\n        isActive: isActive,\n        accordion: accordion,\n        openMotion: openMotion,\n        expandIcon: expandIcon,\n        header: label,\n        collapsible: mergeCollapsible,\n        onItemClick: handleItemClick,\n        destroyInactivePanel: mergeDestroyInactivePanel,\n      }),\n      children,\n    );\n  });\n};\n\n/**\n * @deprecated The next major version will be removed\n */\nvar getNewChild = function getNewChild(child, index, props) {\n  if (!child) return null;\n  var prefixCls = props.prefixCls,\n    accordion = props.accordion,\n    collapsible = props.collapsible,\n    destroyInactivePanel = props.destroyInactivePanel,\n    onItemClick = props.onItemClick,\n    activeKey = props.activeKey,\n    openMotion = props.openMotion,\n    expandIcon = props.expandIcon;\n  var key = child.key || String(index);\n  var _child$props = child.props,\n    header = _child$props.header,\n    headerClass = _child$props.headerClass,\n    childDestroyInactivePanel = _child$props.destroyInactivePanel,\n    childCollapsible = _child$props.collapsible,\n    childOnItemClick = _child$props.onItemClick;\n  var isActive = false;\n  if (accordion) {\n    isActive = activeKey[0] === key;\n  } else {\n    isActive = activeKey.indexOf(key) > -1;\n  }\n  var mergeCollapsible =\n    childCollapsible !== null && childCollapsible !== void 0 ? childCollapsible : collapsible;\n  var handleItemClick = function handleItemClick(value) {\n    if (mergeCollapsible === 'disabled') return;\n    onItemClick(value);\n    childOnItemClick === null || childOnItemClick === void 0 ? void 0 : childOnItemClick(value);\n  };\n  var childProps = {\n    key: key,\n    panelKey: key,\n    header: header,\n    headerClass: headerClass,\n    isActive: isActive,\n    prefixCls: prefixCls,\n    destroyInactivePanel:\n      childDestroyInactivePanel !== null && childDestroyInactivePanel !== void 0\n        ? childDestroyInactivePanel\n        : destroyInactivePanel,\n    openMotion: openMotion,\n    accordion: accordion,\n    children: child.props.children,\n    onItemClick: handleItemClick,\n    expandIcon: expandIcon,\n    collapsible: mergeCollapsible,\n  };\n\n  // https://github.com/ant-design/ant-design/issues/20479\n  if (typeof child.type === 'string') {\n    return child;\n  }\n  Object.keys(childProps).forEach(function (propName) {\n    if (typeof childProps[propName] === 'undefined') {\n      delete childProps[propName];\n    }\n  });\n  return /*#__PURE__*/ React.cloneElement(child, childProps);\n};\nfunction useItems(items, rawChildren, props) {\n  if (Array.isArray(items)) {\n    return convertItemsToNodes(items, props);\n  }\n  return toArray(rawChildren).map(function (child, index) {\n    return getNewChild(child, index, props);\n  });\n}\nexport default useItems;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,UAAU;AACpC,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,sBAAsB,CAAC;AAClG,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACnE,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,oBAAoB,GAAGJ,KAAK,CAACI,oBAAoB;IACjDC,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;EAC/B,OAAOT,KAAK,CAACU,GAAG,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACtC,IAAIC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;MAC1BC,KAAK,GAAGH,IAAI,CAACG,KAAK;MAClBC,MAAM,GAAGJ,IAAI,CAACK,GAAG;MACjBC,cAAc,GAAGN,IAAI,CAACP,WAAW;MACjCc,cAAc,GAAGP,IAAI,CAACL,WAAW;MACjCa,uBAAuB,GAAGR,IAAI,CAACN,oBAAoB;MACnDe,SAAS,GAAG1B,wBAAwB,CAACiB,IAAI,EAAEb,SAAS,CAAC;;IAEvD;IACA;IACA,IAAIkB,GAAG,GAAGK,MAAM,CAACN,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGH,KAAK,CAAC;IACvE,IAAIU,gBAAgB,GAClBL,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGb,WAAW;IACrF,IAAImB,yBAAyB,GAC3BJ,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,GAClEA,uBAAuB,GACvBd,oBAAoB;IAC1B,IAAImB,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;MACpD,IAAIH,gBAAgB,KAAK,UAAU,EAAE;MACrChB,WAAW,CAACmB,KAAK,CAAC;MAClBP,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACO,KAAK,CAAC;IACvF,CAAC;IACD,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIvB,SAAS,EAAE;MACbuB,QAAQ,GAAGnB,SAAS,CAAC,CAAC,CAAC,KAAKS,GAAG;IACjC,CAAC,MAAM;MACLU,QAAQ,GAAGnB,SAAS,CAACoB,OAAO,CAACX,GAAG,CAAC,GAAG,CAAC,CAAC;IACxC;IACA,OAAO,aAAcpB,KAAK,CAACgC,aAAa,CACtC/B,aAAa,EACbJ,QAAQ,CAAC,CAAC,CAAC,EAAE2B,SAAS,EAAE;MACtBlB,SAAS,EAAEA,SAAS;MACpBc,GAAG,EAAEA,GAAG;MACRa,QAAQ,EAAEb,GAAG;MACbU,QAAQ,EAAEA,QAAQ;MAClBvB,SAAS,EAAEA,SAAS;MACpBK,UAAU,EAAEA,UAAU;MACtBC,UAAU,EAAEA,UAAU;MACtBqB,MAAM,EAAEhB,KAAK;MACbV,WAAW,EAAEkB,gBAAgB;MAC7BhB,WAAW,EAAEkB,eAAe;MAC5BnB,oBAAoB,EAAEkB;IACxB,CAAC,CAAC,EACFV,QACF,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAIkB,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEpB,KAAK,EAAEX,KAAK,EAAE;EAC1D,IAAI,CAAC+B,KAAK,EAAE,OAAO,IAAI;EACvB,IAAI9B,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,oBAAoB,GAAGJ,KAAK,CAACI,oBAAoB;IACjDC,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;EAC/B,IAAIO,GAAG,GAAGgB,KAAK,CAAChB,GAAG,IAAIK,MAAM,CAACT,KAAK,CAAC;EACpC,IAAIqB,YAAY,GAAGD,KAAK,CAAC/B,KAAK;IAC5B6B,MAAM,GAAGG,YAAY,CAACH,MAAM;IAC5BI,WAAW,GAAGD,YAAY,CAACC,WAAW;IACtCC,yBAAyB,GAAGF,YAAY,CAAC5B,oBAAoB;IAC7D+B,gBAAgB,GAAGH,YAAY,CAAC7B,WAAW;IAC3CiC,gBAAgB,GAAGJ,YAAY,CAAC3B,WAAW;EAC7C,IAAIoB,QAAQ,GAAG,KAAK;EACpB,IAAIvB,SAAS,EAAE;IACbuB,QAAQ,GAAGnB,SAAS,CAAC,CAAC,CAAC,KAAKS,GAAG;EACjC,CAAC,MAAM;IACLU,QAAQ,GAAGnB,SAAS,CAACoB,OAAO,CAACX,GAAG,CAAC,GAAG,CAAC,CAAC;EACxC;EACA,IAAIM,gBAAgB,GAClBc,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGhC,WAAW;EAC3F,IAAIoB,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;IACpD,IAAIH,gBAAgB,KAAK,UAAU,EAAE;IACrChB,WAAW,CAACmB,KAAK,CAAC;IAClBY,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACZ,KAAK,CAAC;EAC7F,CAAC;EACD,IAAIa,UAAU,GAAG;IACftB,GAAG,EAAEA,GAAG;IACRa,QAAQ,EAAEb,GAAG;IACbc,MAAM,EAAEA,MAAM;IACdI,WAAW,EAAEA,WAAW;IACxBR,QAAQ,EAAEA,QAAQ;IAClBxB,SAAS,EAAEA,SAAS;IACpBG,oBAAoB,EAClB8B,yBAAyB,KAAK,IAAI,IAAIA,yBAAyB,KAAK,KAAK,CAAC,GACtEA,yBAAyB,GACzB9B,oBAAoB;IAC1BG,UAAU,EAAEA,UAAU;IACtBL,SAAS,EAAEA,SAAS;IACpBU,QAAQ,EAAEmB,KAAK,CAAC/B,KAAK,CAACY,QAAQ;IAC9BP,WAAW,EAAEkB,eAAe;IAC5Bf,UAAU,EAAEA,UAAU;IACtBL,WAAW,EAAEkB;EACf,CAAC;;EAED;EACA,IAAI,OAAOU,KAAK,CAACO,IAAI,KAAK,QAAQ,EAAE;IAClC,OAAOP,KAAK;EACd;EACAQ,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,OAAO,CAAC,UAAUC,QAAQ,EAAE;IAClD,IAAI,OAAOL,UAAU,CAACK,QAAQ,CAAC,KAAK,WAAW,EAAE;MAC/C,OAAOL,UAAU,CAACK,QAAQ,CAAC;IAC7B;EACF,CAAC,CAAC;EACF,OAAO,aAAc/C,KAAK,CAACgD,YAAY,CAACZ,KAAK,EAAEM,UAAU,CAAC;AAC5D,CAAC;AACD,SAASO,QAAQA,CAAC7C,KAAK,EAAE8C,WAAW,EAAE7C,KAAK,EAAE;EAC3C,IAAI8C,KAAK,CAACC,OAAO,CAAChD,KAAK,CAAC,EAAE;IACxB,OAAOD,mBAAmB,CAACC,KAAK,EAAEC,KAAK,CAAC;EAC1C;EACA,OAAON,OAAO,CAACmD,WAAW,CAAC,CAACpC,GAAG,CAAC,UAAUsB,KAAK,EAAEpB,KAAK,EAAE;IACtD,OAAOmB,WAAW,CAACC,KAAK,EAAEpB,KAAK,EAAEX,KAAK,CAAC;EACzC,CAAC,CAAC;AACJ;AACA,eAAe4C,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}