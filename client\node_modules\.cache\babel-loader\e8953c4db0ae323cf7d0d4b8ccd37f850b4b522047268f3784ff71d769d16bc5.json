{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar SPLIT = '__@field_split__';\n/**\n * Convert name path into string to fast the fetch speed of Map.\n */\nfunction normalize(namePath) {\n  return namePath.map(function (cell) {\n    return \"\".concat(_typeof(cell), \":\").concat(cell);\n  })\n  // Magic split\n  .join(SPLIT);\n}\n/**\n * NameMap like a `Map` but accepts `string[]` as key.\n */\nvar NameMap = /*#__PURE__*/function () {\n  function NameMap() {\n    _classCallCheck(this, NameMap);\n    this.kvs = new Map();\n  }\n  _createClass(NameMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      this.kvs.set(normalize(key), value);\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.kvs.get(normalize(key));\n    }\n  }, {\n    key: \"update\",\n    value: function update(key, updater) {\n      var origin = this.get(key);\n      var next = updater(origin);\n      if (!next) {\n        this.delete(key);\n      } else {\n        this.set(key, next);\n      }\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete(key) {\n      this.kvs.delete(normalize(key));\n    }\n    // Since we only use this in test, let simply realize this\n  }, {\n    key: \"map\",\n    value: function map(callback) {\n      return _toConsumableArray(this.kvs.entries()).map(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          key = _ref2[0],\n          value = _ref2[1];\n        var cells = key.split(SPLIT);\n        return callback({\n          key: cells.map(function (cell) {\n            var _cell$match = cell.match(/^([^:]*):(.*)$/),\n              _cell$match2 = _slicedToArray(_cell$match, 3),\n              type = _cell$match2[1],\n              unit = _cell$match2[2];\n            return type === 'number' ? Number(unit) : unit;\n          }),\n          value: value\n        });\n      });\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON() {\n      var json = {};\n      this.map(function (_ref3) {\n        var key = _ref3.key,\n          value = _ref3.value;\n        json[key.join('.')] = value;\n        return null;\n      });\n      return json;\n    }\n  }]);\n  return NameMap;\n}();\nexport default NameMap;", "map": {"version": 3, "names": ["_slicedToArray", "_toConsumableArray", "_classCallCheck", "_createClass", "_typeof", "SPLIT", "normalize", "namePath", "map", "cell", "concat", "join", "NameMap", "kvs", "Map", "key", "value", "set", "get", "update", "updater", "origin", "next", "delete", "_delete", "callback", "entries", "_ref", "_ref2", "cells", "split", "_cell$match", "match", "_cell$match2", "type", "unit", "Number", "toJSON", "json", "_ref3"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-field-form/es/utils/NameMap.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar SPLIT = '__@field_split__';\n/**\n * Convert name path into string to fast the fetch speed of Map.\n */\nfunction normalize(namePath) {\n  return namePath.map(function (cell) {\n    return \"\".concat(_typeof(cell), \":\").concat(cell);\n  })\n  // Magic split\n  .join(SPLIT);\n}\n/**\n * NameMap like a `Map` but accepts `string[]` as key.\n */\nvar NameMap = /*#__PURE__*/function () {\n  function NameMap() {\n    _classCallCheck(this, NameMap);\n    this.kvs = new Map();\n  }\n  _createClass(NameMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      this.kvs.set(normalize(key), value);\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.kvs.get(normalize(key));\n    }\n  }, {\n    key: \"update\",\n    value: function update(key, updater) {\n      var origin = this.get(key);\n      var next = updater(origin);\n      if (!next) {\n        this.delete(key);\n      } else {\n        this.set(key, next);\n      }\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete(key) {\n      this.kvs.delete(normalize(key));\n    }\n    // Since we only use this in test, let simply realize this\n  }, {\n    key: \"map\",\n    value: function map(callback) {\n      return _toConsumableArray(this.kvs.entries()).map(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          key = _ref2[0],\n          value = _ref2[1];\n        var cells = key.split(SPLIT);\n        return callback({\n          key: cells.map(function (cell) {\n            var _cell$match = cell.match(/^([^:]*):(.*)$/),\n              _cell$match2 = _slicedToArray(_cell$match, 3),\n              type = _cell$match2[1],\n              unit = _cell$match2[2];\n            return type === 'number' ? Number(unit) : unit;\n          }),\n          value: value\n        });\n      });\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON() {\n      var json = {};\n      this.map(function (_ref3) {\n        var key = _ref3.key,\n          value = _ref3.value;\n        json[key.join('.')] = value;\n        return null;\n      });\n      return json;\n    }\n  }]);\n  return NameMap;\n}();\nexport default NameMap;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,KAAK,GAAG,kBAAkB;AAC9B;AACA;AACA;AACA,SAASC,SAASA,CAACC,QAAQ,EAAE;EAC3B,OAAOA,QAAQ,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;IAClC,OAAO,EAAE,CAACC,MAAM,CAACN,OAAO,CAACK,IAAI,CAAC,EAAE,GAAG,CAAC,CAACC,MAAM,CAACD,IAAI,CAAC;EACnD,CAAC;EACD;EAAA,CACCE,IAAI,CAACN,KAAK,CAAC;AACd;AACA;AACA;AACA;AACA,IAAIO,OAAO,GAAG,aAAa,YAAY;EACrC,SAASA,OAAOA,CAAA,EAAG;IACjBV,eAAe,CAAC,IAAI,EAAEU,OAAO,CAAC;IAC9B,IAAI,CAACC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtB;EACAX,YAAY,CAACS,OAAO,EAAE,CAAC;IACrBG,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASC,GAAGA,CAACF,GAAG,EAAEC,KAAK,EAAE;MAC9B,IAAI,CAACH,GAAG,CAACI,GAAG,CAACX,SAAS,CAACS,GAAG,CAAC,EAAEC,KAAK,CAAC;IACrC;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASE,GAAGA,CAACH,GAAG,EAAE;MACvB,OAAO,IAAI,CAACF,GAAG,CAACK,GAAG,CAACZ,SAAS,CAACS,GAAG,CAAC,CAAC;IACrC;EACF,CAAC,EAAE;IACDA,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASG,MAAMA,CAACJ,GAAG,EAAEK,OAAO,EAAE;MACnC,IAAIC,MAAM,GAAG,IAAI,CAACH,GAAG,CAACH,GAAG,CAAC;MAC1B,IAAIO,IAAI,GAAGF,OAAO,CAACC,MAAM,CAAC;MAC1B,IAAI,CAACC,IAAI,EAAE;QACT,IAAI,CAACC,MAAM,CAACR,GAAG,CAAC;MAClB,CAAC,MAAM;QACL,IAAI,CAACE,GAAG,CAACF,GAAG,EAAEO,IAAI,CAAC;MACrB;IACF;EACF,CAAC,EAAE;IACDP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASQ,OAAOA,CAACT,GAAG,EAAE;MAC3B,IAAI,CAACF,GAAG,CAACU,MAAM,CAACjB,SAAS,CAACS,GAAG,CAAC,CAAC;IACjC;IACA;EACF,CAAC,EAAE;IACDA,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASR,GAAGA,CAACiB,QAAQ,EAAE;MAC5B,OAAOxB,kBAAkB,CAAC,IAAI,CAACY,GAAG,CAACa,OAAO,CAAC,CAAC,CAAC,CAAClB,GAAG,CAAC,UAAUmB,IAAI,EAAE;QAChE,IAAIC,KAAK,GAAG5B,cAAc,CAAC2B,IAAI,EAAE,CAAC,CAAC;UACjCZ,GAAG,GAAGa,KAAK,CAAC,CAAC,CAAC;UACdZ,KAAK,GAAGY,KAAK,CAAC,CAAC,CAAC;QAClB,IAAIC,KAAK,GAAGd,GAAG,CAACe,KAAK,CAACzB,KAAK,CAAC;QAC5B,OAAOoB,QAAQ,CAAC;UACdV,GAAG,EAAEc,KAAK,CAACrB,GAAG,CAAC,UAAUC,IAAI,EAAE;YAC7B,IAAIsB,WAAW,GAAGtB,IAAI,CAACuB,KAAK,CAAC,gBAAgB,CAAC;cAC5CC,YAAY,GAAGjC,cAAc,CAAC+B,WAAW,EAAE,CAAC,CAAC;cAC7CG,IAAI,GAAGD,YAAY,CAAC,CAAC,CAAC;cACtBE,IAAI,GAAGF,YAAY,CAAC,CAAC,CAAC;YACxB,OAAOC,IAAI,KAAK,QAAQ,GAAGE,MAAM,CAACD,IAAI,CAAC,GAAGA,IAAI;UAChD,CAAC,CAAC;UACFnB,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASqB,MAAMA,CAAA,EAAG;MACvB,IAAIC,IAAI,GAAG,CAAC,CAAC;MACb,IAAI,CAAC9B,GAAG,CAAC,UAAU+B,KAAK,EAAE;QACxB,IAAIxB,GAAG,GAAGwB,KAAK,CAACxB,GAAG;UACjBC,KAAK,GAAGuB,KAAK,CAACvB,KAAK;QACrBsB,IAAI,CAACvB,GAAG,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGK,KAAK;QAC3B,OAAO,IAAI;MACb,CAAC,CAAC;MACF,OAAOsB,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAO1B,OAAO;AAChB,CAAC,CAAC,CAAC;AACH,eAAeA,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}