{"ast": null, "code": "import { createContext } from '@rc-component/context';\nvar TableContext = createContext();\nexport default TableContext;", "map": {"version": 3, "names": ["createContext", "TableContext"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/context/TableContext.js"], "sourcesContent": ["import { createContext } from '@rc-component/context';\nvar TableContext = createContext();\nexport default TableContext;"], "mappings": "AAAA,SAASA,aAAa,QAAQ,uBAAuB;AACrD,IAAIC,YAAY,GAAGD,aAAa,CAAC,CAAC;AAClC,eAAeC,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}