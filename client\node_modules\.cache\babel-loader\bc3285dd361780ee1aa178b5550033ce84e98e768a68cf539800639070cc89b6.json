{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { useRef } from 'react';\nimport useColorDrag from \"../hooks/useColorDrag\";\nimport { calculateColor, calculateOffset } from \"../util\";\nimport Handler from \"./Handler\";\nimport Palette from \"./Palette\";\nimport Transform from \"./Transform\";\nvar Picker = function Picker(_ref) {\n  var color = _ref.color,\n    onChange = _ref.onChange,\n    prefixCls = _ref.prefixCls,\n    onChangeComplete = _ref.onChangeComplete,\n    disabled = _ref.disabled;\n  var pickerRef = useRef();\n  var transformRef = useRef();\n  var colorRef = useRef(color);\n  var _useColorDrag = useColorDrag({\n      color: color,\n      containerRef: pickerRef,\n      targetRef: transformRef,\n      calculate: function calculate(containerRef) {\n        return calculateOffset(containerRef, transformRef, color);\n      },\n      onDragChange: function onDragChange(offsetValue) {\n        var calcColor = calculateColor({\n          offset: offsetValue,\n          targetRef: transformRef,\n          containerRef: pickerRef,\n          color: color\n        });\n        colorRef.current = calcColor;\n        onChange(calcColor);\n      },\n      onDragChangeComplete: function onDragChangeComplete() {\n        return onChangeComplete === null || onChangeComplete === void 0 ? void 0 : onChangeComplete(colorRef.current);\n      },\n      disabledDrag: disabled\n    }),\n    _useColorDrag2 = _slicedToArray(_useColorDrag, 2),\n    offset = _useColorDrag2[0],\n    dragStartHandle = _useColorDrag2[1];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: pickerRef,\n    className: \"\".concat(prefixCls, \"-select\"),\n    onMouseDown: dragStartHandle,\n    onTouchStart: dragStartHandle\n  }, /*#__PURE__*/React.createElement(Palette, {\n    prefixCls: prefixCls\n  }, /*#__PURE__*/React.createElement(Transform, {\n    offset: offset,\n    ref: transformRef\n  }, /*#__PURE__*/React.createElement(Handler, {\n    color: color.toRgbString(),\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-saturation\"),\n    style: {\n      backgroundColor: \"hsl(\".concat(color.toHsb().h, \",100%, 50%)\"),\n      backgroundImage: 'linear-gradient(0deg, #000, transparent),linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))'\n    }\n  })));\n};\nexport default Picker;", "map": {"version": 3, "names": ["_slicedToArray", "React", "useRef", "useColorDrag", "calculateColor", "calculateOffset", "Handler", "Palette", "Transform", "Picker", "_ref", "color", "onChange", "prefixCls", "onChangeComplete", "disabled", "pickerRef", "transformRef", "colorRef", "_useColorDrag", "containerRef", "targetRef", "calculate", "onDragChange", "offsetValue", "calcColor", "offset", "current", "onDragChangeComplete", "disabledDrag", "_useColorDrag2", "dragStartHandle", "createElement", "ref", "className", "concat", "onMouseDown", "onTouchStart", "toRgbString", "style", "backgroundColor", "toHsb", "h", "backgroundImage"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/color-picker/es/components/Picker.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { useRef } from 'react';\nimport useColorDrag from \"../hooks/useColorDrag\";\nimport { calculateColor, calculateOffset } from \"../util\";\nimport Handler from \"./Handler\";\nimport Palette from \"./Palette\";\nimport Transform from \"./Transform\";\nvar Picker = function Picker(_ref) {\n  var color = _ref.color,\n    onChange = _ref.onChange,\n    prefixCls = _ref.prefixCls,\n    onChangeComplete = _ref.onChangeComplete,\n    disabled = _ref.disabled;\n  var pickerRef = useRef();\n  var transformRef = useRef();\n  var colorRef = useRef(color);\n  var _useColorDrag = useColorDrag({\n      color: color,\n      containerRef: pickerRef,\n      targetRef: transformRef,\n      calculate: function calculate(containerRef) {\n        return calculateOffset(containerRef, transformRef, color);\n      },\n      onDragChange: function onDragChange(offsetValue) {\n        var calcColor = calculateColor({\n          offset: offsetValue,\n          targetRef: transformRef,\n          containerRef: pickerRef,\n          color: color\n        });\n        colorRef.current = calcColor;\n        onChange(calcColor);\n      },\n      onDragChangeComplete: function onDragChangeComplete() {\n        return onChangeComplete === null || onChangeComplete === void 0 ? void 0 : onChangeComplete(colorRef.current);\n      },\n      disabledDrag: disabled\n    }),\n    _useColorDrag2 = _slicedToArray(_useColorDrag, 2),\n    offset = _useColorDrag2[0],\n    dragStartHandle = _useColorDrag2[1];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: pickerRef,\n    className: \"\".concat(prefixCls, \"-select\"),\n    onMouseDown: dragStartHandle,\n    onTouchStart: dragStartHandle\n  }, /*#__PURE__*/React.createElement(Palette, {\n    prefixCls: prefixCls\n  }, /*#__PURE__*/React.createElement(Transform, {\n    offset: offset,\n    ref: transformRef\n  }, /*#__PURE__*/React.createElement(Handler, {\n    color: color.toRgbString(),\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-saturation\"),\n    style: {\n      backgroundColor: \"hsl(\".concat(color.toHsb().h, \",100%, 50%)\"),\n      backgroundImage: 'linear-gradient(0deg, #000, transparent),linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))'\n    }\n  })));\n};\nexport default Picker;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,cAAc,EAAEC,eAAe,QAAQ,SAAS;AACzD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,SAAS,MAAM,aAAa;AACnC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACjC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACpBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,gBAAgB,GAAGJ,IAAI,CAACI,gBAAgB;IACxCC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;EAC1B,IAAIC,SAAS,GAAGd,MAAM,CAAC,CAAC;EACxB,IAAIe,YAAY,GAAGf,MAAM,CAAC,CAAC;EAC3B,IAAIgB,QAAQ,GAAGhB,MAAM,CAACS,KAAK,CAAC;EAC5B,IAAIQ,aAAa,GAAGhB,YAAY,CAAC;MAC7BQ,KAAK,EAAEA,KAAK;MACZS,YAAY,EAAEJ,SAAS;MACvBK,SAAS,EAAEJ,YAAY;MACvBK,SAAS,EAAE,SAASA,SAASA,CAACF,YAAY,EAAE;QAC1C,OAAOf,eAAe,CAACe,YAAY,EAAEH,YAAY,EAAEN,KAAK,CAAC;MAC3D,CAAC;MACDY,YAAY,EAAE,SAASA,YAAYA,CAACC,WAAW,EAAE;QAC/C,IAAIC,SAAS,GAAGrB,cAAc,CAAC;UAC7BsB,MAAM,EAAEF,WAAW;UACnBH,SAAS,EAAEJ,YAAY;UACvBG,YAAY,EAAEJ,SAAS;UACvBL,KAAK,EAAEA;QACT,CAAC,CAAC;QACFO,QAAQ,CAACS,OAAO,GAAGF,SAAS;QAC5Bb,QAAQ,CAACa,SAAS,CAAC;MACrB,CAAC;MACDG,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;QACpD,OAAOd,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACI,QAAQ,CAACS,OAAO,CAAC;MAC/G,CAAC;MACDE,YAAY,EAAEd;IAChB,CAAC,CAAC;IACFe,cAAc,GAAG9B,cAAc,CAACmB,aAAa,EAAE,CAAC,CAAC;IACjDO,MAAM,GAAGI,cAAc,CAAC,CAAC,CAAC;IAC1BC,eAAe,GAAGD,cAAc,CAAC,CAAC,CAAC;EACrC,OAAO,aAAa7B,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC7CC,GAAG,EAAEjB,SAAS;IACdkB,SAAS,EAAE,EAAE,CAACC,MAAM,CAACtB,SAAS,EAAE,SAAS,CAAC;IAC1CuB,WAAW,EAAEL,eAAe;IAC5BM,YAAY,EAAEN;EAChB,CAAC,EAAE,aAAa9B,KAAK,CAAC+B,aAAa,CAACzB,OAAO,EAAE;IAC3CM,SAAS,EAAEA;EACb,CAAC,EAAE,aAAaZ,KAAK,CAAC+B,aAAa,CAACxB,SAAS,EAAE;IAC7CkB,MAAM,EAAEA,MAAM;IACdO,GAAG,EAAEhB;EACP,CAAC,EAAE,aAAahB,KAAK,CAAC+B,aAAa,CAAC1B,OAAO,EAAE;IAC3CK,KAAK,EAAEA,KAAK,CAAC2B,WAAW,CAAC,CAAC;IAC1BzB,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,EAAE,aAAaZ,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC3CE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACtB,SAAS,EAAE,aAAa,CAAC;IAC9C0B,KAAK,EAAE;MACLC,eAAe,EAAE,MAAM,CAACL,MAAM,CAACxB,KAAK,CAAC8B,KAAK,CAAC,CAAC,CAACC,CAAC,EAAE,aAAa,CAAC;MAC9DC,eAAe,EAAE;IACnB;EACF,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAelC,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}