{"ast": null, "code": "import React, { forwardRef } from 'react';\nvar Transform = /*#__PURE__*/forwardRef(function (props, ref) {\n  var children = props.children,\n    offset = props.offset;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    style: {\n      position: 'absolute',\n      left: offset.x,\n      top: offset.y,\n      zIndex: 1\n    }\n  }, children);\n});\nexport default Transform;", "map": {"version": 3, "names": ["React", "forwardRef", "Transform", "props", "ref", "children", "offset", "createElement", "style", "position", "left", "x", "top", "y", "zIndex"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/color-picker/es/components/Transform.js"], "sourcesContent": ["import React, { forwardRef } from 'react';\nvar Transform = /*#__PURE__*/forwardRef(function (props, ref) {\n  var children = props.children,\n    offset = props.offset;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    style: {\n      position: 'absolute',\n      left: offset.x,\n      top: offset.y,\n      zIndex: 1\n    }\n  }, children);\n});\nexport default Transform;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,IAAIC,SAAS,GAAG,aAAaD,UAAU,CAAC,UAAUE,KAAK,EAAEC,GAAG,EAAE;EAC5D,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,MAAM,GAAGH,KAAK,CAACG,MAAM;EACvB,OAAO,aAAaN,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IAC7CH,GAAG,EAAEA,GAAG;IACRI,KAAK,EAAE;MACLC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAEJ,MAAM,CAACK,CAAC;MACdC,GAAG,EAAEN,MAAM,CAACO,CAAC;MACbC,MAAM,EAAE;IACV;EACF,CAAC,EAAET,QAAQ,CAAC;AACd,CAAC,CAAC;AACF,eAAeH,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}