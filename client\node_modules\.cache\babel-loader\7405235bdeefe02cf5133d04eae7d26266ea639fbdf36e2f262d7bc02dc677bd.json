{"ast": null, "code": "import _defineProperty from '@babel/runtime/helpers/esm/defineProperty';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport KeyCode from 'rc-util/es/KeyCode';\nimport React from 'react';\nimport PanelContent from './PanelContent';\nvar _excluded = ['showArrow', 'headerClass', 'isActive', 'onItemClick', 'forceRender', 'className', 'prefixCls', 'collapsible', 'accordion', 'panelKey', 'extra', 'header', 'expandIcon', 'openMotion', 'destroyInactivePanel', 'children'];\nvar CollapsePanel = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames, _classNames2;\n  var _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow,\n    headerClass = props.headerClass,\n    isActive = props.isActive,\n    onItemClick = props.onItemClick,\n    forceRender = props.forceRender,\n    className = props.className,\n    prefixCls = props.prefixCls,\n    collapsible = props.collapsible,\n    accordion = props.accordion,\n    panelKey = props.panelKey,\n    extra = props.extra,\n    header = props.header,\n    expandIcon = props.expandIcon,\n    openMotion = props.openMotion,\n    destroyInactivePanel = props.destroyInactivePanel,\n    children = props.children,\n    resetProps = _objectWithoutProperties(props, _excluded);\n  var disabled = collapsible === 'disabled';\n  var collapsibleHeader = collapsible === 'header';\n  var collapsibleIcon = collapsible === 'icon';\n  var ifExtraExist = extra !== null && extra !== undefined && typeof extra !== 'boolean';\n  var handleItemClick = function handleItemClick() {\n    onItemClick === null || onItemClick === void 0 ? void 0 : onItemClick(panelKey);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    if (e.key === 'Enter' || e.keyCode === KeyCode.ENTER || e.which === KeyCode.ENTER) {\n      handleItemClick();\n    }\n  };\n\n  // ======================== Icon ========================\n  var iconNode = typeof expandIcon === 'function' ? expandIcon(props) : /*#__PURE__*/React.createElement('i', {\n    className: 'arrow'\n  });\n  if (iconNode) {\n    iconNode = /*#__PURE__*/React.createElement('div', {\n      className: ''.concat(prefixCls, '-expand-icon'),\n      onClick: ['header', 'icon'].includes(collapsible) ? handleItemClick : undefined\n    }, iconNode);\n  }\n  var collapsePanelClassNames = classNames((_classNames = {}, _defineProperty(_classNames, ''.concat(prefixCls, '-item'), true), _defineProperty(_classNames, ''.concat(prefixCls, '-item-active'), isActive), _defineProperty(_classNames, ''.concat(prefixCls, '-item-disabled'), disabled), _classNames), className);\n  var headerClassName = classNames(headerClass, (_classNames2 = {}, _defineProperty(_classNames2, ''.concat(prefixCls, '-header'), true), _defineProperty(_classNames2, ''.concat(prefixCls, '-header-collapsible-only'), collapsibleHeader), _defineProperty(_classNames2, ''.concat(prefixCls, '-icon-collapsible-only'), collapsibleIcon), _classNames2));\n\n  // ======================== HeaderProps ========================\n  var headerProps = {\n    className: headerClassName,\n    'aria-expanded': isActive,\n    'aria-disabled': disabled,\n    onKeyDown: handleKeyDown\n  };\n  if (!collapsibleHeader && !collapsibleIcon) {\n    headerProps.onClick = handleItemClick;\n    headerProps.role = accordion ? 'tab' : 'button';\n    headerProps.tabIndex = disabled ? -1 : 0;\n  }\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement('div', _extends({}, resetProps, {\n    ref: ref,\n    className: collapsePanelClassNames\n  }), /*#__PURE__*/React.createElement('div', headerProps, showArrow && iconNode, /*#__PURE__*/React.createElement('span', {\n    className: ''.concat(prefixCls, '-header-text'),\n    onClick: collapsible === 'header' ? handleItemClick : undefined\n  }, header), ifExtraExist && /*#__PURE__*/React.createElement('div', {\n    className: ''.concat(prefixCls, '-extra')\n  }, extra)), /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: isActive,\n    leavedClassName: ''.concat(prefixCls, '-content-hidden')\n  }, openMotion, {\n    forceRender: forceRender,\n    removeOnLeave: destroyInactivePanel\n  }), function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(PanelContent, {\n      ref: motionRef,\n      prefixCls: prefixCls,\n      className: motionClassName,\n      style: motionStyle,\n      isActive: isActive,\n      forceRender: forceRender,\n      role: accordion ? 'tabpanel' : void 0\n    }, children);\n  }));\n});\nexport default CollapsePanel;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_objectWithoutProperties", "classNames", "CSSMotion", "KeyCode", "React", "PanelContent", "_excluded", "CollapsePanel", "forwardRef", "props", "ref", "_classNames", "_classNames2", "_props$showArrow", "showArrow", "headerClass", "isActive", "onItemClick", "forceRender", "className", "prefixCls", "collapsible", "accordion", "<PERSON><PERSON><PERSON>", "extra", "header", "expandIcon", "openMotion", "destroyInactivePanel", "children", "resetProps", "disabled", "collapsibleHeader", "collapsibleIcon", "ifExtraExist", "undefined", "handleItemClick", "handleKeyDown", "e", "key", "keyCode", "ENTER", "which", "iconNode", "createElement", "concat", "onClick", "includes", "collapsePanelClassNames", "headerClassName", "headerProps", "onKeyDown", "role", "tabIndex", "visible", "leavedClassName", "removeOnLeave", "_ref", "motionRef", "motionClassName", "motionStyle", "style"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-collapse/es/Panel.js"], "sourcesContent": ["import _defineProperty from '@babel/runtime/helpers/esm/defineProperty';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport KeyCode from 'rc-util/es/KeyCode';\nimport React from 'react';\nimport PanelContent from './PanelContent';\nvar _excluded = [\n  'showArrow',\n  'headerClass',\n  'isActive',\n  'onItemClick',\n  'forceRender',\n  'className',\n  'prefixCls',\n  'collapsible',\n  'accordion',\n  'panelKey',\n  'extra',\n  'header',\n  'expandIcon',\n  'openMotion',\n  'destroyInactivePanel',\n  'children',\n];\nvar CollapsePanel = /*#__PURE__*/ React.forwardRef(function (props, ref) {\n  var _classNames, _classNames2;\n  var _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow,\n    headerClass = props.headerClass,\n    isActive = props.isActive,\n    onItemClick = props.onItemClick,\n    forceRender = props.forceRender,\n    className = props.className,\n    prefixCls = props.prefixCls,\n    collapsible = props.collapsible,\n    accordion = props.accordion,\n    panelKey = props.panelKey,\n    extra = props.extra,\n    header = props.header,\n    expandIcon = props.expandIcon,\n    openMotion = props.openMotion,\n    destroyInactivePanel = props.destroyInactivePanel,\n    children = props.children,\n    resetProps = _objectWithoutProperties(props, _excluded);\n  var disabled = collapsible === 'disabled';\n  var collapsibleHeader = collapsible === 'header';\n  var collapsibleIcon = collapsible === 'icon';\n  var ifExtraExist = extra !== null && extra !== undefined && typeof extra !== 'boolean';\n  var handleItemClick = function handleItemClick() {\n    onItemClick === null || onItemClick === void 0 ? void 0 : onItemClick(panelKey);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    if (e.key === 'Enter' || e.keyCode === KeyCode.ENTER || e.which === KeyCode.ENTER) {\n      handleItemClick();\n    }\n  };\n\n  // ======================== Icon ========================\n  var iconNode =\n    typeof expandIcon === 'function'\n      ? expandIcon(props)\n      : /*#__PURE__*/ React.createElement('i', {\n          className: 'arrow',\n        });\n  if (iconNode) {\n    iconNode = /*#__PURE__*/ React.createElement(\n      'div',\n      {\n        className: ''.concat(prefixCls, '-expand-icon'),\n        onClick: ['header', 'icon'].includes(collapsible) ? handleItemClick : undefined,\n      },\n      iconNode,\n    );\n  }\n  var collapsePanelClassNames = classNames(\n    ((_classNames = {}),\n    _defineProperty(_classNames, ''.concat(prefixCls, '-item'), true),\n    _defineProperty(_classNames, ''.concat(prefixCls, '-item-active'), isActive),\n    _defineProperty(_classNames, ''.concat(prefixCls, '-item-disabled'), disabled),\n    _classNames),\n    className,\n  );\n  var headerClassName = classNames(\n    headerClass,\n    ((_classNames2 = {}),\n    _defineProperty(_classNames2, ''.concat(prefixCls, '-header'), true),\n    _defineProperty(\n      _classNames2,\n      ''.concat(prefixCls, '-header-collapsible-only'),\n      collapsibleHeader,\n    ),\n    _defineProperty(_classNames2, ''.concat(prefixCls, '-icon-collapsible-only'), collapsibleIcon),\n    _classNames2),\n  );\n\n  // ======================== HeaderProps ========================\n  var headerProps = {\n    className: headerClassName,\n    'aria-expanded': isActive,\n    'aria-disabled': disabled,\n    onKeyDown: handleKeyDown,\n  };\n  if (!collapsibleHeader && !collapsibleIcon) {\n    headerProps.onClick = handleItemClick;\n    headerProps.role = accordion ? 'tab' : 'button';\n    headerProps.tabIndex = disabled ? -1 : 0;\n  }\n\n  // ======================== Render ========================\n  return /*#__PURE__*/ React.createElement(\n    'div',\n    _extends({}, resetProps, {\n      ref: ref,\n      className: collapsePanelClassNames,\n    }),\n    /*#__PURE__*/ React.createElement(\n      'div',\n      headerProps,\n      showArrow && iconNode,\n      /*#__PURE__*/ React.createElement(\n        'span',\n        {\n          className: ''.concat(prefixCls, '-header-text'),\n          onClick: collapsible === 'header' ? handleItemClick : undefined,\n        },\n        header,\n      ),\n      ifExtraExist &&\n        /*#__PURE__*/ React.createElement(\n          'div',\n          {\n            className: ''.concat(prefixCls, '-extra'),\n          },\n          extra,\n        ),\n    ),\n    /*#__PURE__*/ React.createElement(\n      CSSMotion,\n      _extends(\n        {\n          visible: isActive,\n          leavedClassName: ''.concat(prefixCls, '-content-hidden'),\n        },\n        openMotion,\n        {\n          forceRender: forceRender,\n          removeOnLeave: destroyInactivePanel,\n        },\n      ),\n      function (_ref, motionRef) {\n        var motionClassName = _ref.className,\n          motionStyle = _ref.style;\n        return /*#__PURE__*/ React.createElement(\n          PanelContent,\n          {\n            ref: motionRef,\n            prefixCls: prefixCls,\n            className: motionClassName,\n            style: motionStyle,\n            isActive: isActive,\n            forceRender: forceRender,\n            role: accordion ? 'tabpanel' : void 0,\n          },\n          children,\n        );\n      },\n    ),\n  );\n});\nexport default CollapsePanel;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,SAAS,GAAG,CACd,WAAW,EACX,aAAa,EACb,UAAU,EACV,aAAa,EACb,aAAa,EACb,WAAW,EACX,WAAW,EACX,aAAa,EACb,WAAW,EACX,UAAU,EACV,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,sBAAsB,EACtB,UAAU,CACX;AACD,IAAIC,aAAa,GAAG,aAAcH,KAAK,CAACI,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACvE,IAAIC,WAAW,EAAEC,YAAY;EAC7B,IAAIC,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACjEE,WAAW,GAAGN,KAAK,CAACM,WAAW;IAC/BC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,WAAW,GAAGR,KAAK,CAACQ,WAAW;IAC/BC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,WAAW,GAAGZ,KAAK,CAACY,WAAW;IAC/BC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;IACzBC,KAAK,GAAGf,KAAK,CAACe,KAAK;IACnBC,MAAM,GAAGhB,KAAK,CAACgB,MAAM;IACrBC,UAAU,GAAGjB,KAAK,CAACiB,UAAU;IAC7BC,UAAU,GAAGlB,KAAK,CAACkB,UAAU;IAC7BC,oBAAoB,GAAGnB,KAAK,CAACmB,oBAAoB;IACjDC,QAAQ,GAAGpB,KAAK,CAACoB,QAAQ;IACzBC,UAAU,GAAG9B,wBAAwB,CAACS,KAAK,EAAEH,SAAS,CAAC;EACzD,IAAIyB,QAAQ,GAAGV,WAAW,KAAK,UAAU;EACzC,IAAIW,iBAAiB,GAAGX,WAAW,KAAK,QAAQ;EAChD,IAAIY,eAAe,GAAGZ,WAAW,KAAK,MAAM;EAC5C,IAAIa,YAAY,GAAGV,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKW,SAAS,IAAI,OAAOX,KAAK,KAAK,SAAS;EACtF,IAAIY,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/CnB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACM,QAAQ,CAAC;EACjF,CAAC;EACD,IAAIc,aAAa,GAAG,SAASA,aAAaA,CAACC,CAAC,EAAE;IAC5C,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKrC,OAAO,CAACsC,KAAK,IAAIH,CAAC,CAACI,KAAK,KAAKvC,OAAO,CAACsC,KAAK,EAAE;MACjFL,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,IAAIO,QAAQ,GACV,OAAOjB,UAAU,KAAK,UAAU,GAC5BA,UAAU,CAACjB,KAAK,CAAC,GACjB,aAAcL,KAAK,CAACwC,aAAa,CAAC,GAAG,EAAE;IACrCzB,SAAS,EAAE;EACb,CAAC,CAAC;EACR,IAAIwB,QAAQ,EAAE;IACZA,QAAQ,GAAG,aAAcvC,KAAK,CAACwC,aAAa,CAC1C,KAAK,EACL;MACEzB,SAAS,EAAE,EAAE,CAAC0B,MAAM,CAACzB,SAAS,EAAE,cAAc,CAAC;MAC/C0B,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,CAACC,QAAQ,CAAC1B,WAAW,CAAC,GAAGe,eAAe,GAAGD;IACxE,CAAC,EACDQ,QACF,CAAC;EACH;EACA,IAAIK,uBAAuB,GAAG/C,UAAU,EACpCU,WAAW,GAAG,CAAC,CAAC,EAClBb,eAAe,CAACa,WAAW,EAAE,EAAE,CAACkC,MAAM,CAACzB,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,EACjEtB,eAAe,CAACa,WAAW,EAAE,EAAE,CAACkC,MAAM,CAACzB,SAAS,EAAE,cAAc,CAAC,EAAEJ,QAAQ,CAAC,EAC5ElB,eAAe,CAACa,WAAW,EAAE,EAAE,CAACkC,MAAM,CAACzB,SAAS,EAAE,gBAAgB,CAAC,EAAEW,QAAQ,CAAC,EAC9EpB,WAAW,GACXQ,SACF,CAAC;EACD,IAAI8B,eAAe,GAAGhD,UAAU,CAC9Bc,WAAW,GACTH,YAAY,GAAG,CAAC,CAAC,EACnBd,eAAe,CAACc,YAAY,EAAE,EAAE,CAACiC,MAAM,CAACzB,SAAS,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,EACpEtB,eAAe,CACbc,YAAY,EACZ,EAAE,CAACiC,MAAM,CAACzB,SAAS,EAAE,0BAA0B,CAAC,EAChDY,iBACF,CAAC,EACDlC,eAAe,CAACc,YAAY,EAAE,EAAE,CAACiC,MAAM,CAACzB,SAAS,EAAE,wBAAwB,CAAC,EAAEa,eAAe,CAAC,EAC9FrB,YAAY,CACd,CAAC;;EAED;EACA,IAAIsC,WAAW,GAAG;IAChB/B,SAAS,EAAE8B,eAAe;IAC1B,eAAe,EAAEjC,QAAQ;IACzB,eAAe,EAAEe,QAAQ;IACzBoB,SAAS,EAAEd;EACb,CAAC;EACD,IAAI,CAACL,iBAAiB,IAAI,CAACC,eAAe,EAAE;IAC1CiB,WAAW,CAACJ,OAAO,GAAGV,eAAe;IACrCc,WAAW,CAACE,IAAI,GAAG9B,SAAS,GAAG,KAAK,GAAG,QAAQ;IAC/C4B,WAAW,CAACG,QAAQ,GAAGtB,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;EAC1C;;EAEA;EACA,OAAO,aAAc3B,KAAK,CAACwC,aAAa,CACtC,KAAK,EACL7C,QAAQ,CAAC,CAAC,CAAC,EAAE+B,UAAU,EAAE;IACvBpB,GAAG,EAAEA,GAAG;IACRS,SAAS,EAAE6B;EACb,CAAC,CAAC,EACF,aAAc5C,KAAK,CAACwC,aAAa,CAC/B,KAAK,EACLM,WAAW,EACXpC,SAAS,IAAI6B,QAAQ,EACrB,aAAcvC,KAAK,CAACwC,aAAa,CAC/B,MAAM,EACN;IACEzB,SAAS,EAAE,EAAE,CAAC0B,MAAM,CAACzB,SAAS,EAAE,cAAc,CAAC;IAC/C0B,OAAO,EAAEzB,WAAW,KAAK,QAAQ,GAAGe,eAAe,GAAGD;EACxD,CAAC,EACDV,MACF,CAAC,EACDS,YAAY,IACV,aAAc9B,KAAK,CAACwC,aAAa,CAC/B,KAAK,EACL;IACEzB,SAAS,EAAE,EAAE,CAAC0B,MAAM,CAACzB,SAAS,EAAE,QAAQ;EAC1C,CAAC,EACDI,KACF,CACJ,CAAC,EACD,aAAcpB,KAAK,CAACwC,aAAa,CAC/B1C,SAAS,EACTH,QAAQ,CACN;IACEuD,OAAO,EAAEtC,QAAQ;IACjBuC,eAAe,EAAE,EAAE,CAACV,MAAM,CAACzB,SAAS,EAAE,iBAAiB;EACzD,CAAC,EACDO,UAAU,EACV;IACET,WAAW,EAAEA,WAAW;IACxBsC,aAAa,EAAE5B;EACjB,CACF,CAAC,EACD,UAAU6B,IAAI,EAAEC,SAAS,EAAE;IACzB,IAAIC,eAAe,GAAGF,IAAI,CAACtC,SAAS;MAClCyC,WAAW,GAAGH,IAAI,CAACI,KAAK;IAC1B,OAAO,aAAczD,KAAK,CAACwC,aAAa,CACtCvC,YAAY,EACZ;MACEK,GAAG,EAAEgD,SAAS;MACdtC,SAAS,EAAEA,SAAS;MACpBD,SAAS,EAAEwC,eAAe;MAC1BE,KAAK,EAAED,WAAW;MAClB5C,QAAQ,EAAEA,QAAQ;MAClBE,WAAW,EAAEA,WAAW;MACxBkC,IAAI,EAAE9B,SAAS,GAAG,UAAU,GAAG,KAAK;IACtC,CAAC,EACDO,QACF,CAAC;EACH,CACF,CACF,CAAC;AACH,CAAC,CAAC;AACF,eAAetB,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}