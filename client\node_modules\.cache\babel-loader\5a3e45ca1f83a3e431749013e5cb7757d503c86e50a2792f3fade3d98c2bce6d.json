{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport QuarterHeader from \"./QuarterHeader\";\nimport QuarterBody from \"./QuarterBody\";\nimport { createKeyDownHandler } from \"../../utils/uiUtil\";\nfunction QuarterPanel(props) {\n  var prefixCls = props.prefixCls,\n    operationRef = props.operationRef,\n    onViewDateChange = props.onViewDateChange,\n    generateConfig = props.generateConfig,\n    value = props.value,\n    viewDate = props.viewDate,\n    onPanelChange = props.onPanelChange,\n    _onSelect = props.onSelect;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-quarter-panel\");\n\n  // ======================= Keyboard =======================\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          _onSelect(generateConfig.addMonth(value || viewDate, diff * 3), 'key');\n        },\n        onCtrlLeftRight: function onCtrlLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        },\n        onUpDown: function onUpDown(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        }\n      });\n    }\n  };\n\n  // ==================== View Operation ====================\n  var onYearChange = function onYearChange(diff) {\n    var newDate = generateConfig.addYear(viewDate, diff);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(QuarterHeader, _extends({}, props, {\n    prefixCls: prefixCls,\n    onPrevYear: function onPrevYear() {\n      onYearChange(-1);\n    },\n    onNextYear: function onNextYear() {\n      onYearChange(1);\n    },\n    onYearClick: function onYearClick() {\n      onPanelChange('year', viewDate);\n    }\n  })), /*#__PURE__*/React.createElement(QuarterBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    onSelect: function onSelect(date) {\n      _onSelect(date, 'mouse');\n    }\n  })));\n}\nexport default QuarterPanel;", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON><PERSON><PERSON>", "QuarterBody", "createKeyDownHandler", "QuarterPanel", "props", "prefixCls", "operationRef", "onViewDateChange", "generateConfig", "value", "viewDate", "onPanelChange", "_onSelect", "onSelect", "panelPrefixCls", "concat", "current", "onKeyDown", "event", "onLeftRight", "diff", "addMonth", "onCtrlLeftRight", "addYear", "onUpDown", "onYearChange", "newDate", "createElement", "className", "onPrevYear", "onNextYear", "onYearClick", "date"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/panels/QuarterPanel/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport QuarterHeader from \"./QuarterHeader\";\nimport QuarterBody from \"./QuarterBody\";\nimport { createKeyDownHandler } from \"../../utils/uiUtil\";\nfunction QuarterPanel(props) {\n  var prefixCls = props.prefixCls,\n    operationRef = props.operationRef,\n    onViewDateChange = props.onViewDateChange,\n    generateConfig = props.generateConfig,\n    value = props.value,\n    viewDate = props.viewDate,\n    onPanelChange = props.onPanelChange,\n    _onSelect = props.onSelect;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-quarter-panel\");\n\n  // ======================= Keyboard =======================\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          _onSelect(generateConfig.addMonth(value || viewDate, diff * 3), 'key');\n        },\n        onCtrlLeftRight: function onCtrlLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        },\n        onUpDown: function onUpDown(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        }\n      });\n    }\n  };\n\n  // ==================== View Operation ====================\n  var onYearChange = function onYearChange(diff) {\n    var newDate = generateConfig.addYear(viewDate, diff);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(QuarterHeader, _extends({}, props, {\n    prefixCls: prefixCls,\n    onPrevYear: function onPrevYear() {\n      onYearChange(-1);\n    },\n    onNextYear: function onNextYear() {\n      onYearChange(1);\n    },\n    onYearClick: function onYearClick() {\n      onPanelChange('year', viewDate);\n    }\n  })), /*#__PURE__*/React.createElement(QuarterBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    onSelect: function onSelect(date) {\n      _onSelect(date, 'mouse');\n    }\n  })));\n}\nexport default QuarterPanel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,oBAAoB,QAAQ,oBAAoB;AACzD,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,gBAAgB,GAAGH,KAAK,CAACG,gBAAgB;IACzCC,cAAc,GAAGJ,KAAK,CAACI,cAAc;IACrCC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,aAAa,GAAGP,KAAK,CAACO,aAAa;IACnCC,SAAS,GAAGR,KAAK,CAACS,QAAQ;EAC5B,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACV,SAAS,EAAE,gBAAgB,CAAC;;EAE3D;EACAC,YAAY,CAACU,OAAO,GAAG;IACrBC,SAAS,EAAE,SAASA,SAASA,CAACC,KAAK,EAAE;MACnC,OAAOhB,oBAAoB,CAACgB,KAAK,EAAE;QACjCC,WAAW,EAAE,SAASA,WAAWA,CAACC,IAAI,EAAE;UACtCR,SAAS,CAACJ,cAAc,CAACa,QAAQ,CAACZ,KAAK,IAAIC,QAAQ,EAAEU,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;QACxE,CAAC;QACDE,eAAe,EAAE,SAASA,eAAeA,CAACF,IAAI,EAAE;UAC9CR,SAAS,CAACJ,cAAc,CAACe,OAAO,CAACd,KAAK,IAAIC,QAAQ,EAAEU,IAAI,CAAC,EAAE,KAAK,CAAC;QACnE,CAAC;QACDI,QAAQ,EAAE,SAASA,QAAQA,CAACJ,IAAI,EAAE;UAChCR,SAAS,CAACJ,cAAc,CAACe,OAAO,CAACd,KAAK,IAAIC,QAAQ,EAAEU,IAAI,CAAC,EAAE,KAAK,CAAC;QACnE;MACF,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAACL,IAAI,EAAE;IAC7C,IAAIM,OAAO,GAAGlB,cAAc,CAACe,OAAO,CAACb,QAAQ,EAAEU,IAAI,CAAC;IACpDb,gBAAgB,CAACmB,OAAO,CAAC;IACzBf,aAAa,CAAC,IAAI,EAAEe,OAAO,CAAC;EAC9B,CAAC;EACD,OAAO,aAAa3B,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEd;EACb,CAAC,EAAE,aAAaf,KAAK,CAAC4B,aAAa,CAAC3B,aAAa,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;IACrEC,SAAS,EAAEA,SAAS;IACpBwB,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChCJ,YAAY,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACDK,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChCL,YAAY,CAAC,CAAC,CAAC;IACjB,CAAC;IACDM,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;MAClCpB,aAAa,CAAC,MAAM,EAAED,QAAQ,CAAC;IACjC;EACF,CAAC,CAAC,CAAC,EAAE,aAAaX,KAAK,CAAC4B,aAAa,CAAC1B,WAAW,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;IACrEC,SAAS,EAAEA,SAAS;IACpBQ,QAAQ,EAAE,SAASA,QAAQA,CAACmB,IAAI,EAAE;MAChCpB,SAAS,CAACoB,IAAI,EAAE,OAAO,CAAC;IAC1B;EACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,eAAe7B,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}