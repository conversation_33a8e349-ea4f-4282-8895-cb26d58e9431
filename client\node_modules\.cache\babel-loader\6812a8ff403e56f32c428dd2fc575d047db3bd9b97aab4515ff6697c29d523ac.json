{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\Users\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { getAllUsers, blockUserById, deleteUserById } from \"../../../apicalls/users\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\nimport { TbUsers, TbSearch, Tb<PERSON>ilt<PERSON>, <PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>ser<PERSON>, Tb<PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, TbSchool, TbMail, TbUser } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Users() {\n  _s();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [filteredUsers, setFilteredUsers] = useState([]);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [filterStatus, setFilterStatus] = useState(\"all\");\n  const [loading, setLoading] = useState(false);\n  const dispatch = useDispatch();\n  const getUsersData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllUsers();\n      dispatch(HideLoading());\n      if (response.success) {\n        setUsers(response.users);\n        console.log(\"users\", response);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const blockUser = async studentId => {\n    try {\n      dispatch(ShowLoading());\n      const response = await blockUserById({\n        studentId\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        message.success(response.message);\n        getUsersData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const deleteUser = async studentId => {\n    try {\n      dispatch(ShowLoading());\n      const response = await deleteUserById({\n        studentId\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        message.success(\"User deleted successfully\");\n        getUsersData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const columns = [{\n    title: \"Name\",\n    dataIndex: \"name\"\n  }, {\n    title: \"School\",\n    dataIndex: \"school\"\n  }, {\n    title: \"Class\",\n    dataIndex: \"class\"\n  }, {\n    title: \"Email\",\n    dataIndex: \"email\"\n  }, {\n    title: \"Action\",\n    dataIndex: \"action\",\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between \",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => blockUser(record.studentId),\n        children: record.isBlocked ? \"Unblock\" : \"Block\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        onClick: () => {\n          if (window.confirm(\"Are you sure you want to delete this user?\")) {\n            deleteUser(record.studentId);\n          }\n        },\n        style: {\n          color: \"red\",\n          cursor: \"pointer\"\n        },\n        className: \"cursor-pointer\",\n        children: /*#__PURE__*/_jsxDEV(MdDelete, {\n          fontSize: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this)\n  }];\n  useEffect(() => {\n    getUsersData();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between mt-2 items-end\",\n      children: /*#__PURE__*/_jsxDEV(PageTitle, {\n        title: \"Users\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divider\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: users,\n      rowKey: record => record.studentId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n}\n_s(Users, \"GbEGQMNndIDxv5ILd0fXe67HEzM=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = Users;\nexport default Users;\nvar _c;\n$RefreshReg$(_c, \"Users\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useDispatch", "useNavigate", "motion", "getAllUsers", "blockUserById", "deleteUserById", "Page<PERSON><PERSON>le", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Input", "Loading", "TbUsers", "TbSearch", "Tb<PERSON><PERSON>er", "TbUserCheck", "TbUserX", "TbTrash", "TbEye", "TbSchool", "TbMail", "TbUser", "jsxDEV", "_jsxDEV", "Users", "_s", "navigate", "users", "setUsers", "filteredUsers", "setFilteredUsers", "searchQuery", "setSearch<PERSON>uery", "filterStatus", "setFilterStatus", "loading", "setLoading", "dispatch", "getUsersData", "response", "success", "console", "log", "error", "blockUser", "studentId", "deleteUser", "columns", "title", "dataIndex", "render", "text", "record", "className", "children", "onClick", "isBlocked", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "window", "confirm", "style", "color", "cursor", "MdDelete", "fontSize", "Table", "dataSource", "<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Users/<USER>"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  getAllUsers,\r\n  blockUserById,\r\n  deleteUserById,\r\n} from \"../../../apicalls/users\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\r\nimport {\r\n  TbUsers,\r\n  TbSearch,\r\n  TbFilter,\r\n  TbUserCheck,\r\n  TbUserX,\r\n  TbTrash,\r\n  TbEye,\r\n  TbSchool,\r\n  TbMail,\r\n  TbUser\r\n} from \"react-icons/tb\";\r\n\r\nfunction Users() {\r\n  const navigate = useNavigate();\r\n  const [users, setUsers] = useState([]);\r\n  const [filteredUsers, setFilteredUsers] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"all\");\r\n  const [loading, setLoading] = useState(false);\r\n  const dispatch = useDispatch();\r\n\r\n  const getUsersData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllUsers();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setUsers(response.users);\r\n        console.log(\"users\", response);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const blockUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await blockUserById({\r\n        studentId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteUserById({ studentId });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(\"User deleted successfully\");\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      title: \"Name\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"School\",\r\n      dataIndex: \"school\",\r\n    },\r\n    {\r\n      title: \"Class\",\r\n      dataIndex: \"class\",\r\n    },\r\n    {\r\n      title: \"Email\",\r\n      dataIndex: \"email\",\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center justify-between \">\r\n          <button onClick={() => blockUser(record.studentId)}>\r\n            {record.isBlocked ? \"Unblock\" : \"Block\"}\r\n          </button>\r\n\r\n          <span\r\n            onClick={() => {\r\n              if (\r\n                window.confirm(\"Are you sure you want to delete this user?\")\r\n              ) {\r\n                deleteUser(record.studentId);\r\n              }\r\n            }}\r\n            style={{ color: \"red\", cursor: \"pointer\" }}\r\n            className=\"cursor-pointer\"\r\n          >\r\n            <MdDelete fontSize={20} />\r\n          </span>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between mt-2 items-end\">\r\n        <PageTitle title=\"Users\" />\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n\r\n      <Table\r\n        columns={columns}\r\n        dataSource={users}\r\n        rowKey={(record) => record.studentId}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Users;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,WAAW,EACXC,aAAa,EACbC,cAAc,QACT,yBAAyB;AAChC,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,4BAA4B;AACzE,SACEC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACXC,OAAO,EACPC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,MAAM,QACD,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMuC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAE9B,MAAMuC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFD,QAAQ,CAAC9B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMgC,QAAQ,GAAG,MAAMrC,WAAW,CAAC,CAAC;MACpCmC,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIiC,QAAQ,CAACC,OAAO,EAAE;QACpBZ,QAAQ,CAACW,QAAQ,CAACZ,KAAK,CAAC;QACxBc,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEH,QAAQ,CAAC;MAChC,CAAC,MAAM;QACL5C,OAAO,CAACgD,KAAK,CAACJ,QAAQ,CAAC5C,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACdN,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACgD,KAAK,CAACA,KAAK,CAAChD,OAAO,CAAC;IAC9B;EACF,CAAC;EACD,MAAMiD,SAAS,GAAG,MAAOC,SAAS,IAAK;IACrC,IAAI;MACFR,QAAQ,CAAC9B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMgC,QAAQ,GAAG,MAAMpC,aAAa,CAAC;QACnC0C;MACF,CAAC,CAAC;MACFR,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIiC,QAAQ,CAACC,OAAO,EAAE;QACpB7C,OAAO,CAAC6C,OAAO,CAACD,QAAQ,CAAC5C,OAAO,CAAC;QACjC2C,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACL3C,OAAO,CAACgD,KAAK,CAACJ,QAAQ,CAAC5C,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACdN,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACgD,KAAK,CAACA,KAAK,CAAChD,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMmD,UAAU,GAAG,MAAOD,SAAS,IAAK;IACtC,IAAI;MACFR,QAAQ,CAAC9B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMgC,QAAQ,GAAG,MAAMnC,cAAc,CAAC;QAAEyC;MAAU,CAAC,CAAC;MACpDR,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIiC,QAAQ,CAACC,OAAO,EAAE;QACpB7C,OAAO,CAAC6C,OAAO,CAAC,2BAA2B,CAAC;QAC5CF,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACL3C,OAAO,CAACgD,KAAK,CAACJ,QAAQ,CAAC5C,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACdN,QAAQ,CAAC/B,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACgD,KAAK,CAACA,KAAK,CAAChD,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMoD,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,QAAQ;IACnBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB7B,OAAA;MAAK8B,SAAS,EAAC,oCAAoC;MAAAC,QAAA,gBACjD/B,OAAA;QAAQgC,OAAO,EAAEA,CAAA,KAAMX,SAAS,CAACQ,MAAM,CAACP,SAAS,CAAE;QAAAS,QAAA,EAChDF,MAAM,CAACI,SAAS,GAAG,SAAS,GAAG;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAETrC,OAAA;QACEgC,OAAO,EAAEA,CAAA,KAAM;UACb,IACEM,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAC5D;YACAhB,UAAU,CAACM,MAAM,CAACP,SAAS,CAAC;UAC9B;QACF,CAAE;QACFkB,KAAK,EAAE;UAAEC,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAU,CAAE;QAC3CZ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAE1B/B,OAAA,CAAC2C,QAAQ;UAACC,QAAQ,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAET,CAAC,CACF;EACD/D,SAAS,CAAC,MAAM;IACdyC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EACN,oBACEf,OAAA;IAAA+B,QAAA,gBACE/B,OAAA;MAAK8B,SAAS,EAAC,qCAAqC;MAAAC,QAAA,eAClD/B,OAAA,CAAClB,SAAS;QAAC2C,KAAK,EAAC;MAAO;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eACNrC,OAAA;MAAK8B,SAAS,EAAC;IAAS;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE/BrC,OAAA,CAAC6C,KAAK;MACJrB,OAAO,EAAEA,OAAQ;MACjBsB,UAAU,EAAE1C,KAAM;MAClB2C,MAAM,EAAGlB,MAAM,IAAKA,MAAM,CAACP;IAAU;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACnC,EAAA,CAzHQD,KAAK;EAAA,QACKxB,WAAW,EAMXD,WAAW;AAAA;AAAAwE,EAAA,GAPrB/C,KAAK;AA2Hd,eAAeA,KAAK;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}