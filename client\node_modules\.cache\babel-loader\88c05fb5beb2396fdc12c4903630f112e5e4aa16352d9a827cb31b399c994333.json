{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nvar ImmutableContext = /*#__PURE__*/React.createContext(0);\n\n/**\n * Get render update mark by `makeImmutable` root.\n * Do not deps on the return value as render times\n * but only use for `useMemo` or `useCallback` deps.\n */\nexport function useImmutableMark() {\n  return React.useContext(ImmutableContext);\n}\n/**\n * Wrapped Component will be marked as Immutable.\n * When Component parent trigger render,\n * it will notice children component (use with `responseImmutable`) node that parent has updated.\n\n * @param Component Passed Component\n * @param triggerRender Customize trigger `responseImmutable` children re-render logic. Default will always trigger re-render when this component re-render.\n */\n\nexport function makeImmutable(Component, shouldTriggerRender) {\n  var refAble = supportRef(Component);\n  var ImmutableComponent = function ImmutableComponent(props, ref) {\n    var refProps = refAble ? {\n      ref: ref\n    } : {};\n    var renderTimesRef = React.useRef(0);\n    var prevProps = React.useRef(props);\n    if (\n    // Always trigger re-render if not provide `notTriggerRender`\n    !shouldTriggerRender || shouldTriggerRender(prevProps.current, props)) {\n      renderTimesRef.current += 1;\n    }\n    prevProps.current = props;\n    return /*#__PURE__*/React.createElement(ImmutableContext.Provider, {\n      value: renderTimesRef.current\n    }, /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps)));\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    ImmutableComponent.displayName = \"ImmutableRoot(\".concat(Component.displayName || Component.name, \")\");\n  }\n  return refAble ? /*#__PURE__*/React.forwardRef(ImmutableComponent) : ImmutableComponent;\n}\n/**\n * Wrapped Component with `React.memo`.\n * But will rerender when parent with `makeImmutable` rerender.\n */\n\nexport function responseImmutable(Component, propsAreEqual) {\n  var refAble = supportRef(Component);\n  var ImmutableComponent = function ImmutableComponent(props, ref) {\n    var refProps = refAble ? {\n      ref: ref\n    } : {};\n    useImmutableMark();\n    return /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps));\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    ImmutableComponent.displayName = \"ImmutableResponse(\".concat(Component.displayName || Component.name, \")\");\n  }\n  return refAble ? /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(ImmutableComponent), propsAreEqual) : /*#__PURE__*/React.memo(ImmutableComponent, propsAreEqual);\n}", "map": {"version": 3, "names": ["_extends", "supportRef", "React", "ImmutableContext", "createContext", "useImmutableMark", "useContext", "makeImmutable", "Component", "should<PERSON>rigger<PERSON>ender", "refAble", "ImmutableComponent", "props", "ref", "refProps", "renderTimesRef", "useRef", "prevProps", "current", "createElement", "Provider", "value", "process", "env", "NODE_ENV", "displayName", "concat", "name", "forwardRef", "responseImmutable", "propsAreEqual", "memo"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/context/es/Immutable.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nvar ImmutableContext = /*#__PURE__*/React.createContext(0);\n\n/**\n * Get render update mark by `makeImmutable` root.\n * Do not deps on the return value as render times\n * but only use for `useMemo` or `useCallback` deps.\n */\nexport function useImmutableMark() {\n  return React.useContext(ImmutableContext);\n}\n/**\n * Wrapped Component will be marked as Immutable.\n * When Component parent trigger render,\n * it will notice children component (use with `responseImmutable`) node that parent has updated.\n\n * @param Component Passed Component\n * @param triggerRender Customize trigger `responseImmutable` children re-render logic. Default will always trigger re-render when this component re-render.\n */\n\nexport function makeImmutable(Component, shouldTriggerRender) {\n  var refAble = supportRef(Component);\n\n  var ImmutableComponent = function ImmutableComponent(props, ref) {\n    var refProps = refAble ? {\n      ref: ref\n    } : {};\n    var renderTimesRef = React.useRef(0);\n    var prevProps = React.useRef(props);\n\n    if ( // Always trigger re-render if not provide `notTriggerRender`\n    !shouldTriggerRender || shouldTriggerRender(prevProps.current, props)) {\n      renderTimesRef.current += 1;\n    }\n\n    prevProps.current = props;\n    return /*#__PURE__*/React.createElement(ImmutableContext.Provider, {\n      value: renderTimesRef.current\n    }, /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps)));\n  };\n\n  if (process.env.NODE_ENV !== 'production') {\n    ImmutableComponent.displayName = \"ImmutableRoot(\".concat(Component.displayName || Component.name, \")\");\n  }\n\n  return refAble ? /*#__PURE__*/React.forwardRef(ImmutableComponent) : ImmutableComponent;\n}\n/**\n * Wrapped Component with `React.memo`.\n * But will rerender when parent with `makeImmutable` rerender.\n */\n\nexport function responseImmutable(Component, propsAreEqual) {\n  var refAble = supportRef(Component);\n\n  var ImmutableComponent = function ImmutableComponent(props, ref) {\n    var refProps = refAble ? {\n      ref: ref\n    } : {};\n    useImmutableMark();\n    return /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps));\n  };\n\n  if (process.env.NODE_ENV !== 'production') {\n    ImmutableComponent.displayName = \"ImmutableResponse(\".concat(Component.displayName || Component.name, \")\");\n  }\n\n  return refAble ? /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(ImmutableComponent), propsAreEqual) : /*#__PURE__*/React.memo(ImmutableComponent, propsAreEqual);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,gBAAgB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC;;AAE1D;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EACjC,OAAOH,KAAK,CAACI,UAAU,CAACH,gBAAgB,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASI,aAAaA,CAACC,SAAS,EAAEC,mBAAmB,EAAE;EAC5D,IAAIC,OAAO,GAAGT,UAAU,CAACO,SAAS,CAAC;EAEnC,IAAIG,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/D,IAAIC,QAAQ,GAAGJ,OAAO,GAAG;MACvBG,GAAG,EAAEA;IACP,CAAC,GAAG,CAAC,CAAC;IACN,IAAIE,cAAc,GAAGb,KAAK,CAACc,MAAM,CAAC,CAAC,CAAC;IACpC,IAAIC,SAAS,GAAGf,KAAK,CAACc,MAAM,CAACJ,KAAK,CAAC;IAEnC;IAAK;IACL,CAACH,mBAAmB,IAAIA,mBAAmB,CAACQ,SAAS,CAACC,OAAO,EAAEN,KAAK,CAAC,EAAE;MACrEG,cAAc,CAACG,OAAO,IAAI,CAAC;IAC7B;IAEAD,SAAS,CAACC,OAAO,GAAGN,KAAK;IACzB,OAAO,aAAaV,KAAK,CAACiB,aAAa,CAAChB,gBAAgB,CAACiB,QAAQ,EAAE;MACjEC,KAAK,EAAEN,cAAc,CAACG;IACxB,CAAC,EAAE,aAAahB,KAAK,CAACiB,aAAa,CAACX,SAAS,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEY,KAAK,EAAEE,QAAQ,CAAC,CAAC,CAAC;EAChF,CAAC;EAED,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCb,kBAAkB,CAACc,WAAW,GAAG,gBAAgB,CAACC,MAAM,CAAClB,SAAS,CAACiB,WAAW,IAAIjB,SAAS,CAACmB,IAAI,EAAE,GAAG,CAAC;EACxG;EAEA,OAAOjB,OAAO,GAAG,aAAaR,KAAK,CAAC0B,UAAU,CAACjB,kBAAkB,CAAC,GAAGA,kBAAkB;AACzF;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASkB,iBAAiBA,CAACrB,SAAS,EAAEsB,aAAa,EAAE;EAC1D,IAAIpB,OAAO,GAAGT,UAAU,CAACO,SAAS,CAAC;EAEnC,IAAIG,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/D,IAAIC,QAAQ,GAAGJ,OAAO,GAAG;MACvBG,GAAG,EAAEA;IACP,CAAC,GAAG,CAAC,CAAC;IACNR,gBAAgB,CAAC,CAAC;IAClB,OAAO,aAAaH,KAAK,CAACiB,aAAa,CAACX,SAAS,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEY,KAAK,EAAEE,QAAQ,CAAC,CAAC;EACnF,CAAC;EAED,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCb,kBAAkB,CAACc,WAAW,GAAG,oBAAoB,CAACC,MAAM,CAAClB,SAAS,CAACiB,WAAW,IAAIjB,SAAS,CAACmB,IAAI,EAAE,GAAG,CAAC;EAC5G;EAEA,OAAOjB,OAAO,GAAG,aAAaR,KAAK,CAAC6B,IAAI,EAAE,aAAa7B,KAAK,CAAC0B,UAAU,CAACjB,kBAAkB,CAAC,EAAEmB,aAAa,CAAC,GAAG,aAAa5B,KAAK,CAAC6B,IAAI,CAACpB,kBAAkB,EAAEmB,aAAa,CAAC;AAC1K"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}