{"ast": null, "code": "import _objectDestructuringEmpty from \"@babel/runtime/helpers/esm/objectDestructuringEmpty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"style\", \"motion\", \"motionNodes\", \"motionType\", \"onMotionStart\", \"onMotionEnd\", \"active\", \"treeNodeRequiredProps\"];\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { TreeContext } from './contextTypes';\nimport TreeNode from './TreeNode';\nimport useUnmount from './useUnmount';\nimport { getTreeNodeProps } from './utils/treeUtil';\nvar MotionTreeNode = function MotionTreeNode(_ref, ref) {\n  var className = _ref.className,\n    style = _ref.style,\n    motion = _ref.motion,\n    motionNodes = _ref.motionNodes,\n    motionType = _ref.motionType,\n    onOriginMotionStart = _ref.onMotionStart,\n    onOriginMotionEnd = _ref.onMotionEnd,\n    active = _ref.active,\n    treeNodeRequiredProps = _ref.treeNodeRequiredProps,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  var _React$useContext = React.useContext(TreeContext),\n    prefixCls = _React$useContext.prefixCls;\n  // Calculate target visible here.\n  // And apply in effect to make `leave` motion work.\n  var targetVisible = motionNodes && motionType !== 'hide';\n  useLayoutEffect(function () {\n    if (motionNodes) {\n      if (targetVisible !== visible) {\n        setVisible(targetVisible);\n      }\n    }\n  }, [motionNodes]);\n  var triggerMotionStart = function triggerMotionStart() {\n    if (motionNodes) {\n      onOriginMotionStart();\n    }\n  };\n  // Should only trigger once\n  var triggerMotionEndRef = React.useRef(false);\n  var triggerMotionEnd = function triggerMotionEnd() {\n    if (motionNodes && !triggerMotionEndRef.current) {\n      triggerMotionEndRef.current = true;\n      onOriginMotionEnd();\n    }\n  };\n  // Effect if unmount\n  useUnmount(triggerMotionStart, triggerMotionEnd);\n  // Motion end event\n  var onVisibleChanged = function onVisibleChanged(nextVisible) {\n    if (targetVisible === nextVisible) {\n      triggerMotionEnd();\n    }\n  };\n  if (motionNodes) {\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      ref: ref,\n      visible: visible\n    }, motion, {\n      motionAppear: motionType === 'show',\n      onVisibleChanged: onVisibleChanged\n    }), function (_ref2, motionRef) {\n      var motionClassName = _ref2.className,\n        motionStyle = _ref2.style;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: motionRef,\n        className: classNames(\"\".concat(prefixCls, \"-treenode-motion\"), motionClassName),\n        style: motionStyle\n      }, motionNodes.map(function (treeNode) {\n        var restProps = _extends({}, (_objectDestructuringEmpty(treeNode.data), treeNode.data)),\n          title = treeNode.title,\n          key = treeNode.key,\n          isStart = treeNode.isStart,\n          isEnd = treeNode.isEnd;\n        delete restProps.children;\n        var treeNodeProps = getTreeNodeProps(key, treeNodeRequiredProps);\n        return /*#__PURE__*/React.createElement(TreeNode, _extends({}, restProps, treeNodeProps, {\n          title: title,\n          active: active,\n          data: treeNode.data,\n          key: key,\n          isStart: isStart,\n          isEnd: isEnd\n        }));\n      }));\n    });\n  }\n  return /*#__PURE__*/React.createElement(TreeNode, _extends({\n    domRef: ref,\n    className: className,\n    style: style\n  }, props, {\n    active: active\n  }));\n};\nMotionTreeNode.displayName = 'MotionTreeNode';\nvar RefMotionTreeNode = /*#__PURE__*/React.forwardRef(MotionTreeNode);\nexport default RefMotionTreeNode;", "map": {"version": 3, "names": ["_objectDestructuringEmpty", "_extends", "_slicedToArray", "_objectWithoutProperties", "_excluded", "classNames", "CSSMotion", "useLayoutEffect", "React", "TreeContext", "TreeNode", "useUnmount", "getTreeNodeProps", "MotionTreeNode", "_ref", "ref", "className", "style", "motion", "motionNodes", "motionType", "onOriginMotionStart", "onMotionStart", "onOriginMotionEnd", "onMotionEnd", "active", "treeNodeRequiredProps", "props", "_React$useState", "useState", "_React$useState2", "visible", "setVisible", "_React$useContext", "useContext", "prefixCls", "targetVisible", "triggerMotionStart", "triggerMotionEndRef", "useRef", "triggerMotionEnd", "current", "onVisibleChanged", "nextVisible", "createElement", "motionAppear", "_ref2", "motionRef", "motionClassName", "motionStyle", "concat", "map", "treeNode", "restProps", "data", "title", "key", "isStart", "isEnd", "children", "treeNodeProps", "domRef", "displayName", "RefMotionTreeNode", "forwardRef"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tree/es/MotionTreeNode.js"], "sourcesContent": ["import _objectDestructuringEmpty from \"@babel/runtime/helpers/esm/objectDestructuringEmpty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"style\", \"motion\", \"motionNodes\", \"motionType\", \"onMotionStart\", \"onMotionEnd\", \"active\", \"treeNodeRequiredProps\"];\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { TreeContext } from './contextTypes';\nimport TreeNode from './TreeNode';\nimport useUnmount from './useUnmount';\nimport { getTreeNodeProps } from './utils/treeUtil';\nvar MotionTreeNode = function MotionTreeNode(_ref, ref) {\n  var className = _ref.className,\n    style = _ref.style,\n    motion = _ref.motion,\n    motionNodes = _ref.motionNodes,\n    motionType = _ref.motionType,\n    onOriginMotionStart = _ref.onMotionStart,\n    onOriginMotionEnd = _ref.onMotionEnd,\n    active = _ref.active,\n    treeNodeRequiredProps = _ref.treeNodeRequiredProps,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _React$useState = React.useState(true),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  var _React$useContext = React.useContext(TreeContext),\n    prefixCls = _React$useContext.prefixCls;\n  // Calculate target visible here.\n  // And apply in effect to make `leave` motion work.\n  var targetVisible = motionNodes && motionType !== 'hide';\n  useLayoutEffect(function () {\n    if (motionNodes) {\n      if (targetVisible !== visible) {\n        setVisible(targetVisible);\n      }\n    }\n  }, [motionNodes]);\n  var triggerMotionStart = function triggerMotionStart() {\n    if (motionNodes) {\n      onOriginMotionStart();\n    }\n  };\n  // Should only trigger once\n  var triggerMotionEndRef = React.useRef(false);\n  var triggerMotionEnd = function triggerMotionEnd() {\n    if (motionNodes && !triggerMotionEndRef.current) {\n      triggerMotionEndRef.current = true;\n      onOriginMotionEnd();\n    }\n  };\n  // Effect if unmount\n  useUnmount(triggerMotionStart, triggerMotionEnd);\n  // Motion end event\n  var onVisibleChanged = function onVisibleChanged(nextVisible) {\n    if (targetVisible === nextVisible) {\n      triggerMotionEnd();\n    }\n  };\n  if (motionNodes) {\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      ref: ref,\n      visible: visible\n    }, motion, {\n      motionAppear: motionType === 'show',\n      onVisibleChanged: onVisibleChanged\n    }), function (_ref2, motionRef) {\n      var motionClassName = _ref2.className,\n        motionStyle = _ref2.style;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: motionRef,\n        className: classNames(\"\".concat(prefixCls, \"-treenode-motion\"), motionClassName),\n        style: motionStyle\n      }, motionNodes.map(function (treeNode) {\n        var restProps = _extends({}, (_objectDestructuringEmpty(treeNode.data), treeNode.data)),\n          title = treeNode.title,\n          key = treeNode.key,\n          isStart = treeNode.isStart,\n          isEnd = treeNode.isEnd;\n        delete restProps.children;\n        var treeNodeProps = getTreeNodeProps(key, treeNodeRequiredProps);\n        return /*#__PURE__*/React.createElement(TreeNode, _extends({}, restProps, treeNodeProps, {\n          title: title,\n          active: active,\n          data: treeNode.data,\n          key: key,\n          isStart: isStart,\n          isEnd: isEnd\n        }));\n      }));\n    });\n  }\n  return /*#__PURE__*/React.createElement(TreeNode, _extends({\n    domRef: ref,\n    className: className,\n    style: style\n  }, props, {\n    active: active\n  }));\n};\nMotionTreeNode.displayName = 'MotionTreeNode';\nvar RefMotionTreeNode = /*#__PURE__*/React.forwardRef(MotionTreeNode);\nexport default RefMotionTreeNode;"], "mappings": "AAAA,OAAOA,yBAAyB,MAAM,qDAAqD;AAC3F,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,QAAQ,EAAE,uBAAuB,CAAC;AAChJ,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACtD,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC5BC,KAAK,GAAGH,IAAI,CAACG,KAAK;IAClBC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,WAAW,GAAGL,IAAI,CAACK,WAAW;IAC9BC,UAAU,GAAGN,IAAI,CAACM,UAAU;IAC5BC,mBAAmB,GAAGP,IAAI,CAACQ,aAAa;IACxCC,iBAAiB,GAAGT,IAAI,CAACU,WAAW;IACpCC,MAAM,GAAGX,IAAI,CAACW,MAAM;IACpBC,qBAAqB,GAAGZ,IAAI,CAACY,qBAAqB;IAClDC,KAAK,GAAGxB,wBAAwB,CAACW,IAAI,EAAEV,SAAS,CAAC;EACnD,IAAIwB,eAAe,GAAGpB,KAAK,CAACqB,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAG5B,cAAc,CAAC0B,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,iBAAiB,GAAGzB,KAAK,CAAC0B,UAAU,CAACzB,WAAW,CAAC;IACnD0B,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EACzC;EACA;EACA,IAAIC,aAAa,GAAGjB,WAAW,IAAIC,UAAU,KAAK,MAAM;EACxDb,eAAe,CAAC,YAAY;IAC1B,IAAIY,WAAW,EAAE;MACf,IAAIiB,aAAa,KAAKL,OAAO,EAAE;QAC7BC,UAAU,CAACI,aAAa,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAACjB,WAAW,CAAC,CAAC;EACjB,IAAIkB,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAIlB,WAAW,EAAE;MACfE,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC;EACD;EACA,IAAIiB,mBAAmB,GAAG9B,KAAK,CAAC+B,MAAM,CAAC,KAAK,CAAC;EAC7C,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIrB,WAAW,IAAI,CAACmB,mBAAmB,CAACG,OAAO,EAAE;MAC/CH,mBAAmB,CAACG,OAAO,GAAG,IAAI;MAClClB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EACD;EACAZ,UAAU,CAAC0B,kBAAkB,EAAEG,gBAAgB,CAAC;EAChD;EACA,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,WAAW,EAAE;IAC5D,IAAIP,aAAa,KAAKO,WAAW,EAAE;MACjCH,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC;EACD,IAAIrB,WAAW,EAAE;IACf,OAAO,aAAaX,KAAK,CAACoC,aAAa,CAACtC,SAAS,EAAEL,QAAQ,CAAC;MAC1Dc,GAAG,EAAEA,GAAG;MACRgB,OAAO,EAAEA;IACX,CAAC,EAAEb,MAAM,EAAE;MACT2B,YAAY,EAAEzB,UAAU,KAAK,MAAM;MACnCsB,gBAAgB,EAAEA;IACpB,CAAC,CAAC,EAAE,UAAUI,KAAK,EAAEC,SAAS,EAAE;MAC9B,IAAIC,eAAe,GAAGF,KAAK,CAAC9B,SAAS;QACnCiC,WAAW,GAAGH,KAAK,CAAC7B,KAAK;MAC3B,OAAO,aAAaT,KAAK,CAACoC,aAAa,CAAC,KAAK,EAAE;QAC7C7B,GAAG,EAAEgC,SAAS;QACd/B,SAAS,EAAEX,UAAU,CAAC,EAAE,CAAC6C,MAAM,CAACf,SAAS,EAAE,kBAAkB,CAAC,EAAEa,eAAe,CAAC;QAChF/B,KAAK,EAAEgC;MACT,CAAC,EAAE9B,WAAW,CAACgC,GAAG,CAAC,UAAUC,QAAQ,EAAE;QACrC,IAAIC,SAAS,GAAGpD,QAAQ,CAAC,CAAC,CAAC,GAAGD,yBAAyB,CAACoD,QAAQ,CAACE,IAAI,CAAC,EAAEF,QAAQ,CAACE,IAAI,CAAC,CAAC;UACrFC,KAAK,GAAGH,QAAQ,CAACG,KAAK;UACtBC,GAAG,GAAGJ,QAAQ,CAACI,GAAG;UAClBC,OAAO,GAAGL,QAAQ,CAACK,OAAO;UAC1BC,KAAK,GAAGN,QAAQ,CAACM,KAAK;QACxB,OAAOL,SAAS,CAACM,QAAQ;QACzB,IAAIC,aAAa,GAAGhD,gBAAgB,CAAC4C,GAAG,EAAE9B,qBAAqB,CAAC;QAChE,OAAO,aAAalB,KAAK,CAACoC,aAAa,CAAClC,QAAQ,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEoD,SAAS,EAAEO,aAAa,EAAE;UACvFL,KAAK,EAAEA,KAAK;UACZ9B,MAAM,EAAEA,MAAM;UACd6B,IAAI,EAAEF,QAAQ,CAACE,IAAI;UACnBE,GAAG,EAAEA,GAAG;UACRC,OAAO,EAAEA,OAAO;UAChBC,KAAK,EAAEA;QACT,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EACA,OAAO,aAAalD,KAAK,CAACoC,aAAa,CAAClC,QAAQ,EAAET,QAAQ,CAAC;IACzD4D,MAAM,EAAE9C,GAAG;IACXC,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAEA;EACT,CAAC,EAAEU,KAAK,EAAE;IACRF,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;AACL,CAAC;AACDZ,cAAc,CAACiD,WAAW,GAAG,gBAAgB;AAC7C,IAAIC,iBAAiB,GAAG,aAAavD,KAAK,CAACwD,UAAU,CAACnD,cAAc,CAAC;AACrE,eAAekD,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}