{"ast": null, "code": "export function isNotGrey(color) {\n  // eslint-disable-next-line no-useless-escape\n  const match = (color || '').match(/rgba?\\((\\d*), (\\d*), (\\d*)(, [\\d.]*)?\\)/);\n  if (match && match[1] && match[2] && match[3]) {\n    return !(match[1] === match[2] && match[2] === match[3]);\n  }\n  return true;\n}\nexport function isValidWaveColor(color) {\n  return color && color !== '#fff' && color !== '#ffffff' && color !== 'rgb(255, 255, 255)' && color !== 'rgba(255, 255, 255, 1)' && isNotGrey(color) && !/rgba\\((?:\\d*, ){3}0\\)/.test(color) &&\n  // any transparent rgba color\n  color !== 'transparent';\n}\nexport function getTargetWaveColor(node) {\n  const {\n    borderTopColor,\n    borderColor,\n    backgroundColor\n  } = getComputedStyle(node);\n  if (isValidWaveColor(borderTopColor)) {\n    return borderTopColor;\n  }\n  if (isValidWaveColor(borderColor)) {\n    return borderColor;\n  }\n  if (isValidWaveColor(backgroundColor)) {\n    return backgroundColor;\n  }\n  return null;\n}", "map": {"version": 3, "names": ["isNotGrey", "color", "match", "isValidWaveColor", "test", "getTargetWaveColor", "node", "borderTopColor", "borderColor", "backgroundColor", "getComputedStyle"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/_util/wave/util.js"], "sourcesContent": ["export function isNotGrey(color) {\n  // eslint-disable-next-line no-useless-escape\n  const match = (color || '').match(/rgba?\\((\\d*), (\\d*), (\\d*)(, [\\d.]*)?\\)/);\n  if (match && match[1] && match[2] && match[3]) {\n    return !(match[1] === match[2] && match[2] === match[3]);\n  }\n  return true;\n}\nexport function isValidWaveColor(color) {\n  return color && color !== '#fff' && color !== '#ffffff' && color !== 'rgb(255, 255, 255)' && color !== 'rgba(255, 255, 255, 1)' && isNotGrey(color) && !/rgba\\((?:\\d*, ){3}0\\)/.test(color) &&\n  // any transparent rgba color\n  color !== 'transparent';\n}\nexport function getTargetWaveColor(node) {\n  const {\n    borderTopColor,\n    borderColor,\n    backgroundColor\n  } = getComputedStyle(node);\n  if (isValidWaveColor(borderTopColor)) {\n    return borderTopColor;\n  }\n  if (isValidWaveColor(borderColor)) {\n    return borderColor;\n  }\n  if (isValidWaveColor(backgroundColor)) {\n    return backgroundColor;\n  }\n  return null;\n}"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,KAAK,EAAE;EAC/B;EACA,MAAMC,KAAK,GAAG,CAACD,KAAK,IAAI,EAAE,EAAEC,KAAK,CAAC,yCAAyC,CAAC;EAC5E,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;IAC7C,OAAO,EAAEA,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1D;EACA,OAAO,IAAI;AACb;AACA,OAAO,SAASC,gBAAgBA,CAACF,KAAK,EAAE;EACtC,OAAOA,KAAK,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,oBAAoB,IAAIA,KAAK,KAAK,wBAAwB,IAAID,SAAS,CAACC,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAACG,IAAI,CAACH,KAAK,CAAC;EAC3L;EACAA,KAAK,KAAK,aAAa;AACzB;AACA,OAAO,SAASI,kBAAkBA,CAACC,IAAI,EAAE;EACvC,MAAM;IACJC,cAAc;IACdC,WAAW;IACXC;EACF,CAAC,GAAGC,gBAAgB,CAACJ,IAAI,CAAC;EAC1B,IAAIH,gBAAgB,CAACI,cAAc,CAAC,EAAE;IACpC,OAAOA,cAAc;EACvB;EACA,IAAIJ,gBAAgB,CAACK,WAAW,CAAC,EAAE;IACjC,OAAOA,WAAW;EACpB;EACA,IAAIL,gBAAgB,CAACM,eAAe,CAAC,EAAE;IACrC,OAAOA,eAAe;EACxB;EACA,OAAO,IAAI;AACb"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}