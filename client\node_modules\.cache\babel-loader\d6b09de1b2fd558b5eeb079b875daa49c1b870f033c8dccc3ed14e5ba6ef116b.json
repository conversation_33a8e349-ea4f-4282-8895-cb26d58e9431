{"ast": null, "code": "import { operationUnit } from '../../style';\nconst genExpandStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    controlInteractiveSize: checkboxSize,\n    motionDurationSlow,\n    lineWidth,\n    paddingXS,\n    lineType,\n    tableBorderColor,\n    tableExpandIconBg,\n    tableExpandColumnWidth,\n    borderRadius,\n    fontSize,\n    fontSizeSM,\n    lineHeight,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    tableExpandedRowBg,\n    paddingXXS\n  } = token;\n  const halfInnerSize = checkboxSize / 2 - lineWidth;\n  // must be odd number, unless it cannot align center\n  const expandIconSize = halfInnerSize * 2 + lineWidth * 3;\n  const tableBorder = `${lineWidth}px ${lineType} ${tableBorderColor}`;\n  const expandIconLineOffset = paddingXXS - lineWidth;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-expand-icon-col`]: {\n        width: tableExpandColumnWidth\n      },\n      [`${componentCls}-row-expand-icon-cell`]: {\n        textAlign: 'center',\n        [`${componentCls}-row-expand-icon`]: {\n          display: 'inline-flex',\n          float: 'none',\n          verticalAlign: 'sub'\n        }\n      },\n      [`${componentCls}-row-indent`]: {\n        height: 1,\n        float: 'left'\n      },\n      [`${componentCls}-row-expand-icon`]: Object.assign(Object.assign({}, operationUnit(token)), {\n        position: 'relative',\n        float: 'left',\n        boxSizing: 'border-box',\n        width: expandIconSize,\n        height: expandIconSize,\n        padding: 0,\n        color: 'inherit',\n        lineHeight: `${expandIconSize}px`,\n        background: tableExpandIconBg,\n        border: tableBorder,\n        borderRadius,\n        transform: `scale(${checkboxSize / expandIconSize})`,\n        transition: `all ${motionDurationSlow}`,\n        userSelect: 'none',\n        [`&:focus, &:hover, &:active`]: {\n          borderColor: 'currentcolor'\n        },\n        [`&::before, &::after`]: {\n          position: 'absolute',\n          background: 'currentcolor',\n          transition: `transform ${motionDurationSlow} ease-out`,\n          content: '\"\"'\n        },\n        '&::before': {\n          top: halfInnerSize,\n          insetInlineEnd: expandIconLineOffset,\n          insetInlineStart: expandIconLineOffset,\n          height: lineWidth\n        },\n        '&::after': {\n          top: expandIconLineOffset,\n          bottom: expandIconLineOffset,\n          insetInlineStart: halfInnerSize,\n          width: lineWidth,\n          transform: 'rotate(90deg)'\n        },\n        // Motion effect\n        '&-collapsed::before': {\n          transform: 'rotate(-180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        },\n        '&-spaced': {\n          '&::before, &::after': {\n            display: 'none',\n            content: 'none'\n          },\n          background: 'transparent',\n          border: 0,\n          visibility: 'hidden'\n        }\n      }),\n      [`${componentCls}-row-indent + ${componentCls}-row-expand-icon`]: {\n        marginTop: (fontSize * lineHeight - lineWidth * 3) / 2 - Math.ceil((fontSizeSM * 1.4 - lineWidth * 3) / 2),\n        marginInlineEnd: paddingXS\n      },\n      [`tr${componentCls}-expanded-row`]: {\n        '&, &:hover': {\n          [`> th, > td`]: {\n            background: tableExpandedRowBg\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/25573\n        [`${antCls}-descriptions-view`]: {\n          display: 'flex',\n          table: {\n            flex: 'auto',\n            width: 'auto'\n          }\n        }\n      },\n      // With fixed\n      [`${componentCls}-expanded-row-fixed`]: {\n        position: 'relative',\n        margin: `-${tablePaddingVertical}px -${tablePaddingHorizontal}px`,\n        padding: `${tablePaddingVertical}px ${tablePaddingHorizontal}px`\n      }\n    }\n  };\n};\nexport default genExpandStyle;", "map": {"version": 3, "names": ["operationUnit", "genExpandStyle", "token", "componentCls", "antCls", "controlInteractiveSize", "checkboxSize", "motionDurationSlow", "lineWidth", "paddingXS", "lineType", "tableBorderColor", "tableExpandIconBg", "tableExpandColumnWidth", "borderRadius", "fontSize", "fontSizeSM", "lineHeight", "tablePaddingVertical", "tablePaddingHorizontal", "tableExpandedRowBg", "paddingXXS", "halfInnerSize", "expandIconSize", "tableBorder", "expandIconLineOffset", "width", "textAlign", "display", "float", "verticalAlign", "height", "Object", "assign", "position", "boxSizing", "padding", "color", "background", "border", "transform", "transition", "userSelect", "borderColor", "content", "top", "insetInlineEnd", "insetInlineStart", "bottom", "visibility", "marginTop", "Math", "ceil", "marginInlineEnd", "table", "flex", "margin"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/expand.js"], "sourcesContent": ["import { operationUnit } from '../../style';\nconst genExpandStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    controlInteractiveSize: checkboxSize,\n    motionDurationSlow,\n    lineWidth,\n    paddingXS,\n    lineType,\n    tableBorderColor,\n    tableExpandIconBg,\n    tableExpandColumnWidth,\n    borderRadius,\n    fontSize,\n    fontSizeSM,\n    lineHeight,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    tableExpandedRowBg,\n    paddingXXS\n  } = token;\n  const halfInnerSize = checkboxSize / 2 - lineWidth;\n  // must be odd number, unless it cannot align center\n  const expandIconSize = halfInnerSize * 2 + lineWidth * 3;\n  const tableBorder = `${lineWidth}px ${lineType} ${tableBorderColor}`;\n  const expandIconLineOffset = paddingXXS - lineWidth;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-expand-icon-col`]: {\n        width: tableExpandColumnWidth\n      },\n      [`${componentCls}-row-expand-icon-cell`]: {\n        textAlign: 'center',\n        [`${componentCls}-row-expand-icon`]: {\n          display: 'inline-flex',\n          float: 'none',\n          verticalAlign: 'sub'\n        }\n      },\n      [`${componentCls}-row-indent`]: {\n        height: 1,\n        float: 'left'\n      },\n      [`${componentCls}-row-expand-icon`]: Object.assign(Object.assign({}, operationUnit(token)), {\n        position: 'relative',\n        float: 'left',\n        boxSizing: 'border-box',\n        width: expandIconSize,\n        height: expandIconSize,\n        padding: 0,\n        color: 'inherit',\n        lineHeight: `${expandIconSize}px`,\n        background: tableExpandIconBg,\n        border: tableBorder,\n        borderRadius,\n        transform: `scale(${checkboxSize / expandIconSize})`,\n        transition: `all ${motionDurationSlow}`,\n        userSelect: 'none',\n        [`&:focus, &:hover, &:active`]: {\n          borderColor: 'currentcolor'\n        },\n        [`&::before, &::after`]: {\n          position: 'absolute',\n          background: 'currentcolor',\n          transition: `transform ${motionDurationSlow} ease-out`,\n          content: '\"\"'\n        },\n        '&::before': {\n          top: halfInnerSize,\n          insetInlineEnd: expandIconLineOffset,\n          insetInlineStart: expandIconLineOffset,\n          height: lineWidth\n        },\n        '&::after': {\n          top: expandIconLineOffset,\n          bottom: expandIconLineOffset,\n          insetInlineStart: halfInnerSize,\n          width: lineWidth,\n          transform: 'rotate(90deg)'\n        },\n        // Motion effect\n        '&-collapsed::before': {\n          transform: 'rotate(-180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        },\n        '&-spaced': {\n          '&::before, &::after': {\n            display: 'none',\n            content: 'none'\n          },\n          background: 'transparent',\n          border: 0,\n          visibility: 'hidden'\n        }\n      }),\n      [`${componentCls}-row-indent + ${componentCls}-row-expand-icon`]: {\n        marginTop: (fontSize * lineHeight - lineWidth * 3) / 2 - Math.ceil((fontSizeSM * 1.4 - lineWidth * 3) / 2),\n        marginInlineEnd: paddingXS\n      },\n      [`tr${componentCls}-expanded-row`]: {\n        '&, &:hover': {\n          [`> th, > td`]: {\n            background: tableExpandedRowBg\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/25573\n        [`${antCls}-descriptions-view`]: {\n          display: 'flex',\n          table: {\n            flex: 'auto',\n            width: 'auto'\n          }\n        }\n      },\n      // With fixed\n      [`${componentCls}-expanded-row-fixed`]: {\n        position: 'relative',\n        margin: `-${tablePaddingVertical}px -${tablePaddingHorizontal}px`,\n        padding: `${tablePaddingVertical}px ${tablePaddingHorizontal}px`\n      }\n    }\n  };\n};\nexport default genExpandStyle;"], "mappings": "AAAA,SAASA,aAAa,QAAQ,aAAa;AAC3C,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,sBAAsB,EAAEC,YAAY;IACpCC,kBAAkB;IAClBC,SAAS;IACTC,SAAS;IACTC,QAAQ;IACRC,gBAAgB;IAChBC,iBAAiB;IACjBC,sBAAsB;IACtBC,YAAY;IACZC,QAAQ;IACRC,UAAU;IACVC,UAAU;IACVC,oBAAoB;IACpBC,sBAAsB;IACtBC,kBAAkB;IAClBC;EACF,CAAC,GAAGnB,KAAK;EACT,MAAMoB,aAAa,GAAGhB,YAAY,GAAG,CAAC,GAAGE,SAAS;EAClD;EACA,MAAMe,cAAc,GAAGD,aAAa,GAAG,CAAC,GAAGd,SAAS,GAAG,CAAC;EACxD,MAAMgB,WAAW,GAAI,GAAEhB,SAAU,MAAKE,QAAS,IAAGC,gBAAiB,EAAC;EACpE,MAAMc,oBAAoB,GAAGJ,UAAU,GAAGb,SAAS;EACnD,OAAO;IACL,CAAE,GAAEL,YAAa,UAAS,GAAG;MAC3B,CAAE,GAAEA,YAAa,kBAAiB,GAAG;QACnCuB,KAAK,EAAEb;MACT,CAAC;MACD,CAAE,GAAEV,YAAa,uBAAsB,GAAG;QACxCwB,SAAS,EAAE,QAAQ;QACnB,CAAE,GAAExB,YAAa,kBAAiB,GAAG;UACnCyB,OAAO,EAAE,aAAa;UACtBC,KAAK,EAAE,MAAM;UACbC,aAAa,EAAE;QACjB;MACF,CAAC;MACD,CAAE,GAAE3B,YAAa,aAAY,GAAG;QAC9B4B,MAAM,EAAE,CAAC;QACTF,KAAK,EAAE;MACT,CAAC;MACD,CAAE,GAAE1B,YAAa,kBAAiB,GAAG6B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjC,aAAa,CAACE,KAAK,CAAC,CAAC,EAAE;QAC1FgC,QAAQ,EAAE,UAAU;QACpBL,KAAK,EAAE,MAAM;QACbM,SAAS,EAAE,YAAY;QACvBT,KAAK,EAAEH,cAAc;QACrBQ,MAAM,EAAER,cAAc;QACtBa,OAAO,EAAE,CAAC;QACVC,KAAK,EAAE,SAAS;QAChBpB,UAAU,EAAG,GAAEM,cAAe,IAAG;QACjCe,UAAU,EAAE1B,iBAAiB;QAC7B2B,MAAM,EAAEf,WAAW;QACnBV,YAAY;QACZ0B,SAAS,EAAG,SAAQlC,YAAY,GAAGiB,cAAe,GAAE;QACpDkB,UAAU,EAAG,OAAMlC,kBAAmB,EAAC;QACvCmC,UAAU,EAAE,MAAM;QAClB,CAAE,4BAA2B,GAAG;UAC9BC,WAAW,EAAE;QACf,CAAC;QACD,CAAE,qBAAoB,GAAG;UACvBT,QAAQ,EAAE,UAAU;UACpBI,UAAU,EAAE,cAAc;UAC1BG,UAAU,EAAG,aAAYlC,kBAAmB,WAAU;UACtDqC,OAAO,EAAE;QACX,CAAC;QACD,WAAW,EAAE;UACXC,GAAG,EAAEvB,aAAa;UAClBwB,cAAc,EAAErB,oBAAoB;UACpCsB,gBAAgB,EAAEtB,oBAAoB;UACtCM,MAAM,EAAEvB;QACV,CAAC;QACD,UAAU,EAAE;UACVqC,GAAG,EAAEpB,oBAAoB;UACzBuB,MAAM,EAAEvB,oBAAoB;UAC5BsB,gBAAgB,EAAEzB,aAAa;UAC/BI,KAAK,EAAElB,SAAS;UAChBgC,SAAS,EAAE;QACb,CAAC;QACD;QACA,qBAAqB,EAAE;UACrBA,SAAS,EAAE;QACb,CAAC;QACD,oBAAoB,EAAE;UACpBA,SAAS,EAAE;QACb,CAAC;QACD,UAAU,EAAE;UACV,qBAAqB,EAAE;YACrBZ,OAAO,EAAE,MAAM;YACfgB,OAAO,EAAE;UACX,CAAC;UACDN,UAAU,EAAE,aAAa;UACzBC,MAAM,EAAE,CAAC;UACTU,UAAU,EAAE;QACd;MACF,CAAC,CAAC;MACF,CAAE,GAAE9C,YAAa,iBAAgBA,YAAa,kBAAiB,GAAG;QAChE+C,SAAS,EAAE,CAACnC,QAAQ,GAAGE,UAAU,GAAGT,SAAS,GAAG,CAAC,IAAI,CAAC,GAAG2C,IAAI,CAACC,IAAI,CAAC,CAACpC,UAAU,GAAG,GAAG,GAAGR,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1G6C,eAAe,EAAE5C;MACnB,CAAC;MACD,CAAE,KAAIN,YAAa,eAAc,GAAG;QAClC,YAAY,EAAE;UACZ,CAAE,YAAW,GAAG;YACdmC,UAAU,EAAElB;UACd;QACF,CAAC;QACD;QACA,CAAE,GAAEhB,MAAO,oBAAmB,GAAG;UAC/BwB,OAAO,EAAE,MAAM;UACf0B,KAAK,EAAE;YACLC,IAAI,EAAE,MAAM;YACZ7B,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACD;MACA,CAAE,GAAEvB,YAAa,qBAAoB,GAAG;QACtC+B,QAAQ,EAAE,UAAU;QACpBsB,MAAM,EAAG,IAAGtC,oBAAqB,OAAMC,sBAAuB,IAAG;QACjEiB,OAAO,EAAG,GAAElB,oBAAqB,MAAKC,sBAAuB;MAC/D;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAelB,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}