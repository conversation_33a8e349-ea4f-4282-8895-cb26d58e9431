{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizStart.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Card, Button, Loading } from '../../../components/modern';\nimport { TbClock, TbQuestionMark, TbTrophy, TbAlertTriangle, TbPlay, TbArrowLeft, TbBrain } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizStart = () => {\n  _s();\n  var _examData$questions, _user$name, _user$name$charAt;\n  const [examData, setExamData] = useState(null);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({\n          examId: id\n        });\n        dispatch(HideLoading());\n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n  if (!examData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(Loading, {\n        fullScreen: true,\n        text: \"Loading quiz details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-6 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: examData.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600\",\n          children: \"Ready to test your knowledge?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl p-8 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-semibold text-gray-900 mb-6\",\n              children: \"Quiz Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-4 bg-blue-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Questions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-bold text-blue-600\",\n                  children: ((_examData$questions = examData.questions) === null || _examData$questions === void 0 ? void 0 : _examData$questions.length) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-4 bg-green-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Duration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-bold text-green-600\",\n                  children: [Math.floor(examData.duration / 60), \" minutes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-4 bg-purple-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Total Marks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-bold text-purple-600\",\n                  children: examData.totalMarks\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-4 bg-orange-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Passing Marks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-bold text-orange-600\",\n                  children: examData.passingMarks\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-semibold text-gray-900 mb-6\",\n              children: \"Instructions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 text-gray-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600 font-bold text-sm\",\n                    children: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Read each question carefully before answering\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600 font-bold text-sm\",\n                    children: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"You can navigate between questions using Previous/Next buttons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600 font-bold text-sm\",\n                    children: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Make sure to answer all questions before submitting\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600 font-bold text-sm\",\n                    children: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Keep an eye on the timer - the quiz will auto-submit when time runs out\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg p-6 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-600 font-bold text-lg\",\n              children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900\",\n              children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || 'Student']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: [\"Level: \", (user === null || user === void 0 ? void 0 : user.level) || 'Primary', \" \\u2022 Class: \", (user === null || user === void 0 ? void 0 : user.class) || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/user/quiz'),\n          className: \"px-8 py-4 bg-gray-100 text-gray-700 rounded-xl font-semibold hover:bg-gray-200 transition-colors duration-200 border-0 outline-none focus:outline-none\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleStartQuiz,\n          className: \"px-8 py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 shadow-lg border-0 outline-none focus:outline-none\",\n          children: \"Start Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizStart, \"f4+KHu+9zCjMDEp5I8yw6ulo0NM=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizStart;\nexport default QuizStart;\nvar _c;\n$RefreshReg$(_c, \"QuizStart\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useDispatch", "useSelector", "motion", "message", "getExamById", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Loading", "TbClock", "TbQuestionMark", "TbTrophy", "TbAlertTriangle", "TbPlay", "TbArrowLeft", "TbBrain", "jsxDEV", "_jsxDEV", "QuizStart", "_s", "_examData$questions", "_user$name", "_user$name$charAt", "examData", "setExamData", "id", "navigate", "dispatch", "user", "state", "fetchExamData", "response", "examId", "success", "data", "error", "handleStartQuiz", "className", "children", "fullScreen", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "questions", "length", "Math", "floor", "duration", "totalMarks", "passingMarks", "char<PERSON>t", "toUpperCase", "level", "class", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizStart.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { Card, Button, Loading } from '../../../components/modern';\nimport { TbClock, TbQuestionMark, TbTrophy, TbAlertTriangle, TbPlay, TbArrowLeft, TbBrain } from 'react-icons/tb';\n\nconst QuizStart = () => {\n  const [examData, setExamData] = useState(null);\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({ examId: id });\n        dispatch(HideLoading());\n        \n        if (response.success) {\n          setExamData(response.data);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  const handleStartQuiz = () => {\n    navigate(`/quiz/${id}/play`);\n  };\n\n  if (!examData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center\">\n        <Loading fullScreen text=\"Loading quiz details...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"max-w-4xl mx-auto px-6 py-12\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            {examData.name}\n          </h1>\n          <p className=\"text-xl text-gray-600\">\n            Ready to test your knowledge?\n          </p>\n        </div>\n\n        {/* Quiz Info Card */}\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 mb-8\">\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            {/* Quiz Details */}\n            <div>\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-6\">Quiz Details</h2>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between p-4 bg-blue-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">Questions</span>\n                  <span className=\"font-bold text-blue-600\">{examData.questions?.length || 0}</span>\n                </div>\n                <div className=\"flex items-center justify-between p-4 bg-green-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">Duration</span>\n                  <span className=\"font-bold text-green-600\">{Math.floor(examData.duration / 60)} minutes</span>\n                </div>\n                <div className=\"flex items-center justify-between p-4 bg-purple-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">Total Marks</span>\n                  <span className=\"font-bold text-purple-600\">{examData.totalMarks}</span>\n                </div>\n                <div className=\"flex items-center justify-between p-4 bg-orange-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">Passing Marks</span>\n                  <span className=\"font-bold text-orange-600\">{examData.passingMarks}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Instructions */}\n            <div>\n              <h2 className=\"text-2xl font-semibold text-gray-900 mb-6\">Instructions</h2>\n              <div className=\"space-y-3 text-gray-700\">\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\n                    <span className=\"text-blue-600 font-bold text-sm\">1</span>\n                  </div>\n                  <p>Read each question carefully before answering</p>\n                </div>\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\n                    <span className=\"text-blue-600 font-bold text-sm\">2</span>\n                  </div>\n                  <p>You can navigate between questions using Previous/Next buttons</p>\n                </div>\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\n                    <span className=\"text-blue-600 font-bold text-sm\">3</span>\n                  </div>\n                  <p>Make sure to answer all questions before submitting</p>\n                </div>\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\n                    <span className=\"text-blue-600 font-bold text-sm\">4</span>\n                  </div>\n                  <p>Keep an eye on the timer - the quiz will auto-submit when time runs out</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* User Info */}\n        <div className=\"bg-white rounded-xl shadow-lg p-6 mb-8\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\n              <span className=\"text-blue-600 font-bold text-lg\">\n                {user?.name?.charAt(0)?.toUpperCase() || 'U'}\n              </span>\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Welcome, {user?.name || 'Student'}</h3>\n              <p className=\"text-gray-600\">Level: {user?.level || 'Primary'} • Class: {user?.class || 'N/A'}</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <button\n            onClick={() => navigate('/user/quiz')}\n            className=\"px-8 py-4 bg-gray-100 text-gray-700 rounded-xl font-semibold hover:bg-gray-200 transition-colors duration-200 border-0 outline-none focus:outline-none\"\n          >\n            Back to Quizzes\n          </button>\n          <button\n            onClick={handleStartQuiz}\n            className=\"px-8 py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 shadow-lg border-0 outline-none focus:outline-none\"\n          >\n            Start Quiz\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizStart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,4BAA4B;AAClE,SAASC,OAAO,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElH,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,UAAA,EAAAC,iBAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM;IAAE8B;EAAG,CAAC,GAAG5B,SAAS,CAAC,CAAC;EAC1B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B;EAAK,CAAC,GAAG5B,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnDhC,SAAS,CAAC,MAAM;IACd,MAAMkC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAM0B,QAAQ,GAAG,MAAM5B,WAAW,CAAC;UAAE6B,MAAM,EAAEP;QAAG,CAAC,CAAC;QAClDE,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAI2B,QAAQ,CAACE,OAAO,EAAE;UACpBT,WAAW,CAACO,QAAQ,CAACG,IAAI,CAAC;QAC5B,CAAC,MAAM;UACLhC,OAAO,CAACiC,KAAK,CAACJ,QAAQ,CAAC7B,OAAO,CAAC;UAC/BwB,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdR,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;QACvBF,OAAO,CAACiC,KAAK,CAACA,KAAK,CAACjC,OAAO,CAAC;QAC5BwB,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAED,IAAID,EAAE,EAAE;MACNK,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACL,EAAE,EAAEE,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE5B,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5BV,QAAQ,CAAE,SAAQD,EAAG,OAAM,CAAC;EAC9B,CAAC;EAED,IAAI,CAACF,QAAQ,EAAE;IACb,oBACEN,OAAA;MAAKoB,SAAS,EAAC,yFAAyF;MAAAC,QAAA,eACtGrB,OAAA,CAACT,OAAO;QAAC+B,UAAU;QAACC,IAAI,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEV;EAEA,oBACE3B,OAAA;IAAKoB,SAAS,EAAC,2DAA2D;IAAAC,QAAA,eACxErB,OAAA;MAAKoB,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAE3CrB,OAAA;QAAKoB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrB,OAAA;UAAIoB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAClDf,QAAQ,CAACsB;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACL3B,OAAA;UAAGoB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN3B,OAAA;QAAKoB,SAAS,EAAC,yCAAyC;QAAAC,QAAA,eACtDrB,OAAA;UAAKoB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAExCrB,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAIoB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3E3B,OAAA;cAAKoB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrB,OAAA;gBAAKoB,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAC1ErB,OAAA;kBAAMoB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5D3B,OAAA;kBAAMoB,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAE,EAAAlB,mBAAA,GAAAG,QAAQ,CAACuB,SAAS,cAAA1B,mBAAA,uBAAlBA,mBAAA,CAAoB2B,MAAM,KAAI;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,eACN3B,OAAA;gBAAKoB,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3ErB,OAAA;kBAAMoB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3D3B,OAAA;kBAAMoB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,GAAEU,IAAI,CAACC,KAAK,CAAC1B,QAAQ,CAAC2B,QAAQ,GAAG,EAAE,CAAC,EAAC,UAAQ;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC,eACN3B,OAAA;gBAAKoB,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,gBAC5ErB,OAAA;kBAAMoB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9D3B,OAAA;kBAAMoB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEf,QAAQ,CAAC4B;gBAAU;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACN3B,OAAA;gBAAKoB,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,gBAC5ErB,OAAA;kBAAMoB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChE3B,OAAA;kBAAMoB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEf,QAAQ,CAAC6B;gBAAY;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3B,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAIoB,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3E3B,OAAA;cAAKoB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCrB,OAAA;gBAAKoB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCrB,OAAA;kBAAKoB,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,eACrGrB,OAAA;oBAAMoB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACN3B,OAAA;kBAAAqB,QAAA,EAAG;gBAA6C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN3B,OAAA;gBAAKoB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCrB,OAAA;kBAAKoB,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,eACrGrB,OAAA;oBAAMoB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACN3B,OAAA;kBAAAqB,QAAA,EAAG;gBAA8D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACN3B,OAAA;gBAAKoB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCrB,OAAA;kBAAKoB,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,eACrGrB,OAAA;oBAAMoB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACN3B,OAAA;kBAAAqB,QAAA,EAAG;gBAAmD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN3B,OAAA;gBAAKoB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCrB,OAAA;kBAAKoB,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,eACrGrB,OAAA;oBAAMoB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACN3B,OAAA;kBAAAqB,QAAA,EAAG;gBAAuE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAKoB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDrB,OAAA;UAAKoB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CrB,OAAA;YAAKoB,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClFrB,OAAA;cAAMoB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC9C,CAAAV,IAAI,aAAJA,IAAI,wBAAAP,UAAA,GAAJO,IAAI,CAAEiB,IAAI,cAAAxB,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYgC,MAAM,CAAC,CAAC,CAAC,cAAA/B,iBAAA,uBAArBA,iBAAA,CAAuBgC,WAAW,CAAC,CAAC,KAAI;YAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN3B,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAIoB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GAAC,WAAS,EAAC,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,IAAI,KAAI,SAAS;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnF3B,OAAA;cAAGoB,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,SAAO,EAAC,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,KAAK,KAAI,SAAS,EAAC,iBAAU,EAAC,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,KAAK,KAAI,KAAK;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAKoB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DrB,OAAA;UACEwC,OAAO,EAAEA,CAAA,KAAM/B,QAAQ,CAAC,YAAY,CAAE;UACtCW,SAAS,EAAC,wJAAwJ;UAAAC,QAAA,EACnK;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3B,OAAA;UACEwC,OAAO,EAAErB,eAAgB;UACzBC,SAAS,EAAC,+JAA+J;UAAAC,QAAA,EAC1K;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CArJID,SAAS;EAAA,QAEErB,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAA0D,EAAA,GALxBxC,SAAS;AAuJf,eAAeA,SAAS;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}