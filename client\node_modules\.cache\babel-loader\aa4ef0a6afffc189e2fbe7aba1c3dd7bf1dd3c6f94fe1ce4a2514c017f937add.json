{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar _excluded = [\"disabled\", \"title\", \"children\", \"style\", \"className\"];\nimport classNames from 'classnames';\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport List from 'rc-virtual-list';\nimport * as React from 'react';\nimport { useEffect } from 'react';\nimport useBaseProps from \"./hooks/useBaseProps\";\nimport SelectContext from \"./SelectContext\";\nimport TransBtn from \"./TransBtn\";\nimport { isPlatformMac } from \"./utils/platformUtil\";\n\n// export interface OptionListProps<OptionsType extends object[]> {\n\nfunction isTitleType(content) {\n  return typeof content === 'string' || typeof content === 'number';\n}\n\n/**\n * Using virtual list of option display.\n * Will fallback to dom if use customize render.\n */\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    id = _useBaseProps.id,\n    open = _useBaseProps.open,\n    multiple = _useBaseProps.multiple,\n    mode = _useBaseProps.mode,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    notFoundContent = _useBaseProps.notFoundContent,\n    onPopupScroll = _useBaseProps.onPopupScroll;\n  var _React$useContext = React.useContext(SelectContext),\n    flattenOptions = _React$useContext.flattenOptions,\n    onActiveValue = _React$useContext.onActiveValue,\n    defaultActiveFirstOption = _React$useContext.defaultActiveFirstOption,\n    onSelect = _React$useContext.onSelect,\n    menuItemSelectedIcon = _React$useContext.menuItemSelectedIcon,\n    rawValues = _React$useContext.rawValues,\n    fieldNames = _React$useContext.fieldNames,\n    virtual = _React$useContext.virtual,\n    direction = _React$useContext.direction,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight;\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n  var memoFlattenOptions = useMemo(function () {\n    return flattenOptions;\n  }, [open, flattenOptions], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // =========================== List ===========================\n  var listRef = React.useRef(null);\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var scrollIntoView = function scrollIntoView(args) {\n    if (listRef.current) {\n      listRef.current.scrollTo(typeof args === 'number' ? {\n        index: args\n      } : args);\n    }\n  };\n\n  // ========================== Active ==========================\n  var getEnabledActiveIndex = function getEnabledActiveIndex(index) {\n    var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var len = memoFlattenOptions.length;\n    for (var i = 0; i < len; i += 1) {\n      var current = (index + i * offset + len) % len;\n      var _memoFlattenOptions$c = memoFlattenOptions[current],\n        group = _memoFlattenOptions$c.group,\n        data = _memoFlattenOptions$c.data;\n      if (!group && !data.disabled) {\n        return current;\n      }\n    }\n    return -1;\n  };\n  var _React$useState = React.useState(function () {\n      return getEnabledActiveIndex(0);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeIndex = _React$useState2[0],\n    setActiveIndex = _React$useState2[1];\n  var setActive = function setActive(index) {\n    var fromKeyboard = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    setActiveIndex(index);\n    var info = {\n      source: fromKeyboard ? 'keyboard' : 'mouse'\n    };\n\n    // Trigger active event\n    var flattenItem = memoFlattenOptions[index];\n    if (!flattenItem) {\n      onActiveValue(null, -1, info);\n      return;\n    }\n    onActiveValue(flattenItem.value, index, info);\n  };\n\n  // Auto active first item when list length or searchValue changed\n  useEffect(function () {\n    setActive(defaultActiveFirstOption !== false ? getEnabledActiveIndex(0) : -1);\n  }, [memoFlattenOptions.length, searchValue]);\n\n  // https://github.com/ant-design/ant-design/issues/34975\n  var isSelected = React.useCallback(function (value) {\n    return rawValues.has(value) && mode !== 'combobox';\n  }, [mode, _toConsumableArray(rawValues).toString(), rawValues.size]);\n\n  // Auto scroll to item position in single mode\n  useEffect(function () {\n    /**\n     * React will skip `onChange` when component update.\n     * `setActive` function will call root accessibility state update which makes re-render.\n     * So we need to delay to let Input component trigger onChange first.\n     */\n    var timeoutId = setTimeout(function () {\n      if (!multiple && open && rawValues.size === 1) {\n        var value = Array.from(rawValues)[0];\n        var index = memoFlattenOptions.findIndex(function (_ref) {\n          var data = _ref.data;\n          return data.value === value;\n        });\n        if (index !== -1) {\n          setActive(index);\n          scrollIntoView(index);\n        }\n      }\n    });\n\n    // Force trigger scrollbar visible when open\n    if (open) {\n      var _listRef$current;\n      (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.scrollTo(undefined);\n    }\n    return function () {\n      return clearTimeout(timeoutId);\n    };\n  }, [open, searchValue, flattenOptions.length]);\n\n  // ========================== Values ==========================\n  var onSelectValue = function onSelectValue(value) {\n    if (value !== undefined) {\n      onSelect(value, {\n        selected: !rawValues.has(value)\n      });\n    }\n\n    // Single mode should always close by select\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================= Keyboard =========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which,\n          ctrlKey = event.ctrlKey;\n        switch (which) {\n          // >>> Arrow keys & ctrl + n/p on Mac\n          case KeyCode.N:\n          case KeyCode.P:\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              } else if (isPlatformMac() && ctrlKey) {\n                if (which === KeyCode.N) {\n                  offset = 1;\n                } else if (which === KeyCode.P) {\n                  offset = -1;\n                }\n              }\n              if (offset !== 0) {\n                var nextActiveIndex = getEnabledActiveIndex(activeIndex + offset, offset);\n                scrollIntoView(nextActiveIndex);\n                setActive(nextActiveIndex, true);\n              }\n              break;\n            }\n\n          // >>> Select\n          case KeyCode.ENTER:\n            {\n              // value\n              var item = memoFlattenOptions[activeIndex];\n              if (item && !item.data.disabled) {\n                onSelectValue(item.value);\n              } else {\n                onSelectValue(undefined);\n              }\n              if (open) {\n                event.preventDefault();\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {},\n      scrollTo: function scrollTo(index) {\n        scrollIntoView(index);\n      }\n    };\n  });\n\n  // ========================== Render ==========================\n  if (memoFlattenOptions.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      id: \"\".concat(id, \"_list\"),\n      className: \"\".concat(itemPrefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var omitFieldNameList = Object.keys(fieldNames).map(function (key) {\n    return fieldNames[key];\n  });\n  var getLabel = function getLabel(item) {\n    return item.label;\n  };\n  function getItemAriaProps(item, index) {\n    var group = item.group;\n    return {\n      role: group ? 'presentation' : 'option',\n      id: \"\".concat(id, \"_list_\").concat(index)\n    };\n  }\n  var renderItem = function renderItem(index) {\n    var item = memoFlattenOptions[index];\n    if (!item) return null;\n    var itemData = item.data || {};\n    var value = itemData.value;\n    var group = item.group;\n    var attrs = pickAttrs(itemData, true);\n    var mergedLabel = getLabel(item);\n    return item ? /*#__PURE__*/React.createElement(\"div\", _extends({\n      \"aria-label\": typeof mergedLabel === 'string' && !group ? mergedLabel : null\n    }, attrs, {\n      key: index\n    }, getItemAriaProps(item, index), {\n      \"aria-selected\": isSelected(value)\n    }), value) : null;\n  };\n  var a11yProps = {\n    role: 'listbox',\n    id: \"\".concat(id, \"_list\")\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, virtual && /*#__PURE__*/React.createElement(\"div\", _extends({}, a11yProps, {\n    style: {\n      height: 0,\n      width: 0,\n      overflow: 'hidden'\n    }\n  }), renderItem(activeIndex - 1), renderItem(activeIndex), renderItem(activeIndex + 1)), /*#__PURE__*/React.createElement(List, {\n    itemKey: \"key\",\n    ref: listRef,\n    data: memoFlattenOptions,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    fullHeight: false,\n    onMouseDown: onListMouseDown,\n    onScroll: onPopupScroll,\n    virtual: virtual,\n    direction: direction,\n    innerProps: virtual ? null : a11yProps\n  }, function (item, itemIndex) {\n    var _classNames;\n    var group = item.group,\n      groupOption = item.groupOption,\n      data = item.data,\n      label = item.label,\n      value = item.value;\n    var key = data.key;\n\n    // Group\n    if (group) {\n      var _data$title;\n      var groupTitle = (_data$title = data.title) !== null && _data$title !== void 0 ? _data$title : isTitleType(label) ? label.toString() : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(itemPrefixCls, \"\".concat(itemPrefixCls, \"-group\")),\n        title: groupTitle\n      }, label !== undefined ? label : key);\n    }\n    var disabled = data.disabled,\n      title = data.title,\n      children = data.children,\n      style = data.style,\n      className = data.className,\n      otherProps = _objectWithoutProperties(data, _excluded);\n    var passedProps = omit(otherProps, omitFieldNameList);\n\n    // Option\n    var selected = isSelected(value);\n    var optionPrefixCls = \"\".concat(itemPrefixCls, \"-option\");\n    var optionClassName = classNames(itemPrefixCls, optionPrefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-grouped\"), groupOption), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-active\"), activeIndex === itemIndex && !disabled), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-selected\"), selected), _classNames));\n    var mergedLabel = getLabel(item);\n    var iconVisible = !menuItemSelectedIcon || typeof menuItemSelectedIcon === 'function' || selected;\n\n    // https://github.com/ant-design/ant-design/issues/34145\n    var content = typeof mergedLabel === 'number' ? mergedLabel : mergedLabel || value;\n    // https://github.com/ant-design/ant-design/issues/26717\n    var optionTitle = isTitleType(content) ? content.toString() : undefined;\n    if (title !== undefined) {\n      optionTitle = title;\n    }\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, pickAttrs(passedProps), !virtual ? getItemAriaProps(item, itemIndex) : {}, {\n      \"aria-selected\": selected,\n      className: optionClassName,\n      title: optionTitle,\n      onMouseMove: function onMouseMove() {\n        if (activeIndex === itemIndex || disabled) {\n          return;\n        }\n        setActive(itemIndex);\n      },\n      onClick: function onClick() {\n        if (!disabled) {\n          onSelectValue(value);\n        }\n      },\n      style: style\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(optionPrefixCls, \"-content\")\n    }, content), /*#__PURE__*/React.isValidElement(menuItemSelectedIcon) || selected, iconVisible && /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(itemPrefixCls, \"-option-state\"),\n      customizeIcon: menuItemSelectedIcon,\n      customizeIconProps: {\n        isSelected: selected\n      }\n    }, selected ? '✓' : null));\n  }));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nRefOptionList.displayName = 'OptionList';\nexport default RefOptionList;", "map": {"version": 3, "names": ["_defineProperty", "_objectWithoutProperties", "_extends", "_toConsumableArray", "_slicedToArray", "_excluded", "classNames", "useMemo", "KeyCode", "omit", "pickAttrs", "List", "React", "useEffect", "useBaseProps", "SelectContext", "TransBtn", "isPlatformMac", "isTitleType", "content", "OptionList", "_", "ref", "_useBaseProps", "prefixCls", "id", "open", "multiple", "mode", "searchValue", "toggle<PERSON><PERSON>", "notFoundContent", "onPopupScroll", "_React$useContext", "useContext", "flattenOptions", "onActiveValue", "defaultActiveFirstOption", "onSelect", "menuItemSelectedIcon", "rawValues", "fieldNames", "virtual", "direction", "listHeight", "listItemHeight", "itemPrefixCls", "concat", "memoFlattenOptions", "prev", "next", "listRef", "useRef", "onListMouseDown", "event", "preventDefault", "scrollIntoView", "args", "current", "scrollTo", "index", "getEnabledActiveIndex", "offset", "arguments", "length", "undefined", "len", "i", "_memoFlattenOptions$c", "group", "data", "disabled", "_React$useState", "useState", "_React$useState2", "activeIndex", "setActiveIndex", "setActive", "fromKeyboard", "info", "source", "flattenItem", "value", "isSelected", "useCallback", "has", "toString", "size", "timeoutId", "setTimeout", "Array", "from", "findIndex", "_ref", "_listRef$current", "clearTimeout", "onSelectValue", "selected", "useImperativeHandle", "onKeyDown", "which", "ctrl<PERSON>ey", "N", "P", "UP", "DOWN", "nextActiveIndex", "ENTER", "item", "ESC", "stopPropagation", "onKeyUp", "createElement", "role", "className", "onMouseDown", "omitFieldNameList", "Object", "keys", "map", "key", "get<PERSON><PERSON><PERSON>", "label", "getItemAriaProps", "renderItem", "itemData", "attrs", "mergedLabel", "a11yProps", "Fragment", "style", "height", "width", "overflow", "itemKey", "itemHeight", "fullHeight", "onScroll", "innerProps", "itemIndex", "_classNames", "groupOption", "_data$title", "groupTitle", "title", "children", "otherProps", "passedProps", "optionPrefixCls", "optionClassName", "iconVisible", "optionTitle", "onMouseMove", "onClick", "isValidElement", "customizeIcon", "customizeIconProps", "RefOptionList", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-select/es/OptionList.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar _excluded = [\"disabled\", \"title\", \"children\", \"style\", \"className\"];\nimport classNames from 'classnames';\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport List from 'rc-virtual-list';\nimport * as React from 'react';\nimport { useEffect } from 'react';\nimport useBaseProps from \"./hooks/useBaseProps\";\nimport SelectContext from \"./SelectContext\";\nimport TransBtn from \"./TransBtn\";\nimport { isPlatformMac } from \"./utils/platformUtil\";\n\n// export interface OptionListProps<OptionsType extends object[]> {\n\nfunction isTitleType(content) {\n  return typeof content === 'string' || typeof content === 'number';\n}\n\n/**\n * Using virtual list of option display.\n * Will fallback to dom if use customize render.\n */\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    id = _useBaseProps.id,\n    open = _useBaseProps.open,\n    multiple = _useBaseProps.multiple,\n    mode = _useBaseProps.mode,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    notFoundContent = _useBaseProps.notFoundContent,\n    onPopupScroll = _useBaseProps.onPopupScroll;\n  var _React$useContext = React.useContext(SelectContext),\n    flattenOptions = _React$useContext.flattenOptions,\n    onActiveValue = _React$useContext.onActiveValue,\n    defaultActiveFirstOption = _React$useContext.defaultActiveFirstOption,\n    onSelect = _React$useContext.onSelect,\n    menuItemSelectedIcon = _React$useContext.menuItemSelectedIcon,\n    rawValues = _React$useContext.rawValues,\n    fieldNames = _React$useContext.fieldNames,\n    virtual = _React$useContext.virtual,\n    direction = _React$useContext.direction,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight;\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n  var memoFlattenOptions = useMemo(function () {\n    return flattenOptions;\n  }, [open, flattenOptions], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // =========================== List ===========================\n  var listRef = React.useRef(null);\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var scrollIntoView = function scrollIntoView(args) {\n    if (listRef.current) {\n      listRef.current.scrollTo(typeof args === 'number' ? {\n        index: args\n      } : args);\n    }\n  };\n\n  // ========================== Active ==========================\n  var getEnabledActiveIndex = function getEnabledActiveIndex(index) {\n    var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var len = memoFlattenOptions.length;\n    for (var i = 0; i < len; i += 1) {\n      var current = (index + i * offset + len) % len;\n      var _memoFlattenOptions$c = memoFlattenOptions[current],\n        group = _memoFlattenOptions$c.group,\n        data = _memoFlattenOptions$c.data;\n      if (!group && !data.disabled) {\n        return current;\n      }\n    }\n    return -1;\n  };\n  var _React$useState = React.useState(function () {\n      return getEnabledActiveIndex(0);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeIndex = _React$useState2[0],\n    setActiveIndex = _React$useState2[1];\n  var setActive = function setActive(index) {\n    var fromKeyboard = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    setActiveIndex(index);\n    var info = {\n      source: fromKeyboard ? 'keyboard' : 'mouse'\n    };\n\n    // Trigger active event\n    var flattenItem = memoFlattenOptions[index];\n    if (!flattenItem) {\n      onActiveValue(null, -1, info);\n      return;\n    }\n    onActiveValue(flattenItem.value, index, info);\n  };\n\n  // Auto active first item when list length or searchValue changed\n  useEffect(function () {\n    setActive(defaultActiveFirstOption !== false ? getEnabledActiveIndex(0) : -1);\n  }, [memoFlattenOptions.length, searchValue]);\n\n  // https://github.com/ant-design/ant-design/issues/34975\n  var isSelected = React.useCallback(function (value) {\n    return rawValues.has(value) && mode !== 'combobox';\n  }, [mode, _toConsumableArray(rawValues).toString(), rawValues.size]);\n\n  // Auto scroll to item position in single mode\n  useEffect(function () {\n    /**\n     * React will skip `onChange` when component update.\n     * `setActive` function will call root accessibility state update which makes re-render.\n     * So we need to delay to let Input component trigger onChange first.\n     */\n    var timeoutId = setTimeout(function () {\n      if (!multiple && open && rawValues.size === 1) {\n        var value = Array.from(rawValues)[0];\n        var index = memoFlattenOptions.findIndex(function (_ref) {\n          var data = _ref.data;\n          return data.value === value;\n        });\n        if (index !== -1) {\n          setActive(index);\n          scrollIntoView(index);\n        }\n      }\n    });\n\n    // Force trigger scrollbar visible when open\n    if (open) {\n      var _listRef$current;\n      (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.scrollTo(undefined);\n    }\n    return function () {\n      return clearTimeout(timeoutId);\n    };\n  }, [open, searchValue, flattenOptions.length]);\n\n  // ========================== Values ==========================\n  var onSelectValue = function onSelectValue(value) {\n    if (value !== undefined) {\n      onSelect(value, {\n        selected: !rawValues.has(value)\n      });\n    }\n\n    // Single mode should always close by select\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================= Keyboard =========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which,\n          ctrlKey = event.ctrlKey;\n        switch (which) {\n          // >>> Arrow keys & ctrl + n/p on Mac\n          case KeyCode.N:\n          case KeyCode.P:\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              } else if (isPlatformMac() && ctrlKey) {\n                if (which === KeyCode.N) {\n                  offset = 1;\n                } else if (which === KeyCode.P) {\n                  offset = -1;\n                }\n              }\n              if (offset !== 0) {\n                var nextActiveIndex = getEnabledActiveIndex(activeIndex + offset, offset);\n                scrollIntoView(nextActiveIndex);\n                setActive(nextActiveIndex, true);\n              }\n              break;\n            }\n\n          // >>> Select\n          case KeyCode.ENTER:\n            {\n              // value\n              var item = memoFlattenOptions[activeIndex];\n              if (item && !item.data.disabled) {\n                onSelectValue(item.value);\n              } else {\n                onSelectValue(undefined);\n              }\n              if (open) {\n                event.preventDefault();\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {},\n      scrollTo: function scrollTo(index) {\n        scrollIntoView(index);\n      }\n    };\n  });\n\n  // ========================== Render ==========================\n  if (memoFlattenOptions.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      id: \"\".concat(id, \"_list\"),\n      className: \"\".concat(itemPrefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var omitFieldNameList = Object.keys(fieldNames).map(function (key) {\n    return fieldNames[key];\n  });\n  var getLabel = function getLabel(item) {\n    return item.label;\n  };\n  function getItemAriaProps(item, index) {\n    var group = item.group;\n    return {\n      role: group ? 'presentation' : 'option',\n      id: \"\".concat(id, \"_list_\").concat(index)\n    };\n  }\n  var renderItem = function renderItem(index) {\n    var item = memoFlattenOptions[index];\n    if (!item) return null;\n    var itemData = item.data || {};\n    var value = itemData.value;\n    var group = item.group;\n    var attrs = pickAttrs(itemData, true);\n    var mergedLabel = getLabel(item);\n    return item ? /*#__PURE__*/React.createElement(\"div\", _extends({\n      \"aria-label\": typeof mergedLabel === 'string' && !group ? mergedLabel : null\n    }, attrs, {\n      key: index\n    }, getItemAriaProps(item, index), {\n      \"aria-selected\": isSelected(value)\n    }), value) : null;\n  };\n  var a11yProps = {\n    role: 'listbox',\n    id: \"\".concat(id, \"_list\")\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, virtual && /*#__PURE__*/React.createElement(\"div\", _extends({}, a11yProps, {\n    style: {\n      height: 0,\n      width: 0,\n      overflow: 'hidden'\n    }\n  }), renderItem(activeIndex - 1), renderItem(activeIndex), renderItem(activeIndex + 1)), /*#__PURE__*/React.createElement(List, {\n    itemKey: \"key\",\n    ref: listRef,\n    data: memoFlattenOptions,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    fullHeight: false,\n    onMouseDown: onListMouseDown,\n    onScroll: onPopupScroll,\n    virtual: virtual,\n    direction: direction,\n    innerProps: virtual ? null : a11yProps\n  }, function (item, itemIndex) {\n    var _classNames;\n    var group = item.group,\n      groupOption = item.groupOption,\n      data = item.data,\n      label = item.label,\n      value = item.value;\n    var key = data.key;\n\n    // Group\n    if (group) {\n      var _data$title;\n      var groupTitle = (_data$title = data.title) !== null && _data$title !== void 0 ? _data$title : isTitleType(label) ? label.toString() : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(itemPrefixCls, \"\".concat(itemPrefixCls, \"-group\")),\n        title: groupTitle\n      }, label !== undefined ? label : key);\n    }\n    var disabled = data.disabled,\n      title = data.title,\n      children = data.children,\n      style = data.style,\n      className = data.className,\n      otherProps = _objectWithoutProperties(data, _excluded);\n    var passedProps = omit(otherProps, omitFieldNameList);\n\n    // Option\n    var selected = isSelected(value);\n    var optionPrefixCls = \"\".concat(itemPrefixCls, \"-option\");\n    var optionClassName = classNames(itemPrefixCls, optionPrefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-grouped\"), groupOption), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-active\"), activeIndex === itemIndex && !disabled), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(optionPrefixCls, \"-selected\"), selected), _classNames));\n    var mergedLabel = getLabel(item);\n    var iconVisible = !menuItemSelectedIcon || typeof menuItemSelectedIcon === 'function' || selected;\n\n    // https://github.com/ant-design/ant-design/issues/34145\n    var content = typeof mergedLabel === 'number' ? mergedLabel : mergedLabel || value;\n    // https://github.com/ant-design/ant-design/issues/26717\n    var optionTitle = isTitleType(content) ? content.toString() : undefined;\n    if (title !== undefined) {\n      optionTitle = title;\n    }\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, pickAttrs(passedProps), !virtual ? getItemAriaProps(item, itemIndex) : {}, {\n      \"aria-selected\": selected,\n      className: optionClassName,\n      title: optionTitle,\n      onMouseMove: function onMouseMove() {\n        if (activeIndex === itemIndex || disabled) {\n          return;\n        }\n        setActive(itemIndex);\n      },\n      onClick: function onClick() {\n        if (!disabled) {\n          onSelectValue(value);\n        }\n      },\n      style: style\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(optionPrefixCls, \"-content\")\n    }, content), /*#__PURE__*/React.isValidElement(menuItemSelectedIcon) || selected, iconVisible && /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(itemPrefixCls, \"-option-state\"),\n      customizeIcon: menuItemSelectedIcon,\n      customizeIconProps: {\n        isSelected: selected\n      }\n    }, selected ? '✓' : null));\n  }));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nRefOptionList.displayName = 'OptionList';\nexport default RefOptionList;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,aAAa,QAAQ,sBAAsB;;AAEpD;;AAEA,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC5B,OAAO,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ;AACnE;;AAEA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC3C,IAAIC,aAAa,GAAGT,YAAY,CAAC,CAAC;IAChCU,SAAS,GAAGD,aAAa,CAACC,SAAS;IACnCC,EAAE,GAAGF,aAAa,CAACE,EAAE;IACrBC,IAAI,GAAGH,aAAa,CAACG,IAAI;IACzBC,QAAQ,GAAGJ,aAAa,CAACI,QAAQ;IACjCC,IAAI,GAAGL,aAAa,CAACK,IAAI;IACzBC,WAAW,GAAGN,aAAa,CAACM,WAAW;IACvCC,UAAU,GAAGP,aAAa,CAACO,UAAU;IACrCC,eAAe,GAAGR,aAAa,CAACQ,eAAe;IAC/CC,aAAa,GAAGT,aAAa,CAACS,aAAa;EAC7C,IAAIC,iBAAiB,GAAGrB,KAAK,CAACsB,UAAU,CAACnB,aAAa,CAAC;IACrDoB,cAAc,GAAGF,iBAAiB,CAACE,cAAc;IACjDC,aAAa,GAAGH,iBAAiB,CAACG,aAAa;IAC/CC,wBAAwB,GAAGJ,iBAAiB,CAACI,wBAAwB;IACrEC,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACrCC,oBAAoB,GAAGN,iBAAiB,CAACM,oBAAoB;IAC7DC,SAAS,GAAGP,iBAAiB,CAACO,SAAS;IACvCC,UAAU,GAAGR,iBAAiB,CAACQ,UAAU;IACzCC,OAAO,GAAGT,iBAAiB,CAACS,OAAO;IACnCC,SAAS,GAAGV,iBAAiB,CAACU,SAAS;IACvCC,UAAU,GAAGX,iBAAiB,CAACW,UAAU;IACzCC,cAAc,GAAGZ,iBAAiB,CAACY,cAAc;EACnD,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACvB,SAAS,EAAE,OAAO,CAAC;EACjD,IAAIwB,kBAAkB,GAAGzC,OAAO,CAAC,YAAY;IAC3C,OAAO4B,cAAc;EACvB,CAAC,EAAE,CAACT,IAAI,EAAES,cAAc,CAAC,EAAE,UAAUc,IAAI,EAAEC,IAAI,EAAE;IAC/C,OAAOA,IAAI,CAAC,CAAC,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,CAAC;;EAEF;EACA,IAAIC,OAAO,GAAGvC,KAAK,CAACwC,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;IACpDA,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;IACjD,IAAIN,OAAO,CAACO,OAAO,EAAE;MACnBP,OAAO,CAACO,OAAO,CAACC,QAAQ,CAAC,OAAOF,IAAI,KAAK,QAAQ,GAAG;QAClDG,KAAK,EAAEH;MACT,CAAC,GAAGA,IAAI,CAAC;IACX;EACF,CAAC;;EAED;EACA,IAAII,qBAAqB,GAAG,SAASA,qBAAqBA,CAACD,KAAK,EAAE;IAChE,IAAIE,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAClF,IAAIG,GAAG,GAAGlB,kBAAkB,CAACgB,MAAM;IACnC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,IAAI,CAAC,EAAE;MAC/B,IAAIT,OAAO,GAAG,CAACE,KAAK,GAAGO,CAAC,GAAGL,MAAM,GAAGI,GAAG,IAAIA,GAAG;MAC9C,IAAIE,qBAAqB,GAAGpB,kBAAkB,CAACU,OAAO,CAAC;QACrDW,KAAK,GAAGD,qBAAqB,CAACC,KAAK;QACnCC,IAAI,GAAGF,qBAAqB,CAACE,IAAI;MACnC,IAAI,CAACD,KAAK,IAAI,CAACC,IAAI,CAACC,QAAQ,EAAE;QAC5B,OAAOb,OAAO;MAChB;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD,IAAIc,eAAe,GAAG5D,KAAK,CAAC6D,QAAQ,CAAC,YAAY;MAC7C,OAAOZ,qBAAqB,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC;IACFa,gBAAgB,GAAGtE,cAAc,CAACoE,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,SAAS,GAAG,SAASA,SAASA,CAACjB,KAAK,EAAE;IACxC,IAAIkB,YAAY,GAAGf,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC5Fa,cAAc,CAAChB,KAAK,CAAC;IACrB,IAAImB,IAAI,GAAG;MACTC,MAAM,EAAEF,YAAY,GAAG,UAAU,GAAG;IACtC,CAAC;;IAED;IACA,IAAIG,WAAW,GAAGjC,kBAAkB,CAACY,KAAK,CAAC;IAC3C,IAAI,CAACqB,WAAW,EAAE;MAChB7C,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE2C,IAAI,CAAC;MAC7B;IACF;IACA3C,aAAa,CAAC6C,WAAW,CAACC,KAAK,EAAEtB,KAAK,EAAEmB,IAAI,CAAC;EAC/C,CAAC;;EAED;EACAlE,SAAS,CAAC,YAAY;IACpBgE,SAAS,CAACxC,wBAAwB,KAAK,KAAK,GAAGwB,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/E,CAAC,EAAE,CAACb,kBAAkB,CAACgB,MAAM,EAAEnC,WAAW,CAAC,CAAC;;EAE5C;EACA,IAAIsD,UAAU,GAAGvE,KAAK,CAACwE,WAAW,CAAC,UAAUF,KAAK,EAAE;IAClD,OAAO1C,SAAS,CAAC6C,GAAG,CAACH,KAAK,CAAC,IAAItD,IAAI,KAAK,UAAU;EACpD,CAAC,EAAE,CAACA,IAAI,EAAEzB,kBAAkB,CAACqC,SAAS,CAAC,CAAC8C,QAAQ,CAAC,CAAC,EAAE9C,SAAS,CAAC+C,IAAI,CAAC,CAAC;;EAEpE;EACA1E,SAAS,CAAC,YAAY;IACpB;AACJ;AACA;AACA;AACA;IACI,IAAI2E,SAAS,GAAGC,UAAU,CAAC,YAAY;MACrC,IAAI,CAAC9D,QAAQ,IAAID,IAAI,IAAIc,SAAS,CAAC+C,IAAI,KAAK,CAAC,EAAE;QAC7C,IAAIL,KAAK,GAAGQ,KAAK,CAACC,IAAI,CAACnD,SAAS,CAAC,CAAC,CAAC,CAAC;QACpC,IAAIoB,KAAK,GAAGZ,kBAAkB,CAAC4C,SAAS,CAAC,UAAUC,IAAI,EAAE;UACvD,IAAIvB,IAAI,GAAGuB,IAAI,CAACvB,IAAI;UACpB,OAAOA,IAAI,CAACY,KAAK,KAAKA,KAAK;QAC7B,CAAC,CAAC;QACF,IAAItB,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBiB,SAAS,CAACjB,KAAK,CAAC;UAChBJ,cAAc,CAACI,KAAK,CAAC;QACvB;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIlC,IAAI,EAAE;MACR,IAAIoE,gBAAgB;MACpB,CAACA,gBAAgB,GAAG3C,OAAO,CAACO,OAAO,MAAM,IAAI,IAAIoC,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACnC,QAAQ,CAACM,SAAS,CAAC;IAC9H;IACA,OAAO,YAAY;MACjB,OAAO8B,YAAY,CAACP,SAAS,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,CAAC9D,IAAI,EAAEG,WAAW,EAAEM,cAAc,CAAC6B,MAAM,CAAC,CAAC;;EAE9C;EACA,IAAIgC,aAAa,GAAG,SAASA,aAAaA,CAACd,KAAK,EAAE;IAChD,IAAIA,KAAK,KAAKjB,SAAS,EAAE;MACvB3B,QAAQ,CAAC4C,KAAK,EAAE;QACde,QAAQ,EAAE,CAACzD,SAAS,CAAC6C,GAAG,CAACH,KAAK;MAChC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACvD,QAAQ,EAAE;MACbG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAlB,KAAK,CAACsF,mBAAmB,CAAC5E,GAAG,EAAE,YAAY;IACzC,OAAO;MACL6E,SAAS,EAAE,SAASA,SAASA,CAAC7C,KAAK,EAAE;QACnC,IAAI8C,KAAK,GAAG9C,KAAK,CAAC8C,KAAK;UACrBC,OAAO,GAAG/C,KAAK,CAAC+C,OAAO;QACzB,QAAQD,KAAK;UACX;UACA,KAAK5F,OAAO,CAAC8F,CAAC;UACd,KAAK9F,OAAO,CAAC+F,CAAC;UACd,KAAK/F,OAAO,CAACgG,EAAE;UACf,KAAKhG,OAAO,CAACiG,IAAI;YACf;cACE,IAAI3C,MAAM,GAAG,CAAC;cACd,IAAIsC,KAAK,KAAK5F,OAAO,CAACgG,EAAE,EAAE;gBACxB1C,MAAM,GAAG,CAAC,CAAC;cACb,CAAC,MAAM,IAAIsC,KAAK,KAAK5F,OAAO,CAACiG,IAAI,EAAE;gBACjC3C,MAAM,GAAG,CAAC;cACZ,CAAC,MAAM,IAAI7C,aAAa,CAAC,CAAC,IAAIoF,OAAO,EAAE;gBACrC,IAAID,KAAK,KAAK5F,OAAO,CAAC8F,CAAC,EAAE;kBACvBxC,MAAM,GAAG,CAAC;gBACZ,CAAC,MAAM,IAAIsC,KAAK,KAAK5F,OAAO,CAAC+F,CAAC,EAAE;kBAC9BzC,MAAM,GAAG,CAAC,CAAC;gBACb;cACF;cACA,IAAIA,MAAM,KAAK,CAAC,EAAE;gBAChB,IAAI4C,eAAe,GAAG7C,qBAAqB,CAACc,WAAW,GAAGb,MAAM,EAAEA,MAAM,CAAC;gBACzEN,cAAc,CAACkD,eAAe,CAAC;gBAC/B7B,SAAS,CAAC6B,eAAe,EAAE,IAAI,CAAC;cAClC;cACA;YACF;;UAEF;UACA,KAAKlG,OAAO,CAACmG,KAAK;YAChB;cACE;cACA,IAAIC,IAAI,GAAG5D,kBAAkB,CAAC2B,WAAW,CAAC;cAC1C,IAAIiC,IAAI,IAAI,CAACA,IAAI,CAACtC,IAAI,CAACC,QAAQ,EAAE;gBAC/ByB,aAAa,CAACY,IAAI,CAAC1B,KAAK,CAAC;cAC3B,CAAC,MAAM;gBACLc,aAAa,CAAC/B,SAAS,CAAC;cAC1B;cACA,IAAIvC,IAAI,EAAE;gBACR4B,KAAK,CAACC,cAAc,CAAC,CAAC;cACxB;cACA;YACF;;UAEF;UACA,KAAK/C,OAAO,CAACqG,GAAG;YACd;cACE/E,UAAU,CAAC,KAAK,CAAC;cACjB,IAAIJ,IAAI,EAAE;gBACR4B,KAAK,CAACwD,eAAe,CAAC,CAAC;cACzB;YACF;QACJ;MACF,CAAC;MACDC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC,CAAC;MAC9BpD,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;QACjCJ,cAAc,CAACI,KAAK,CAAC;MACvB;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,IAAIZ,kBAAkB,CAACgB,MAAM,KAAK,CAAC,EAAE;IACnC,OAAO,aAAapD,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;MAC7CC,IAAI,EAAE,SAAS;MACfxF,EAAE,EAAE,EAAE,CAACsB,MAAM,CAACtB,EAAE,EAAE,OAAO,CAAC;MAC1ByF,SAAS,EAAE,EAAE,CAACnE,MAAM,CAACD,aAAa,EAAE,QAAQ,CAAC;MAC7CqE,WAAW,EAAE9D;IACf,CAAC,EAAEtB,eAAe,CAAC;EACrB;EACA,IAAIqF,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAAC7E,UAAU,CAAC,CAAC8E,GAAG,CAAC,UAAUC,GAAG,EAAE;IACjE,OAAO/E,UAAU,CAAC+E,GAAG,CAAC;EACxB,CAAC,CAAC;EACF,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACb,IAAI,EAAE;IACrC,OAAOA,IAAI,CAACc,KAAK;EACnB,CAAC;EACD,SAASC,gBAAgBA,CAACf,IAAI,EAAEhD,KAAK,EAAE;IACrC,IAAIS,KAAK,GAAGuC,IAAI,CAACvC,KAAK;IACtB,OAAO;MACL4C,IAAI,EAAE5C,KAAK,GAAG,cAAc,GAAG,QAAQ;MACvC5C,EAAE,EAAE,EAAE,CAACsB,MAAM,CAACtB,EAAE,EAAE,QAAQ,CAAC,CAACsB,MAAM,CAACa,KAAK;IAC1C,CAAC;EACH;EACA,IAAIgE,UAAU,GAAG,SAASA,UAAUA,CAAChE,KAAK,EAAE;IAC1C,IAAIgD,IAAI,GAAG5D,kBAAkB,CAACY,KAAK,CAAC;IACpC,IAAI,CAACgD,IAAI,EAAE,OAAO,IAAI;IACtB,IAAIiB,QAAQ,GAAGjB,IAAI,CAACtC,IAAI,IAAI,CAAC,CAAC;IAC9B,IAAIY,KAAK,GAAG2C,QAAQ,CAAC3C,KAAK;IAC1B,IAAIb,KAAK,GAAGuC,IAAI,CAACvC,KAAK;IACtB,IAAIyD,KAAK,GAAGpH,SAAS,CAACmH,QAAQ,EAAE,IAAI,CAAC;IACrC,IAAIE,WAAW,GAAGN,QAAQ,CAACb,IAAI,CAAC;IAChC,OAAOA,IAAI,GAAG,aAAahG,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE9G,QAAQ,CAAC;MAC7D,YAAY,EAAE,OAAO6H,WAAW,KAAK,QAAQ,IAAI,CAAC1D,KAAK,GAAG0D,WAAW,GAAG;IAC1E,CAAC,EAAED,KAAK,EAAE;MACRN,GAAG,EAAE5D;IACP,CAAC,EAAE+D,gBAAgB,CAACf,IAAI,EAAEhD,KAAK,CAAC,EAAE;MAChC,eAAe,EAAEuB,UAAU,CAACD,KAAK;IACnC,CAAC,CAAC,EAAEA,KAAK,CAAC,GAAG,IAAI;EACnB,CAAC;EACD,IAAI8C,SAAS,GAAG;IACdf,IAAI,EAAE,SAAS;IACfxF,EAAE,EAAE,EAAE,CAACsB,MAAM,CAACtB,EAAE,EAAE,OAAO;EAC3B,CAAC;EACD,OAAO,aAAab,KAAK,CAACoG,aAAa,CAACpG,KAAK,CAACqH,QAAQ,EAAE,IAAI,EAAEvF,OAAO,IAAI,aAAa9B,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE9G,QAAQ,CAAC,CAAC,CAAC,EAAE8H,SAAS,EAAE;IACvIE,KAAK,EAAE;MACLC,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC,EAAET,UAAU,CAACjD,WAAW,GAAG,CAAC,CAAC,EAAEiD,UAAU,CAACjD,WAAW,CAAC,EAAEiD,UAAU,CAACjD,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,aAAa/D,KAAK,CAACoG,aAAa,CAACrG,IAAI,EAAE;IAC7H2H,OAAO,EAAE,KAAK;IACdhH,GAAG,EAAE6B,OAAO;IACZmB,IAAI,EAAEtB,kBAAkB;IACxBmF,MAAM,EAAEvF,UAAU;IAClB2F,UAAU,EAAE1F,cAAc;IAC1B2F,UAAU,EAAE,KAAK;IACjBrB,WAAW,EAAE9D,eAAe;IAC5BoF,QAAQ,EAAEzG,aAAa;IACvBU,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpB+F,UAAU,EAAEhG,OAAO,GAAG,IAAI,GAAGsF;EAC/B,CAAC,EAAE,UAAUpB,IAAI,EAAE+B,SAAS,EAAE;IAC5B,IAAIC,WAAW;IACf,IAAIvE,KAAK,GAAGuC,IAAI,CAACvC,KAAK;MACpBwE,WAAW,GAAGjC,IAAI,CAACiC,WAAW;MAC9BvE,IAAI,GAAGsC,IAAI,CAACtC,IAAI;MAChBoD,KAAK,GAAGd,IAAI,CAACc,KAAK;MAClBxC,KAAK,GAAG0B,IAAI,CAAC1B,KAAK;IACpB,IAAIsC,GAAG,GAAGlD,IAAI,CAACkD,GAAG;;IAElB;IACA,IAAInD,KAAK,EAAE;MACT,IAAIyE,WAAW;MACf,IAAIC,UAAU,GAAG,CAACD,WAAW,GAAGxE,IAAI,CAAC0E,KAAK,MAAM,IAAI,IAAIF,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG5H,WAAW,CAACwG,KAAK,CAAC,GAAGA,KAAK,CAACpC,QAAQ,CAAC,CAAC,GAAGrB,SAAS;MAChJ,OAAO,aAAarD,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;QAC7CE,SAAS,EAAE5G,UAAU,CAACwC,aAAa,EAAE,EAAE,CAACC,MAAM,CAACD,aAAa,EAAE,QAAQ,CAAC,CAAC;QACxEkG,KAAK,EAAED;MACT,CAAC,EAAErB,KAAK,KAAKzD,SAAS,GAAGyD,KAAK,GAAGF,GAAG,CAAC;IACvC;IACA,IAAIjD,QAAQ,GAAGD,IAAI,CAACC,QAAQ;MAC1ByE,KAAK,GAAG1E,IAAI,CAAC0E,KAAK;MAClBC,QAAQ,GAAG3E,IAAI,CAAC2E,QAAQ;MACxBf,KAAK,GAAG5D,IAAI,CAAC4D,KAAK;MAClBhB,SAAS,GAAG5C,IAAI,CAAC4C,SAAS;MAC1BgC,UAAU,GAAGjJ,wBAAwB,CAACqE,IAAI,EAAEjE,SAAS,CAAC;IACxD,IAAI8I,WAAW,GAAG1I,IAAI,CAACyI,UAAU,EAAE9B,iBAAiB,CAAC;;IAErD;IACA,IAAInB,QAAQ,GAAGd,UAAU,CAACD,KAAK,CAAC;IAChC,IAAIkE,eAAe,GAAG,EAAE,CAACrG,MAAM,CAACD,aAAa,EAAE,SAAS,CAAC;IACzD,IAAIuG,eAAe,GAAG/I,UAAU,CAACwC,aAAa,EAAEsG,eAAe,EAAElC,SAAS,GAAG0B,WAAW,GAAG,CAAC,CAAC,EAAE5I,eAAe,CAAC4I,WAAW,EAAE,EAAE,CAAC7F,MAAM,CAACqG,eAAe,EAAE,UAAU,CAAC,EAAEP,WAAW,CAAC,EAAE7I,eAAe,CAAC4I,WAAW,EAAE,EAAE,CAAC7F,MAAM,CAACqG,eAAe,EAAE,SAAS,CAAC,EAAEzE,WAAW,KAAKgE,SAAS,IAAI,CAACpE,QAAQ,CAAC,EAAEvE,eAAe,CAAC4I,WAAW,EAAE,EAAE,CAAC7F,MAAM,CAACqG,eAAe,EAAE,WAAW,CAAC,EAAE7E,QAAQ,CAAC,EAAEvE,eAAe,CAAC4I,WAAW,EAAE,EAAE,CAAC7F,MAAM,CAACqG,eAAe,EAAE,WAAW,CAAC,EAAEnD,QAAQ,CAAC,EAAE2C,WAAW,CAAC,CAAC;IAC9c,IAAIb,WAAW,GAAGN,QAAQ,CAACb,IAAI,CAAC;IAChC,IAAI0C,WAAW,GAAG,CAAC/G,oBAAoB,IAAI,OAAOA,oBAAoB,KAAK,UAAU,IAAI0D,QAAQ;;IAEjG;IACA,IAAI9E,OAAO,GAAG,OAAO4G,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGA,WAAW,IAAI7C,KAAK;IAClF;IACA,IAAIqE,WAAW,GAAGrI,WAAW,CAACC,OAAO,CAAC,GAAGA,OAAO,CAACmE,QAAQ,CAAC,CAAC,GAAGrB,SAAS;IACvE,IAAI+E,KAAK,KAAK/E,SAAS,EAAE;MACvBsF,WAAW,GAAGP,KAAK;IACrB;IACA,OAAO,aAAapI,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE9G,QAAQ,CAAC,CAAC,CAAC,EAAEQ,SAAS,CAACyI,WAAW,CAAC,EAAE,CAACzG,OAAO,GAAGiF,gBAAgB,CAACf,IAAI,EAAE+B,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;MACrI,eAAe,EAAE1C,QAAQ;MACzBiB,SAAS,EAAEmC,eAAe;MAC1BL,KAAK,EAAEO,WAAW;MAClBC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;QAClC,IAAI7E,WAAW,KAAKgE,SAAS,IAAIpE,QAAQ,EAAE;UACzC;QACF;QACAM,SAAS,CAAC8D,SAAS,CAAC;MACtB,CAAC;MACDc,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B,IAAI,CAAClF,QAAQ,EAAE;UACbyB,aAAa,CAACd,KAAK,CAAC;QACtB;MACF,CAAC;MACDgD,KAAK,EAAEA;IACT,CAAC,CAAC,EAAE,aAAatH,KAAK,CAACoG,aAAa,CAAC,KAAK,EAAE;MAC1CE,SAAS,EAAE,EAAE,CAACnE,MAAM,CAACqG,eAAe,EAAE,UAAU;IAClD,CAAC,EAAEjI,OAAO,CAAC,EAAE,aAAaP,KAAK,CAAC8I,cAAc,CAACnH,oBAAoB,CAAC,IAAI0D,QAAQ,EAAEqD,WAAW,IAAI,aAAa1I,KAAK,CAACoG,aAAa,CAAChG,QAAQ,EAAE;MAC1IkG,SAAS,EAAE,EAAE,CAACnE,MAAM,CAACD,aAAa,EAAE,eAAe,CAAC;MACpD6G,aAAa,EAAEpH,oBAAoB;MACnCqH,kBAAkB,EAAE;QAClBzE,UAAU,EAAEc;MACd;IACF,CAAC,EAAEA,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAI4D,aAAa,GAAG,aAAajJ,KAAK,CAACkJ,UAAU,CAAC1I,UAAU,CAAC;AAC7DyI,aAAa,CAACE,WAAW,GAAG,YAAY;AACxC,eAAeF,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}