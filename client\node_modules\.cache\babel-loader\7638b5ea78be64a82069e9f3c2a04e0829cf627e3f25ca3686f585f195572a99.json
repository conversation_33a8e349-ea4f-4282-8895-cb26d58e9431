{"ast": null, "code": "export var ColorFormat;\n(function (ColorFormat) {\n  ColorFormat[\"hex\"] = \"hex\";\n  ColorFormat[\"rgb\"] = \"rgb\";\n  ColorFormat[\"hsb\"] = \"hsb\";\n})(ColorFormat || (ColorFormat = {}));", "map": {"version": 3, "names": ["ColorFormat"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/color-picker/interface.js"], "sourcesContent": ["export var ColorFormat;\n(function (ColorFormat) {\n  ColorFormat[\"hex\"] = \"hex\";\n  ColorFormat[\"rgb\"] = \"rgb\";\n  ColorFormat[\"hsb\"] = \"hsb\";\n})(ColorFormat || (ColorFormat = {}));"], "mappings": "AAAA,OAAO,IAAIA,WAAW;AACtB,CAAC,UAAUA,WAAW,EAAE;EACtBA,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK;EAC1BA,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK;EAC1BA,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK;AAC5B,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}