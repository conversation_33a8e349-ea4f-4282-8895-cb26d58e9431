{"ast": null, "code": "import Menu, { MenuItem } from 'rc-menu';\nimport * as React from 'react';\nimport MentionsContext from \"./MentionsContext\";\n/**\n * We only use Menu to display the candidate.\n * The focus is controlled by textarea to make accessibility easy.\n */\nfunction DropdownMenu(props) {\n  var _React$useContext = React.useContext(MentionsContext),\n    notFoundContent = _React$useContext.notFoundContent,\n    activeIndex = _React$useContext.activeIndex,\n    setActiveIndex = _React$useContext.setActiveIndex,\n    selectOption = _React$useContext.selectOption,\n    onFocus = _React$useContext.onFocus,\n    onBlur = _React$useContext.onBlur;\n  var prefixCls = props.prefixCls,\n    options = props.options;\n  var activeOption = options[activeIndex] || {};\n  return /*#__PURE__*/React.createElement(Menu, {\n    prefixCls: \"\".concat(prefixCls, \"-menu\"),\n    activeKey: activeOption.key,\n    onSelect: function onSelect(_ref) {\n      var key = _ref.key;\n      var option = options.find(function (_ref2) {\n        var optionKey = _ref2.key;\n        return optionKey === key;\n      });\n      selectOption(option);\n    },\n    onFocus: onFocus,\n    onBlur: onBlur\n  }, options.map(function (option, index) {\n    var key = option.key,\n      disabled = option.disabled,\n      className = option.className,\n      style = option.style,\n      label = option.label;\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: key,\n      disabled: disabled,\n      className: className,\n      style: style,\n      onMouseEnter: function onMouseEnter() {\n        setActiveIndex(index);\n      }\n    }, label);\n  }), !options.length && /*#__PURE__*/React.createElement(MenuItem, {\n    disabled: true\n  }, notFoundContent));\n}\nexport default DropdownMenu;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "MenuItem", "React", "MentionsContext", "DropdownMenu", "props", "_React$useContext", "useContext", "notFoundContent", "activeIndex", "setActiveIndex", "selectOption", "onFocus", "onBlur", "prefixCls", "options", "activeOption", "createElement", "concat", "active<PERSON><PERSON>", "key", "onSelect", "_ref", "option", "find", "_ref2", "optionKey", "map", "index", "disabled", "className", "style", "label", "onMouseEnter", "length"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-mentions/es/DropdownMenu.js"], "sourcesContent": ["import Menu, { MenuItem } from 'rc-menu';\nimport * as React from 'react';\nimport MentionsContext from \"./MentionsContext\";\n/**\n * We only use Menu to display the candidate.\n * The focus is controlled by textarea to make accessibility easy.\n */\nfunction DropdownMenu(props) {\n  var _React$useContext = React.useContext(MentionsContext),\n    notFoundContent = _React$useContext.notFoundContent,\n    activeIndex = _React$useContext.activeIndex,\n    setActiveIndex = _React$useContext.setActiveIndex,\n    selectOption = _React$useContext.selectOption,\n    onFocus = _React$useContext.onFocus,\n    onBlur = _React$useContext.onBlur;\n  var prefixCls = props.prefixCls,\n    options = props.options;\n  var activeOption = options[activeIndex] || {};\n  return /*#__PURE__*/React.createElement(Menu, {\n    prefixCls: \"\".concat(prefixCls, \"-menu\"),\n    activeKey: activeOption.key,\n    onSelect: function onSelect(_ref) {\n      var key = _ref.key;\n      var option = options.find(function (_ref2) {\n        var optionKey = _ref2.key;\n        return optionKey === key;\n      });\n      selectOption(option);\n    },\n    onFocus: onFocus,\n    onBlur: onBlur\n  }, options.map(function (option, index) {\n    var key = option.key,\n      disabled = option.disabled,\n      className = option.className,\n      style = option.style,\n      label = option.label;\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: key,\n      disabled: disabled,\n      className: className,\n      style: style,\n      onMouseEnter: function onMouseEnter() {\n        setActiveIndex(index);\n      }\n    }, label);\n  }), !options.length && /*#__PURE__*/React.createElement(MenuItem, {\n    disabled: true\n  }, notFoundContent));\n}\nexport default DropdownMenu;"], "mappings": "AAAA,OAAOA,IAAI,IAAIC,QAAQ,QAAQ,SAAS;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,iBAAiB,GAAGJ,KAAK,CAACK,UAAU,CAACJ,eAAe,CAAC;IACvDK,eAAe,GAAGF,iBAAiB,CAACE,eAAe;IACnDC,WAAW,GAAGH,iBAAiB,CAACG,WAAW;IAC3CC,cAAc,GAAGJ,iBAAiB,CAACI,cAAc;IACjDC,YAAY,GAAGL,iBAAiB,CAACK,YAAY;IAC7CC,OAAO,GAAGN,iBAAiB,CAACM,OAAO;IACnCC,MAAM,GAAGP,iBAAiB,CAACO,MAAM;EACnC,IAAIC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC7BC,OAAO,GAAGV,KAAK,CAACU,OAAO;EACzB,IAAIC,YAAY,GAAGD,OAAO,CAACN,WAAW,CAAC,IAAI,CAAC,CAAC;EAC7C,OAAO,aAAaP,KAAK,CAACe,aAAa,CAACjB,IAAI,EAAE;IAC5Cc,SAAS,EAAE,EAAE,CAACI,MAAM,CAACJ,SAAS,EAAE,OAAO,CAAC;IACxCK,SAAS,EAAEH,YAAY,CAACI,GAAG;IAC3BC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;MAChC,IAAIF,GAAG,GAAGE,IAAI,CAACF,GAAG;MAClB,IAAIG,MAAM,GAAGR,OAAO,CAACS,IAAI,CAAC,UAAUC,KAAK,EAAE;QACzC,IAAIC,SAAS,GAAGD,KAAK,CAACL,GAAG;QACzB,OAAOM,SAAS,KAAKN,GAAG;MAC1B,CAAC,CAAC;MACFT,YAAY,CAACY,MAAM,CAAC;IACtB,CAAC;IACDX,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA;EACV,CAAC,EAAEE,OAAO,CAACY,GAAG,CAAC,UAAUJ,MAAM,EAAEK,KAAK,EAAE;IACtC,IAAIR,GAAG,GAAGG,MAAM,CAACH,GAAG;MAClBS,QAAQ,GAAGN,MAAM,CAACM,QAAQ;MAC1BC,SAAS,GAAGP,MAAM,CAACO,SAAS;MAC5BC,KAAK,GAAGR,MAAM,CAACQ,KAAK;MACpBC,KAAK,GAAGT,MAAM,CAACS,KAAK;IACtB,OAAO,aAAa9B,KAAK,CAACe,aAAa,CAAChB,QAAQ,EAAE;MAChDmB,GAAG,EAAEA,GAAG;MACRS,QAAQ,EAAEA,QAAQ;MAClBC,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEA,KAAK;MACZE,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;QACpCvB,cAAc,CAACkB,KAAK,CAAC;MACvB;IACF,CAAC,EAAEI,KAAK,CAAC;EACX,CAAC,CAAC,EAAE,CAACjB,OAAO,CAACmB,MAAM,IAAI,aAAahC,KAAK,CAACe,aAAa,CAAChB,QAAQ,EAAE;IAChE4B,QAAQ,EAAE;EACZ,CAAC,EAAErB,eAAe,CAAC,CAAC;AACtB;AACA,eAAeJ,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}