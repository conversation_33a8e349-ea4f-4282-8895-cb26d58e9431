{"ast": null, "code": "import * as React from 'react';\nimport { MenuContext } from \"../context/MenuContext\";\nexport default function useDirectionStyle(level) {\n  var _React$useContext = React.useContext(MenuContext),\n    mode = _React$useContext.mode,\n    rtl = _React$useContext.rtl,\n    inlineIndent = _React$useContext.inlineIndent;\n  if (mode !== 'inline') {\n    return null;\n  }\n  var len = level;\n  return rtl ? {\n    paddingRight: len * inlineIndent\n  } : {\n    paddingLeft: len * inlineIndent\n  };\n}", "map": {"version": 3, "names": ["React", "MenuContext", "useDirectionStyle", "level", "_React$useContext", "useContext", "mode", "rtl", "inlineIndent", "len", "paddingRight", "paddingLeft"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-menu/es/hooks/useDirectionStyle.js"], "sourcesContent": ["import * as React from 'react';\nimport { MenuContext } from \"../context/MenuContext\";\nexport default function useDirectionStyle(level) {\n  var _React$useContext = React.useContext(MenuContext),\n    mode = _React$useContext.mode,\n    rtl = _React$useContext.rtl,\n    inlineIndent = _React$useContext.inlineIndent;\n  if (mode !== 'inline') {\n    return null;\n  }\n  var len = level;\n  return rtl ? {\n    paddingRight: len * inlineIndent\n  } : {\n    paddingLeft: len * inlineIndent\n  };\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,wBAAwB;AACpD,eAAe,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAC/C,IAAIC,iBAAiB,GAAGJ,KAAK,CAACK,UAAU,CAACJ,WAAW,CAAC;IACnDK,IAAI,GAAGF,iBAAiB,CAACE,IAAI;IAC7BC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,YAAY,GAAGJ,iBAAiB,CAACI,YAAY;EAC/C,IAAIF,IAAI,KAAK,QAAQ,EAAE;IACrB,OAAO,IAAI;EACb;EACA,IAAIG,GAAG,GAAGN,KAAK;EACf,OAAOI,GAAG,GAAG;IACXG,YAAY,EAAED,GAAG,GAAGD;EACtB,CAAC,GAAG;IACFG,WAAW,EAAEF,GAAG,GAAGD;EACrB,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}