{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nexport default function DefaultPanel(props) {\n  var prefixCls = props.prefixCls,\n    current = props.current,\n    total = props.total,\n    title = props.title,\n    description = props.description,\n    onClose = props.onClose,\n    onPrev = props.onPrev,\n    onNext = props.onNext,\n    onFinish = props.onFinish,\n    className = props.className;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className)\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\")\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onClose,\n    \"aria-label\": \"Close\",\n    className: \"\".concat(prefixCls, \"-close\")\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-close-x\")\n  }, \"\\xD7\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-header\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-description\")\n  }, description), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-sliders\")\n  }, total > 1 ? _toConsumableArray(Array.from({\n    length: total\n  }).keys()).map(function (item, index) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      key: item,\n      className: index === current ? 'active' : ''\n    });\n  }) : null), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-buttons\")\n  }, current !== 0 ? /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-prev-btn\"),\n    onClick: onPrev\n  }, \"Prev\") : null, current === total - 1 ? /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-finish-btn\"),\n    onClick: onFinish\n  }, \"Finish\") : /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-next-btn\"),\n    onClick: onNext\n  }, \"Next\")))));\n}", "map": {"version": 3, "names": ["_toConsumableArray", "React", "classNames", "DefaultPanel", "props", "prefixCls", "current", "total", "title", "description", "onClose", "onPrev", "onNext", "onFinish", "className", "createElement", "concat", "type", "onClick", "Array", "from", "length", "keys", "map", "item", "index", "key"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/tour/es/TourStep/DefaultPanel.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nexport default function DefaultPanel(props) {\n  var prefixCls = props.prefixCls,\n    current = props.current,\n    total = props.total,\n    title = props.title,\n    description = props.description,\n    onClose = props.onClose,\n    onPrev = props.onPrev,\n    onNext = props.onNext,\n    onFinish = props.onFinish,\n    className = props.className;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className)\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\")\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onClose,\n    \"aria-label\": \"Close\",\n    className: \"\".concat(prefixCls, \"-close\")\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-close-x\")\n  }, \"\\xD7\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-header\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-description\")\n  }, description), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-sliders\")\n  }, total > 1 ? _toConsumableArray(Array.from({\n    length: total\n  }).keys()).map(function (item, index) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      key: item,\n      className: index === current ? 'active' : ''\n    });\n  }) : null), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-buttons\")\n  }, current !== 0 ? /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-prev-btn\"),\n    onClick: onPrev\n  }, \"Prev\") : null, current === total - 1 ? /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-finish-btn\"),\n    onClick: onFinish\n  }, \"Finish\") : /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-next-btn\"),\n    onClick: onNext\n  }, \"Next\")))));\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,eAAe,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC1C,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,OAAO,GAAGN,KAAK,CAACM,OAAO;IACvBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,MAAM,GAAGR,KAAK,CAACQ,MAAM;IACrBC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,SAAS,GAAGV,KAAK,CAACU,SAAS;EAC7B,OAAO,aAAab,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IAC7CD,SAAS,EAAEZ,UAAU,CAAC,EAAE,CAACc,MAAM,CAACX,SAAS,EAAE,UAAU,CAAC,EAAES,SAAS;EACnE,CAAC,EAAE,aAAab,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IACzCD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAE,aAAaJ,KAAK,CAACc,aAAa,CAAC,QAAQ,EAAE;IAC5CE,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAER,OAAO;IAChB,YAAY,EAAE,OAAO;IACrBI,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAE,aAAaJ,KAAK,CAACc,aAAa,CAAC,MAAM,EAAE;IAC1CD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,aAAaJ,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IACnDD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAE,aAAaJ,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IACzCD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEG,KAAK,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IAClDD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,cAAc;EAChD,CAAC,EAAEI,WAAW,CAAC,EAAE,aAAaR,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IACvDD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAE,aAAaJ,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IACzCD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEE,KAAK,GAAG,CAAC,GAAGP,kBAAkB,CAACmB,KAAK,CAACC,IAAI,CAAC;IAC3CC,MAAM,EAAEd;EACV,CAAC,CAAC,CAACe,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACpC,OAAO,aAAaxB,KAAK,CAACc,aAAa,CAAC,MAAM,EAAE;MAC9CW,GAAG,EAAEF,IAAI;MACTV,SAAS,EAAEW,KAAK,KAAKnB,OAAO,GAAG,QAAQ,GAAG;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,aAAaL,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IAClDD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEC,OAAO,KAAK,CAAC,GAAG,aAAaL,KAAK,CAACc,aAAa,CAAC,QAAQ,EAAE;IAC5DD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,WAAW,CAAC;IAC5Ca,OAAO,EAAEP;EACX,CAAC,EAAE,MAAM,CAAC,GAAG,IAAI,EAAEL,OAAO,KAAKC,KAAK,GAAG,CAAC,GAAG,aAAaN,KAAK,CAACc,aAAa,CAAC,QAAQ,EAAE;IACpFD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,aAAa,CAAC;IAC9Ca,OAAO,EAAEL;EACX,CAAC,EAAE,QAAQ,CAAC,GAAG,aAAaZ,KAAK,CAACc,aAAa,CAAC,QAAQ,EAAE;IACxDD,SAAS,EAAE,EAAE,CAACE,MAAM,CAACX,SAAS,EAAE,WAAW,CAAC;IAC5Ca,OAAO,EAAEN;EACX,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}