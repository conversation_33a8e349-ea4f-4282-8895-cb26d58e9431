{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resetState = resetState;\nexports.log = log;\nvar htmlClassList = {};\nvar docBodyClassList = {};\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction removeClass(at, cls) {\n  at.classList.remove(cls);\n}\n\n/* istanbul ignore next */\nfunction resetState() {\n  var htmlElement = document.getElementsByTagName(\"html\")[0];\n  for (var cls in htmlClassList) {\n    removeClass(htmlElement, htmlClassList[cls]);\n  }\n  var body = document.body;\n  for (var _cls in docBodyClassList) {\n    removeClass(body, docBodyClassList[_cls]);\n  }\n  htmlClassList = {};\n  docBodyClassList = {};\n}\n\n/* istanbul ignore next */\nfunction log() {\n  if (process.env.NODE_ENV !== \"production\") {\n    var classes = document.getElementsByTagName(\"html\")[0].className;\n    var buffer = \"Show tracked classes:\\n\\n\";\n    buffer += \"<html /> (\" + classes + \"):\\n  \";\n    for (var x in htmlClassList) {\n      buffer += \"  \" + x + \" \" + htmlClassList[x] + \"\\n  \";\n    }\n    classes = document.body.className;\n    buffer += \"\\n\\ndoc.body (\" + classes + \"):\\n  \";\n    for (var _x in docBodyClassList) {\n      buffer += \"  \" + _x + \" \" + docBodyClassList[_x] + \"\\n  \";\n    }\n    buffer += \"\\n\";\n    console.log(buffer);\n  }\n}\n/* eslint-enable no-console */\n\n/**\n * Track the number of reference of a class.\n * @param {object} poll The poll to receive the reference.\n * @param {string} className The class name.\n * @return {string}\n */\nvar incrementReference = function incrementReference(poll, className) {\n  if (!poll[className]) {\n    poll[className] = 0;\n  }\n  poll[className] += 1;\n  return className;\n};\n\n/**\n * Drop the reference of a class.\n * @param {object} poll The poll to receive the reference.\n * @param {string} className The class name.\n * @return {string}\n */\nvar decrementReference = function decrementReference(poll, className) {\n  if (poll[className]) {\n    poll[className] -= 1;\n  }\n  return className;\n};\n\n/**\n * Track a class and add to the given class list.\n * @param {Object} classListRef A class list of an element.\n * @param {Object} poll         The poll to be used.\n * @param {Array}  classes      The list of classes to be tracked.\n */\nvar trackClass = function trackClass(classListRef, poll, classes) {\n  classes.forEach(function (className) {\n    incrementReference(poll, className);\n    classListRef.add(className);\n  });\n};\n\n/**\n * Untrack a class and remove from the given class list if the reference\n * reaches 0.\n * @param {Object} classListRef A class list of an element.\n * @param {Object} poll         The poll to be used.\n * @param {Array}  classes      The list of classes to be untracked.\n */\nvar untrackClass = function untrackClass(classListRef, poll, classes) {\n  classes.forEach(function (className) {\n    decrementReference(poll, className);\n    poll[className] === 0 && classListRef.remove(className);\n  });\n};\n\n/**\n * Public inferface to add classes to the document.body.\n * @param {string} bodyClass The class string to be added.\n *                           It may contain more then one class\n *                           with ' ' as separator.\n */\nvar add = exports.add = function add(element, classString) {\n  return trackClass(element.classList, element.nodeName.toLowerCase() == \"html\" ? htmlClassList : docBodyClassList, classString.split(\" \"));\n};\n\n/**\n * Public inferface to remove classes from the document.body.\n * @param {string} bodyClass The class string to be added.\n *                           It may contain more then one class\n *                           with ' ' as separator.\n */\nvar remove = exports.remove = function remove(element, classString) {\n  return untrackClass(element.classList, element.nodeName.toLowerCase() == \"html\" ? htmlClassList : docBodyClassList, classString.split(\" \"));\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "resetState", "log", "htmlClassList", "docBodyClassList", "removeClass", "at", "cls", "classList", "remove", "htmlElement", "document", "getElementsByTagName", "body", "_cls", "process", "env", "NODE_ENV", "classes", "className", "buffer", "x", "_x", "console", "incrementReference", "poll", "decrementReference", "trackClass", "classListRef", "for<PERSON>ach", "add", "untrackClass", "element", "classString", "nodeName", "toLowerCase", "split"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/react-modal/lib/helpers/classList.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resetState = resetState;\nexports.log = log;\nvar htmlClassList = {};\nvar docBodyClassList = {};\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction removeClass(at, cls) {\n  at.classList.remove(cls);\n}\n\n/* istanbul ignore next */\nfunction resetState() {\n  var htmlElement = document.getElementsByTagName(\"html\")[0];\n  for (var cls in htmlClassList) {\n    removeClass(htmlElement, htmlClassList[cls]);\n  }\n\n  var body = document.body;\n  for (var _cls in docBodyClassList) {\n    removeClass(body, docBodyClassList[_cls]);\n  }\n\n  htmlClassList = {};\n  docBodyClassList = {};\n}\n\n/* istanbul ignore next */\nfunction log() {\n  if (process.env.NODE_ENV !== \"production\") {\n    var classes = document.getElementsByTagName(\"html\")[0].className;\n    var buffer = \"Show tracked classes:\\n\\n\";\n\n    buffer += \"<html /> (\" + classes + \"):\\n  \";\n    for (var x in htmlClassList) {\n      buffer += \"  \" + x + \" \" + htmlClassList[x] + \"\\n  \";\n    }\n\n    classes = document.body.className;\n\n    buffer += \"\\n\\ndoc.body (\" + classes + \"):\\n  \";\n    for (var _x in docBodyClassList) {\n      buffer += \"  \" + _x + \" \" + docBodyClassList[_x] + \"\\n  \";\n    }\n\n    buffer += \"\\n\";\n\n    console.log(buffer);\n  }\n}\n/* eslint-enable no-console */\n\n/**\n * Track the number of reference of a class.\n * @param {object} poll The poll to receive the reference.\n * @param {string} className The class name.\n * @return {string}\n */\nvar incrementReference = function incrementReference(poll, className) {\n  if (!poll[className]) {\n    poll[className] = 0;\n  }\n  poll[className] += 1;\n  return className;\n};\n\n/**\n * Drop the reference of a class.\n * @param {object} poll The poll to receive the reference.\n * @param {string} className The class name.\n * @return {string}\n */\nvar decrementReference = function decrementReference(poll, className) {\n  if (poll[className]) {\n    poll[className] -= 1;\n  }\n  return className;\n};\n\n/**\n * Track a class and add to the given class list.\n * @param {Object} classListRef A class list of an element.\n * @param {Object} poll         The poll to be used.\n * @param {Array}  classes      The list of classes to be tracked.\n */\nvar trackClass = function trackClass(classListRef, poll, classes) {\n  classes.forEach(function (className) {\n    incrementReference(poll, className);\n    classListRef.add(className);\n  });\n};\n\n/**\n * Untrack a class and remove from the given class list if the reference\n * reaches 0.\n * @param {Object} classListRef A class list of an element.\n * @param {Object} poll         The poll to be used.\n * @param {Array}  classes      The list of classes to be untracked.\n */\nvar untrackClass = function untrackClass(classListRef, poll, classes) {\n  classes.forEach(function (className) {\n    decrementReference(poll, className);\n    poll[className] === 0 && classListRef.remove(className);\n  });\n};\n\n/**\n * Public inferface to add classes to the document.body.\n * @param {string} bodyClass The class string to be added.\n *                           It may contain more then one class\n *                           with ' ' as separator.\n */\nvar add = exports.add = function add(element, classString) {\n  return trackClass(element.classList, element.nodeName.toLowerCase() == \"html\" ? htmlClassList : docBodyClassList, classString.split(\" \"));\n};\n\n/**\n * Public inferface to remove classes from the document.body.\n * @param {string} bodyClass The class string to be added.\n *                           It may contain more then one class\n *                           with ' ' as separator.\n */\nvar remove = exports.remove = function remove(element, classString) {\n  return untrackClass(element.classList, element.nodeName.toLowerCase() == \"html\" ? htmlClassList : docBodyClassList, classString.split(\" \"));\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/BF,OAAO,CAACG,GAAG,GAAGA,GAAG;AACjB,IAAIC,aAAa,GAAG,CAAC,CAAC;AACtB,IAAIC,gBAAgB,GAAG,CAAC,CAAC;;AAEzB;AACA;AACA,SAASC,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAE;EAC5BD,EAAE,CAACE,SAAS,CAACC,MAAM,CAACF,GAAG,CAAC;AAC1B;;AAEA;AACA,SAASN,UAAUA,CAAA,EAAG;EACpB,IAAIS,WAAW,GAAGC,QAAQ,CAACC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1D,KAAK,IAAIL,GAAG,IAAIJ,aAAa,EAAE;IAC7BE,WAAW,CAACK,WAAW,EAAEP,aAAa,CAACI,GAAG,CAAC,CAAC;EAC9C;EAEA,IAAIM,IAAI,GAAGF,QAAQ,CAACE,IAAI;EACxB,KAAK,IAAIC,IAAI,IAAIV,gBAAgB,EAAE;IACjCC,WAAW,CAACQ,IAAI,EAAET,gBAAgB,CAACU,IAAI,CAAC,CAAC;EAC3C;EAEAX,aAAa,GAAG,CAAC,CAAC;EAClBC,gBAAgB,GAAG,CAAC,CAAC;AACvB;;AAEA;AACA,SAASF,GAAGA,CAAA,EAAG;EACb,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIC,OAAO,GAAGP,QAAQ,CAACC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACO,SAAS;IAChE,IAAIC,MAAM,GAAG,2BAA2B;IAExCA,MAAM,IAAI,YAAY,GAAGF,OAAO,GAAG,QAAQ;IAC3C,KAAK,IAAIG,CAAC,IAAIlB,aAAa,EAAE;MAC3BiB,MAAM,IAAI,IAAI,GAAGC,CAAC,GAAG,GAAG,GAAGlB,aAAa,CAACkB,CAAC,CAAC,GAAG,MAAM;IACtD;IAEAH,OAAO,GAAGP,QAAQ,CAACE,IAAI,CAACM,SAAS;IAEjCC,MAAM,IAAI,gBAAgB,GAAGF,OAAO,GAAG,QAAQ;IAC/C,KAAK,IAAII,EAAE,IAAIlB,gBAAgB,EAAE;MAC/BgB,MAAM,IAAI,IAAI,GAAGE,EAAE,GAAG,GAAG,GAAGlB,gBAAgB,CAACkB,EAAE,CAAC,GAAG,MAAM;IAC3D;IAEAF,MAAM,IAAI,IAAI;IAEdG,OAAO,CAACrB,GAAG,CAACkB,MAAM,CAAC;EACrB;AACF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAII,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,IAAI,EAAEN,SAAS,EAAE;EACpE,IAAI,CAACM,IAAI,CAACN,SAAS,CAAC,EAAE;IACpBM,IAAI,CAACN,SAAS,CAAC,GAAG,CAAC;EACrB;EACAM,IAAI,CAACN,SAAS,CAAC,IAAI,CAAC;EACpB,OAAOA,SAAS;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAIO,kBAAkB,GAAG,SAASA,kBAAkBA,CAACD,IAAI,EAAEN,SAAS,EAAE;EACpE,IAAIM,IAAI,CAACN,SAAS,CAAC,EAAE;IACnBM,IAAI,CAACN,SAAS,CAAC,IAAI,CAAC;EACtB;EACA,OAAOA,SAAS;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAIQ,UAAU,GAAG,SAASA,UAAUA,CAACC,YAAY,EAAEH,IAAI,EAAEP,OAAO,EAAE;EAChEA,OAAO,CAACW,OAAO,CAAC,UAAUV,SAAS,EAAE;IACnCK,kBAAkB,CAACC,IAAI,EAAEN,SAAS,CAAC;IACnCS,YAAY,CAACE,GAAG,CAACX,SAAS,CAAC;EAC7B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIY,YAAY,GAAG,SAASA,YAAYA,CAACH,YAAY,EAAEH,IAAI,EAAEP,OAAO,EAAE;EACpEA,OAAO,CAACW,OAAO,CAAC,UAAUV,SAAS,EAAE;IACnCO,kBAAkB,CAACD,IAAI,EAAEN,SAAS,CAAC;IACnCM,IAAI,CAACN,SAAS,CAAC,KAAK,CAAC,IAAIS,YAAY,CAACnB,MAAM,CAACU,SAAS,CAAC;EACzD,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAIW,GAAG,GAAG/B,OAAO,CAAC+B,GAAG,GAAG,SAASA,GAAGA,CAACE,OAAO,EAAEC,WAAW,EAAE;EACzD,OAAON,UAAU,CAACK,OAAO,CAACxB,SAAS,EAAEwB,OAAO,CAACE,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,MAAM,GAAGhC,aAAa,GAAGC,gBAAgB,EAAE6B,WAAW,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC;AAC3I,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAI3B,MAAM,GAAGV,OAAO,CAACU,MAAM,GAAG,SAASA,MAAMA,CAACuB,OAAO,EAAEC,WAAW,EAAE;EAClE,OAAOF,YAAY,CAACC,OAAO,CAACxB,SAAS,EAAEwB,OAAO,CAACE,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,MAAM,GAAGhC,aAAa,GAAGC,gBAAgB,EAAE6B,WAAW,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7I,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}