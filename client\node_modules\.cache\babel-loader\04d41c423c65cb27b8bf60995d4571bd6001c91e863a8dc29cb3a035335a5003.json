{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = findTabbableDescendants;\n/*!\n * Adapted from jQuery UI core\n *\n * http://jqueryui.com\n *\n * Copyright 2014 jQuery Foundation and other contributors\n * Released under the MIT license.\n * http://jquery.org/license\n *\n * http://api.jqueryui.com/category/ui-core/\n */\n\nvar DISPLAY_NONE = \"none\";\nvar DISPLAY_CONTENTS = \"contents\";\n\n// match the whole word to prevent fuzzy searching\nvar tabbableNode = /^(input|select|textarea|button|object|iframe)$/;\nfunction isNotOverflowing(element, style) {\n  return style.getPropertyValue(\"overflow\") !== \"visible\" ||\n  // if 'overflow: visible' set, check if there is actually any overflow\n  element.scrollWidth <= 0 && element.scrollHeight <= 0;\n}\nfunction hidesContents(element) {\n  var zeroSize = element.offsetWidth <= 0 && element.offsetHeight <= 0;\n\n  // If the node is empty, this is good enough\n  if (zeroSize && !element.innerHTML) return true;\n  try {\n    // Otherwise we need to check some styles\n    var style = window.getComputedStyle(element);\n    var displayValue = style.getPropertyValue(\"display\");\n    return zeroSize ? displayValue !== DISPLAY_CONTENTS && isNotOverflowing(element, style) : displayValue === DISPLAY_NONE;\n  } catch (exception) {\n    // eslint-disable-next-line no-console\n    console.warn(\"Failed to inspect element style\");\n    return false;\n  }\n}\nfunction visible(element) {\n  var parentElement = element;\n  var rootNode = element.getRootNode && element.getRootNode();\n  while (parentElement) {\n    if (parentElement === document.body) break;\n\n    // if we are not hidden yet, skip to checking outside the Web Component\n    if (rootNode && parentElement === rootNode) parentElement = rootNode.host.parentNode;\n    if (hidesContents(parentElement)) return false;\n    parentElement = parentElement.parentNode;\n  }\n  return true;\n}\nfunction focusable(element, isTabIndexNotNaN) {\n  var nodeName = element.nodeName.toLowerCase();\n  var res = tabbableNode.test(nodeName) && !element.disabled || (nodeName === \"a\" ? element.href || isTabIndexNotNaN : isTabIndexNotNaN);\n  return res && visible(element);\n}\nfunction tabbable(element) {\n  var tabIndex = element.getAttribute(\"tabindex\");\n  if (tabIndex === null) tabIndex = undefined;\n  var isTabIndexNaN = isNaN(tabIndex);\n  return (isTabIndexNaN || tabIndex >= 0) && focusable(element, !isTabIndexNaN);\n}\nfunction findTabbableDescendants(element) {\n  var descendants = [].slice.call(element.querySelectorAll(\"*\"), 0).reduce(function (finished, el) {\n    return finished.concat(!el.shadowRoot ? [el] : findTabbableDescendants(el.shadowRoot));\n  }, []);\n  return descendants.filter(tabbable);\n}\nmodule.exports = exports[\"default\"];", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "findTabbableDescendants", "DISPLAY_NONE", "DISPLAY_CONTENTS", "tabbableNode", "isNotOverflowing", "element", "style", "getPropertyValue", "scrollWidth", "scrollHeight", "hidesContents", "zeroSize", "offsetWidth", "offsetHeight", "innerHTML", "window", "getComputedStyle", "displayValue", "exception", "console", "warn", "visible", "parentElement", "rootNode", "getRootNode", "document", "body", "host", "parentNode", "focusable", "isTabIndexNotNaN", "nodeName", "toLowerCase", "res", "test", "disabled", "href", "tabbable", "tabIndex", "getAttribute", "undefined", "isTabIndexNaN", "isNaN", "descendants", "slice", "call", "querySelectorAll", "reduce", "finished", "el", "concat", "shadowRoot", "filter", "module"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/react-modal/lib/helpers/tabbable.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = findTabbableDescendants;\n/*!\n * Adapted from jQuery UI core\n *\n * http://jqueryui.com\n *\n * Copyright 2014 jQuery Foundation and other contributors\n * Released under the MIT license.\n * http://jquery.org/license\n *\n * http://api.jqueryui.com/category/ui-core/\n */\n\nvar DISPLAY_NONE = \"none\";\nvar DISPLAY_CONTENTS = \"contents\";\n\n// match the whole word to prevent fuzzy searching\nvar tabbableNode = /^(input|select|textarea|button|object|iframe)$/;\n\nfunction isNotOverflowing(element, style) {\n  return style.getPropertyValue(\"overflow\") !== \"visible\" ||\n  // if 'overflow: visible' set, check if there is actually any overflow\n  element.scrollWidth <= 0 && element.scrollHeight <= 0;\n}\n\nfunction hidesContents(element) {\n  var zeroSize = element.offsetWidth <= 0 && element.offsetHeight <= 0;\n\n  // If the node is empty, this is good enough\n  if (zeroSize && !element.innerHTML) return true;\n\n  try {\n    // Otherwise we need to check some styles\n    var style = window.getComputedStyle(element);\n    var displayValue = style.getPropertyValue(\"display\");\n    return zeroSize ? displayValue !== DISPLAY_CONTENTS && isNotOverflowing(element, style) : displayValue === DISPLAY_NONE;\n  } catch (exception) {\n    // eslint-disable-next-line no-console\n    console.warn(\"Failed to inspect element style\");\n    return false;\n  }\n}\n\nfunction visible(element) {\n  var parentElement = element;\n  var rootNode = element.getRootNode && element.getRootNode();\n  while (parentElement) {\n    if (parentElement === document.body) break;\n\n    // if we are not hidden yet, skip to checking outside the Web Component\n    if (rootNode && parentElement === rootNode) parentElement = rootNode.host.parentNode;\n\n    if (hidesContents(parentElement)) return false;\n    parentElement = parentElement.parentNode;\n  }\n  return true;\n}\n\nfunction focusable(element, isTabIndexNotNaN) {\n  var nodeName = element.nodeName.toLowerCase();\n  var res = tabbableNode.test(nodeName) && !element.disabled || (nodeName === \"a\" ? element.href || isTabIndexNotNaN : isTabIndexNotNaN);\n  return res && visible(element);\n}\n\nfunction tabbable(element) {\n  var tabIndex = element.getAttribute(\"tabindex\");\n  if (tabIndex === null) tabIndex = undefined;\n  var isTabIndexNaN = isNaN(tabIndex);\n  return (isTabIndexNaN || tabIndex >= 0) && focusable(element, !isTabIndexNaN);\n}\n\nfunction findTabbableDescendants(element) {\n  var descendants = [].slice.call(element.querySelectorAll(\"*\"), 0).reduce(function (finished, el) {\n    return finished.concat(!el.shadowRoot ? [el] : findTabbableDescendants(el.shadowRoot));\n  }, []);\n  return descendants.filter(tabbable);\n}\nmodule.exports = exports[\"default\"];"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,uBAAuB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAG,MAAM;AACzB,IAAIC,gBAAgB,GAAG,UAAU;;AAEjC;AACA,IAAIC,YAAY,GAAG,gDAAgD;AAEnE,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,KAAK,EAAE;EACxC,OAAOA,KAAK,CAACC,gBAAgB,CAAC,UAAU,CAAC,KAAK,SAAS;EACvD;EACAF,OAAO,CAACG,WAAW,IAAI,CAAC,IAAIH,OAAO,CAACI,YAAY,IAAI,CAAC;AACvD;AAEA,SAASC,aAAaA,CAACL,OAAO,EAAE;EAC9B,IAAIM,QAAQ,GAAGN,OAAO,CAACO,WAAW,IAAI,CAAC,IAAIP,OAAO,CAACQ,YAAY,IAAI,CAAC;;EAEpE;EACA,IAAIF,QAAQ,IAAI,CAACN,OAAO,CAACS,SAAS,EAAE,OAAO,IAAI;EAE/C,IAAI;IACF;IACA,IAAIR,KAAK,GAAGS,MAAM,CAACC,gBAAgB,CAACX,OAAO,CAAC;IAC5C,IAAIY,YAAY,GAAGX,KAAK,CAACC,gBAAgB,CAAC,SAAS,CAAC;IACpD,OAAOI,QAAQ,GAAGM,YAAY,KAAKf,gBAAgB,IAAIE,gBAAgB,CAACC,OAAO,EAAEC,KAAK,CAAC,GAAGW,YAAY,KAAKhB,YAAY;EACzH,CAAC,CAAC,OAAOiB,SAAS,EAAE;IAClB;IACAC,OAAO,CAACC,IAAI,CAAC,iCAAiC,CAAC;IAC/C,OAAO,KAAK;EACd;AACF;AAEA,SAASC,OAAOA,CAAChB,OAAO,EAAE;EACxB,IAAIiB,aAAa,GAAGjB,OAAO;EAC3B,IAAIkB,QAAQ,GAAGlB,OAAO,CAACmB,WAAW,IAAInB,OAAO,CAACmB,WAAW,CAAC,CAAC;EAC3D,OAAOF,aAAa,EAAE;IACpB,IAAIA,aAAa,KAAKG,QAAQ,CAACC,IAAI,EAAE;;IAErC;IACA,IAAIH,QAAQ,IAAID,aAAa,KAAKC,QAAQ,EAAED,aAAa,GAAGC,QAAQ,CAACI,IAAI,CAACC,UAAU;IAEpF,IAAIlB,aAAa,CAACY,aAAa,CAAC,EAAE,OAAO,KAAK;IAC9CA,aAAa,GAAGA,aAAa,CAACM,UAAU;EAC1C;EACA,OAAO,IAAI;AACb;AAEA,SAASC,SAASA,CAACxB,OAAO,EAAEyB,gBAAgB,EAAE;EAC5C,IAAIC,QAAQ,GAAG1B,OAAO,CAAC0B,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,IAAIC,GAAG,GAAG9B,YAAY,CAAC+B,IAAI,CAACH,QAAQ,CAAC,IAAI,CAAC1B,OAAO,CAAC8B,QAAQ,KAAKJ,QAAQ,KAAK,GAAG,GAAG1B,OAAO,CAAC+B,IAAI,IAAIN,gBAAgB,GAAGA,gBAAgB,CAAC;EACtI,OAAOG,GAAG,IAAIZ,OAAO,CAAChB,OAAO,CAAC;AAChC;AAEA,SAASgC,QAAQA,CAAChC,OAAO,EAAE;EACzB,IAAIiC,QAAQ,GAAGjC,OAAO,CAACkC,YAAY,CAAC,UAAU,CAAC;EAC/C,IAAID,QAAQ,KAAK,IAAI,EAAEA,QAAQ,GAAGE,SAAS;EAC3C,IAAIC,aAAa,GAAGC,KAAK,CAACJ,QAAQ,CAAC;EACnC,OAAO,CAACG,aAAa,IAAIH,QAAQ,IAAI,CAAC,KAAKT,SAAS,CAACxB,OAAO,EAAE,CAACoC,aAAa,CAAC;AAC/E;AAEA,SAASzC,uBAAuBA,CAACK,OAAO,EAAE;EACxC,IAAIsC,WAAW,GAAG,EAAE,CAACC,KAAK,CAACC,IAAI,CAACxC,OAAO,CAACyC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAACC,MAAM,CAAC,UAAUC,QAAQ,EAAEC,EAAE,EAAE;IAC/F,OAAOD,QAAQ,CAACE,MAAM,CAAC,CAACD,EAAE,CAACE,UAAU,GAAG,CAACF,EAAE,CAAC,GAAGjD,uBAAuB,CAACiD,EAAE,CAACE,UAAU,CAAC,CAAC;EACxF,CAAC,EAAE,EAAE,CAAC;EACN,OAAOR,WAAW,CAACS,MAAM,CAACf,QAAQ,CAAC;AACrC;AACAgB,MAAM,CAACxD,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}