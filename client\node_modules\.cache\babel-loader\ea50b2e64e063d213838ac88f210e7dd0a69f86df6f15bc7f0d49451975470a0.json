{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"disabled\", \"visible\", \"children\", \"popupElement\", \"containerWidth\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"direction\", \"placement\", \"builtinPlacements\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"getPopupContainer\", \"empty\", \"getTriggerDOMNode\", \"onPopupVisibleChange\", \"onPopupMouseEnter\"];\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar getBuiltInPlacements = function getBuiltInPlacements(dropdownMatchSelectWidth) {\n  // Enable horizontal overflow auto-adjustment when a custom dropdown width is provided\n  var adjustX = dropdownMatchSelectWidth === true ? 0 : 1;\n  return {\n    bottomLeft: {\n      points: ['tl', 'bl'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    bottomRight: {\n      points: ['tr', 'br'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    topLeft: {\n      points: ['bl', 'tl'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    topRight: {\n      points: ['br', 'tr'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    }\n  };\n};\nvar SelectTrigger = function SelectTrigger(props, ref) {\n  var prefixCls = props.prefixCls,\n    disabled = props.disabled,\n    visible = props.visible,\n    children = props.children,\n    popupElement = props.popupElement,\n    containerWidth = props.containerWidth,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction,\n    placement = props.placement,\n    builtinPlacements = props.builtinPlacements,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    getPopupContainer = props.getPopupContainer,\n    empty = props.empty,\n    getTriggerDOMNode = props.getTriggerDOMNode,\n    onPopupVisibleChange = props.onPopupVisibleChange,\n    onPopupMouseEnter = props.onPopupMouseEnter,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var popupNode = popupElement;\n  if (dropdownRender) {\n    popupNode = dropdownRender(popupElement);\n  }\n  var mergedBuiltinPlacements = React.useMemo(function () {\n    return builtinPlacements || getBuiltInPlacements(dropdownMatchSelectWidth);\n  }, [builtinPlacements, dropdownMatchSelectWidth]);\n\n  // ===================== Motion ======================\n  var mergedTransitionName = animation ? \"\".concat(dropdownPrefixCls, \"-\").concat(animation) : transitionName;\n\n  // ======================= Ref =======================\n  var popupRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      getPopupElement: function getPopupElement() {\n        return popupRef.current;\n      }\n    };\n  });\n  var popupStyle = _objectSpread({\n    minWidth: containerWidth\n  }, dropdownStyle);\n  if (typeof dropdownMatchSelectWidth === 'number') {\n    popupStyle.width = dropdownMatchSelectWidth;\n  } else if (dropdownMatchSelectWidth) {\n    popupStyle.width = containerWidth;\n  }\n  return /*#__PURE__*/React.createElement(Trigger, _extends({}, restProps, {\n    showAction: onPopupVisibleChange ? ['click'] : [],\n    hideAction: onPopupVisibleChange ? ['click'] : [],\n    popupPlacement: placement || (direction === 'rtl' ? 'bottomRight' : 'bottomLeft'),\n    builtinPlacements: mergedBuiltinPlacements,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: mergedTransitionName,\n    popup: /*#__PURE__*/React.createElement(\"div\", {\n      ref: popupRef,\n      onMouseEnter: onPopupMouseEnter\n    }, popupNode),\n    popupAlign: dropdownAlign,\n    popupVisible: visible,\n    getPopupContainer: getPopupContainer,\n    popupClassName: classNames(dropdownClassName, _defineProperty({}, \"\".concat(dropdownPrefixCls, \"-empty\"), empty)),\n    popupStyle: popupStyle,\n    getTriggerDOMNode: getTriggerDOMNode,\n    onPopupVisibleChange: onPopupVisibleChange\n  }), children);\n};\nvar RefSelectTrigger = /*#__PURE__*/React.forwardRef(SelectTrigger);\nRefSelectTrigger.displayName = 'SelectTrigger';\nexport default RefSelectTrigger;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "_objectWithoutProperties", "_excluded", "<PERSON><PERSON>", "classNames", "React", "getBuiltInPlacements", "dropdownMatchSelectWidth", "adjustX", "bottomLeft", "points", "offset", "overflow", "adjustY", "htmlRegion", "bottomRight", "topLeft", "topRight", "SelectTrigger", "props", "ref", "prefixCls", "disabled", "visible", "children", "popupElement", "containerWidth", "animation", "transitionName", "dropdownStyle", "dropdownClassName", "_props$direction", "direction", "placement", "builtinPlacements", "dropdownRender", "dropdownAlign", "getPopupContainer", "empty", "getTriggerDOMNode", "onPopupVisibleChange", "onPopupMouseEnter", "restProps", "dropdownPrefixCls", "concat", "popupNode", "mergedBuiltinPlacements", "useMemo", "mergedTransitionName", "popupRef", "useRef", "useImperativeHandle", "getPopupElement", "current", "popupStyle", "min<PERSON><PERSON><PERSON>", "width", "createElement", "showAction", "hideAction", "popupPlacement", "popupTransitionName", "popup", "onMouseEnter", "popupAlign", "popupVisible", "popupClassName", "RefSelectTrigger", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-select/es/SelectTrigger.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"disabled\", \"visible\", \"children\", \"popupElement\", \"containerWidth\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"direction\", \"placement\", \"builtinPlacements\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"getPopupContainer\", \"empty\", \"getTriggerDOMNode\", \"onPopupVisibleChange\", \"onPopupMouseEnter\"];\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar getBuiltInPlacements = function getBuiltInPlacements(dropdownMatchSelectWidth) {\n  // Enable horizontal overflow auto-adjustment when a custom dropdown width is provided\n  var adjustX = dropdownMatchSelectWidth === true ? 0 : 1;\n  return {\n    bottomLeft: {\n      points: ['tl', 'bl'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    bottomRight: {\n      points: ['tr', 'br'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    topLeft: {\n      points: ['bl', 'tl'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    topRight: {\n      points: ['br', 'tr'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    }\n  };\n};\nvar SelectTrigger = function SelectTrigger(props, ref) {\n  var prefixCls = props.prefixCls,\n    disabled = props.disabled,\n    visible = props.visible,\n    children = props.children,\n    popupElement = props.popupElement,\n    containerWidth = props.containerWidth,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction,\n    placement = props.placement,\n    builtinPlacements = props.builtinPlacements,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    getPopupContainer = props.getPopupContainer,\n    empty = props.empty,\n    getTriggerDOMNode = props.getTriggerDOMNode,\n    onPopupVisibleChange = props.onPopupVisibleChange,\n    onPopupMouseEnter = props.onPopupMouseEnter,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var popupNode = popupElement;\n  if (dropdownRender) {\n    popupNode = dropdownRender(popupElement);\n  }\n  var mergedBuiltinPlacements = React.useMemo(function () {\n    return builtinPlacements || getBuiltInPlacements(dropdownMatchSelectWidth);\n  }, [builtinPlacements, dropdownMatchSelectWidth]);\n\n  // ===================== Motion ======================\n  var mergedTransitionName = animation ? \"\".concat(dropdownPrefixCls, \"-\").concat(animation) : transitionName;\n\n  // ======================= Ref =======================\n  var popupRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      getPopupElement: function getPopupElement() {\n        return popupRef.current;\n      }\n    };\n  });\n  var popupStyle = _objectSpread({\n    minWidth: containerWidth\n  }, dropdownStyle);\n  if (typeof dropdownMatchSelectWidth === 'number') {\n    popupStyle.width = dropdownMatchSelectWidth;\n  } else if (dropdownMatchSelectWidth) {\n    popupStyle.width = containerWidth;\n  }\n  return /*#__PURE__*/React.createElement(Trigger, _extends({}, restProps, {\n    showAction: onPopupVisibleChange ? ['click'] : [],\n    hideAction: onPopupVisibleChange ? ['click'] : [],\n    popupPlacement: placement || (direction === 'rtl' ? 'bottomRight' : 'bottomLeft'),\n    builtinPlacements: mergedBuiltinPlacements,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: mergedTransitionName,\n    popup: /*#__PURE__*/React.createElement(\"div\", {\n      ref: popupRef,\n      onMouseEnter: onPopupMouseEnter\n    }, popupNode),\n    popupAlign: dropdownAlign,\n    popupVisible: visible,\n    getPopupContainer: getPopupContainer,\n    popupClassName: classNames(dropdownClassName, _defineProperty({}, \"\".concat(dropdownPrefixCls, \"-empty\"), empty)),\n    popupStyle: popupStyle,\n    getTriggerDOMNode: getTriggerDOMNode,\n    onPopupVisibleChange: onPopupVisibleChange\n  }), children);\n};\nvar RefSelectTrigger = /*#__PURE__*/React.forwardRef(SelectTrigger);\nRefSelectTrigger.displayName = 'SelectTrigger';\nexport default RefSelectTrigger;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,WAAW,EAAE,WAAW,EAAE,mBAAmB,EAAE,0BAA0B,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,OAAO,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,mBAAmB,CAAC;AACrX,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,wBAAwB,EAAE;EACjF;EACA,IAAIC,OAAO,GAAGD,wBAAwB,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;EACvD,OAAO;IACLE,UAAU,EAAE;MACVC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX,CAAC;MACDC,UAAU,EAAE;IACd,CAAC;IACDC,WAAW,EAAE;MACXL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX,CAAC;MACDC,UAAU,EAAE;IACd,CAAC;IACDE,OAAO,EAAE;MACPN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACfC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX,CAAC;MACDC,UAAU,EAAE;IACd,CAAC;IACDG,QAAQ,EAAE;MACRP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACfC,QAAQ,EAAE;QACRJ,OAAO,EAAEA,OAAO;QAChBK,OAAO,EAAE;MACX,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC;AACH,CAAC;AACD,IAAII,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,OAAO,GAAGJ,KAAK,CAACI,OAAO;IACvBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,YAAY,GAAGN,KAAK,CAACM,YAAY;IACjCC,cAAc,GAAGP,KAAK,CAACO,cAAc;IACrCC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,cAAc,GAAGT,KAAK,CAACS,cAAc;IACrCC,aAAa,GAAGV,KAAK,CAACU,aAAa;IACnCC,iBAAiB,GAAGX,KAAK,CAACW,iBAAiB;IAC3CC,gBAAgB,GAAGZ,KAAK,CAACa,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEE,SAAS,GAAGd,KAAK,CAACc,SAAS;IAC3BC,iBAAiB,GAAGf,KAAK,CAACe,iBAAiB;IAC3C3B,wBAAwB,GAAGY,KAAK,CAACZ,wBAAwB;IACzD4B,cAAc,GAAGhB,KAAK,CAACgB,cAAc;IACrCC,aAAa,GAAGjB,KAAK,CAACiB,aAAa;IACnCC,iBAAiB,GAAGlB,KAAK,CAACkB,iBAAiB;IAC3CC,KAAK,GAAGnB,KAAK,CAACmB,KAAK;IACnBC,iBAAiB,GAAGpB,KAAK,CAACoB,iBAAiB;IAC3CC,oBAAoB,GAAGrB,KAAK,CAACqB,oBAAoB;IACjDC,iBAAiB,GAAGtB,KAAK,CAACsB,iBAAiB;IAC3CC,SAAS,GAAGzC,wBAAwB,CAACkB,KAAK,EAAEjB,SAAS,CAAC;EACxD,IAAIyC,iBAAiB,GAAG,EAAE,CAACC,MAAM,CAACvB,SAAS,EAAE,WAAW,CAAC;EACzD,IAAIwB,SAAS,GAAGpB,YAAY;EAC5B,IAAIU,cAAc,EAAE;IAClBU,SAAS,GAAGV,cAAc,CAACV,YAAY,CAAC;EAC1C;EACA,IAAIqB,uBAAuB,GAAGzC,KAAK,CAAC0C,OAAO,CAAC,YAAY;IACtD,OAAOb,iBAAiB,IAAI5B,oBAAoB,CAACC,wBAAwB,CAAC;EAC5E,CAAC,EAAE,CAAC2B,iBAAiB,EAAE3B,wBAAwB,CAAC,CAAC;;EAEjD;EACA,IAAIyC,oBAAoB,GAAGrB,SAAS,GAAG,EAAE,CAACiB,MAAM,CAACD,iBAAiB,EAAE,GAAG,CAAC,CAACC,MAAM,CAACjB,SAAS,CAAC,GAAGC,cAAc;;EAE3G;EACA,IAAIqB,QAAQ,GAAG5C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACjC7C,KAAK,CAAC8C,mBAAmB,CAAC/B,GAAG,EAAE,YAAY;IACzC,OAAO;MACLgC,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;QAC1C,OAAOH,QAAQ,CAACI,OAAO;MACzB;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIC,UAAU,GAAGtD,aAAa,CAAC;IAC7BuD,QAAQ,EAAE7B;EACZ,CAAC,EAAEG,aAAa,CAAC;EACjB,IAAI,OAAOtB,wBAAwB,KAAK,QAAQ,EAAE;IAChD+C,UAAU,CAACE,KAAK,GAAGjD,wBAAwB;EAC7C,CAAC,MAAM,IAAIA,wBAAwB,EAAE;IACnC+C,UAAU,CAACE,KAAK,GAAG9B,cAAc;EACnC;EACA,OAAO,aAAarB,KAAK,CAACoD,aAAa,CAACtD,OAAO,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAE4C,SAAS,EAAE;IACvEgB,UAAU,EAAElB,oBAAoB,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE;IACjDmB,UAAU,EAAEnB,oBAAoB,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE;IACjDoB,cAAc,EAAE3B,SAAS,KAAKD,SAAS,KAAK,KAAK,GAAG,aAAa,GAAG,YAAY,CAAC;IACjFE,iBAAiB,EAAEY,uBAAuB;IAC1CzB,SAAS,EAAEsB,iBAAiB;IAC5BkB,mBAAmB,EAAEb,oBAAoB;IACzCc,KAAK,EAAE,aAAazD,KAAK,CAACoD,aAAa,CAAC,KAAK,EAAE;MAC7CrC,GAAG,EAAE6B,QAAQ;MACbc,YAAY,EAAEtB;IAChB,CAAC,EAAEI,SAAS,CAAC;IACbmB,UAAU,EAAE5B,aAAa;IACzB6B,YAAY,EAAE1C,OAAO;IACrBc,iBAAiB,EAAEA,iBAAiB;IACpC6B,cAAc,EAAE9D,UAAU,CAAC0B,iBAAiB,EAAE/B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC6C,MAAM,CAACD,iBAAiB,EAAE,QAAQ,CAAC,EAAEL,KAAK,CAAC,CAAC;IACjHgB,UAAU,EAAEA,UAAU;IACtBf,iBAAiB,EAAEA,iBAAiB;IACpCC,oBAAoB,EAAEA;EACxB,CAAC,CAAC,EAAEhB,QAAQ,CAAC;AACf,CAAC;AACD,IAAI2C,gBAAgB,GAAG,aAAa9D,KAAK,CAAC+D,UAAU,CAAClD,aAAa,CAAC;AACnEiD,gBAAgB,CAACE,WAAW,GAAG,eAAe;AAC9C,eAAeF,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}