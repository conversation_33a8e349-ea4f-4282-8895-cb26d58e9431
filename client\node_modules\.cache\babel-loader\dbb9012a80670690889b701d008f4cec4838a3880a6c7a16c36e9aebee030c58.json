{"ast": null, "code": "// https://zhuanlan.zhihu.com/p/32746810\nexport default function getFontSizes(base) {\n  const fontSizes = new Array(10).fill(null).map((_, index) => {\n    const i = index - 1;\n    const baseSize = base * Math.pow(2.71828, i / 5);\n    const intSize = index > 1 ? Math.floor(baseSize) : Math.ceil(baseSize);\n    // Convert to even\n    return Math.floor(intSize / 2) * 2;\n  });\n  fontSizes[1] = base;\n  return fontSizes.map(size => {\n    const height = size + 8;\n    return {\n      size,\n      lineHeight: height / size\n    };\n  });\n}", "map": {"version": 3, "names": ["getFontSizes", "base", "fontSizes", "Array", "fill", "map", "_", "index", "i", "baseSize", "Math", "pow", "intSize", "floor", "ceil", "size", "height", "lineHeight"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/theme/themes/shared/genFontSizes.js"], "sourcesContent": ["// https://zhuanlan.zhihu.com/p/32746810\nexport default function getFontSizes(base) {\n  const fontSizes = new Array(10).fill(null).map((_, index) => {\n    const i = index - 1;\n    const baseSize = base * Math.pow(2.71828, i / 5);\n    const intSize = index > 1 ? Math.floor(baseSize) : Math.ceil(baseSize);\n    // Convert to even\n    return Math.floor(intSize / 2) * 2;\n  });\n  fontSizes[1] = base;\n  return fontSizes.map(size => {\n    const height = size + 8;\n    return {\n      size,\n      lineHeight: height / size\n    };\n  });\n}"], "mappings": "AAAA;AACA,eAAe,SAASA,YAAYA,CAACC,IAAI,EAAE;EACzC,MAAMC,SAAS,GAAG,IAAIC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;IAC3D,MAAMC,CAAC,GAAGD,KAAK,GAAG,CAAC;IACnB,MAAME,QAAQ,GAAGR,IAAI,GAAGS,IAAI,CAACC,GAAG,CAAC,OAAO,EAAEH,CAAC,GAAG,CAAC,CAAC;IAChD,MAAMI,OAAO,GAAGL,KAAK,GAAG,CAAC,GAAGG,IAAI,CAACG,KAAK,CAACJ,QAAQ,CAAC,GAAGC,IAAI,CAACI,IAAI,CAACL,QAAQ,CAAC;IACtE;IACA,OAAOC,IAAI,CAACG,KAAK,CAACD,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;EACpC,CAAC,CAAC;EACFV,SAAS,CAAC,CAAC,CAAC,GAAGD,IAAI;EACnB,OAAOC,SAAS,CAACG,GAAG,CAACU,IAAI,IAAI;IAC3B,MAAMC,MAAM,GAAGD,IAAI,GAAG,CAAC;IACvB,OAAO;MACLA,IAAI;MACJE,UAAU,EAAED,MAAM,GAAGD;IACvB,CAAC;EACH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}