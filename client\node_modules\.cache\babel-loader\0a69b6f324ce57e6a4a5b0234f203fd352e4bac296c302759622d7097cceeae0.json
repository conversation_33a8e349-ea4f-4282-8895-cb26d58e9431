{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState, useMemo } from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { isInViewPort } from \"../util\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nexport default function useTarget(target, open, gap, scrollIntoViewOptions) {\n  // ========================= Target =========================\n  // We trade `undefined` as not get target by function yet.\n  // `null` as empty target.\n  var _useState = useState(undefined),\n    _useState2 = _slicedToArray(_useState, 2),\n    targetElement = _useState2[0],\n    setTargetElement = _useState2[1];\n  useLayoutEffect(function () {\n    var nextElement = typeof target === 'function' ? target() : target;\n    setTargetElement(nextElement || null);\n  });\n\n  // ========================= Align ==========================\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    posInfo = _useState4[0],\n    setPosInfo = _useState4[1];\n  var updatePos = useEvent(function () {\n    if (targetElement) {\n      // Exist target element. We should scroll and get target position\n      if (!isInViewPort(targetElement) && open) {\n        targetElement.scrollIntoView(scrollIntoViewOptions);\n      }\n      var _targetElement$getBou = targetElement.getBoundingClientRect(),\n        left = _targetElement$getBou.left,\n        top = _targetElement$getBou.top,\n        width = _targetElement$getBou.width,\n        height = _targetElement$getBou.height;\n      var nextPosInfo = {\n        left: left,\n        top: top,\n        width: width,\n        height: height,\n        radius: 0\n      };\n      setPosInfo(function (origin) {\n        if (JSON.stringify(origin) !== JSON.stringify(nextPosInfo)) {\n          return nextPosInfo;\n        }\n        return origin;\n      });\n    } else {\n      // Not exist target which means we just show in center\n      setPosInfo(null);\n    }\n  });\n  useLayoutEffect(function () {\n    updatePos();\n    // update when window resize\n    window.addEventListener('resize', updatePos);\n    return function () {\n      window.removeEventListener('resize', updatePos);\n    };\n  }, [targetElement, open, updatePos]);\n\n  // ======================== PosInfo =========================\n  var mergedPosInfo = useMemo(function () {\n    if (!posInfo) {\n      return posInfo;\n    }\n    var gapOffset = (gap === null || gap === void 0 ? void 0 : gap.offset) || 6;\n    var gapRadius = (gap === null || gap === void 0 ? void 0 : gap.radius) || 2;\n    return {\n      left: posInfo.left - gapOffset,\n      top: posInfo.top - gapOffset,\n      width: posInfo.width + gapOffset * 2,\n      height: posInfo.height + gapOffset * 2,\n      radius: gapRadius\n    };\n  }, [posInfo, gap]);\n  return [mergedPosInfo, targetElement];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useState", "useMemo", "useLayoutEffect", "isInViewPort", "useEvent", "useTarget", "target", "open", "gap", "scrollIntoViewOptions", "_useState", "undefined", "_useState2", "targetElement", "setTargetElement", "nextElement", "_useState3", "_useState4", "posInfo", "setPosInfo", "updatePos", "scrollIntoView", "_targetElement$getBou", "getBoundingClientRect", "left", "top", "width", "height", "nextPosInfo", "radius", "origin", "JSON", "stringify", "window", "addEventListener", "removeEventListener", "mergedPosInfo", "gapOffset", "offset", "gapRadius"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/tour/es/hooks/useTarget.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState, useMemo } from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { isInViewPort } from \"../util\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nexport default function useTarget(target, open, gap, scrollIntoViewOptions) {\n  // ========================= Target =========================\n  // We trade `undefined` as not get target by function yet.\n  // `null` as empty target.\n  var _useState = useState(undefined),\n    _useState2 = _slicedToArray(_useState, 2),\n    targetElement = _useState2[0],\n    setTargetElement = _useState2[1];\n  useLayoutEffect(function () {\n    var nextElement = typeof target === 'function' ? target() : target;\n    setTargetElement(nextElement || null);\n  });\n\n  // ========================= Align ==========================\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    posInfo = _useState4[0],\n    setPosInfo = _useState4[1];\n  var updatePos = useEvent(function () {\n    if (targetElement) {\n      // Exist target element. We should scroll and get target position\n      if (!isInViewPort(targetElement) && open) {\n        targetElement.scrollIntoView(scrollIntoViewOptions);\n      }\n      var _targetElement$getBou = targetElement.getBoundingClientRect(),\n        left = _targetElement$getBou.left,\n        top = _targetElement$getBou.top,\n        width = _targetElement$getBou.width,\n        height = _targetElement$getBou.height;\n      var nextPosInfo = {\n        left: left,\n        top: top,\n        width: width,\n        height: height,\n        radius: 0\n      };\n      setPosInfo(function (origin) {\n        if (JSON.stringify(origin) !== JSON.stringify(nextPosInfo)) {\n          return nextPosInfo;\n        }\n        return origin;\n      });\n    } else {\n      // Not exist target which means we just show in center\n      setPosInfo(null);\n    }\n  });\n  useLayoutEffect(function () {\n    updatePos();\n    // update when window resize\n    window.addEventListener('resize', updatePos);\n    return function () {\n      window.removeEventListener('resize', updatePos);\n    };\n  }, [targetElement, open, updatePos]);\n\n  // ======================== PosInfo =========================\n  var mergedPosInfo = useMemo(function () {\n    if (!posInfo) {\n      return posInfo;\n    }\n    var gapOffset = (gap === null || gap === void 0 ? void 0 : gap.offset) || 6;\n    var gapRadius = (gap === null || gap === void 0 ? void 0 : gap.radius) || 2;\n    return {\n      left: posInfo.left - gapOffset,\n      top: posInfo.top - gapOffset,\n      width: posInfo.width + gapOffset * 2,\n      height: posInfo.height + gapOffset * 2,\n      radius: gapRadius\n    };\n  }, [posInfo, gap]);\n  return [mergedPosInfo, targetElement];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AACzC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,YAAY,QAAQ,SAAS;AACtC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,eAAe,SAASC,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,qBAAqB,EAAE;EAC1E;EACA;EACA;EACA,IAAIC,SAAS,GAAGV,QAAQ,CAACW,SAAS,CAAC;IACjCC,UAAU,GAAGb,cAAc,CAACW,SAAS,EAAE,CAAC,CAAC;IACzCG,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAClCV,eAAe,CAAC,YAAY;IAC1B,IAAIa,WAAW,GAAG,OAAOT,MAAM,KAAK,UAAU,GAAGA,MAAM,CAAC,CAAC,GAAGA,MAAM;IAClEQ,gBAAgB,CAACC,WAAW,IAAI,IAAI,CAAC;EACvC,CAAC,CAAC;;EAEF;EACA,IAAIC,UAAU,GAAGhB,QAAQ,CAAC,IAAI,CAAC;IAC7BiB,UAAU,GAAGlB,cAAc,CAACiB,UAAU,EAAE,CAAC,CAAC;IAC1CE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC5B,IAAIG,SAAS,GAAGhB,QAAQ,CAAC,YAAY;IACnC,IAAIS,aAAa,EAAE;MACjB;MACA,IAAI,CAACV,YAAY,CAACU,aAAa,CAAC,IAAIN,IAAI,EAAE;QACxCM,aAAa,CAACQ,cAAc,CAACZ,qBAAqB,CAAC;MACrD;MACA,IAAIa,qBAAqB,GAAGT,aAAa,CAACU,qBAAqB,CAAC,CAAC;QAC/DC,IAAI,GAAGF,qBAAqB,CAACE,IAAI;QACjCC,GAAG,GAAGH,qBAAqB,CAACG,GAAG;QAC/BC,KAAK,GAAGJ,qBAAqB,CAACI,KAAK;QACnCC,MAAM,GAAGL,qBAAqB,CAACK,MAAM;MACvC,IAAIC,WAAW,GAAG;QAChBJ,IAAI,EAAEA,IAAI;QACVC,GAAG,EAAEA,GAAG;QACRC,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA,MAAM;QACdE,MAAM,EAAE;MACV,CAAC;MACDV,UAAU,CAAC,UAAUW,MAAM,EAAE;QAC3B,IAAIC,IAAI,CAACC,SAAS,CAACF,MAAM,CAAC,KAAKC,IAAI,CAACC,SAAS,CAACJ,WAAW,CAAC,EAAE;UAC1D,OAAOA,WAAW;QACpB;QACA,OAAOE,MAAM;MACf,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAX,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC,CAAC;EACFjB,eAAe,CAAC,YAAY;IAC1BkB,SAAS,CAAC,CAAC;IACX;IACAa,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEd,SAAS,CAAC;IAC5C,OAAO,YAAY;MACjBa,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEf,SAAS,CAAC;IACjD,CAAC;EACH,CAAC,EAAE,CAACP,aAAa,EAAEN,IAAI,EAAEa,SAAS,CAAC,CAAC;;EAEpC;EACA,IAAIgB,aAAa,GAAGnC,OAAO,CAAC,YAAY;IACtC,IAAI,CAACiB,OAAO,EAAE;MACZ,OAAOA,OAAO;IAChB;IACA,IAAImB,SAAS,GAAG,CAAC7B,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC8B,MAAM,KAAK,CAAC;IAC3E,IAAIC,SAAS,GAAG,CAAC/B,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACqB,MAAM,KAAK,CAAC;IAC3E,OAAO;MACLL,IAAI,EAAEN,OAAO,CAACM,IAAI,GAAGa,SAAS;MAC9BZ,GAAG,EAAEP,OAAO,CAACO,GAAG,GAAGY,SAAS;MAC5BX,KAAK,EAAER,OAAO,CAACQ,KAAK,GAAGW,SAAS,GAAG,CAAC;MACpCV,MAAM,EAAET,OAAO,CAACS,MAAM,GAAGU,SAAS,GAAG,CAAC;MACtCR,MAAM,EAAEU;IACV,CAAC;EACH,CAAC,EAAE,CAACrB,OAAO,EAAEV,GAAG,CAAC,CAAC;EAClB,OAAO,CAAC4B,aAAa,EAAEvB,aAAa,CAAC;AACvC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}