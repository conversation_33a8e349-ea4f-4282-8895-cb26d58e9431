{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport Dropdown from 'rc-dropdown';\nimport Menu, { MenuItem } from 'rc-menu';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { useEffect, useState } from 'react';\nimport AddButton from \"./AddButton\";\nimport { getRemovable } from \"../util\";\nfunction OperationNode(_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n    id = _ref.id,\n    tabs = _ref.tabs,\n    locale = _ref.locale,\n    mobile = _ref.mobile,\n    _ref$moreIcon = _ref.moreIcon,\n    moreIcon = _ref$moreIcon === void 0 ? 'More' : _ref$moreIcon,\n    moreTransitionName = _ref.moreTransitionName,\n    style = _ref.style,\n    className = _ref.className,\n    editable = _ref.editable,\n    tabBarGutter = _ref.tabBarGutter,\n    rtl = _ref.rtl,\n    removeAriaLabel = _ref.removeAriaLabel,\n    onTabClick = _ref.onTabClick,\n    getPopupContainer = _ref.getPopupContainer,\n    popupClassName = _ref.popupClassName;\n  // ======================== Dropdown ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedKey = _useState4[0],\n    setSelectedKey = _useState4[1];\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var menu = /*#__PURE__*/React.createElement(Menu, {\n    onClick: function onClick(_ref2) {\n      var key = _ref2.key,\n        domEvent = _ref2.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    prefixCls: \"\".concat(dropdownPrefix, \"-menu\"),\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var closable = tab.closable,\n      disabled = tab.disabled,\n      closeIcon = tab.closeIcon,\n      key = tab.key,\n      label = tab.label;\n    var removable = getRemovable(closable, closeIcon, editable, disabled);\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: key,\n      id: \"\".concat(popupId, \"-\").concat(key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n      disabled: disabled\n    }, /*#__PURE__*/React.createElement(\"span\", null, label), removable && /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, key);\n      }\n    }, closeIcon || editable.removeIcon || '×'));\n  }));\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n  function onKeyDown(e) {\n    var which = e.which;\n    if (!open) {\n      if ([KeyCode.DOWN, KeyCode.SPACE, KeyCode.ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n      return;\n    }\n    switch (which) {\n      case KeyCode.UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n      case KeyCode.DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n      case KeyCode.ESC:\n        setOpen(false);\n        break;\n      case KeyCode.SPACE:\n      case KeyCode.ENTER:\n        if (selectedKey !== null) onTabClick(selectedKey, e);\n        break;\n    }\n  }\n\n  // ========================= Effect =========================\n  useEffect(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  useEffect(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]);\n\n  // ========================= Render =========================\n  var moreStyle = _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n  var overlayClassName = classNames(_defineProperty({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/React.createElement(Dropdown, {\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    trigger: ['hover'],\n    visible: tabs.length ? open : false,\n    transitionName: moreTransitionName,\n    onVisibleChange: setOpen,\n    overlayClassName: classNames(overlayClassName, popupClassName),\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    getPopupContainer: getPopupContainer\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\",\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/React.createElement(AddButton, {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n}\nexport default /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(OperationNode), function (_, next) {\n  return (\n    // https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n});", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "classNames", "Dropdown", "<PERSON><PERSON>", "MenuItem", "KeyCode", "React", "useEffect", "useState", "AddButton", "getRemovable", "OperationNode", "_ref", "ref", "prefixCls", "id", "tabs", "locale", "mobile", "_ref$moreIcon", "moreIcon", "moreTransitionName", "style", "className", "editable", "tabBarGutter", "rtl", "removeAriaLabel", "onTabClick", "getPopupContainer", "popupClassName", "_useState", "_useState2", "open", "<PERSON><PERSON><PERSON>", "_useState3", "_useState4", "<PERSON><PERSON><PERSON>", "setSelectedKey", "popupId", "concat", "dropdownPrefix", "selectedItemId", "dropdownAriaLabel", "onRemoveTab", "event", "key", "preventDefault", "stopPropagation", "onEdit", "menu", "createElement", "onClick", "_ref2", "domEvent", "tabIndex", "role", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "map", "tab", "closable", "disabled", "closeIcon", "label", "removable", "type", "e", "removeIcon", "selectOffset", "offset", "enabledTabs", "filter", "selectedIndex", "findIndex", "len", "length", "i", "onKeyDown", "which", "DOWN", "SPACE", "ENTER", "includes", "UP", "ESC", "ele", "document", "getElementById", "scrollIntoView", "moreStyle", "visibility", "order", "overlayClassName", "moreNode", "overlay", "trigger", "visible", "transitionName", "onVisibleChange", "mouseEnterDelay", "mouseLeaveDelay", "memo", "forwardRef", "_", "next", "tabMoving"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tabs/es/TabNavList/OperationNode.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport Dropdown from 'rc-dropdown';\nimport Menu, { MenuItem } from 'rc-menu';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { useEffect, useState } from 'react';\nimport AddButton from \"./AddButton\";\nimport { getRemovable } from \"../util\";\nfunction OperationNode(_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n    id = _ref.id,\n    tabs = _ref.tabs,\n    locale = _ref.locale,\n    mobile = _ref.mobile,\n    _ref$moreIcon = _ref.moreIcon,\n    moreIcon = _ref$moreIcon === void 0 ? 'More' : _ref$moreIcon,\n    moreTransitionName = _ref.moreTransitionName,\n    style = _ref.style,\n    className = _ref.className,\n    editable = _ref.editable,\n    tabBarGutter = _ref.tabBarGutter,\n    rtl = _ref.rtl,\n    removeAriaLabel = _ref.removeAriaLabel,\n    onTabClick = _ref.onTabClick,\n    getPopupContainer = _ref.getPopupContainer,\n    popupClassName = _ref.popupClassName;\n  // ======================== Dropdown ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedKey = _useState4[0],\n    setSelectedKey = _useState4[1];\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var menu = /*#__PURE__*/React.createElement(Menu, {\n    onClick: function onClick(_ref2) {\n      var key = _ref2.key,\n        domEvent = _ref2.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    prefixCls: \"\".concat(dropdownPrefix, \"-menu\"),\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var closable = tab.closable,\n      disabled = tab.disabled,\n      closeIcon = tab.closeIcon,\n      key = tab.key,\n      label = tab.label;\n    var removable = getRemovable(closable, closeIcon, editable, disabled);\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: key,\n      id: \"\".concat(popupId, \"-\").concat(key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n      disabled: disabled\n    }, /*#__PURE__*/React.createElement(\"span\", null, label), removable && /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, key);\n      }\n    }, closeIcon || editable.removeIcon || '×'));\n  }));\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n  function onKeyDown(e) {\n    var which = e.which;\n    if (!open) {\n      if ([KeyCode.DOWN, KeyCode.SPACE, KeyCode.ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n      return;\n    }\n    switch (which) {\n      case KeyCode.UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n      case KeyCode.DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n      case KeyCode.ESC:\n        setOpen(false);\n        break;\n      case KeyCode.SPACE:\n      case KeyCode.ENTER:\n        if (selectedKey !== null) onTabClick(selectedKey, e);\n        break;\n    }\n  }\n\n  // ========================= Effect =========================\n  useEffect(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  useEffect(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]);\n\n  // ========================= Render =========================\n  var moreStyle = _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n  var overlayClassName = classNames(_defineProperty({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/React.createElement(Dropdown, {\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    trigger: ['hover'],\n    visible: tabs.length ? open : false,\n    transitionName: moreTransitionName,\n    onVisibleChange: setOpen,\n    overlayClassName: classNames(overlayClassName, popupClassName),\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    getPopupContainer: getPopupContainer\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\",\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/React.createElement(AddButton, {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n}\nexport default /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(OperationNode), function (_, next) {\n  return (\n    // https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n});"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,IAAI,IAAIC,QAAQ,QAAQ,SAAS;AACxC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,YAAY,QAAQ,SAAS;AACtC,SAASC,aAAaA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAChC,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC5BC,EAAE,GAAGH,IAAI,CAACG,EAAE;IACZC,IAAI,GAAGJ,IAAI,CAACI,IAAI;IAChBC,MAAM,GAAGL,IAAI,CAACK,MAAM;IACpBC,MAAM,GAAGN,IAAI,CAACM,MAAM;IACpBC,aAAa,GAAGP,IAAI,CAACQ,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,aAAa;IAC5DE,kBAAkB,GAAGT,IAAI,CAACS,kBAAkB;IAC5CC,KAAK,GAAGV,IAAI,CAACU,KAAK;IAClBC,SAAS,GAAGX,IAAI,CAACW,SAAS;IAC1BC,QAAQ,GAAGZ,IAAI,CAACY,QAAQ;IACxBC,YAAY,GAAGb,IAAI,CAACa,YAAY;IAChCC,GAAG,GAAGd,IAAI,CAACc,GAAG;IACdC,eAAe,GAAGf,IAAI,CAACe,eAAe;IACtCC,UAAU,GAAGhB,IAAI,CAACgB,UAAU;IAC5BC,iBAAiB,GAAGjB,IAAI,CAACiB,iBAAiB;IAC1CC,cAAc,GAAGlB,IAAI,CAACkB,cAAc;EACtC;EACA,IAAIC,SAAS,GAAGvB,QAAQ,CAAC,KAAK,CAAC;IAC7BwB,UAAU,GAAGhC,cAAc,CAAC+B,SAAS,EAAE,CAAC,CAAC;IACzCE,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;IACpBE,OAAO,GAAGF,UAAU,CAAC,CAAC,CAAC;EACzB,IAAIG,UAAU,GAAG3B,QAAQ,CAAC,IAAI,CAAC;IAC7B4B,UAAU,GAAGpC,cAAc,CAACmC,UAAU,EAAE,CAAC,CAAC;IAC1CE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC3BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;EAChC,IAAIG,OAAO,GAAG,EAAE,CAACC,MAAM,CAACzB,EAAE,EAAE,aAAa,CAAC;EAC1C,IAAI0B,cAAc,GAAG,EAAE,CAACD,MAAM,CAAC1B,SAAS,EAAE,WAAW,CAAC;EACtD,IAAI4B,cAAc,GAAGL,WAAW,KAAK,IAAI,GAAG,EAAE,CAACG,MAAM,CAACD,OAAO,EAAE,GAAG,CAAC,CAACC,MAAM,CAACH,WAAW,CAAC,GAAG,IAAI;EAC9F,IAAIM,iBAAiB,GAAG1B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC0B,iBAAiB;EAChG,SAASC,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/BD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;IACvBxB,QAAQ,CAACyB,MAAM,CAAC,QAAQ,EAAE;MACxBH,GAAG,EAAEA,GAAG;MACRD,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EACA,IAAIK,IAAI,GAAG,aAAa5C,KAAK,CAAC6C,aAAa,CAAChD,IAAI,EAAE;IAChDiD,OAAO,EAAE,SAASA,OAAOA,CAACC,KAAK,EAAE;MAC/B,IAAIP,GAAG,GAAGO,KAAK,CAACP,GAAG;QACjBQ,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC3B1B,UAAU,CAACkB,GAAG,EAAEQ,QAAQ,CAAC;MACzBpB,OAAO,CAAC,KAAK,CAAC;IAChB,CAAC;IACDpB,SAAS,EAAE,EAAE,CAAC0B,MAAM,CAACC,cAAc,EAAE,OAAO,CAAC;IAC7C1B,EAAE,EAAEwB,OAAO;IACXgB,QAAQ,EAAE,CAAC,CAAC;IACZC,IAAI,EAAE,SAAS;IACf,uBAAuB,EAAEd,cAAc;IACvCe,YAAY,EAAE,CAACpB,WAAW,CAAC;IAC3B,YAAY,EAAEM,iBAAiB,KAAKe,SAAS,GAAGf,iBAAiB,GAAG;EACtE,CAAC,EAAE3B,IAAI,CAAC2C,GAAG,CAAC,UAAUC,GAAG,EAAE;IACzB,IAAIC,QAAQ,GAAGD,GAAG,CAACC,QAAQ;MACzBC,QAAQ,GAAGF,GAAG,CAACE,QAAQ;MACvBC,SAAS,GAAGH,GAAG,CAACG,SAAS;MACzBjB,GAAG,GAAGc,GAAG,CAACd,GAAG;MACbkB,KAAK,GAAGJ,GAAG,CAACI,KAAK;IACnB,IAAIC,SAAS,GAAGvD,YAAY,CAACmD,QAAQ,EAAEE,SAAS,EAAEvC,QAAQ,EAAEsC,QAAQ,CAAC;IACrE,OAAO,aAAaxD,KAAK,CAAC6C,aAAa,CAAC/C,QAAQ,EAAE;MAChD0C,GAAG,EAAEA,GAAG;MACR/B,EAAE,EAAE,EAAE,CAACyB,MAAM,CAACD,OAAO,EAAE,GAAG,CAAC,CAACC,MAAM,CAACM,GAAG,CAAC;MACvCU,IAAI,EAAE,QAAQ;MACd,eAAe,EAAEzC,EAAE,IAAI,EAAE,CAACyB,MAAM,CAACzB,EAAE,EAAE,SAAS,CAAC,CAACyB,MAAM,CAACM,GAAG,CAAC;MAC3DgB,QAAQ,EAAEA;IACZ,CAAC,EAAE,aAAaxD,KAAK,CAAC6C,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEa,KAAK,CAAC,EAAEC,SAAS,IAAI,aAAa3D,KAAK,CAAC6C,aAAa,CAAC,QAAQ,EAAE;MAChHe,IAAI,EAAE,QAAQ;MACd,YAAY,EAAEvC,eAAe,IAAI,QAAQ;MACzC4B,QAAQ,EAAE,CAAC;MACXhC,SAAS,EAAE,EAAE,CAACiB,MAAM,CAACC,cAAc,EAAE,mBAAmB,CAAC;MACzDW,OAAO,EAAE,SAASA,OAAOA,CAACe,CAAC,EAAE;QAC3BA,CAAC,CAACnB,eAAe,CAAC,CAAC;QACnBJ,WAAW,CAACuB,CAAC,EAAErB,GAAG,CAAC;MACrB;IACF,CAAC,EAAEiB,SAAS,IAAIvC,QAAQ,CAAC4C,UAAU,IAAI,GAAG,CAAC,CAAC;EAC9C,CAAC,CAAC,CAAC;EACH,SAASC,YAAYA,CAACC,MAAM,EAAE;IAC5B,IAAIC,WAAW,GAAGvD,IAAI,CAACwD,MAAM,CAAC,UAAUZ,GAAG,EAAE;MAC3C,OAAO,CAACA,GAAG,CAACE,QAAQ;IACtB,CAAC,CAAC;IACF,IAAIW,aAAa,GAAGF,WAAW,CAACG,SAAS,CAAC,UAAUd,GAAG,EAAE;MACvD,OAAOA,GAAG,CAACd,GAAG,KAAKT,WAAW;IAChC,CAAC,CAAC,IAAI,CAAC;IACP,IAAIsC,GAAG,GAAGJ,WAAW,CAACK,MAAM;IAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;MAC/BJ,aAAa,GAAG,CAACA,aAAa,GAAGH,MAAM,GAAGK,GAAG,IAAIA,GAAG;MACpD,IAAIf,GAAG,GAAGW,WAAW,CAACE,aAAa,CAAC;MACpC,IAAI,CAACb,GAAG,CAACE,QAAQ,EAAE;QACjBxB,cAAc,CAACsB,GAAG,CAACd,GAAG,CAAC;QACvB;MACF;IACF;EACF;EACA,SAASgC,SAASA,CAACX,CAAC,EAAE;IACpB,IAAIY,KAAK,GAAGZ,CAAC,CAACY,KAAK;IACnB,IAAI,CAAC9C,IAAI,EAAE;MACT,IAAI,CAAC5B,OAAO,CAAC2E,IAAI,EAAE3E,OAAO,CAAC4E,KAAK,EAAE5E,OAAO,CAAC6E,KAAK,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAAC,EAAE;QAChE7C,OAAO,CAAC,IAAI,CAAC;QACbiC,CAAC,CAACpB,cAAc,CAAC,CAAC;MACpB;MACA;IACF;IACA,QAAQgC,KAAK;MACX,KAAK1E,OAAO,CAAC+E,EAAE;QACbf,YAAY,CAAC,CAAC,CAAC,CAAC;QAChBF,CAAC,CAACpB,cAAc,CAAC,CAAC;QAClB;MACF,KAAK1C,OAAO,CAAC2E,IAAI;QACfX,YAAY,CAAC,CAAC,CAAC;QACfF,CAAC,CAACpB,cAAc,CAAC,CAAC;QAClB;MACF,KAAK1C,OAAO,CAACgF,GAAG;QACdnD,OAAO,CAAC,KAAK,CAAC;QACd;MACF,KAAK7B,OAAO,CAAC4E,KAAK;MAClB,KAAK5E,OAAO,CAAC6E,KAAK;QAChB,IAAI7C,WAAW,KAAK,IAAI,EAAET,UAAU,CAACS,WAAW,EAAE8B,CAAC,CAAC;QACpD;IACJ;EACF;;EAEA;EACA5D,SAAS,CAAC,YAAY;IACpB;IACA,IAAI+E,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAAC9C,cAAc,CAAC;IACjD,IAAI4C,GAAG,IAAIA,GAAG,CAACG,cAAc,EAAE;MAC7BH,GAAG,CAACG,cAAc,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,EAAE,CAACpD,WAAW,CAAC,CAAC;EACjB9B,SAAS,CAAC,YAAY;IACpB,IAAI,CAAC0B,IAAI,EAAE;MACTK,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,CAACL,IAAI,CAAC,CAAC;;EAEV;EACA,IAAIyD,SAAS,GAAG3F,eAAe,CAAC,CAAC,CAAC,EAAE2B,GAAG,GAAG,aAAa,GAAG,YAAY,EAAED,YAAY,CAAC;EACrF,IAAI,CAACT,IAAI,CAAC4D,MAAM,EAAE;IAChBc,SAAS,CAACC,UAAU,GAAG,QAAQ;IAC/BD,SAAS,CAACE,KAAK,GAAG,CAAC;EACrB;EACA,IAAIC,gBAAgB,GAAG5F,UAAU,CAACF,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACyC,MAAM,CAACC,cAAc,EAAE,MAAM,CAAC,EAAEf,GAAG,CAAC,CAAC;EAC9F,IAAIoE,QAAQ,GAAG5E,MAAM,GAAG,IAAI,GAAG,aAAaZ,KAAK,CAAC6C,aAAa,CAACjD,QAAQ,EAAE;IACxEY,SAAS,EAAE2B,cAAc;IACzBsD,OAAO,EAAE7C,IAAI;IACb8C,OAAO,EAAE,CAAC,OAAO,CAAC;IAClBC,OAAO,EAAEjF,IAAI,CAAC4D,MAAM,GAAG3C,IAAI,GAAG,KAAK;IACnCiE,cAAc,EAAE7E,kBAAkB;IAClC8E,eAAe,EAAEjE,OAAO;IACxB2D,gBAAgB,EAAE5F,UAAU,CAAC4F,gBAAgB,EAAE/D,cAAc,CAAC;IAC9DsE,eAAe,EAAE,GAAG;IACpBC,eAAe,EAAE,GAAG;IACpBxE,iBAAiB,EAAEA;EACrB,CAAC,EAAE,aAAavB,KAAK,CAAC6C,aAAa,CAAC,QAAQ,EAAE;IAC5Ce,IAAI,EAAE,QAAQ;IACd3C,SAAS,EAAE,EAAE,CAACiB,MAAM,CAAC1B,SAAS,EAAE,WAAW,CAAC;IAC5CQ,KAAK,EAAEoE,SAAS;IAChBnC,QAAQ,EAAE,CAAC,CAAC;IACZ,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE,SAAS;IAC1B,eAAe,EAAEhB,OAAO;IACxBxB,EAAE,EAAE,EAAE,CAACyB,MAAM,CAACzB,EAAE,EAAE,OAAO,CAAC;IAC1B,eAAe,EAAEkB,IAAI;IACrB6C,SAAS,EAAEA;EACb,CAAC,EAAE1D,QAAQ,CAAC,CAAC;EACb,OAAO,aAAad,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;IAC7C5B,SAAS,EAAEtB,UAAU,CAAC,EAAE,CAACuC,MAAM,CAAC1B,SAAS,EAAE,iBAAiB,CAAC,EAAES,SAAS,CAAC;IACzED,KAAK,EAAEA,KAAK;IACZT,GAAG,EAAEA;EACP,CAAC,EAAEiF,QAAQ,EAAE,aAAaxF,KAAK,CAAC6C,aAAa,CAAC1C,SAAS,EAAE;IACvDK,SAAS,EAAEA,SAAS;IACpBG,MAAM,EAAEA,MAAM;IACdO,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL;AACA,eAAe,aAAalB,KAAK,CAACgG,IAAI,EAAE,aAAahG,KAAK,CAACiG,UAAU,CAAC5F,aAAa,CAAC,EAAE,UAAU6F,CAAC,EAAEC,IAAI,EAAE;EACvG;IACE;IACA;IACAA,IAAI,CAACC;EAAS;AAElB,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}